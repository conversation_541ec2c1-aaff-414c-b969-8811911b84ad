<template>
  <FlotoContentLoader :loading="loading">
    <FlotoModuleNoData
      v-if="noData"
      :module="`inventory-${category.toLowerCase()}`"
    />
    <div
      v-else-if="isWidgetView"
      class="flex flex-1 min-h-0 flex-col dashboard-container pl-2 -ml-2"
    >
      <TemplateView
        :widget-params="widgetParams"
        for-group-template
        :template-type="templateType"
        :template-id="templateId"
        use-force-drill-down
        @force-drill-down="handleForceDrillDown"
      />
    </div>
    <div v-else class="flex flex-1 min-h-0 flex-col px-2">
      <InventoryGrid
        :category="category"
        :group-ids="groupIds"
        :cloud-filters="CloudServiceTypeFilterContext.filters"
        @change-widget-view="setWidgetView"
      />
    </div>
    <GaugeDrilldownDrawer
      :open="Boolean(showDrillDownFor)"
      :item="showDrillDownFor"
      @hide="showDrillDownFor = null"
    />
  </FlotoContentLoader>
</template>

<script>
import Constants from '@constants'
import InventoryGrid from '../components/inventory-grid.vue'
import { getTemplateIdApi } from '../inventory-api'
import { objectDBWorker } from '@/src/workers/index'
import { UserPreferenceMethods } from '@/src/state/modules/user-preference'
import GaugeDrilldownDrawer from '@components/widgets/views/components/gauge-drilldown-drawer.vue'

export default {
  name: 'GroupTemplate',
  page() {
    return {
      title: `${
        this.InventoryViewContext.target
          ? this.InventoryViewContext.target.resourceType === 'CLOUD_TYPE'
            ? this.InventoryViewContext.target.id
            : this.InventoryViewContext.target.name
          : this.category
      }`,
    }
  },
  components: {
    InventoryGrid,
    GaugeDrilldownDrawer,
  },
  inject: {
    groupHierarchyContext: { default: { hierarchy: [] } },
    groupContext: { default: { options: new Map() } },
    InventoryViewContext: { default: {} },
    CloudServiceTypeFilterContext: { default: { filters: {} } },
  },
  data() {
    return {
      loading: true,
      noData: false,
      templateId: undefined,
      templateType: undefined,
      showDrillDownFor: null,
    }
  },
  computed: {
    heatmapAttrs() {
      if (this.category === Constants.VIRTUALIZATION) {
        return {
          firstLevelGroupKey: 'type',
          denseColumns: 2,
        }
      }
      return {
        firstLevelGroupKey: 'cloudType',
        secondLevelGroupKey: 'type',
      }
    },
    isWidgetView() {
      if (
        [
          Constants.SERVICE,
          Constants.SERVICE_CHECK,
          Constants.CONTAINER_ORCHESTRATION,
        ].includes(this.category)
      ) {
        return false
      }
      return this.InventoryViewContext.view === 'widget'
    },
    category() {
      return this.$route.params.category
    },
    groupIds() {
      if (this.InventoryViewContext.target) {
        if (this.InventoryViewContext.target.resourceType === 'CLOUD_TYPE') {
          return Array.from(this.groupContext.options.values())
            .filter((g) => g.groupName === this.InventoryViewContext.target.id)
            .map((i) => i.key)
        }

        return Array.from(this.groupContext.options.values())
          .filter((g) => g.id === this.InventoryViewContext.target.id)
          .map((i) => i.key)
      }

      return []
    },
    widgetParams() {
      if (
        this.InventoryViewContext.target &&
        this.InventoryViewContext.target.resourceType === 'CLOUD_TYPE'
      ) {
        const groups = Array.from(this.groupContext.options.values()).filter(
          (g) => g.groupName === this.InventoryViewContext.target.id
        )
        return {
          ...(groups && groups.length
            ? {
                'entity.type': 'Group',
                entities: groups.map((g) => g.key),
              }
            : {}),
          ...(Object.keys(this.CloudServiceTypeFilterContext.filters).length > 0
            ? { filters: this.CloudServiceTypeFilterContext.filters }
            : {}),
        }
      }
      if (
        [
          this.$constants.SERVICE,
          this.$constants.PROCESS,
          this.$constants.INTERFACE,
        ].includes(this.category)
      ) {
        return {
          'entity.type': 'category',
          entities: [
            [this.$constants.SERVICE, this.$constants.PROCESS].includes(
              this.category
            )
              ? this.$constants.SERVER
              : this.$constants.NETWORK,
          ],
        }
      }
      return {
        'entity.type': 'category',
        entities: [this.category],
      }
    },
  },
  watch: {
    'InventoryViewContext.view': {
      handler(newValue, oldValue) {
        if (oldValue && newValue) {
          if (newValue !== oldValue) {
            if (newValue === 'grid') {
              this.loading = false
            }
          }
        }
      },
    },
    noData: {
      handler(newValue) {
        this.$nextTick(() => {
          this.$root.$emit('no-data', newValue)
        })
      },
      immediate: true,
    },
    // 'InventoryViewContext.target': {
    //   handler(newValue, oldValue) {
    //     if (newValue && newValue.resourceType === 'region') {
    //       this.loading = true
    //       this.fetchRegionTemplate()
    //     }
    //   },
    // },
  },
  async created() {
    const isPreviouslyRendered =
      await this.getStaticLendingPagePreferenceByModule({
        module: `${this.category}-inventory`,
      })
    if (
      [
        Constants.SERVICE_CHECK,
        // Constants.VIRTUALIZATION,
        Constants.SERVICE,
        Constants.STORAGE,
        Constants.CONTAINER_ORCHESTRATION,
      ].includes(this.category)
    ) {
      this.loading = false
      let hasData = isPreviouslyRendered

      if (!hasData) {
        hasData = await this.checkIfHasData()
      }

      if (!hasData) {
        this.noData = true
      } else {
        if (!isPreviouslyRendered)
          await this.updateStaticLendingPagePreferences({
            lendingPagePreferences: { [`${this.category}-inventory`]: true },
          })
      }
    } else {
      // if (this.category === Constants.CLOUD) {
      //   const target = this.InventoryViewContext.target
      //   if ((target || {}).key || (target || {}).id) {
      //     this.fetchTemplate()
      //   } else {
      //     this.loading = false
      //   }
      //   return
      // }
      let hasData = isPreviouslyRendered

      if (!hasData) {
        hasData = await this.checkIfHasData()
      }

      if (hasData) {
        if (!isPreviouslyRendered)
          await this.updateStaticLendingPagePreferences({
            lendingPagePreferences: { [`${this.category}-inventory`]: true },
          })

        this.fetchTemplate()
      } else {
        this.noData = true
        this.loading = false
      }
    }
  },
  methods: {
    ...UserPreferenceMethods,
    async checkIfHasData() {
      if (
        this.InventoryViewContext.target &&
        this.InventoryViewContext.target.resourceType === 'CLOUD_TYPE'
      ) {
        return
      }
      let category = this.category
      if (
        [
          this.$constants.SERVICE,
          this.$constants.PROCESS,
          this.$constants.CONTAINER,
        ].includes(this.category)
      ) {
        category = this.$constants.SERVER
      } else if (
        this.$constants.INTERFACE === this.category ||
        this.$constants.WAN_LINK === this.category
      ) {
        category = this.$constants.NETWORK
      }

      let objectCount = await objectDBWorker.getObjectCounts({ category })
      if (objectCount <= 0) {
        return false
      } else {
        return true
      }
    },
    setWidgetView() {
      this.InventoryViewContext.setCurrentView('widget')
    },
    fetchTemplate() {
      const target = this.InventoryViewContext.target
      getTemplateIdApi(
        'Group',
        target && (target.key || target.id),
        this.category
      )
        .then((template) => {
          this.templateId = template['template.id']
          this.templateType = template['template.type']
          this.loading = false
        })
        .catch((e) => {
          throw new Error(e)
        })
    },
    // fetchRegionTemplate() {
    //   const target = this.InventoryViewContext.target
    //   getTemplateIdApi(
    //     'Group',
    //     target && target.resourceType !== 'region' ? target.key : undefined,
    //     this.category,
    //     { cloudType: target.cloudType }
    //   )
    //     .then((template) => {
    //       this.templateId = template['template.id']
    //       this.loading = false
    //     })
    //     .catch((e) => {
    //       throw new Error(e)
    //     })
    // },
    handleForceDrillDown(e) {
      const drillDownObjectType = e.event?.point?.category
        ? e.event?.point?.category
        : undefined

      const severity = e.event?.point?.category
        ? e.event?.point?.series?.userOptions?.name ||
          e.event?.point?.category ||
          e.event?.point?.name
        : e.event?.point?.category || e.event?.point?.name

      let widget = e.widget

      widget.groups[0].resultBy = []
      this.showDrillDownFor = {
        type: 'policy',
        widget: widget,
        drilldownSeries: e.event?.point?.category
          ? { name: severity }
          : e?.event?.point?.series?.userOptions?.data[e?.event?.point?.index],
        timeRange: e.widget.timeRange,
        drillDownObjectType: drillDownObjectType,
        category: this.category,

        forGroupTemplate: true,
      }

      // this.$router.push(
      //   this.$modules.getModuleRoute('alert', 'stream', {
      //     params: {
      //       tab: 'metric',
      //       category: this.category,
      //     },
      //     query: {
      //       view: 'live',

      //       ...(severity
      //         ? {
      //             ...(severity ? { drillDownSeverity: severity } : {}),
      //           }
      //         : {}),
      //       ...(drillDownObjectType
      //         ? {
      //             ...(drillDownObjectType
      //               ? { drillDownObjectType: drillDownObjectType }
      //               : {}),
      //           }
      //         : {}),
      //     },
      //   })
      // )
    },
  },
}
</script>
