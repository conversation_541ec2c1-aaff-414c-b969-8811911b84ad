<template>
  <FlotoContentLoader :loading="loading">
    <MonitorTypeProvider :device-types="deviceCategories">
      <MPersistedColumns
        v-model="columns"
        :default-value="availableColumns"
        :module-key="`inventory-${category}`"
        :available-columns="availableColumns"
      >
        <template
          v-slot="{
            columns: persistedColumns,
            setColumns: updatePersistedColumns,
          }"
        >
          <div class="flex flex-1 flex-col min-h-0">
            <div class="mt-3">
              <div class="flex">
                <div class="flex-1">
                  <MInput
                    v-model="searchTerm"
                    name="search-agent"
                    class="search-box"
                    placeholder="Search"
                  >
                    <template v-slot:prefix>
                      <MIcon name="search" />
                    </template>
                    <template v-if="searchTerm" v-slot:suffix>
                      <MIcon
                        name="times-circle"
                        class="text-neutral-light cursor-pointer"
                        @click="searchTerm = undefined"
                      />
                    </template>
                  </MInput>
                </div>
                <div class="flex-1 text-right">
                  <MPermissionChecker
                    :permission="$constants.MY_ACCOUNT_UPDATE_PERMISSION"
                  >
                    <ColumnSelector
                      v-model="columns"
                      :columns="availableColumns"
                      @change="updatePersistedColumns"
                    />
                  </MPermissionChecker>
                  <ObjectTagPicker
                    v-if="shouldShowTags"
                    :value="selectedTags"
                    use-popover
                    :disabled-options="disabledTagOptions"
                    @change="onChangeTagSelection"
                  />

                  <MButton
                    :shadow="false"
                    class="squared-button mr-2"
                    :rounded="false"
                    title="Export As PDF"
                    variant="neutral-lightest"
                    @click="exportGrid('pdf')"
                  >
                    <MIcon name="export-pdf" />
                  </MButton>
                  <MButton
                    :shadow="false"
                    class="squared-button mr-2"
                    :rounded="false"
                    variant="neutral-lightest"
                    title="Export As CSV"
                    @click="exportGrid('csv')"
                  >
                    <MIcon name="export-csv" />
                  </MButton>
                  <MButton
                    id="btn-filter-agent"
                    title="Filter"
                    :shadow="false"
                    class="squared-button mr-2"
                    :rounded="false"
                    :variant="
                      showFilters ? 'neutral-lighter' : 'neutral-lightest'
                    "
                    @click="showFilters = !showFilters"
                  >
                    <MIcon name="filter" />
                  </MButton>
                </div>
              </div>
              <div v-if="showFilters" class="mt-4">
                <MRow>
                  <MCol :size="12">
                    <InventoryFilters
                      v-model="appliedFilters"
                      class="px-4"
                      :category="category"
                      :monitor-options="monitorOptions"
                      :is-instance-grid="isInstanceCategoryGrid"
                      @change="showFilters = false"
                      @hide="showFilters = !showFilters"
                    />
                  </MCol>
                </MRow>
              </div>
            </div>
            <MGrid
              ref="gridRef"
              :columns="persistedColumns"
              :data="items"
              :filters="filters"
              :search-term="searchTerm"
              @loaded-once="onGridLoaded"
              @current-page-ids="setCurrentPageObjectIds"
              @column-change="updatePersistedColumns"
            >
              <template v-slot:monitor="{ item }">
                <div class="flex min-w-0 items-center">
                  <Severity :severity="item.severity" class="mr-2" />
                  <FlotoLink
                    v-if="
                      !ignoreCloudTypeToDrilldown.includes(
                        (item.type || '').toLowerCase()
                      )
                    "
                    :to="
                      $currentModule.getRoute('monitor-template', {
                        params: {
                          monitorId: [
                            $constants.CLOUD,
                            $constants.STORAGE,
                          ].includes(category)
                            ? item.id
                            : item.monitorId,
                          category: category,
                        },
                      })
                    "
                    class="text-ellipsis"
                  >
                    {{
                      category === $constants.SERVICE_CHECK
                        ? item.monitorName
                        : item.name || item.target || item.ip
                    }}
                  </FlotoLink>
                  <span v-else>
                    {{
                      category === $constants.SERVICE_CHECK
                        ? item.monitorName
                        : item.name
                    }}
                  </span>
                </div>
              </template>
              <template v-slot:name="{ item }">
                {{ item.name }}
              </template>
              <template v-slot:interfaceName="{ item }">
                <div class="flex flex-1 min-w-0">
                  <Severity
                    :severity="item.severity"
                    counter="status"
                    class="mr-2"
                  />
                  <a class="text-ellipsis" @click.stop="showTemplateFor = item">
                    {{ item.interface }}
                  </a>
                </div>
              </template>

              <template v-slot:interfaceNameValue="{ item }">
                {{ item.interfaceName }}
              </template>
              <template v-slot:image="{ item }">
                {{ item.image }}
              </template>
              <template v-slot:serviceName="{ item }">
                <div class="flex flex-1 min-w-0">
                  <Severity
                    :severity="item.severity"
                    counter="status"
                    class="mr-2"
                  />
                  <span class="text-ellipsis">
                    {{ item.name }}
                  </span>
                </div>
              </template>
              <template v-slot:processName="{ item }">
                <div class="flex flex-1 min-w-0">
                  <Severity
                    :severity="item.severity"
                    counter="status"
                    class="mr-2"
                  />
                  <a class="text-ellipsis" @click.stop="showTemplateFor = item">
                    {{
                      item.processName === undefined
                        ? item.name !== undefined && item.name.includes('|')
                          ? item.name.split('|')[0]
                          : item.name
                        : item.processName
                    }}
                  </a>
                  <MTooltip>
                    <template v-slot:trigger>
                      <MIcon
                        name="info-circle"
                        class="text-primary text-sm ml-1"
                      />
                    </template>
                    {{ item.name }}
                  </MTooltip>
                </div>
              </template>
              <template v-slot:wanLinkName="{ item }">
                <div class="flex flex-1 min-w-0">
                  <Severity
                    :severity="item.severity"
                    counter="status"
                    class="mr-2"
                  />
                  <a class="text-ellipsis" @click.stop="showTemplateFor = item">
                    {{ item.name }}
                  </a>
                </div>
              </template>
              <template v-slot:containerName="{ item }">
                <div class="flex flex-1 min-w-0">
                  <Severity
                    :severity="item.severity"
                    counter="status"
                    class="mr-2"
                  />
                  <a class="text-ellipsis" @click.stop="showTemplateFor = item">
                    {{ item.container }}
                  </a>
                </div>
              </template>
              <template v-slot:monitorName="{ item }">
                <FlotoLink
                  :to="
                    $currentModule.getRoute('monitor-template', {
                      params: {
                        monitorId: item.monitorId,
                        category: [
                          $constants.WAN_LINK,
                          $constants.INTERFACE,
                        ].includes(category)
                          ? $constants.NETWORK
                          : [
                              $constants.PROCESS,
                              $constants.SERVICE,
                              $constants.CONTAINER,
                            ].includes(category)
                          ? $constants.SERVER
                          : category,
                      },
                    })
                  "
                >
                  {{ item.monitorName }}
                </FlotoLink>
              </template>
              <template v-slot:groups="{ item }">
                <GroupPicker
                  :value="item.groups"
                  multiple
                  disabled
                  :wrap="false"
                />
              </template>
              <template v-slot:exactly="{ props }">
                {{ props.dataItem[props.field] }}
              </template>
              <template v-slot:type="{ item }">
                <div class="flex min-w-0 overflow-hidden">
                  <div class="mr-2">
                    <MonitorType disable-tooltip :type="item.type" />
                  </div>
                  <div>
                    <MonitorType
                      v-if="
                        item.objectDiscoveryMethod ===
                          $constants.AGENT_METHOD || item.isAgent
                      "
                      disable-tooltip
                      :type="$constants.AGENT"
                    />
                  </div>
                </div>
              </template>
              <template v-slot:apps="{ item }">
                <MonitorApplicationList :value="item.apps" />
              </template>
              <template v-slot:monitorInterfaces="{ item }">
                <div
                  v-if="item.interfaces !== undefined"
                  class="used-count-pill ant-tag rounded"
                  :class="{ 'cursor-pointer': item.interfaces > 0 }"
                  @click="handleShowInterfaceDetails(item)"
                >
                  {{ item.interfaces }}
                </div>
                <span v-else />
              </template>
              <template v-slot:dockerProcesses="{ item }">
                <div
                  v-if="item.process !== undefined"
                  class="used-count-pill ant-tag rounded"
                  :class="{ 'cursor-pointer': item.process > 0 }"
                >
                  {{ item.process }}
                </div>
                <span v-else />
              </template>
              <template v-slot:status="{ item }">
                <MStatusTag :status="item.statusFormatted || item.status" />
              </template>
              <template v-slot:label="{ item, props }">
                <div
                  v-if="item[props.field] !== undefined"
                  class="used-count-pill ant-tag rounded cursor-auto"
                >
                  <template v-if="item[`${props.field}Formatted`]">
                    {{ item[`${props.field}Formatted`] }}
                  </template>
                  <template v-else>
                    {{ item[props.field] | numberFormat }}
                  </template>
                </div>
              </template>
              <template v-slot:vms="{ item }">
                <div
                  v-if="item.vms > 0"
                  class="used-count-pill ant-tag rounded cursor-auto"
                  :class="{ 'cursor-pointer': item.vms > 0 }"
                  @click="handleShowVmDetails(item)"
                >
                  {{ item.vms || 0 }}
                </div>
                <span v-else />
              </template>
              <template v-slot:flowStatus="{ item }">
                <MIcon
                  v-if="item.flowStatus"
                  name="check-circle"
                  size="lg"
                  class="text-secondary-green"
                />
              </template>
              <template v-slot:inTraffic="{ item }">
                <div
                  v-if="item.inTraffic !== undefined"
                  class="used-count-pill ant-tag rounded cursor-auto"
                >
                  <MIcon
                    name="long-arrow-right"
                    size="lg"
                    class="text-primary mr-1"
                  />
                  {{ item.inTrafficFormatted }}
                </div>
              </template>
              <template v-slot:outTraffic="{ item }">
                <div
                  v-if="item.outTraffic !== undefined"
                  class="used-count-pill ant-tag rounded cursor-auto"
                >
                  <MIcon
                    name="long-arrow-left"
                    size="lg"
                    class="text-primary mr-1"
                  />
                  {{ item.outTrafficFormatted }}
                </div>
              </template>
              <template v-slot:progress="{ item, props }">
                <div
                  v-if="item[props.field] !== undefined"
                  class="flex flex-col"
                >
                  <Progress
                    :width="parseInt(item[props.field] || 0)"
                    :type="'normal'"
                  />
                  <div class="text-neutral-light" style="font-size: 11px">
                    {{ item[props.field] || 0 }}%
                  </div>
                </div>
              </template>
              <template v-slot:tags="{ item }">
                <LooseTags :value="item.tags" disabled />
              </template>
              <template v-slot:instanceTags="{ item }">
                <LooseTags :value="item.instanceTags" disabled />
              </template>

              <template v-slot:alerts="{ item }">
                <ObjectAlertCounts :item="item" />
              </template>

              <template v-slot:creationTime="{ item }">
                {{ item.creationTime | datetime }}
              </template>
              <!-- <template v-slot:wanProbe="{ item }">
                {{ wanProbeMap[item.wanProbe] }}
              </template> -->
            </MGrid>
            <MRow
              v-if="paginationLabelOffset"
              class="absolute mb-3 severity-row"
              :style="{
                bottom: 0,
                left: `${paginationLabelOffset}px`,
              }"
            >
              <MCol id="agent-status" class="flex" auto-size>
                <div
                  v-for="severity in severities"
                  :key="severity.key"
                  class="mr-2 flex items-center"
                >
                  <Severity :severity="severity.key" class="mr-1" />
                  {{ severity.text }}
                </div>
              </MCol>
            </MRow>
            <MonitorInstanceGrid
              v-if="shouldShowInstanceGrid"
              :open="showInstanceGrid"
              :columns="instanceGridColumns"
              :category="category"
              :title="instanceGridData.title"
              :monitor="instanceGridData.resource"
              @hide="handleHideInstanceDetails"
            />
            <InterfaceTemplate
              v-if="$constants.INTERFACE === category"
              :open="Boolean(showTemplateFor)"
              :interface-item="showTemplateFor"
              @hide="showTemplateFor = null"
            />
            <ProcessTemplate
              v-if="$constants.PROCESS === category"
              :open="Boolean(showTemplateFor)"
              :process="showTemplateFor"
              @hide="showTemplateFor = null"
            />
            <WanLinkTemplate
              v-if="$constants.WAN_LINK === category"
              :open="Boolean(showTemplateFor)"
              :wan-link-item="showTemplateFor"
              @hide="showTemplateFor = null"
            />
            <ContainerTemplate
              v-if="$constants.CONTAINER === category"
              :open="Boolean(showTemplateFor)"
              :container-item="showTemplateFor"
              @hide="showTemplateFor = null"
            />
          </div>
        </template>
      </MPersistedColumns>
    </MonitorTypeProvider>

    <!-- <FlotoModuleNoData v-else :module="`inventory-${category.toLowerCase()}`" /> -->
  </FlotoContentLoader>
</template>

<script>
import Intersection from 'lodash/intersection'
import Capitalize from 'lodash/capitalize'
import Debounce from 'lodash/debounce'
import Flatten from 'lodash/flatten'
import Uniq from 'lodash/uniq'

// import Throttle from 'lodash/throttle'
import IsEqual from 'lodash/isEqual'
import MonitorType from '@components/monitor-type.vue'
import { generateId } from '@utils/id'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import { UserPreferenceMethods } from '@/src/state/modules/user-preference'

import MonitorTypeProvider from '@components/data-provider/monitor-type-provider.vue'
import MonitorApplicationList from '@modules/settings/monitoring/components/monitor-application-list'
import Severity from '@components/severity.vue'
import ColumnSelector from '@components/column-selector.vue'
import Constants from '@constants'
import { SEVERITY_MAP } from '@data/monitor'
import { objectDBWorker, severityDBWorker } from '@/src/workers'
import InterfaceTemplate from '@components/templates/interface-template.vue'
import ProcessTemplate from '@components/templates/process-template.vue'
import WanLinkTemplate from '@components/templates/wan-link-template.vue'
import ContainerTemplate from '@components/templates/container-template.vue'
import LooseTags from '@components/loose-tags.vue'
import Bus from '@utils/emitter'
import Progress from '@components/progress.vue'
import exportData from '@modules/settings/monitoring/helpers/export-pdf-csv'
import { downloadFile } from '@utils/download'
import { getMonitorsApi } from '@modules/settings/monitoring/monitors-api'
import {
  COLUMN_MAP,
  INVENTORY_COLUMNS,
  IGNORE_CLOUD_TYPE_TO_DRILLDOWN,
} from '../helpers/inventory'
import InventoryFilters from './inventory-grid/inventory-filters.vue'
import MonitorInstanceGrid from './inventory-grid/monitor-instance-grid.vue'
import {
  getWidgetForAlertCountData,
  CATEGORY_ID_MAP,
  INVENTORY_SERVERSIDE_DEF,
} from '../helpers/data-widgets'
import ObjectAlertCounts from './inventory-grid/object-alert-counts.vue'
import ObjectTagPicker from '@components/data-picker/object-tag-picker.vue'
import { wanProbeMap } from '@/src/components/rediscover-results/rediscover-api'

const INSTANCE_GRID_COLUMNS = {
  [Constants.NETWORK]: [
    {
      key: 'interface',
      name: 'Interface Name',
      searchable: true,
      sortable: true,
    },
    { key: 'alias', name: 'Alias', searchable: true, sortable: true },
    {
      key: 'status',
      name: 'Status',
      searchable: true,
      sortable: true,
      width: '100px',
      searchKey: 'statusFormatted',
    },
    // {
    //   key: 'flowStatus',
    //   name: 'Flow Status',
    //   searchable: false,
    //   sortable: false,
    //   width: '120px',
    // },
    {
      key: 'inTraffic',
      name: 'IN Traffic',
      searchable: true,
      sortable: true,
    },
    {
      key: 'outTraffic',
      name: 'OUT Traffic',
      searchable: true,
      sortable: true,
    },
    // {
    //   key: 'instanceTags',
    //   name: 'Tags',
    //   searchable: true,
    //   sortable: true,
    //   hidden: false,
    //   searchKey: 'tagsStr',
    //   sortKey: 'tagsStr',
    //   width: '100px',
    //   minWidth: '100px',
    // },
    {
      key: 'receivedErrorPackets',
      name: 'Received Error Packets',
      searchable: true,
      sortable: true,
      align: 'center',
    },
    {
      key: 'sendErrorPackets',
      name: 'Sent Error Packets',
      searchable: true,
      sortable: true,
      align: 'center',
    },
    {
      key: 'trafficPercent',
      name: 'Interface Utilization',
      searchable: true,
      sortable: true,
    },
  ],
  [Constants.VIRTUALIZATION]: [
    { key: 'name', name: 'Name', searchable: true, sortable: true },
    {
      key: 'type',
      name: 'Type',
      searchable: true,
      sortable: true,
      align: 'center',
      width: '150px',
    },
    { key: 'instanceIp', name: 'IP', searchable: true, sortable: true },
    // {
    //   key: 'tags',
    //   name: 'Tags',
    //   searchable: true,
    //   sortable: true,
    //   hidden: false,
    //   searchKey: 'tagsStr',
    //   sortKey: 'tagsStr',
    //   width: '100px',
    //   minWidth: '100px',
    // },
    {
      key: 'status',
      name: 'Status',
      searchable: true,
      sortable: true,
      searchKey: 'statusFormatted',
    },
  ],
  [Constants.HYPERCONVERGED_INFRASTRUCTURE]: [
    { key: 'name', name: 'Name', searchable: true, sortable: true },
    {
      key: 'type',
      name: 'Type',
      searchable: true,
      sortable: true,
      align: 'center',
      width: '150px',
    },
    { key: 'instanceIp', name: 'IP', searchable: true, sortable: true },
    // {
    //   key: 'tags',
    //   name: 'Tags',
    //   searchable: true,
    //   sortable: true,
    //   hidden: false,
    //   searchKey: 'tagsStr',
    //   sortKey: 'tagsStr',
    //   width: '100px',
    //   minWidth: '100px',
    // },
    {
      key: 'status',
      name: 'Status',
      searchable: true,
      sortable: true,
      searchKey: 'statusFormatted',
    },
  ],
}

export default {
  name: 'InventoryGrid',
  components: {
    MonitorType,
    Progress,
    MonitorTypeProvider,
    MonitorApplicationList,
    Severity,
    ColumnSelector,
    InventoryFilters,
    MonitorInstanceGrid,
    InterfaceTemplate,
    ProcessTemplate,
    WanLinkTemplate,
    LooseTags,
    ObjectAlertCounts,
    ObjectTagPicker,
    ContainerTemplate,
  },
  inject: { monitoringFieldsContext: { default: { options: new Map() } } },
  props: {
    category: { type: String, required: true },
    groupIds: {
      type: [Array],
      default() {
        return []
      },
    },
    cloudFilters: {
      type: [Object],
      default() {
        return {}
      },
    },
  },
  data() {
    this.MAX_TAG_SELECTION_LIMIT = 5
    this.wanProbeMap = wanProbeMap
    this._heartbeatInterval = null
    this.severities = Object.keys(SEVERITY_MAP)
      .filter(
        (s) =>
          ![
            'UP',
            'MAINTENANCE',
            'Disable',
            'SUSPENDED',
            'UNKNOWN',
            'DISABLE',
          ].includes(s)
      )
      .map((s) => ({
        key: s,
        text: Capitalize(s),
      }))
    return {
      items: [],
      currentPageObjectIds: [],
      searchTerm: undefined,
      paginationLabelOffset: 0,
      showFilters: false,
      counters: {},
      columns: Object.freeze(INVENTORY_COLUMNS[this.category]),
      appliedFilters: {
        groups: [],
        types: [],
        severity: [],
        apps: [],
        status: undefined,
        tags: [],
        wanProbe: undefined,
        monitors: [],
      },
      monitorOptions: [],
      showInstanceGrid: false,
      instanceGridData: {
        title: undefined,
      },
      showTemplateFor: null,
      uuid: generateId(),
      progress: undefined,
      loading: false,
      widgetDef: {},
      selectedTags: [],
      tagOptions: [],
    }
  },
  computed: {
    ...UserPreferenceComputed,
    shouldShowTags() {
      return ![
        Constants.CLOUD,
        // Constants.WAN_LINK,
        Constants.SERVICE,
        Constants.STORAGE,
        // Constants.CONTAINER,
      ].includes(this.category)
    },
    instanceGridColumns() {
      return INSTANCE_GRID_COLUMNS[this.category]
    },
    shouldShowInstanceGrid() {
      return [
        Constants.NETWORK,
        Constants.VIRTUALIZATION,
        Constants.HYPERCONVERGED_INFRASTRUCTURE,
        Constants.SDN,
      ].includes(this.category)
    },
    deviceCategories() {
      const currentCategory = this.category
      if (
        [Constants.SERVICE, Constants.PROCESS, Constants.CONTAINER].includes(
          currentCategory
        )
      ) {
        return [Constants.SERVER]
      } else if (
        [Constants.NETWORK, Constants.INTERFACE, Constants.WAN_LINK].includes(
          currentCategory
        )
      ) {
        return [Constants.NETWORK, Constants.WIRELESS]
      }
      return [this.category]
    },
    availableColumns() {
      if (
        [
          Constants.SERVICE,
          Constants.PROCESS,
          Constants.INTERFACE,
          Constants.WAN_LINK,
          Constants.CONTAINER,
        ].includes(this.category)
      ) {
        return INVENTORY_COLUMNS[this.category].concat(
          this.selectedTags?.length
            ? this.selectedTags.map((tag) => ({
                key: tag,
                name: tag,
                searchable: true,
                sortable: true,
                width: '200px',
                cellRender: 'exactly',
              }))
            : []
        )
      } else {
        const monitoringFields = Array.from(
          this.monitoringFieldsContext.options.values()
        )
        return [
          ...INVENTORY_COLUMNS[this.category],
          ...monitoringFields.map(({ key, name }) => ({
            key: this.hasSingleSortRequest ? name.toLowerCase() : String(key),
            name,
            searchable: true,
            sortable: true,
            width: '200px',
            cellRender: 'exactly',
            hidden: true,
          })),
          ...(this.selectedTags
            ? this.selectedTags.map((tag) => ({
                key: tag,
                name: tag,
                searchable: true,
                sortable: true,
                width: '200px',
                cellRender: 'exactly',
              }))
            : []),
        ]
      }
    },
    isInstanceCategoryGrid() {
      return [
        this.$constants.INTERFACE,
        this.$constants.PROCESS,
        this.$constants.SERVICE,
        this.$constants.WAN_LINK,
      ].includes(this.category)
    },

    hasSingleSortRequest() {
      return [
        this.$constants.SERVER,
        this.$constants.NETWORK,
        this.$constants.VIRTUALIZATION,
        this.$constants.OTHER,
        this.$constants.SERVICE_CHECK,
        this.$constants.WAN_LINK,
        this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
        this.$constants.SDN,
        this.$constants.CONTAINER,
        this.$constants.CONTAINER_ORCHESTRATION,
      ].includes(this.category)
    },

    filters() {
      let filters
      const value = this.appliedFilters

      if (value.groups.length) {
        filters = [
          ...(filters || []),
          {
            field: 'groups',
            operator: 'array_contains',
            value: value.groups,
          },
        ]
      }
      if (value.types && value.types.length) {
        filters = [
          ...(filters || []),
          {
            field: 'type',
            operator: 'array_contains',
            value: value.types,
          },
        ]
      }
      if (value.severity && value.severity.length) {
        filters = [
          ...(filters || []),
          {
            field: 'severity',
            operator: 'array_contains',
            value: value.severity,
          },
        ]
      }
      if (value.apps && value.apps.length) {
        filters = [
          ...(filters || []),
          {
            field: 'apps',
            operator: 'array_contains',
            value: value.apps,
          },
        ]
      }
      if (value.status && value.status.length) {
        filters = [
          ...(filters || []),
          {
            field: [
              this.$constants.SERVER,
              this.$constants.NETWORK,
              this.$constants.CLOUD,
              this.$constants.SERVICE_CHECK,
              this.$constants.VIRTUALIZATION,
              this.$constants.OTHER,
              this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
              this.$constants.SDN,
            ].includes(this.category)
              ? 'status'
              : 'statusFormatted',
            operator: 'array_contains',
            value: value.status,
          },
        ]
      }
      if (this.cloudFilters) {
        if (this.cloudFilters.groups && this.cloudFilters.groups.length) {
          filters = [
            ...(filters || []),
            {
              field: 'groups',
              operator: 'array_contains',
              value: this.cloudFilters.groups,
            },
          ]
        }
        if (this.cloudFilters.regions && this.cloudFilters.regions.length) {
          filters = [
            ...(filters || []),
            {
              field: 'region',
              operator: 'array_contains',
              value: this.cloudFilters.regions,
            },
          ]
        }
        if (this.cloudFilters.tags && this.cloudFilters.tags.length) {
          filters = [
            ...(filters || []),
            {
              field: 'tags',
              operator: 'array_contains',
              value: this.cloudFilters.tags,
            },
          ]
        }
        if (this.cloudFilters.accounts && this.cloudFilters.accounts.length) {
          filters = [
            ...(filters || []),
            {
              field: 'accountId',
              operator: 'array_contains',
              value: this.cloudFilters.accounts,
            },
          ]
        }
      }
      if (value.tags && value.tags.length) {
        filters = [
          ...(filters || []),
          {
            field: [
              this.$constants.PROCESS,
              this.$constants.INTERFACE,
              this.$constants.WAN_LINK,
            ].includes(this.category)
              ? 'instanceTags'
              : 'tags',
            operator: 'array_contains',
            value: value.tags,
          },
        ]
      }
      if (value.wanProbe && value.wanProbe.length) {
        filters = [
          ...(filters || []),
          {
            field: 'wanProbe',
            operator: 'eq',
            value: value.wanProbe,
          },
        ]
      }
      if (value.monitors && value.monitors.length) {
        filters = [
          ...(filters || []),
          {
            field: 'monitorId',
            operator: 'array_contains',
            value: value.monitors,
          },
        ]
      }
      return filters
    },
    ignoreCloudTypeToDrilldown() {
      return IGNORE_CLOUD_TYPE_TO_DRILLDOWN
    },

    columnMapWithCustomMonitoringField() {
      const monitoringFields = Array.from(
        this.monitoringFieldsContext.options.values()
      )

      return {
        ...COLUMN_MAP,
        ...monitoringFields.reduce(
          (acc, c) => ({
            ...acc,
            [c.name.toLowerCase()]: c.name.toLowerCase(),
          }),
          {}
        ),

        ...(this.selectedTags || []).reduce(
          (acc, tag) => ({
            ...acc,
            [tag]: tag,
          }),
          {}
        ),
      }
    },
    disabledTagOptions() {
      return Uniq(
        Flatten(this.availableColumns.map((c) => [c.key, c.cellRender]))
          .concat(Object.values(COLUMN_MAP))
          .filter((c) => !(this.selectedTags || []).includes(c))
          .filter(Boolean)
      )
    },
  },
  watch: {
    loading: {
      handler(newValue) {
        if (
          newValue === false &&
          [
            // this.$constants.SERVER,
            // this.$constants.NETWORK,
            // this.$constants.VIRTUALIZATION,
            this.$constants.CLOUD,
            // this.$constants.OTHER,
            this.$constants.STORAGE,
          ].includes(this.category)
        ) {
          this.requrstAlertData()
        }
      },
    },
  },
  async created() {
    this.selectedTags =
      (await this.getModulePreferenceByModule({
        module: `${this.category}-inventory-grid`,
        key: 'userPrefrenceSelectedTags',
      })) || []
    Bus.$once(
      this.$constants.EVENT_SEVERITY_COUNTER_DB_CHANGED,
      this.handleSeverityChange
    )

    // this.patchMonitorCounterValues = Throttle(
    //   this.patchMonitorCounterValuesRaw,
    //   700,
    //   { leading: true }
    // )

    Bus.$on(Constants.UI_WIDGET_RESULT_EVENT, this.patchMonitorCounterValues)
    this.fetchGridData()

    this.askForMonitorDetailsDebounced = Debounce(
      this.askForMonitorDetails,
      700,
      {
        trailing: true,
        leading: false,
      }
    )
  },
  beforeDestroy() {
    clearInterval(this._heartbeatInterval)
    // if (this.__streamingTimer) {
    //   clearInterval(this.__streamingTimer)
    //   this.__streamingTimer = null
    // }
    Bus.$off(
      this.$constants.EVENT_SEVERITY_COUNTER_DB_CHANGED,
      this.handleSeverityChange
    )
    Bus.$off(Constants.UI_WIDGET_RESULT_EVENT, this.patchMonitorCounterValues)
    // Bus.$emit('server:event', {
    //   'event.type': this.$constants.UI_WIDGET_INACTIVE_SESSION,
    //   'event.context': {
    //     [this.$constants.UI_EVENT_UUID]: this.uuid,
    //     id: CATEGORY_ID_MAP[this.category],
    //   },
    // })
  },

  methods: {
    ...UserPreferenceMethods,

    handleSeverityChange() {
      if (this.isInstanceCategoryGrid || this.hasSingleSortRequest) {
        return
      }
      setTimeout(async () => {
        let getSeverityIds = []

        // let objects = this.items

        if (this.isInstanceCategoryGrid) {
          getSeverityIds = this.items.map(
            (value) => `${value.id.replace('@@', '``||``')}`
          )
        } else {
          getSeverityIds = this.items.map((value) => value.id)
        }
        let severityMap = await severityDBWorker.getSeverityMap(
          {
            id: getSeverityIds,
          },
          this.isInstanceCategoryGrid
        )
        // for (let index in this.items) {
        //     // this.$set(this.items, index, {
        //     //   ...this.items[index],
        //     //   severity: currentObjectSeverity || this.items[index].severity,
        //     //   severityNumber:
        //     //     SEVERITY_MAP[
        //     //       currentObjectSeverity || this.items[index].severity
        //     //     ],
        //     // })
        //   }
        // }

        let updatedItems = []

        for (let index = 0; index < this.items.length; index++) {
          const item = this.items[index]
          const currentObjectSeverity = severityMap[this.items[index].id]

          if (currentObjectSeverity !== item.severity) {
            updatedItems.push({
              ...item,
              severity: currentObjectSeverity || item.severity,
              severityNumber:
                SEVERITY_MAP[currentObjectSeverity || item.severity],
            })
          } else {
            updatedItems.push(item)
          }
        }
        this.items = Object.freeze(updatedItems)
        // this.items = Object.freeze(objects)
        severityMap = null
        updatedItems = null
      })
    },
    // scheduleUpdate() {
    //   if (this.__streamingTimer) {
    //     clearInterval(this.__streamingTimer)
    //     this.__streamingTimer = null
    //   }
    //   this.__streamingTimer = setInterval(this.sendActiveSessionEvent, 10000)
    // },
    // sendActiveSessionEvent() {
    //   Bus.$emit('server:event', {
    //     'event.type': this.$constants.UI_WIDGET_ACTIVE_SESSION,
    //     'event.context': {
    //       [this.$constants.UI_EVENT_UUID]: this.uuid,
    //       id: CATEGORY_ID_MAP[this.category],
    //     },
    //   })
    // },
    async exportGrid(type) {
      const columns = this.columns.filter((obj) => obj.key && !obj.hidden)
      let items =
        (await this.$refs?.gridRef?.getFilteredDataWithoutTake())?.data || []
      const contextData = this.$refs.gridRef.getContextData()
      this.$successNotification({
        message: 'Success',
        description: `The file will be downloaded once ready`,
      })
      exportData(columns, items, type, contextData).then((blob) => {
        downloadFile(blob, undefined, `monitors.${type}`)
      })
    },
    startDataReceiving() {
      if (CATEGORY_ID_MAP[this.category]) {
        this.askForMonitorDetails()
      }

      // this._heartbeatInterval = setInterval(
      //   this.askForMonitorDetails,
      //   5 * 60 * 1000 - 30000
      // )
    },
    async setCurrentPageObjectIds(ids) {
      if (
        ((this.isInstanceCategoryGrid || this.hasSingleSortRequest) &&
          this.currentPageObjectIds.length === 0) ||
        IsEqual(this.currentPageObjectIds, ids)
      ) {
        return
      }
      this.currentPageObjectIds = Object.freeze(ids)
      if (this.__lastReceivedEvent) {
        this.patchMonitorCounterValues(this.__lastReceivedEvent)
      }
    },
    async patchMonitorCounterValues(event) {
      if (event[this.$constants.UI_EVENT_UUID] !== this.uuid) {
        return
      }

      if (event.result.queryMeta.progress !== 100) {
        if (![this.$constants.INTERFACE].includes(this.category)) {
          return
        }
      }
      const isPolicyStreamEvent = event['visualization.data.sources'].find(
        (g) => g.type === 'policy.stream'
      )

      // if (event.result.error) {
      //   this.$errorToast(event.result.error)
      // }
      if (!isPolicyStreamEvent) {
        this.progress = event.result.queryMeta.progress
        this.__lastReceivedEvent = event
      }

      const result = await objectDBWorker.updateObjectCounterValues(
        {
          category: this.category,
          objects:
            this.isInstanceCategoryGrid || this.hasSingleSortRequest
              ? []
              : this.items,
          receivedEvent: event.result || {},
          idsToPatch: this.currentPageObjectIds,
          columnMap: this.columnMapWithCustomMonitoringField,
          useObjectId: false,
        },
        isPolicyStreamEvent
      )

      let objects = result

      if (result && result.items) {
        objects = result.items
        if (result.monitorOptions) {
          this.monitorOptions = result.monitorOptions
        }
      }

      if (
        !this.hasSingleSortRequest &&
        !this.isInstanceCategoryGrid &&
        objects
      ) {
        let getSeverityIds

        if (this.isInstanceCategoryGrid) {
          getSeverityIds = objects.map(
            (value) => `${value.id.replace('@@', '``||``')}`
          )
        } else {
          getSeverityIds = objects.map((value) => value.id)
        }

        let severityMap = await severityDBWorker.getSeverityMap(
          {
            id: getSeverityIds,
          },
          this.isInstanceCategoryGrid
        )

        for (let index in objects) {
          let severity = severityMap[`${objects[index].id}`]
          if (!severity) {
            severity = 'UNKNOWN'
          }
          objects[index].severity = severity
          objects[index].severityNumber = SEVERITY_MAP[severity]
        }

        severityMap = null
      }

      if (objects) {
        this.items = Object.freeze(objects)
      }

      this.loading = false
    },
    async askForMonitorDetails() {
      // this.widgetDef = await getWidgetApi(CATEGORY_ID_MAP[this.category])

      // const serverDef = transformWidgetForServer(this.widgetDef)

      const widgetDef = INVENTORY_SERVERSIDE_DEF[this.category]

      Bus.$emit('server:event', {
        'event.type': Constants.UI_WIDGET_RESULT_EVENT,
        'event.context': {
          // ...serverDef,
          [Constants.UI_EVENT_UUID]: this.uuid,
          ...widgetDef,
          'visualization.tags': [
            ...(this.selectedTags || []),
            ...(widgetDef['visualization.tags']
              ? widgetDef['visualization.tags']
              : []),
          ],
          incremental: 'yes',
          id: -1,

          // id: CATEGORY_ID_MAP[this.category],
        },
      })
      // this.sendActiveSessionEvent()
      // this.scheduleUpdate()
    },
    async fetchGridData() {
      this.loading = true
      if (this.isInstanceCategoryGrid || this.hasSingleSortRequest) {
        this.startDataReceiving()
        return
      }
      const groups = this.groupIds
      let ObjectsByCategory = await objectDBWorker.getObjectsByCategory(
        this.category,
        {
          groups,
        }
      )

      const agentHealthTypeObjectList = (ObjectsByCategory || [])
        .filter((m) => m.agentHealthType)
        .map((m) => m.ip)

      let objects = await getMonitorsApi({
        params: {
          'object.category': JSON.stringify([this.category]),
        },
      })

      if (groups.length) {
        objects = objects.filter(
          (item) =>
            Intersection(item.groups, groups).length > 0 &&
            !agentHealthTypeObjectList.includes(item.ip)
        )
      }
      if (agentHealthTypeObjectList.length) {
        objects = objects.filter(
          (item) => !agentHealthTypeObjectList.includes(item.ip)
        )
      }

      // objects = objects.map(object => {
      //   const matchingID = severityList.find(val => val.id === object.id)
      //   if (matchingID) {
      //     return { ...object, severity: matchingID.severity }
      //   }
      // })

      const getSeverityIds = objects.map((value) => value.id)
      let severityMap = await severityDBWorker.getSeverityMap(
        {
          id: getSeverityIds,
        },
        this.isInstanceCategoryGrid
      )

      for (let index in objects) {
        let severity =
          severityMap[objects[index].monitorId || objects[index].id]
        if (!severity) {
          severity = 'UNKNOWN'
        }
        objects[index].severity = severity
        objects[index].severityNumber = SEVERITY_MAP[severity]
      }

      severityMap = null

      this.items = Object.freeze(objects)
      this.loading = false
    },
    handleShowInterfaceDetails(monitor) {
      if (monitor.interfaces <= 0) {
        return
      }
      this.showInstanceGrid = true
      this.instanceGridData = Object.freeze({
        title: `Interface Details of ${monitor.name}`,
        resource: monitor,
      })
    },
    handleShowVmDetails(monitor) {
      if (monitor.vms <= 0) {
        return
      }
      this.showInstanceGrid = true
      this.instanceGridData = Object.freeze({
        title: `VM Details of ${monitor.name}`,
        resource: monitor,
      })
    },
    handleHideInstanceDetails() {
      this.showInstanceGrid = false
      this.instanceGridData = {
        title: undefined,
      }
    },
    onGridLoaded() {
      this.calculateLeftOffset()
      if (!this.isInstanceCategoryGrid && !this.hasSingleSortRequest) {
        this.startDataReceiving()
      }
    },
    calculateLeftOffset() {
      if (document.querySelector('.k-pager-wrap.k-grid-pager')) {
        const pagerWidth = document.querySelector(
          '.k-pager-wrap.k-grid-pager'
        ).clientWidth
        const infoWidth = document.querySelector(
          '.k-pager-info.k-label'
        ).clientWidth
        this.paginationLabelOffset = pagerWidth + 100 - infoWidth
      } else {
        setTimeout(this.calculateLeftOffset, 500)
      }
    },
    requrstAlertData() {
      const widget = getWidgetForAlertCountData(this.uuid)
      Bus.$emit('server:event', {
        'event.type': Constants.UI_WIDGET_RESULT_EVENT,
        'event.context': widget,
      })
    },
    onChangeTagSelection(tags) {
      if (tags?.length > this.MAX_TAG_SELECTION_LIMIT) {
        tags = tags.slice(tags?.length - this.MAX_TAG_SELECTION_LIMIT)
      }
      this.selectedTags = tags

      this.askForMonitorDetailsDebounced(this.selectedTags)
      this.updateModulePreferenceForCategory()
    },
    updateModulePreferenceForCategory() {
      this.updateModulePreference({
        module: `${this.category}-inventory-grid`,
        prefrences: {
          userPrefrenceSelectedTags: this.selectedTags,
        },
      })
    },
  },
}
</script>
