<template>
  <FlotoDrawer
    :open="open"
    width="85%"
    :wrap-style="{ zIndex: 99 }"
    :scrolled-content="false"
    @hide="$emit('hide')"
  >
    <template v-slot:title>
      {{ title }}
    </template>
    <FlotoContentLoader :loading="loading">
      <div class="flex flex-col flex-1 min-h-0 px-2">
        <div class="mt-3">
          <div class="flex">
            <div class="flex-1">
              <MInput
                v-model="searchTerm"
                class="search-box"
                placeholder="Search"
                name="search"
              >
                <template v-slot:prefix>
                  <MIcon name="search" />
                </template>
                <template v-if="searchTerm" v-slot:suffix>
                  <MIcon
                    name="times-circle"
                    class="text-neutral-light cursor-pointer"
                    @click="searchTerm = undefined"
                  />
                </template>
              </MInput>
            </div>
            <div class="flex-1 text-right">
              <MButton
                :shadow="false"
                class="squared-button mr-2"
                :rounded="false"
                title="Export As PDF"
                variant="neutral-lightest"
                @click="exportGrid('pdf')"
              >
                <MIcon name="export-pdf" />
              </MButton>
              <MButton
                :shadow="false"
                class="squared-button mr-2"
                :rounded="false"
                variant="neutral-lightest"
                title="Export As CSV"
                @click="exportGrid('csv')"
              >
                <MIcon name="export-csv" />
              </MButton>
              <MButton
                id="btn-filter-agent"
                title="Filter"
                :shadow="false"
                class="squared-button mr-2"
                :rounded="false"
                :variant="showFilters ? 'neutral-lighter' : 'neutral-lightest'"
                @click="showFilters = !showFilters"
              >
                <MIcon name="filter" />
              </MButton>
            </div>
          </div>
          <div v-if="showFilters" class="mt-4">
            <MRow>
              <MCol :size="12">
                <InventoryFilters
                  v-model="appliedFilters"
                  class="px-4"
                  :category="category"
                  :is-instance-grid="true"
                  @change="showFilters = false"
                  @hide="showFilters = !showFilters"
                />
              </MCol>
            </MRow>
          </div>
        </div>
        <MGrid
          ref="interfaceRef"
          :search-term="searchTerm"
          :data="data"
          :columns="columns"
          :filters="filters"
        >
          <template v-slot:interface="{ item }">
            <div class="flex flex-1 min-w-0">
              <Severity :severity="item.severity" class="mr-2" />
              <a
                class="text-ellipsis"
                @click.stop="
                  showTemplateFor = {
                    ...item,
                    monitorId: monitor.id,
                    monitorName: monitor.name,
                  }
                "
              >
                {{ item.interface }}
              </a>
            </div>
          </template>
          <template v-slot:status="{ item }">
            <MStatusTag
              v-if="item.status"
              :status="item.statusFormatted || item.status"
            />
            <span />
          </template>
          <template v-slot:flowStatus="{ item }">
            <MIcon
              v-if="item.flowStatus"
              name="check-circle"
              size="lg"
              class="text-secondary-green"
            />
          </template>
          <template v-slot:inTraffic="{ item }">
            <div class="used-count-pill ant-tag rounded cursor-auto">
              <MIcon
                name="long-arrow-right"
                size="lg"
                class="text-primary mr-1"
              />
              {{ item.inTrafficFormatted }}
            </div>
          </template>
          <template v-slot:receivedErrorPackets="{ item }">
            <div class="used-count-pill ant-tag rounded cursor-auto">
              {{ item.receivedErrorPackets }}
            </div>
          </template>
          <template v-slot:sendErrorPackets="{ item }">
            <div class="used-count-pill ant-tag rounded cursor-auto">
              {{ item.sendErrorPackets }}
            </div>
          </template>
          <template v-slot:outTraffic="{ item }">
            <div class="used-count-pill ant-tag rounded cursor-auto">
              <MIcon
                name="long-arrow-left"
                size="lg"
                class="text-primary mr-1"
              />
              {{ item.outTrafficFormatted }}
            </div>
          </template>
          <template v-slot:trafficPercent="{ item }">
            <div class="used-count-pill ant-tag rounded cursor-auto">
              {{ item.trafficPercentFormatted }}
            </div>
          </template>
          <template v-slot:type>
            <MonitorType type="vm" disable-tooltip />
          </template>
        </MGrid>
        <InterfaceTemplate
          :open="Boolean(showTemplateFor)"
          :interface-item="showTemplateFor"
          @hide="showTemplateFor = null"
        />
      </div>
    </FlotoContentLoader>
  </FlotoDrawer>
</template>

<script>
import Bus from '@utils/emitter'
import Constants from '@constants'
import MonitorType from '@components/monitor-type.vue'
import { generateId } from '@utils/id'
import { objectDBWorker } from '@/src/workers'
import InterfaceTemplate from '@components/templates/interface-template.vue'
import Severity from '@components/severity.vue'
import { WidgetTypeConstants } from '@components/widgets/constants'
import { COLUMN_MAP, instanceTypeMap } from '../../helpers/inventory'
import { getWidgetForInstance } from '../../helpers/data-widgets'
import { getInstanceSeverityApi } from '@utils/socket-event-as-api'
import InventoryFilters from './inventory-filters.vue'
import exportData from '@modules/settings/monitoring/helpers/export-pdf-csv'
import { downloadFile } from '@utils/download'

export default {
  name: 'MonitorInstanceGrid',
  components: {
    MonitorType,
    InterfaceTemplate,
    Severity,
    InventoryFilters,
  },
  props: {
    title: {
      type: String,
      default: undefined,
    },
    open: {
      type: Boolean,
      default: false,
    },
    columns: {
      type: Array,
      default() {
        return []
      },
    },
    category: {
      type: String,
      required: true,
    },
    monitor: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      searchTerm: undefined,
      data: [],
      guid: generateId(),
      loading: true,
      showTemplateFor: null,
      showFilters: false,
      appliedFilters: {
        severity: [],
        status: undefined,
        tags: [],
      },
    }
  },
  computed: {
    filters() {
      let filters
      const value = this.appliedFilters

      if (value.severity && value.severity.length) {
        filters = [
          ...(filters || []),
          {
            field: 'severity',
            operator: 'array_contains',
            value: value.severity,
          },
        ]
      }
      if (value.status && value.status.length) {
        filters = [
          ...(filters || []),
          {
            field: 'status',
            operator: 'array_contains',
            value: value.status,
          },
        ]
      }
      if (value.tags && value.tags.length) {
        filters = [
          ...(filters || []),
          {
            field: 'tags',
            operator: 'array_contains',
            value: value.tags,
          },
        ]
      }
      return filters
    },
  },
  watch: {
    monitor: {
      immediate: true,
      handler(newValue, oldValue) {
        if (newValue !== oldValue && newValue) {
          if (newValue.id !== (oldValue || {}).id) {
            this.fetchGridData()
          }
        }
      },
    },
  },
  mounted() {
    Bus.$on(Constants.UI_WIDGET_RESULT_EVENT, this.getData)
  },
  beforeDestroy() {
    Bus.$off(Constants.UI_WIDGET_RESULT_EVENT, this.getData)
  },
  methods: {
    fetchGridData() {
      this.loading = true
      const widget = getWidgetForInstance(
        this.monitor.id,
        instanceTypeMap[this.monitor.type] || 'interface',
        this.guid
      )
      Bus.$emit('server:event', {
        'event.type': Constants.UI_WIDGET_RESULT_EVENT,
        'event.context': widget,
      })
    },
    async getData(response) {
      if (response[Constants.UI_EVENT_UUID] === this.guid) {
        let result = response.result
        if (((response.result || {}).queryMeta || {}).progress === 100) {
          let monitorId = this.monitor.id
          let instanceSeverityMap = await getInstanceSeverityApi({
            instance: true,
            'event.context': (result[WidgetTypeConstants.GRID] || {}).data.map(
              (i) => ({
                'entity.id': monitorId,
                instance: i[instanceTypeMap[this.monitor.type] || 'interface'],
              })
            ),
          })
          if (Object.keys(instanceSeverityMap).length) {
            result[WidgetTypeConstants.GRID].data = result[
              WidgetTypeConstants.GRID
            ].data.map((i) => ({
              ...i,
              severity:
                instanceSeverityMap[
                  `${monitorId}\`\`||\`\`${
                    i[instanceTypeMap[this.monitor.type] || 'interface']
                  }`
                ] || 'unknown',
            }))
          }
        }
        const data = await objectDBWorker.createObjectGrid({
          event: result,
          columnMap: COLUMN_MAP,
          idKey: instanceTypeMap[this.monitor.type] || 'interface',
        })
        this.data = Object.freeze(data)
        this.loading = false
        setTimeout(() => {
          result = null
        })
      }
    },
    async exportGrid(type) {
      const columns = this.columns.filter((obj) => obj.key && !obj.hidden)
      let items =
        (await this.$refs?.interfaceRef?.getFilteredDataWithoutTake())?.data ||
        []

      if (
        this.category === this.$constants.VIRTUALIZATION ||
        this.category === this.$constants.HYPERCONVERGED_INFRASTRUCTURE
      ) {
        items = items.map((item) => ({ ...item, type: 'VM' }))
      }
      const contextData = this.$refs.interfaceRef.getContextData()
      this.$successNotification({
        message: 'Success',
        description: `The file will be downloaded once ready`,
      })
      exportData(columns, items, type, contextData).then((blob) => {
        const filename =
          this.category === this.$constants.NETWORK
            ? `interface.${type}`
            : `VM_list.${type}`
        downloadFile(blob, undefined, filename)
      })
    },
  },
}
</script>
