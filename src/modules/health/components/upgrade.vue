<template>
  <!-- <FlotoScrollView> -->
  <div class="flex h-100 flex-col flex-1">
    <MRow :gutter="0" class="flex justify-between mt-4">
      <MCol class="flex items-center ml-2">
        <MRadioGroup v-model="type" as-button :options="options" />

        <MInput
          v-model="searchTerm"
          class="search-box ml-2"
          placeholder="Search"
          name="search"
        >
          <template v-slot:prefix>
            <MIcon name="search" />
          </template>
          <template v-if="searchTerm" v-slot:suffix>
            <MIcon
              name="times-circle"
              class="text-neutral-light cursor-pointer"
              @click="searchTerm = undefined"
            />
          </template>
        </MInput>
      </MCol>

      <MButton
        v-if="type === 'agent'"
        id="btn-show-hide-columns"
        :shadow="false"
        :rounded="false"
        variant="neutral-lightest"
        class="squared-button mx-2"
        :disabled="!hasHealthMonitoringPermission"
        @click="isUpgradeUploadModalVisible = true"
      >
        <MIcon name="upload" />
      </MButton>
    </MRow>

    <FlotoContentLoader :loading="loading">
      <div class="flex-1 overflow-auto">
        <MGrid
          style="height: calc(100% - 6rem)"
          :columns="columns(type)"
          :data="rows"
          :search-term="searchTerm"
        >
          <template v-slot:version="{ item }">
            <SelectedItemPills :value="[item.version]" :max-items="1" />
          </template>
          <template v-slot:mode="{ item }">
            <SelectedItemPills
              v-if="item.mode"
              :value="[item.mode]"
              :max-items="1"
            />
          </template>

          <template v-slot:compatibility="{ item }">
            <span v-if="item.isVersionCompatible">
              <MIcon name="check-circle" class="text-secondary-green" />
              Compatible
            </span>

            <span v-else>
              <MTooltip style="display: flex; flex: unset; align-items: center">
                <template v-slot:trigger>
                  <MIcon
                    name="exclamation-triangle"
                    class="text-secondary-yellow mr-2"
                  />
                  <span class="text-secondary-yellow">Not Compatible</span>
                </template>

                {{
                  $message('upgrade_message', {
                    type,
                  })
                }}
              </MTooltip>
            </span>
          </template>

          <template v-slot:upgradStatus="{ item }">
            <template v-if="!isCompatibilityTestRunning">
              <span
                v-if="
                  (!item.isVersionCompatible || item.updateRequired) &&
                  !item.isUpdating
                "
              >
                <MButton
                  variant="transparent"
                  :shadow="false"
                  :rounded="false"
                  class="p-0 flex items-center"
                  :disabled="!hasHealthMonitoringPermission"
                  @click="showConfirmModel(item)"
                >
                  <MTooltip
                    v-if="!item.isVersionCompatible"
                    style="display: flex; flex: unset; align-items: center"
                  >
                    <template v-slot:trigger>
                      <MIcon
                        name="exclamation-triangle"
                        class="text-secondary-yellow mr-2"
                      />
                    </template>

                    {{
                      $message('upgrade_message', {
                        type,
                      })
                    }}
                  </MTooltip>

                  <span
                    :class="{
                      'text-primary': item.isVersionCompatible,
                      'text-secondary-yellow': !item.isVersionCompatible,
                    }"
                    class="mr-2"
                    >Upgrade now</span
                  >
                </MButton>
              </span>

              <span v-else-if="!item.isUpdating">
                <MIcon name="check-circle" class="text-secondary-green" />
                Up-to-date
              </span>

              <div v-else class="flex flex-col">
                <div
                  class="text-neutral-light flex text-ellipsis flex justify-between -mb-2"
                  style="font-size: 11px"
                >
                  <span>In progress ...</span>
                  <span>{{ item.progress || 0 }}%</span>
                </div>
                <Progress
                  class="mr-4"
                  :width="item.progress || 0"
                  type="success"
                />
              </div>
            </template>

            <div v-else class="flex items-center">
              <span class="loader mr-2"></span>
              {{ $message('compatibility_test_message') }}
            </div>
          </template>

          <template v-slot:lastActionStatus="{ item }">
            <span v-if="item.lastActionStatus">
              {{ item.lastActionStatus }}

              <MTooltip
                v-if="item.upgradeStatus === $constants.EVENT_FAIL_STATUS"
              >
                <template v-slot:trigger>
                  <MIcon name="info-circle" class="text-primary ml-2" />
                </template>

                {{ item.errorMessage }}
              </MTooltip>
            </span>

            <span v-else>---</span>
          </template>

          <template v-slot:type="{ item }">
            <MonitorType
              :type="item.type || item.rpeType"
              class="mx-1 inline-flex"
            />
          </template>
        </MGrid>

        <!-- <MCol :size="12" class="ml-2 mt-2">
            <span class="text-neutral"> For more information: </span>
            <a
              href="https://docs.motadata.com/motadata-aiops-docs/motadata-aiops-upgrade/overview"
              target="_blank"
              >Motadata AIOps Upgrade</a
            >
            <MIcon name="external-link" class="ml-1 text-primary" />
          </MCol> -->
      </div>
    </FlotoContentLoader>

    <FlotoConfirmModal
      :open="showUpgradeItem !== null"
      variant="primary-alt"
      :width="500"
      hide-icon
    >
      <template v-slot:icon>
        <span />
      </template>
      <template v-slot:message>
        <h4 class="text-primary"> {{ confirmModelTitle }}</h4>

        <MDivider />

        <span v-html="confirmModelMessage"></span>
      </template>

      <template v-slot:action-container>
        <MRow class="flex justify-end w-full" :gutter="16">
          <MCol class="text-right">
            <MButton
              id="close-id"
              class="mr-2"
              outline
              variant="default"
              @click="showUpgradeItem = null"
            >
              Cancel
            </MButton>

            <MButton
              id="confirm-yes"
              variant="primary-alt"
              @click.stop="handelConfirmUpgrade"
            >
              Confirm
            </MButton>
          </MCol>
        </MRow>
      </template>
    </FlotoConfirmModal>

    <MModal
      v-if="isUpgradeUploadModalVisible"
      :width="500"
      centered
      :open="isUpgradeUploadModalVisible"
      @cancel="handleCancel"
    >
      <template v-slot:title>
        <div class="flex justify-between items-center">
          <p class="text-primary mt-2 ml-2">Import Upgrade File</p>

          <span class="mr-2 cursor-pointer" @click="handleCancel">
            <MIcon name="times" />
          </span>
        </div>
      </template>

      <FileDropper
        v-model="importFile"
        :max-files="1"
        class="bg"
        :allowed-extensions="['zip']"
        @remove="fileRemove"
        @change="handleChangeUploadFile"
      />
      <template v-slot:footer>
        <MRow class="flex-row-reverse">
          <div v-if="!importFile.length"> </div>

          <MButton v-else class="mr-2" @click="runCompatibility"
            >Run Compatibility Check
          </MButton>
        </MRow>
      </template>
    </MModal>
  </div>
  <!-- </FlotoScrollView> -->
</template>

<script>
import { authComputed } from '@state/modules/auth'
import Bus from '@utils/emitter'
import Constants from '@constants'
import Config from '../config'

import {
  COLUMNS,
  getUpgradeGridDataByType,
  // updateAgent,
  runCompatibilityApi,
  // upgradeMasterOrCollector,
  upgradeApi,
} from '../upgrade-api'
import Progress from '@components/progress.vue'
import MonitorType from '@components/monitor-type.vue'

import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import FileDropper from '@components/file-dropper.vue'

export default {
  name: 'Upgrade',
  components: {
    SelectedItemPills,
    Progress,
    MonitorType,
    FileDropper,
  },
  data() {
    this.options = [
      {
        text: 'Master',
        value: 'master',
      },
      {
        text: 'Collectors',
        value: 'collectors',
      },
      {
        text: 'Agent',
        value: 'agent',
      },
    ]
    return {
      rows: [],
      searchTerm: undefined,
      type: 'master',
      showUpgradeItem: null,
      loading: true,
      importFile: [],
      isUpgradeUploadModalVisible: false,
      fileName: undefined,
      isCompatibilityTestRunning: false,
    }
  },

  computed: {
    ...authComputed,
    hasHealthMonitoringPermission() {
      return this.hasPermission(
        this.$constants.HEALTH_MONITORING_CREATE_PERMISSION
      )
    },
    confirmModelTitle() {
      if (this.showUpgradeItem) {
        return `${
          this.type === 'agent'
            ? `MotadataAgent Upgrade - ${this.showUpgradeItem.name}`
            : this.type === 'master'
            ? `Master Application Upgrade`
            : `Collector Upgrade - ${this.showUpgradeItem.rpeName}`
        } `
      }
      return ''
    },
    confirmModelMessage() {
      if (this.showUpgradeItem) {
        return `${
          this.type === 'agent'
            ? this.$message('agent_upgrade')
            : this.type === 'master'
            ? this.$message('master_upgrade')
            : this.$message('collector_upgrade')
        } `
      }
      return ''
    },
  },

  watch: {
    type(newValue, oldValue) {
      this.requestData()
    },
  },
  created() {
    Bus.$on(Config.UI_ACTION_AGENT_UPGRADE_PROGRESS, this.handleUpgradeProgress)
    Bus.$on(
      Config.UI_ACTION_REMOTE_EVENT_UPGRADE_PROGRESS,
      this.handleUpgradeProgress
    )
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(
        Config.UI_ACTION_AGENT_UPGRADE_PROGRESS,
        this.handleUpgradeProgress
      )
      Bus.$off(
        Config.UI_ACTION_REMOTE_EVENT_UPGRADE_PROGRESS,
        this.handleUpgradeProgress
      )
    })
    this.requestData()
  },

  methods: {
    requestData() {
      this.loading = true
      this.rows = []
      this.fileName = undefined
      this.importFile = []
      getUpgradeGridDataByType(this.type).then((data) => {
        this.loading = false
        // if (this.type === 'master') {
        //   this.rows = data.filter((rpe) => rpe.rpeType === 'DEFAULT') || []
        // } else if (this.type === 'collectors') {
        //   this.rows = data.filter((rpe) => rpe.rpeType !== 'DEFAULT') || []
        // } else {
        this.rows = data || []
        // }
      })
    },
    columns(type) {
      return COLUMNS[type]
    },
    download() {},
    upgrade() {},

    handelConfirmUpgrade() {
      if (this.showUpgradeItem?.isVersionCompatible) {
        // const fn = ['collectors', 'master'].includes(this.type)
        //   ? upgradeMasterOrCollector
        //   : updateAgent

        return upgradeApi(
          this.showUpgradeItem.id,
          this.fileName,
          this.type,
          this.showUpgradeItem
        ).then(() => {
          this.showUpgradeItem = null
        })
      } else {
        this.$errorNotification({
          message: 'version is not compatible',
        })
      }

      this.showUpgradeItem = null
    },
    handleUpgradeProgress(event) {
      if (['collectors', 'agent'].includes(this.type)) {
        const id = event[Constants.ID_PROPERTY]
        const index = this.rows.findIndex((row) => row.id === id)
        if (index !== -1) {
          this.rows = Object.freeze([
            ...this.rows.slice(0, index),
            {
              ...this.rows[index],
              ...this.patchContext(event),
            },
            ...this.rows.slice(index + 1),
          ])
        }
      }
    },

    patchContext(event) {
      return {
        isUpdating:
          event.progress !== 100 &&
          event.status !== Constants.EVENT_FAIL_STATUS,
        progress: Math.round(event.progress || 0),
        upgradeStatus: event.status,
        errorMessage: event.message,

        ...(event.progress === 100 &&
        event.status === Constants.EVENT_SUCCESS_STATUS
          ? {
              updateRequired: false,
              version:
                this.type === 'agent'
                  ? event['agent.version']
                  : event['remote.event.processor.version'],
              lastActionStatus:
                this.type === 'agent'
                  ? event['artifact.upgrade.status']
                  : event['artifact.upgrade.status'],
            }
          : {}),

        ...(event.status === Constants.EVENT_FAIL_STATUS
          ? {
              lastActionStatus:
                this.type === 'agent'
                  ? event['artifact.upgrade.status']
                  : event['artifact.upgrade.status'],
            }
          : {}),
      }
    },
    handleCancel() {
      this.isUpgradeUploadModalVisible = false
      this.importFile = []
    },
    handleChangeUploadFile(file) {
      this.fileName = file[0].result
    },
    upgradeFile() {
      this.showRestoreConfirmBox = true
    },

    runCompatibility() {
      this.isUpgradeUploadModalVisible = false
      this.isCompatibilityTestRunning = true

      return runCompatibilityApi(this.importFile[0].result, this.type)
        .then((payload) => {
          this.rows = this.rows.map((row) => {
            const item = (payload.result || []).find((i) => i.id === row.id)

            if (item) {
              return {
                ...row,
                updateRequired:
                  item['error.code'] &&
                  item['error.code'] === Constants.ERROR_CODE_UPGRADE_REQUIRED,

                isUpdating: false,
                isVersionCompatible:
                  item['error.code'] &&
                  item['error.code'] !== Constants.ERROR_CODE_SUCCESS
                    ? !item['error.code'] ===
                        Constants.ERROR_CODE_NOT_COMPATIBLE ||
                      item['error.code'] ===
                        Constants.ERROR_CODE_UPGRADE_REQUIRED
                    : true,
              }
            }

            return row
          })
        })
        .finally(() => {
          this.handleCancel()
          setTimeout(() => {
            this.isCompatibilityTestRunning = false
          }, 500)
        })
    },
    showConfirmModel(item) {
      if (this.fileName) {
        this.showUpgradeItem = item
      } else {
        this.$errorNotification({
          message: 'Please Upload File',
        })
      }
    },
    fileRemove(file) {
      this.importFile = this.importFile.filter((f) => f.result !== file.result)
    },
  },
}
</script>

<style lang="less" scoped>
.spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 2;
  width: 50px;
  height: 50px;
  margin: -25px 0 0 -25px;
  animation: rotate 2s linear infinite;

  & .path {
    stroke: hsl(210deg, 70, 75);
    stroke-linecap: round;
    animation: dash 1.5s ease-in-out infinite;
  }
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }

  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

.loader {
  box-sizing: border-box;
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid var(--primary);
  border-bottom-color: transparent;
  border-radius: 50%;
  animation: rotation 1s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
