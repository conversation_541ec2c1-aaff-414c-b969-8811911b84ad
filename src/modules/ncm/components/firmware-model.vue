<template>
  <MModal
    open
    :width="620"
    :mask-closable="false"
    overlay-class-name="confirm-modal"
    centered
    @hide="hide"
  >
    <template v-slot:title>
      <h5 class="text-primary my-2">
        {{ forBulkAction ? 'Bulk Firmware Upgrade' : `Firmware Upgrade` }}</h5
      >
    </template>

    <MRow v-if="!loading" :gutter="0">
      <MCol :size="12" class="flex flex-col w-full">
        <FlotoForm class="" @submit="onFirmwareUpgradeSubmit">
          <MRow v-if="!uplodedImage.length" :gutter="0" class="w-full">
            <FlotoFormItem
              v-if="!uplodedImage.length"
              label="Select Firmware image"
              class="w-full"
              rules="required"
            >
              <FlotoDropdownPicker
                v-model="firmwareUpgradeImage"
                :options="firmwareUpgradeImageOptions"
                class="w-full"
                allow-clear
                @change="getMD5Context"
              />
            </FlotoFormItem>
          </MRow>

          <MRow
            v-if="!firmwareUpgradeImage && !uplodedImage.length"
            :gutter="0"
            class="w-full relative"
          >
            <MDivider />

            <div
              style="top: 5px; background: transparent"
              class="flex items-center absolute w-full justify-center"
            >
              <span
                style="background: transparent; backdrop-filter: blur(50px)"
                class="px-2 text-neutral"
                >OR</span
              >
            </div>
          </MRow>

          <MRow v-if="!firmwareUpgradeImage" :gutter="0" class="w-full">
            <FlotoFormItem
              id="firmware-image-select-id"
              class="w-full"
              label="Upload Firmware image"
              validation-label="Firmwareimage"
              rules="required"
            >
              <FileDropper
                id="csv-upload"
                v-model="uplodedImage"
                button-text="Upload Image"
                :multiple="false"
                :max-files="1"
                url="/upload-firmware"
                should-emit-full-response
                @change="handleChangeUploadFile"
                @remove="fileRemove"
                @upload-start="isMd5Loading = true"
              >
                <span class="text-neutral-light">
                  <MIcon name="export-csv" />
                  Drag & Drop file or
                  <a class="text-underline">Browse File</a>
                </span>
              </FileDropper>
            </FlotoFormItem>
          </MRow>

          <MRow v-if="selectedFirmwareImage" :gutter="0" class="my-4">
            <MCol>
              <MRow :gutter="0" class="code-container">
                <MCol
                  class="grow-0 bg-neutral-lightest p-1 flex justify-center items-center text-primary"
                  >MD5 Hash
                </MCol>

                <MCol class="" style="max-width: 400px">
                  <div
                    v-if="isMd5Loading"
                    class="w-full flex items-center justify-center h-full"
                  >
                    Loading
                    <div
                      v-if="!dataReceived"
                      class="flex dot-loader self-end"
                      style="margin-bottom: 4px"
                    >
                      <div class="dot"> </div>
                      <div class="dot"></div>
                      <div class="dot"></div
                    ></div>
                    <!-- <MLoader height="150" width="150" /> -->
                  </div>

                  <div
                    v-else
                    class="px-2 py-2 whitespace-nowrap text-ellipsis"
                    >{{ selectedFirmwareImage.result }}</div
                  >
                </MCol>
              </MRow>
            </MCol>
          </MRow>

          <template v-if="uplodedImage.length && uplodedImageFile">
            <MRow :gutter="0" class="text-neutral">
              image upload complete. Your file has been saved to:
            </MRow>
            <MRow :gutter="0" class="mb-2">
              {{ firmwareFilePath }}
            </MRow>
          </template>
          <template v-slot:submit="{ submit }">
            <MRow :gutter="0">
              <MCol class="text-right">
                <MButton class="mr-2" variant="default" @click="hide"
                  >Cancel</MButton
                >

                <MPopover
                  ref="popover"
                  placement="bottomRight"
                  :overlay-style="{
                    width: '300px',
                    padding: '12px',
                    borderRadius: '8px',
                  }"
                  :visible="isPopoverVisible"
                  @visibleChange="handleVisibleChange"
                >
                  <div ref="calendarContainer" class="relative">
                    <ARangePicker
                      v-model="schedularDate"
                      input-read-only
                      :disabled-date="disabledDate"
                      :show-input="false"
                      :show-today="false"
                      open
                      :style="{
                        width: '100%',
                        position: 'static',
                      }"
                      :popup-class-name="`custom-calender`"
                    >
                      <template v-slot:renderExtraFooter>
                        <div class="mt-3">
                          <label>Select Time</label>
                          <Timepicker
                            v-model="times"
                            class="w-full"
                            :multiple="false"
                            use-popover
                          />
                        </div>

                        <div class="my-4 flex justify-end">
                          <MButton class="mr-2" outline @click="closePopover"
                            >Cancel</MButton
                          >
                          <MButton class="" @click="applySchedule"
                            >Apply</MButton
                          >
                        </div>
                      </template>
                    </ARangePicker>
                  </div>

                  <template v-slot:trigger="{ toggle }">
                    <MButton
                      v-if="
                        !(
                          processing ||
                          isMd5Loading ||
                          !(selectedFirmwareImage || {}).result
                        )
                      "
                      class="mr-2"
                      outline
                      @click="toggle"
                      >{{
                        appliedScheduleContext
                          ? `${appliedScheduleContext.formattedDateTime}`
                          : 'Schedule Upgrade'
                      }}</MButton
                    >
                    <span v-else />
                  </template>
                </MPopover>
                <MButton
                  :disabled="
                    processing ||
                    isMd5Loading ||
                    !(selectedFirmwareImage || {}).result
                  "
                  :loading="processing"
                  @click="submit"
                  >Upgrade</MButton
                >
              </MCol>
            </MRow>
          </template>
        </FlotoForm>
      </MCol>
    </MRow>

    <div v-else class="flex flex-col flex-1 items-center justify-center">
      <MLoader />
    </div>

    <template v-slot:footer>
      <slot name="footer">
        <span />
      </slot>
    </template>
  </MModal>
</template>

<script>
import Moment from 'moment'

// import Bus from '@utils/emitter'
// import Config from '../config'
import {
  // calculateVersion,
  // STATUS_MAP,
  // FILE_TYPE,
  ACTION_MAP,
} from '../helpers/explorer'
import {
  getFirmwareImages,
  sendConfigRequest,
  getMD5ContextApi,
  createConfigUpgradeOpsSchedularApi,
  // moveFirmwareUpgradeFilesApi,
} from '../ncm-api'
import FileDropper from '@components/file-dropper.vue'
import ADatePicker from 'ant-design-vue/es/date-picker'
import Timepicker from '@components/time-picker.vue'
import { UserPreferenceComputed } from '@/src/state/modules/user-preference'
import { TIME_FORMAT } from '@/src/components/widgets/constants'
import { authComputed } from '@/src/state/modules/auth'

export default {
  name: 'FirmwareModel',
  components: { FileDropper, ARangePicker: ADatePicker, Timepicker },

  props: {
    item: {
      type: Object,
      default() {
        return {}
      },
    },
    forBulkAction: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      processing: false,
      firmwareUpgradeImage: undefined,
      firmwareUpgradeImageOptions: [],
      uplodedImage: [],
      uplodedImageFile: undefined,
      loading: false,
      md5Context: undefined,
      isMd5Loading: true,
      schedularDate: Moment(),
      times: '00:00',
      isPopoverVisible: false,
      appliedScheduleContext: null,
    }
  },
  computed: {
    ...UserPreferenceComputed,
    ...authComputed,
    selectedFirmwareImage() {
      const selectedFile = this.firmwareUpgradeImageOptions.find(
        (op) => op.key === this.firmwareUpgradeImage
      )
      return (
        (selectedFile && this.md5Context
          ? {
              ...selectedFile,
              result:
                this.md5Context?.['config.firmware.image.file.md5.checksum'],
            }
          : undefined) || this.uplodedImageFile
      )
    },
    firmwareFilePath() {
      const basePath = this.directoryMetadata?.['current.directory.path'] || ''
      const separator = this.directoryMetadata?.['path.separator'] || '/'
      return `${basePath}${separator}config-management${separator}firmware-files`
    },
  },
  async created() {
    await this.buildFirmwareImagesOptions()
  },
  mounted() {
    document.body.classList.add('hide-calendar-input')
  },
  beforeDestroy() {
    document.body.classList.remove('hide-calendar-input')
  },
  methods: {
    hide() {
      this.$emit('hide', 400)
    },

    hideForm() {
      this.open = false
      setTimeout(() => {
        this.processing = false

        this.$emit('hide')
      }, 700)
    },
    onFirmwareUpgradeSubmit() {
      this.processing = true

      if (this.appliedScheduleContext) {
        return this.createConfigUpgradeOpsSchedular()
          .then(() => {
            this.hideForm()
          })
          .catch(() => {
            this.processing = false
          })
      } else {
        return sendConfigRequest(
          this.forBulkAction ? this.item.selectedItems : [this.item.id],
          ACTION_MAP.FIRMWARE,
          {
            ...(this.selectedFirmwareImage
              ? {
                  'config.firmware.image.file.name':
                    this.selectedFirmwareImage.name,
                  'config.firmware.image.file.size':
                    this.selectedFirmwareImage.size,
                }
              : {}),
          }
        )
          .then(() => {
            this.hideForm()
          })
          .catch(() => {
            this.processing = false
          })
      }
    },
    fileRemove(file) {
      this.uplodedImage = this.uplodedImage.filter(
        (f) => f.result !== file.result
      )
      this.uplodedImageFile = undefined
    },

    handleChangeUploadFile(file) {
      const fileContext = file?.[0]?.['config.firmware.image.files']
      const responseContext = file?.[0]
      this.isMd5Loading = true
      this.uplodedImageFile = undefined
      // const fileId = file?.[0]?.result

      if (responseContext.status === this.$constants.EVENT_FAIL_STATUS) {
        this.$errorNotification({
          message: responseContext.message,
        })
        this.uplodedImage = []
      }

      if (fileContext) {
        this.uplodedImageFile = {
          name: fileContext['config.firmware.image.file.name'],
          size: fileContext['config.firmware.image.file.size'],
          result: fileContext['config.firmware.image.file.md5.checksum'],
        }
      }

      this.isMd5Loading = false
    },

    buildFirmwareImagesOptions() {
      this.loading = true
      try {
        return getFirmwareImages()
          .then((options) => {
            this.firmwareUpgradeImageOptions = options
          })
          .catch((e) => {
            this.firmwareUpgradeImageOptions = []
            this.loading = false
          })
          .finally(() => {
            this.processing = false
            this.loading = false
          })
      } catch (e) {
        this.firmwareUpgradeImageOptions = []
        this.loading = false
      }
    },
    getMD5Context() {
      this.isMd5Loading = true

      if (this.firmwareUpgradeImage) {
        return getMD5ContextApi(this.firmwareUpgradeImage).then((md5) => {
          this.md5Context = md5
          this.isMd5Loading = false
        })
      } else {
        this.md5Context = undefined
        this.isMd5Loading = false
      }
    },
    handleVisibleChange(visible) {
      this.isPopoverVisible = visible
    },
    closePopover(shouldClearContext = true) {
      if (this.$refs.popover) {
        this.$refs.popover.hide()
      }

      if (shouldClearContext) {
        this.appliedScheduleContext = null
      }
    },

    applySchedule() {
      const dateTime = Moment(this.schedularDate).local()

      if (this.times) {
        const [hours, minutes] = this.times.split(':').map(Number)
        dateTime.hour(hours).minute(minutes).second(0)
      }

      this.appliedScheduleContext = {
        formattedDateTime: dateTime.format(this.dateFormat),
        selectedDate: Moment(this.schedularDate, TIME_FORMAT)
          .utc()
          .format('DD-MM-YYYY'),
        selectedTime: this.times,
      }
      this.closePopover(false)
    },
    createConfigUpgradeOpsSchedular() {
      return createConfigUpgradeOpsSchedularApi(
        this.appliedScheduleContext,
        this.item.selectedItems ? this.item.selectedItems : [this.item.id],
        this.uplodedImage.length
          ? this.uplodedImage[0]?.['config.firmware.image.files']
          : this.md5Context
      )
    },
    disabledDate(current) {
      // Can not select days before today
      return current && current < Moment().startOf('day')
    },
  },
}
</script>

<style lang="less">
.hide-calendar-input .ant-calendar-input-wrap {
  display: none !important;
}
</style>
