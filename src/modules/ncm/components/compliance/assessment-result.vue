<template>
  <div
    class="vue-grid-item h-full flex flex-col overflow-x-hidden overflow-y-scroll"
  >
    <small class="font-500 mx-2 my-2 mb-0 inline-block text-xs"
      >Overall Rule Assessment Result</small
    >
    <div class="flex-1 min-h-0 flex flex-col justify-around">
      <FlotoContentLoader v-if="loading" loading :row-gutter="0" />

      <FlotoNoData
        v-else-if="!responseData.totalCount"
        hide-svg
        header-tag="h5"
        variant="neutral"
        icon="exclamation-triangle"
      />

      <template v-else>
        <div class="w-full flex justify-center flex-wrap px-2 text-sm mb-2">
          <h1 class="self-end mb-0 text-3xl font-600">{{
            responseData['totalCount']
          }}</h1>
          <h6 class="self-end ml-1 cursor-text">| Total</h6>
        </div>

        <div class="w-full flex justify-center items-center mb-6">
          <div class="rounded bordered flex" style="min-width: 300px">
            <div
              class="border-right flex flex-col px-8 py-1 items-center justify-center pt-2 w-3/6 cursor-pointer"
              @click="handelDrillDown($constants.EVENT_SUCCEDED_STATUS)"
            >
              <h5
                class="mb-0 text-lg font-600"
                style="color: var(--secondary-green)"
                >Pass</h5
              >

              <h1
                class="mb-0 font-600 font-size-three-two-pexels"
                style="color: var(--secondary-green)"
                >{{ responseData[$constants.EVENT_SUCCESS_STATUS] }}</h1
              >
            </div>

            <div
              class="px-8 py-2 pt-2 flex flex-col items-center justify-center w-3/6 cursor-pointer"
              @click="handelDrillDown($constants.EVENT_FAILED_STATUS)"
            >
              <h5
                class="mb-0 text-lg font-600"
                style="color: var(--secondary-red)"
                >Fail</h5
              >

              <h1
                class="mb-0 font-600 font-size-three-two-pexels"
                style="color: var(--secondary-red)"
                >{{ responseData[$constants.EVENT_FAIL_STATUS] }}</h1
              >
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { getOverallRuleAssessmentApi } from '../../compliance-api'

export default {
  name: 'AssessmentResult',
  data() {
    return {
      loading: true,
      responseData: {},
    }
  },
  created() {
    this.getOverallRuleAssessment()
  },
  methods: {
    getOverallRuleAssessment() {
      return getOverallRuleAssessmentApi().then((data) => {
        this.responseData = data
        this.loading = false
      })
    },
    handelDrillDown(status) {
      this.$emit('drill-down', {
        title: `Overall Rule Assessment Result - ${
          status === this.$constants.EVENT_SUCCEDED_STATUS ? 'Pass' : 'Fail'
        }`,
        status,
      })
    },
  },
}
</script>
