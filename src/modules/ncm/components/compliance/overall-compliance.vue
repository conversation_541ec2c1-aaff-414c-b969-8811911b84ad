<template>
  <div
    class="vue-grid-item h-full flex flex-col relative overflow-x-hidden overflow-y-scroll"
  >
    <small class="font-500 mx-2 my-2 m-0 inline-block text-xs"
      >Overall Compliance</small
    >

    <div v-if="loading" class="flex flex-1 w-full">
      <FlotoContentLoader loading :row-gutter="0" />
    </div>

    <FlotoNoData
      v-else-if="isNodata"
      hide-svg
      header-tag="h5"
      variant="neutral"
      icon="exclamation-triangle"
    />
    <div v-else class="flex-1 min-h-0 flex flex-col justify-between p-2 pr-0">
      <div
        class="font-extrabold flex items-center justify-center text-primary"
        style="height: 70%; font-size: 4rem"
      >
        {{ compliancePercentageToday }}%
      </div>

      <div class="flex justify-end" style="height: 30%">
        <div class="flex flex-col">
          <MIcon
            :name="difference >= 0 ? 'trending-up' : 'trending-down'"
            :class="`text-secondary-${difference >= 0 ? 'green' : 'red'}`"
          />

          <h6 class="text-neutral-light mb-0 text-lg font-600">
            {{ absDifference(difference) }} %
          </h6>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Bus from '@utils/emitter'

import { getOverallComplianceApi } from '../../compliance-api'

export default {
  name: 'OverallCompliance',
  data() {
    return {
      loading: true,
      compliancePercentageToday: undefined,
      compliancePercentageLastDay: undefined,
      difference: undefined,
    }
  },

  computed: {
    isNodata() {
      return (
        this.compliancePercentageToday === undefined ||
        this.compliancePercentageToday === null
      )
    },
  },
  created() {
    // this.getOverallCompliance()

    Bus.$on('fetched:overallcompliance', this.handelDataRecived)
    this.$once('hook:beforeDestroy', () => {
      Bus.$off('fetched:overallcompliance', this.handelDataRecived)
    })
  },

  methods: {
    // getOverallCompliance() {
    //   return getOverallComplianceApi().then((data) => {
    //     this.compliancePercentageToday = data

    //     return getOverallComplianceApi({ selectedKey: 'yesterday' }).then(
    //       (data) => {
    //         this.compliancePercentageLastDay = data

    //         this.difference =
    //           (this.compliancePercentageToday || 0) -
    //           (this.compliancePercentageLastDay || 0)

    //         this.loading = false
    //       }
    //     )
    //   })
    // },
    absDifference(value) {
      return Math.abs(value)
    },
    handelDataRecived(data) {
      this.compliancePercentageToday = data.today

      return getOverallComplianceApi({ selectedKey: '-1d' }).then((data) => {
        this.compliancePercentageLastDay = data

        this.difference =
          (this.compliancePercentageToday || 0) -
          (this.compliancePercentageLastDay || 0)

        this.loading = false
      })
    },
  },
}
</script>
