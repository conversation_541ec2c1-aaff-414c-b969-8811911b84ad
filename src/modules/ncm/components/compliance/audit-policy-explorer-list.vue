<template>
  <FlotoFixedView :gutter="0">
    <FlotoContentLoader :loading="loading">
      <BenchmarkProvider>
        <div
          class="flex flex-1 min-h-0 flex-col"
          :class="{ 'dashboard-container': view !== 'grid-view' }"
        >
          <div
            class="flex justify-between items-center mt-2"
            :class="{ 'mb-2': view !== 'grid-view' }"
          >
            <MInput
              v-model="searchTerm"
              class="search-box"
              placeholder="Search"
              name="search"
            >
              <template v-slot:prefix>
                <MIcon name="search" />
              </template>
              <template v-if="searchTerm" v-slot:suffix>
                <MIcon
                  name="times-circle"
                  class="text-neutral-light cursor-pointer"
                  @click="searchTerm = undefined"
                />
              </template>
            </MInput>
            <div class="flex items-right">
              <template v-if="view === 'grid-view'">
                <MButton
                  :shadow="false"
                  class="squared-button mr-2"
                  :rounded="false"
                  title="Export As PDF"
                  variant="neutral-lightest"
                  @click="exportGrid('pdf')"
                >
                  <MIcon name="export-pdf" />
                </MButton>
                <MButton
                  :shadow="false"
                  class="squared-button mr-2"
                  :rounded="false"
                  title="Export As CSV"
                  variant="neutral-lightest"
                  @click="exportGrid('csv')"
                >
                  <MIcon name="export-csv" />
                </MButton>
              </template>

              <MButton
                shape="circle"
                :title="view === 'dashboard-view' ? 'Grid' : 'Dashboard'"
                class="squared-button mr-2"
                variant="neutral-lightest"
                @click="toggleView"
              >
                <MIcon
                  :name="view === 'dashboard-view' ? 'grid' : 'widget-view'"
                  class="excluded-header-icon"
                />
              </MButton>
            </div>
          </div>

          <div class="flex flex-col flex-1 min-h-0">
            <Transition name="placeholder" mode="out-in">
              <MGrid
                v-if="view === 'grid-view'"
                :key="view"
                ref="gridRef"
                :data="rows"
                :columns="availableColumns"
                :search-term="searchTerm"
              >
                <template v-slot:name="{ item }">
                  <a
                    class="text-ellipsis"
                    :title="item.name"
                    @click="nevigateToAuditPolicyDevices(item)"
                  >
                    {{ item.name }}
                  </a>
                </template>
                <template v-slot:benchmark="{ item }">
                  <BenchmarkPicker
                    v-if="(item.benchmarkContext || {}).benchmark"
                    id="benchmark-options-id"
                    :value="item.benchmarkContext.benchmark"
                    disabled
                  />
                </template>
                <template v-slot:last_scan_timestamp="{ item }">
                  {{ formatDateTime(item.last_scan_timestamp) }}
                </template>

                <template v-slot:deviceSeverity="{ item }">
                  <template v-if="(item.deviceSeverity || []).length">
                    <span
                      v-for="(severity, index) in item.deviceSeverity"
                      :key="severity.key"
                      :class="{ text: true, [severity.color]: true }"
                    >
                      <b>
                        {{ severity.value }}
                      </b>
                      <span
                        v-if="index < (item.deviceSeverity || []).length - 1"
                        class="mx-1 text-neutral-light"
                      >
                        |
                      </span>
                    </span>
                  </template>
                  <span v-else />
                </template>
                <template v-slot:usedCounts="{ item }">
                  <UsedCounts
                    :title="`Used Count for ${item.name}`"
                    :display-count="item.usedCounts"
                    class="text-center"
                    allow-export
                    :parent-resource-id="item.compliance_policy_id"
                    parent-resource-type="compliance-policies"
                    :count-types="[
                      {
                        countType: 'monitor',
                        title: 'Monitors',
                        resourceKey: $constants.MONITOR,
                      },
                      {
                        countType: 'group',
                        title: 'Groups',
                        resourceKey: 'Group',
                      },
                    ]"
                  />
                </template>
                <template v-slot:compliance_percentage_sort="{ item }">
                  <span class="flex items-center">
                    <span style="width: 100px">
                      <Progress
                        :stroke-width="4"
                        class="mr-4"
                        :width="item.compliance_percentage_sort"
                        type="success"
                      />
                    </span>
                    <span class="ml-1 text-xs">
                      {{ item.compliance_percentage_sort }}%</span
                    >
                  </span>
                </template>

                <!-- <template v-slot:actions="{ item }">
                  <FlotoGridActions
                    :actions="gridItemActions"
                    :resource="item"
                  />
                </template> -->
              </MGrid>
              <AuditKpiView
                v-else
                :data="rows"
                :columns="availableColumns"
                :search-term="searchTerm"
                :loading="loading"
                class="h-screen"
                :grid-actions="gridItemActions"
                @drilldown="nevigateToAuditPolicyDevices"
              />
            </Transition>
          </div>
        </div>
      </BenchmarkProvider>
    </FlotoContentLoader>
  </FlotoFixedView>
</template>

<script>
import Bus from '@utils/emitter'

import SumBy from 'lodash/sumBy'

import AuditKpiView from './audit-kpi-view.vue'
import UsedCounts from '@components/used-counts/used-counts.vue'
import Progress from '@components/progress.vue'
import BenchmarkProvider from '@src/components/data-provider/benchmark-provider.vue'
import BenchmarkPicker from '@src/components/data-picker/benchmark-picker.vue'
import { getAuditPolicyDataApi } from '../../compliance-api'
import datetime from '@src/filters/datetime'
import exportData from '@/src/modules/settings/monitoring/helpers/export-pdf-csv'
import { downloadFile } from '@/src/utils/download'
import { UserPreferenceComputed } from '@state/modules/user-preference'

export default {
  name: 'AuditPolicyExporterList',
  components: {
    AuditKpiView,
    UsedCounts,
    Progress,
    BenchmarkProvider,
    BenchmarkPicker,
  },
  inject: { benchmarkContext: { default: { options: new Map() } } },
  data() {
    this.gridItemActions = [
      { key: 'edit', name: 'Edit Role', icon: 'pencil' },
      { key: 'delete', name: 'Delete Role', icon: 'trash-alt', isDanger: true },
    ]
    return {
      searchTerm: undefined,
      loading: true,

      rows: [],
      view: this.$route.params.view || 'grid-view',
      availableColumns: [
        {
          key: 'name',
          name: 'Compliance Policy',
          searchable: true,
          sortable: true,
          align: 'left',
        },
        {
          key: 'compliance_percentage_sort',
          name: 'Compliance Score',
          searchable: true,
          sortable: true,
          align: 'left',
          exportKey: 'compliance_percentage',
        },
        {
          key: 'deviceSeverity',
          name: 'Device Severity',
          searchable: false,
          sortable: false,
          // width: '200px',
          minWidth: '100px',
          align: 'center',
        },

        {
          key: 'usedCounts',
          name: 'Used Count',
          width: '150px',
          sortable: true,
          searchable: true,
          align: 'center',
          minWidth: '100px',
        },
        {
          key: 'benchmark',
          name: 'Benchmark',
          searchable: false,
          sortable: false,
          align: 'left',
        },
        {
          key: 'last_scan_timestamp',
          name: 'Last Scan At',
          searchable: true,
          sortable: true,
          align: 'left',
          exportType: 'roundedDateTime',
          exportKey: 'last_scan_timestamp',
        },
        // {
        //   key: 'actions',
        //   name: 'Actions',
        //   width: '120px',
        //   align: 'right',
        //   minWidth: '100px',
        // },
      ],
    }
  },

  computed: {
    ...UserPreferenceComputed,
  },
  created() {
    this.getAuditPolicyData()
  },
  methods: {
    formatDateTime(value) {
      return datetime(Math.round(value / 1000))
    },
    handelDrilldown() {},
    toggleView() {
      if (this.view === 'grid-view') {
        this.view = 'dashboard-view'
      } else {
        this.view = 'grid-view'
      }
    },
    nevigateToAuditPolicyDevices(item) {
      this.$router.push(
        this.$currentModule.getRoute('audit-policy-device', {
          params: {
            policyId: item.compliance_policy_id,
          },
          query: {
            row: encodeURIComponent(btoa(JSON.stringify(item))),
          },
        })
      )
    },
    getAuditPolicyData() {
      return getAuditPolicyDataApi().then((rows) => {
        this.rows = rows
        this.loading = false

        Bus.$emit('fetched:overallcompliance', {
          today: rows.length
            ? (SumBy(rows, 'compliance_percentage_sort') / rows.length).toFixed(
                0
              )
            : undefined,
        })
      })
    },
    async exportGrid(type) {
      const columns = this.availableColumns.filter(
        (obj) => obj.key && !obj.hidden
      )
      let items = await this.$refs?.gridRef?.getFilteredData()
      const contextData = this.$refs?.gridRef?.getContextData()

      items.data = items.data.map((item) => {
        if (item.deviceSeverity && Array.isArray(item.deviceSeverity)) {
          item.deviceSeverity = item.deviceSeverity
            .map((severity) => severity.value)
            .join(' | ')
        }
        item.benchmark =
          this.benchmarkContext.options?.get(item.benchmark)?.name ||
          item.benchmark ||
          ''
        item.usedCounts = item.usedCounts || 0
        return item
      })
      this.$successNotification({
        message: 'Success',
        description: `The file will be downloaded once ready`,
      })
      exportData(columns, items.data, type, contextData, {
        dateTimeFormat: this.dateFormat,
        timezone: this.timezone,
      }).then((blob) => {
        downloadFile(blob, undefined, `Compliance Performance.${type}`)
      })
    },
  },
}
</script>
