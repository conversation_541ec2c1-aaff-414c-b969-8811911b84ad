<template>
  <div class="flex flex-col flex-1 items-center justify-center">
    <CounterProvider
      v-if="tab === 'change_summary'"
      type="Log"
      :search-params="counterSearchParams"
      @counters="setCounters"
    >
      <span></span>
    </CounterProvider>
    <IncrementalResultProvider
      v-if="widetDef"
      ref="resultProviderRef"
      :serverside-widget-defination="widetDef"
      disabled-streaming-timer
      full-response
      @patchRecived="patchRecived"
    >
      <MGrid
        v-if="!loading"
        :key="tab"
        class="min-w-0"
        :columns="columns"
        :data="rows"
        :paging="paging"
      >
        <template v-slot:operation="{ item }">
          <MButton
            variant="transparent"
            :shadow="false"
            :rounded="false"
            class="p-0"
            @click="showItemForLastPerfomAction = item"
          >
            <span class="text-primary">{{ item.operation }}</span>
          </MButton>
        </template>

        <template v-slot:timestamp="{ item }">
          {{ Math.round(item.timestamp / 1000) | datetime }}
        </template>

        <template v-slot:status="{ item }">
          <NCMCredentialStatus :status="item.status" use-status-map use-pill />
        </template>

        <template v-slot:view="{ item }">
          <MButton
            variant="transparent"
            :shadow="false"
            :rounded="false"
            class="p-0 flex items-center gap-1 view-log-btn"
            @click="redirectToLogExplorer(item)"
          >
            <MIcon name="eye" class="text-sm" />
            <span>View in Log Explorer</span>
          </MButton>
        </template>
      </MGrid>
      <MLoader v-else />
    </IncrementalResultProvider>

    <MGrid
      v-if="!widetDef && tab === 'change_summary' && !loading"
      :key="tab"
      class="min-w-0"
      :columns="columns"
      :data="[]"
    ></MGrid>

    <FlotoDrawer
      :key="(dataItem || {}).id || 'id'"
      :open="Boolean(showItemForLastPerfomAction)"
      :scrolled-content="false"
      @hide="showItemForLastPerfomAction = null"
    >
      <template v-slot:title>
        <h5 v-if="showItemForLastPerfomAction" class="text-ellipsis mb-0 mr-2">
          Performed Action - {{ showItemForLastPerfomAction.operation }}
        </h5>
      </template>

      <template v-if="showItemForLastPerfomAction">
        <FlotoScrollView class="h-full px-4">
          <MRow :gutter="0" class="mt-2">
            <MCol
              v-for="(detail, index) in actionDetailsKeys"
              :key="detail.key"
              :size="(index + 1) % 2 !== 0 ? 6 : 6"
              class="mb-4"
            >
              <MRow :gutter="0">
                <MCol
                  :size="4"
                  class="text-neutral-light font-500"
                  style="flex-shrink: 0; color: var(--neutral-regular)"
                  >{{ detail.label }}:</MCol
                >
                <MCol
                  :size="8"
                  class="text-ellipsis"
                  :title="showItemForLastPerfomAction[detail.key] || ''"
                >
                  <NCMCredentialStatus
                    v-if="detail.key === 'status'"
                    :status="showItemForLastPerfomAction.status"
                    use-status-map
                  />

                  <template v-else-if="detail.key === 'timestamp'">
                    {{
                      Math.round(showItemForLastPerfomAction.timestamp / 1000)
                        | datetime
                    }}
                  </template>

                  <template v-else>
                    {{ showItemForLastPerfomAction[detail.key] || '' }}
                  </template>
                </MCol>
              </MRow>
            </MCol>

            <MCol
              v-if="(showItemForLastPerfomAction || {}).firmwareImageFileName"
              class="mb-4"
              :size="12"
            >
              <MRow :gutter="0">
                <MCol
                  :size="2"
                  class="text-neutral-light font-500"
                  style="flex-shrink: 0; color: var(--neutral-regular)"
                >
                  Upgrade File:
                </MCol>
                <MCol :size="10">
                  {{ showItemForLastPerfomAction.firmwareImageFileName }}
                </MCol>
              </MRow>
            </MCol>
            <MCol :size="12">
              <MRow :gutter="0">
                <MCol
                  :size="2"
                  class="text-neutral-light font-500"
                  style="flex-shrink: 0; color: var(--neutral-regular)"
                >
                  Message:
                </MCol>
                <MCol :size="10">
                  {{ showItemForLastPerfomAction.message }}
                </MCol>
              </MRow>
            </MCol>
          </MRow>

          <MRow :gutter="0">
            <MDivider class="" />
          </MRow>

          <template v-if="trimedOutput && trimedOutput.length">
            <MRow :gutter="0" class="w-full">
              <MCol>
                <h5 class="text-primary">Action Execution Output :</h5>
              </MCol>
            </MRow>

            <pre class="output-shower">{{ trimedOutput }}</pre>
          </template>
        </FlotoScrollView>
      </template>
    </FlotoDrawer>
  </div>
</template>

<script>
import Trim from 'lodash/trim'
import Moment from 'moment'
import { WidgetTypeConstants } from '@components/widgets/constants'

import {
  // getNcmDetails,
  generateNcmDetailWidgetDefinition,
  transformData,
} from '../ncm-api'

import NCMCredentialStatus from '@modules/settings/ncm-settings/components/ncm-credential-status.vue'
import IncrementalResultProvider from '@components/data-provider/incremental-result-provider.vue'
import { generateId } from '@/src/utils/id'
import CounterProvider from '@components/data-provider/counter-provider.vue'
import { getAllLogInventoryApi } from '../../settings/log-settings/log-settings-api'

export default {
  name: 'NcmDeviceDetails',
  components: {
    NCMCredentialStatus,
    IncrementalResultProvider,
    CounterProvider,
  },
  props: {
    tab: {
      type: String,
      required: true,
    },
    dataItem: {
      type: Object,
      required: true,
    },
  },

  data() {
    this.counterSearchParams = {
      'visualization.group.type': 'log',
    }

    return {
      rows: [],
      loading: true,
      defaultDetails: [
        {
          key: 'timestamp',
          label: 'Timestamp',
        },
        {
          key: 'status',
          label: 'Action Status',
        },

        {
          key: 'userName',
          label: 'Executed By',
        },
      ],
      showItemForLastPerfomAction: null,
      logCategoryMap: [],
      widetDef: null,
    }
  },
  computed: {
    actionDetailsKeys() {
      if (
        this.showItemForLastPerfomAction &&
        this.showItemForLastPerfomAction.operation === 'runbook'
      ) {
        return [
          {
            key: 'runbookName',
            label: 'Runbook',
          },
          {
            key: 'runbookDescription',
            label: 'Description',
          },
          ...this.defaultDetails,
        ]
      }
      return this.defaultDetails
    },
    columns() {
      if (this.tab === 'action_history') {
        return [
          {
            key: 'timestamp',
            name: 'Timestamp',
            searchable: true,
            sortable: true,
          },
          {
            key: 'operation',
            name: 'Action',
            searchable: true,
            sortable: true,
          },
          {
            key: 'status',
            name: 'STATUS',
            searchable: true,
            sortable: true,
          },
          {
            key: 'userName',
            name: 'Executed By',
            searchable: true,
            sortable: true,
          },
        ]
      } else if (this.tab === 'hardware_detail') {
      } else if (this.tab === 'change_summary') {
        return [
          {
            key: 'timestamp',
            name: 'Timestamp',
            searchable: true,
            sortable: true,
          },
          {
            key: 'userName',
            name: 'User',
            searchable: true,
            sortable: true,
          },
          {
            key: 'remoteIp',
            name: 'Remote IP',
            searchable: true,
            sortable: true,
          },
          {
            key: 'session',
            name: 'Session',
            searchable: true,
            sortable: true,
          },
          {
            key: 'view',
            name: ' ',
            searchable: false,
            sortable: false,
          },
        ]
      }

      return []
    },

    paging() {
      return ['action_history'].includes(this.tab)
    },

    trimedOutput() {
      return Trim(this.showItemForLastPerfomAction?.output) || ''
    },
    // widetDef() {
    //   const currentLogCategory = this.currentLogCategory
    //   return generateNcmDetailWidgetDefinition(
    //     this.tab,
    //     this.dataItem.ip,
    //     currentLogCategory
    //   )
    // },
  },

  watch: {
    tab: {
      immediate: true,
      handler(newTab, oldTab) {
        if (newTab !== oldTab) {
          this.rows = []
          this.loading = true
          this.widetDef = null

          if (newTab === 'change_summary') {
            this.logCategoryMap = []
          } else {
            this.requestIncrementalData()
          }
        }
      },
    },
  },
  methods: {
    // requestData() {
    //   this.loading = true
    //   this.rows = []
    //   getNcmDetails(this.tab, this.entityId).then((data) => {
    //     this.rows = data
    //     this.loading = false
    //   })
    // },
    patchRecived(response) {
      const data =
        ((response.result || {})[WidgetTypeConstants.GRID] || {}).data || []
      this.loading = false
      this.rows = [
        ...this.rows,
        ...(data || []).map((row) =>
          transformData(row, this.tab, this.currentLogCategory)
        ),
      ]

      if (
        this.tab === 'change_summary' &&
        response.result.queryMeta.progress === 100 &&
        !this.rows.length
      ) {
        this.requestIncrementalDataForNextLogCategory()
      }
    },

    requestIncrementalData() {
      this.loading = true
      this.rows = []

      this.widetDef = generateNcmDetailWidgetDefinition(
        this.tab,
        this.dataItem.ip,
        this.currentLogCategory
      )

      if (this.$refs.resultProviderRef) {
        this.$refs.resultProviderRef.requestData()
      }
    },
    redirectToLogExplorer(item) {
      const filter = {
        pre: {
          condition: 'and',
          groups: [
            {
              key: generateId(),
              condition: 'and',
              inclusion: 'include',
              conditions: [
                {
                  key: generateId(),
                  operand: 'event.source',
                  operator: 'in',
                  value: [this.dataItem.ip],
                },
                {
                  key: generateId(),
                  operand: 'event.category',
                  operator: 'in',
                  value: [this.currentLogCategory],
                },
              ],
            },
          ],
          inclusion: 'include',
        },
      }

      const timestamp = Math.round(item.timestamp / 1000)
      const thirtyMinutesInSeconds = 30 * 60
      const startTime = timestamp - thirtyMinutesInSeconds
      const endTime = timestamp + thirtyMinutesInSeconds

      const routePath = this.$modules.getModuleRoute('log', 'log-search', {
        query: {
          t: encodeURIComponent(
            btoa(
              JSON.stringify({
                selectedKey: 'custom',
                startDate: startTime * 1000,
                endDate: endTime * 1000,
                startTime: Moment.unix(startTime).format('HH:mm:ss'),
                endTime: Moment.unix(endTime).format('HH:mm:ss'),
                dailyRollingData: false,
              })
            )
          ),
          filter: encodeURIComponent(btoa(JSON.stringify(filter))),
        },
      })

      const fullPath = this.$router.resolve(routePath).href

      // Open the URL in a new tab
      window.open(fullPath, '_blank')
    },
    async setCounters(optionsMap) {
      const logInventory = await getAllLogInventoryApi()

      const matchingSource = logInventory.find(
        (item) => item.source === this.dataItem.ip
      )
      const pluginIds = matchingSource?.pluginId || []

      const categories = Array.from(optionsMap.values()).filter((c) =>
        c.key.includes('config.user.name')
      )

      // Filter by matching plugin IDs
      this.logCategoryMap = categories.filter((c) =>
        c.metricPlugins?.some((id) => pluginIds.includes(id))
      )

      if (this.logCategoryMap.length > 0) {
        this.requestIncrementalDataForNextLogCategory()
      } else {
        this.loading = false
      }
    },
    requestIncrementalDataForNextLogCategory() {
      const currentLogCategoryObj = this.logCategoryMap.shift()

      if (currentLogCategoryObj) {
        this.currentLogCategory = currentLogCategoryObj.eventCategory
        this.widetDef = generateNcmDetailWidgetDefinition(
          this.tab,
          this.dataItem.ip,
          this.currentLogCategory
        )

        this.requestIncrementalData()
      } else {
        this.loading = false
      }
    },
  },
}
</script>

<style lang="less" scoped>
.output-shower {
  @apply my-2 w-full text-white;

  height: auto; /* Let the pre tag adjust its height automatically */
  min-height: 80%;
  max-height: fit-content;
  max-height: 100%; /* Constrain the height to not exceed the parent's height */
  padding: 10px;
  overflow: scroll;
  background: black;
  border-radius: 5px;
}

.view-log-btn {
  font-size: 0.7rem;
  color: #099dd9 !important;

  span {
    margin-left: 2px;
  }
}
</style>
