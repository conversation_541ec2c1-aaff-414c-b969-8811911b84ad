import api from '@api'
import UniqBy from 'lodash/uniqBy'

import { <PERSON><PERSON><PERSON> } from 'buffer'
import SnappyJs from 'snappyjs'
import Constants from '@constants'
import { WidgetTypeConstants } from '@components/widgets/constants'

import { calculateLastTimeline } from '@components/widgets/helper'
import {
  wrapSocketEventToPromise,
  makeCounter,
  buildWidgetContext,
} from '@/src/utils/socket-event-as-api'
import { transformNcmObjectesForClient } from './helpers/explorer'
import Config from './config'
import { transformMonitorForSelection } from '@/src/data/monitor'

export function getNcmObjectsApi(useObjectID) {
  return api
    .get('/settings/configurations/', {
      params: {
        'object.category': 'Network',
        'config.backup.required': 'yes',
      },
    })
    .then((data) =>
      (data.result || []).map((ncmObject) =>
        transformNcmObjectesForClient(ncmObject, useObjectID)
      )
    )
}

export function getNcmObjectsAsDeviceMonitorApi() {
  return api
    .get('/settings/configurations/', {
      params: {
        'object.category': 'Network',
        'config.backup.required': 'yes',
      },
    })
    .then((data) => (data.result || []).map(transformMonitorForSelection))
}

export function getSingleNcmObjectsApi(id) {
  return api
    .get('/settings/configurations/' + id, {
      params: { 'object.category': 'Network', 'config.backup.required': 'yes' },
    })
    .then((data) => transformNcmObjectesForClient(data.result || {}))
}

export function exportBackup(data, forSingleExport) {
  return wrapSocketEventToPromise(
    Config.EXPORT_BACKUP,
    {
      ...(forSingleExport
        ? {
            id: data.id,
            'file.type': data.fileType,
            version: data.version,
            uuid: data.uuid,
          }
        : {
            id: data.id,
            uuid: data.uuid,
          }),
    },
    false,
    false,
    true
  )
}

export function changeBaselineApi(item, action, baselineVersion) {
  return api
    .put(
      `/settings/configurations/${item.id}/${
        action === 'setup_as_baseline' ? 'assign-baseline' : 'remove-baseline'
      }`,
      {
        'config.backup.file.baseline.version': baselineVersion,
      }
    )
    .then((data) => {
      return data
    })
}

export function fetchNetworkBackup(id, version, fileType) {
  return api
    .get(
      `/settings/configurations/${id}/backup-content/?version=${version}&file.type=${fileType}`
    )
    .then((data) => {
      const decodedFileContent = decoadBackup(
        data['config.backup.file.content']
      )

      return {
        decodedNetworkBackup: decodedFileContent,
        backupTime: data['config.backup.time'],
      }
    })
}

export function decoadBackup(backup) {
  if (backup) {
    return new TextDecoder('utf-8').decode(
      SnappyJs.uncompress(Buffer.from(backup, 'base64'))
    )
  }
  return ''
}

export function sendConfigRequest(id, type, context = {}) {
  return api.post('/settings/configurations/execute-operation', {
    'config.operation': type,
    objects: Array.isArray(id) ? id : [id],
    ...context,
  })
}

export function getNcmDetails(tab, entities) {
  return wrapSocketEventToPromise(
    Constants.UI_WIDGET_RESULT_EVENT,
    {
      ...generateNcmDetailWidgetDefinition(tab, entities),
    },
    true,
    true,
    true
  ).then(({ result }) => {
    return ((result[WidgetTypeConstants.GRID] || {}).data || []).map(
      transformData
    )
  })
}

export function generateNcmDetailWidgetDefinition(tab, entities, logCategory) {
  const logCategoryName = logCategory?.toLowerCase()?.split(' ')?.join('.')
  const def = buildWidgetContext({
    category: WidgetTypeConstants.CHART,
    groupType: tab === 'change_summary' ? 'log' : 'config',
    widgetType: WidgetTypeConstants.GRID,
    timeline: calculateLastTimeline(30, 'day'),
    counters:
      tab === 'change_summary'
        ? [
            makeCounter(
              `${logCategoryName}.config.user.name`,
              '__NONE__',
              'event.source',
              entities
            ),
            makeCounter(
              `${logCategoryName}.config.remote.ip`,
              '__NONE__',
              'event.source',
              entities
            ),
            makeCounter(
              `${logCategoryName}.config.session`,
              '__NONE__',
              'event.source',
              entities
            ),
          ]
        : [
            makeCounter('config.event', '__NONE__', 'event.source', entities),
            // makeCounter('config.operation.output', '__NONE__', 'Monitor', entities),
            // makeCounter('config.operation.message', '__NONE__', 'Monitor', entities),
            // makeCounter('config.operation.status', '__NONE__', 'Monitor', entities),
            // // makeCounter('timestamp', '__NONE__', 'Monitor', entities),
            // makeCounter('user.name', '__NONE__', 'Monitor', entities),
            // // makeCounter('remote.address', '__NONE__', 'Monitor', entities),
            // makeCounter(
            //   'config.firmware.image.file.name',
            //   '__NONE__',
            //   'Monitor',
            //   entities
            // ),
          ],
    preFilters: {
      ...(logCategory && tab === 'change_summary'
        ? {
            condition: 'and',
            inclusion: 'include',
            conditions: [
              {
                operand: 'event.category',
                operator: '=',
                value: logCategory,
              },
            ],
          }
        : {}),
    },
  })
    .appendToGroup(tab === 'change_summary' ? 'log' : 'config', {
      ...(tab === 'action_history'
        ? {
            additionalUntouchedRequestChunk: {
              ...(tab !== 'change_summary' ? { 'join.type': 'custom' } : {}),
              type: tab === 'change_summary' ? 'log' : 'config',
            },
          }
        : {
            target: {
              entityType: 'event.source',
              entities: [entities],
            },
          }),
    })
    .generateWidgetDefinition()
  return {
    ...def,
    ...(tab !== 'change_summary'
      ? { 'join.type': 'custom', 'join.result': 'config.event' }
      : {}),
  }
}

export function transformData(row, tab, logCategory) {
  const logCategoryName = logCategory?.toLowerCase()?.split(' ')?.join('.')
  if (tab === 'change_summary') {
    return {
      userName: row[`${logCategoryName}.config.user.name.value`] || '-',
      session: row[`${logCategoryName}.config.session.value`] || '-',
      remoteIp: row[`${logCategoryName}.config.remote.ip.value`] || '-',
      timestamp: row['timestamp'],
    }
  }

  return {
    operation: row['config.operation'],
    output: row['config.operation.output'],
    message: row['config.operation.message'],
    status: row['config.operation.status'],
    timestamp: row['timestamp'],
    userName: row['user.name'],
    runbookDescription: row['runbook.plugin.description'],
    runbookName: row['runbook.plugin.name'],
    remoteAddress: row['remote.address'],

    ...(row['config.operation'] === 'upgrade'
      ? {
          firmwareImageFileName: row['config.firmware.image.file.name'],
        }
      : {}),
  }
}

export function getFirmwareImages() {
  return api.get('/misc/firmware-files').then((result) => {
    return UniqBy(
      (result['config.firmware.image.files'] || []).map((item) => ({
        key: item['config.firmware.image.file.name'],
        text: item['config.firmware.image.file.name'],
        size: item['config.firmware.image.file.size'],
        name: item['config.firmware.image.file.name'],
      })),
      'key'
    )
  })
}

export function moveFirmwareUpgradeFilesApi(fileId) {
  return api.post(`/settings/configurations/move-firmware-upgrade-image-file`, {
    'config.firmware.image.file.name': fileId,
  })
}

export function getMD5ContextApi(fileName) {
  return api
    .get(`misc/md5-checksum`, {
      params: {
        'config.firmware.image.file.name': fileName,
      },
    })
    .then((response) => {
      return response?.['config.firmware.image.files']
    })
}

export function createConfigUpgradeOpsSchedularApi(
  appliedScheduleContext,
  objectId,
  md5Context
) {
  return api.post(`/settings/schedulers`, {
    'scheduler.job.type': 'Config Upgrade Ops',
    'scheduler.start.date': appliedScheduleContext.selectedDate,
    'scheduler.times': [appliedScheduleContext.selectedTime],
    'scheduler.timeline': 'Once',
    'scheduler.context': {
      objects: objectId,
      'config.firmware.image.file.name':
        md5Context?.['config.firmware.image.file.name'],
      'config.firmware.image.file.size':
        md5Context?.['config.firmware.image.file.size'],
    },
  })
}
