<template>
  <FlotoFixedView>
    <MRow :gutter="0" class="flex justify-between items-center py-2">
      <div class="flex items-center h-full">
        <FlotoBackButton
          :to="$currentModule.getRoute('compliance')"
          class="text-neutral-light"
        />

        <h4 class="font-500 text-primary mb-0">
          {{ drilldownContext.name }}
        </h4>
      </div>

      <div class="flex items-center justify-end my-2">
        <RadialView
          v-if="!loading && shouldShowRadialGauge"
          :data="raidalViewSeries"
          for-monitor-details
          use-compliance-severity
          @filter-by-severity="updateFilteredSevertiy"
        />
      </div>
    </MRow>
    <MDivider class="my-0 mx-0" />

    <FlotoFixedView :gutter="0">
      <MPersistedColumns
        v-model="gridColumns"
        :default-value="gridColumns"
        module-key="ncm"
        :available-columns="availableColumns"
      >
        <template
          v-slot="{
            columns: persistedColumns,
            setColumns: updatePersistedColumns,
          }"
        >
          <FlotoFixedView>
            <FlotoScrollView>
              <FlotoPaginatedCrud
                ref="paginatedCrudRef"
                as-table
                resource-name="audit-policy-device"
                default-sort="-name"
                :columns="persistedColumns"
                :fetch-fn="getAuditPolicyDevices"
                :filters="appliedFilters"
                @column-change="updateFilteredSevertiy"
              >
                <template
                  v-slot:add-controls="{ filter, resetFilter, searchTerm }"
                >
                  <MRow :gutter="0" class="-mt-2">
                    <MCol :size="5">
                      <MInput
                        :value="searchTerm"
                        class="search-box"
                        placeholder="Search"
                        name="search"
                        @update="filter"
                      >
                        <template v-slot:prefix>
                          <MIcon name="search" />
                        </template>
                        <template v-if="searchTerm" v-slot:suffix>
                          <MIcon
                            name="times-circle"
                            class="text-neutral-light cursor-pointer"
                            @click="resetFilter"
                          />
                        </template>
                      </MInput>
                    </MCol>

                    <MCol
                      :size="7"
                      class="text-right flex items-center justify-end"
                    >
                      <div class="flex items-center">
                        <ColumnSelector
                          v-model="gridColumns"
                          :columns="availableColumns"
                          @change="updatePersistedColumns"
                        />

                        <MButton
                          :shadow="false"
                          class="squared-button mr-2"
                          :rounded="false"
                          title="Export As PDF"
                          variant="neutral-lightest"
                          @click="handleExportMonitors('pdf')"
                        >
                          <MIcon name="export-pdf" />
                        </MButton>
                        <MButton
                          :shadow="false"
                          class="squared-button mr-2"
                          :rounded="false"
                          variant="neutral-lightest"
                          title="Export As CSV"
                          @click="handleExportMonitors('csv')"
                        >
                          <MIcon name="export-csv" />
                        </MButton>
                        <MButton
                          id="filter-btn"
                          class="squared-button mr-2"
                          variant="neutral-lightest"
                          @click="toggleFilters"
                        >
                          <MIcon name="filter" class="excluded-header-icon" />
                        </MButton>
                      </div>
                    </MCol>
                  </MRow>

                  <!-- Filter Panel -->
                  <ComplianceDrilldownFilter
                    v-model="filters"
                    class="mr-2"
                    :visible="isFilterVisible"
                    @change="applyFilter"
                    @hide="isFilterVisible = false"
                  />
                </template>

                <template v-slot:object_id="{ item }">
                  <a
                    class="text-ellipsis"
                    :title="item.object_id"
                    @click="nevigateToBreakdown(item)"
                  >
                    {{ item.object_id }}
                  </a>
                </template>

                <template v-slot:severity="{ item }">
                  <ComplianceSeverity :severity="item.severity" />
                </template>

                <template v-slot:last_scan_timestamp="{ item }">
                  {{ formatDateTime(item.last_scan_timestamp) }}
                </template>
                <template v-slot:last_scan_status="{ item }">
                  <div
                    v-if="
                      item.last_scan_status === $constants.EVENT_SUCCEDED_STATUS
                    "
                    class="flex"
                  >
                    <MIcon
                      class="mr-1 text-secondary-green"
                      name="check-circle"
                    >
                    </MIcon>
                    Success
                  </div>
                  <div v-else class="flex">
                    <MIcon class="mr-1 text-secondary-red" name="check-circle">
                    </MIcon>
                    Failed
                  </div>
                </template>

                <template v-slot:compliance_percentage="{ item }">
                  <div class="flex items-center" style="max-width: 150px">
                    <Progress
                      :stroke-width="4"
                      :width="item.compliance_percentage_sort"
                      :stroke-color="getComplianceSeverityColors(item.severity)"
                    />

                    <span
                      class="ml-2"
                      :style="{
                        color: `${getComplianceSeverityColors(item.severity)}`,
                      }"
                    >
                      {{ item.compliance_percentage }}</span
                    >
                  </div>
                </template>
              </FlotoPaginatedCrud>
            </FlotoScrollView>
          </FlotoFixedView>
        </template>
      </MPersistedColumns>
    </FlotoFixedView>
  </FlotoFixedView>
</template>

<script>
import Uniq from 'lodash/uniq'
import CloneDeep from 'lodash/cloneDeep'

import Progress from '@components/progress.vue'
import datetime from '@src/filters/datetime'

import { convertGridResultToGauge } from '@components/widgets/helper'
import ColumnSelector from '@components/column-selector.vue'
import RadialView from '@/src/components/widgets/views/radial-view.vue'
import { getAuditPolicyDevicesApi } from '../compliance-api'
import ComplianceSeverity from '../components/compliance/compliance-severity.vue'
import {
  getColorByComplianceSeverity,
  COMPLIANCE_SEVERITY,
} from '../helpers/compliance'
import exportData from '@modules/settings/monitoring/helpers/export-pdf-csv'
import { downloadFile } from '@utils/download'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import ComplianceDrilldownFilter from '../components/compliance/compliance-drilldown-filter.vue'

export default {
  name: 'AuditPolicyDevice',
  components: {
    RadialView,
    ColumnSelector,
    ComplianceSeverity,
    Progress,
    ComplianceDrilldownFilter,
  },

  data() {
    this.columns = [
      {
        key: 'object_id',
        name: 'Device',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        disable: true,

        cellRender: 'device',
      },
      {
        key: 'severity',
        name: 'Severity',
        searchable: true,
        sortable: true,
        minWidth: '100px',
      },
      {
        key: 'compliance_percentage',
        name: 'Compliance',
        searchable: true,
        sortable: true,
        minWidth: '100px',
      },
      {
        key: 'scanned_rule',
        name: 'Scanned Rule',
        searchable: true,
        sortable: true,
        minWidth: '100px',
      },
      {
        key: 'last_scan_timestamp',
        name: 'Last Scan',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        exportType: 'roundedDateTime',
        exportKey: 'last_scan_timestamp',
      },
      {
        key: 'last_scan_status',
        name: 'Last Scan Status',
        searchable: true,
        sortable: true,
        minWidth: '100px',
      },
      {
        key: 'message',
        name: 'Remarks',
        searchable: true,
        sortable: true,
        minWidth: '100px',
      },
    ]

    return {
      gridColumns: this.columns,
      loading: true,
      drilldownContext: {},
      raidalViewSeries: {},
      filters: {
        severity: [],
        lastScanStatus: undefined,
      },
      filteredSevertiy: [],
      isFilterVisible: false,
    }
  },

  computed: {
    ...UserPreferenceComputed,

    availableColumns() {
      return this.columns
    },
    appliedFilters() {
      let filters = []

      const value = this.filters

      if (value.severity && value.severity.length) {
        filters = [
          ...(filters || []),
          {
            field: 'severity',
            operator: 'array_contains',
            value: value.severity,
          },
        ]
      }

      if (value.lastScanStatus) {
        filters = [
          ...(filters || []),
          {
            field: 'last_scan_status',
            operator: 'eq',
            value: value.lastScanStatus,
          },
        ]
      }

      return filters
    },
    shouldShowRadialGauge() {
      return (CloneDeep(this.raidalViewSeries?.series?.[0]?.data) || [])?.length
    },
  },
  watch: {
    filteredSevertiy: {
      immediate: true,
      handler(newValue) {
        this.filters = {
          ...this.filters,
          severities: newValue,
        }
      },
    },
  },

  created() {
    const r = this.$route.query.row

    if (r) {
      try {
        this.drilldownContext = JSON.parse(atob(decodeURIComponent(r)))
      } catch (e) {
        this.drilldownContext = {}
      }
    }
  },
  methods: {
    formatDateTime(value) {
      return datetime(Math.round(value / 1000))
    },
    getComplianceSeverityColors(severity) {
      return getColorByComplianceSeverity(severity)
    },
    filterBySeverity(severity) {},

    buildSeriesForRadialView(data) {
      this.raidalViewSeries = convertGridResultToGauge(
        data,
        undefined,
        COMPLIANCE_SEVERITY
      )
    },
    getAuditPolicyDevices() {
      return getAuditPolicyDevicesApi(
        this.drilldownContext.compliance_policy_id ||
          this.drilldownContext.policyId
      ).then(async (data) => {
        await this.buildSeriesForRadialView(data)

        this.loading = false

        return data
      })
    },

    nevigateToBreakdown(item) {
      this.$router.push(
        this.$currentModule.getRoute('device-policy-breakdown', {
          params: {
            policyId:
              this.drilldownContext.compliance_policy_id ||
              this.drilldownContext.policyId,
            objectId: item['entity.id'],
          },
          query: {
            row: encodeURIComponent(
              btoa(
                JSON.stringify({
                  ...item,
                  policyName: this.drilldownContext.name,
                  policyId:
                    this.drilldownContext.compliance_policy_id ||
                    this.drilldownContext.policyId,
                  monitor: item.object_id,
                  benchmark: this.drilldownContext.benchmark,
                })
              )
            ),
          },
        })
      )
    },
    async handleExportMonitors(type) {
      const items = await this.$refs?.paginatedCrudRef?.getFilteredData()

      const options = {
        dateTimeFormat: this.dateFormat,
        timezone: this.timezone,
      }
      this.$successNotification({
        message: 'Success',
        description: `The file will be downloaded once ready`,
      })
      exportData(
        this.gridColumns.filter((obj) => obj.key && !obj.hidden),
        items,
        type,
        this.$refs.paginatedCrudRef.getContextData(),
        options
      ).then((blob) => {
        downloadFile(blob, undefined, `${this.drilldownContext.name}.${type}`)
      })
    },
    resetSeverityFilter() {
      this.filteredSevertiy = []
    },
    updateFilteredSevertiy(context) {
      if (context.active) {
        this.filteredSevertiy = Uniq([
          ...this.filteredSevertiy,
          context.severity,
        ])
      } else {
        this.filteredSevertiy = this.filteredSevertiy.filter(
          (s) => s !== context.severity
        )
      }
    },
    toggleFilters() {
      this.isFilterVisible = !this.isFilterVisible
    },
    applyFilter(filters) {
      this.filters = {
        ...filters,
      }
      this.isFilterVisible = false
    },
  },
}
</script>
