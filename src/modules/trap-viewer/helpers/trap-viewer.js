import { generateId } from '@utils/id'
import DateTime from '@src/filters/datetime'

export function transformHistoryData(history) {
  let dateTime = history['time'].split(' ')
  return {
    date: dateTime[0],
    time: dateTime[1],
    severity: history['trap.severity'],
    text: history['trap.raw.message'],
    source: history['trap.source'],
    enterpriseId: history['trap.enterprise.id'],
    acknowledge: history['trap.acknowledge'] === 'Acknowledge',
    version: history['trap.version'],
    vendor: history['trap.vendor'],
    message: history['trap.raw.message'],
    trapOid: history['trap.oid'],
    description: history['trap.message'],
  }
}

export function transformFilterForServer(filter) {
  return {
    'trap.oids': filter['trapOids'],
    'trap.severities': filter['severities'],
    'trap.sources': filter['sources'],
    'trap.vendors': filter['vendors'],
    'trap.acknowledge': filter['acknowledge'],
  }
}

export function transformGridData(trap) {
  return {
    id: generateId(),
    severity: trap['trap.severity.last'],
    name: trap['trap.name'],
    oid: trap['trap.oid'],
    source: trap['event.source'],
    enterprise: trap['trap.enterprise.last'],
    enterpriseId: trap['trap.enterprise.id.last'],
    vendor: trap['trap.vendor.last'],
    version: trap['trap.version.last'],
    type: trap['trap.type.last'],
    count: trap['trap.message.count'],
    message: trap['trap.raw.message.last'],
    acknowledged: trap['trap.ack.status'] === 'yes',
    timestamp: trap['timestamp.last'] ? trap['timestamp.last'] / 1000 : null,
    timestampSearch: trap['timestamp.last']
      ? DateTime(trap['timestamp.last'])
      : null,
    description: trap['trap.message.last'],
  }
}

export function transformItemForServer(trap) {
  return {
    'trap.severity': trap['severity'],
    'trap.name': trap['name'],
    'trap.oid': trap['oid'],
    'event.source': trap['source'],
    'trap.enterprise': trap['enterprise'],
    'trap.enterprise.id': trap['enterpriseId'],
    'trap.vendor': trap['vendor'],
    'trap.version': trap['version'],
    'trap.type': trap['type'],
    'trap.message.count': trap['count'],
    'trap.raw.message': trap['message'],
    'trap.acknowledged': trap['acknowledged'] ? 'yes' : 'no',
    'event.timestamp': trap['timestamp'],
    'trap.message': trap['description'],
  }
}

export const AVAILABLE_COLUMNS = [
  {
    key: 'name',
    name: 'Trap Name',
    searchable: true,
    sortable: true,
    width: '320px',
    disable: true,
  },
  {
    key: 'oid',
    name: 'Trap OID',
    sortable: true,
    searchable: true,
    width: '320px',
    disable: true,
  },
  {
    key: 'source',
    name: 'Source',
    searchable: true,
    sortable: true,
    width: '220px',
    disable: true,
  },
  {
    key: 'version',
    name: 'Version',
    width: '220px',
    searchable: true,
    sortable: true,
    hidden: true,
  },
  {
    key: 'type',
    name: 'Type',
    searchable: true,
    sortable: true,
    hidden: true,
    width: '220px',
  },
  {
    key: 'enterprise',
    name: 'Enterprise',
    sortable: true,
    searchable: true,
    hidden: true,
    width: '120px',
  },
  {
    key: 'enterpriseId',
    name: 'Enterprise ID',
    sortable: true,
    searchable: true,
    hidden: true,
    width: '120px',
  },
  {
    key: 'vendor',
    name: 'Vendor',
    searchable: true,
    sortable: true,
    hidden: true,
    width: '120px',
  },
  {
    key: 'count',
    name: 'Count',
    width: '200px',
    searchable: true,
    sortable: true,
    disable: true,
  },
  {
    key: 'description',
    name: 'Message',
    searchable: true,
    sortable: true,
    width: '240px',
  },
  {
    key: 'timestamp',
    name: 'Timestamp',
    searchable: true,
    sortable: true,
    width: '220px',
    searchKey: 'timestampSearch',
    exportType: 'datetime',
  },
  {
    key: 'acknowledge',
    name: 'Acknowledged',
    sortable: false,
    width: '120px',
    align: 'center',
    exportType: 'boolean',
    exportKey: 'acknowledged',
  },
  {
    key: 'actions',
    name: 'Action',
    width: '120px',
    disable: true,
    hidden: false,
    selectable: false,
    export: false,
  },
]
