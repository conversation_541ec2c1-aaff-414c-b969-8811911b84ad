<template>
  <div ref="mainContainer" class="flex flex-col flex-1 min-h-0">
    <FlotoContentLoader :loading="loading" :row-gutter="0">
      <div class="w-full flex flex-col flex-1 min-h-0 h-full">
        <div class="h-full flex flex-col flex-1 relative">
          <MRow class="flex-1 min-h-0" :gutter="0">
            <MCol :size="12" style="background: var(--topology-background)">
              <span
                v-if="fullscreen"
                class="block text-right pt-6 bg-neutral-lightest"
              >
                <span
                  class="text-neutral-light cursor-pointer"
                  style="margin-right: 30px"
                  @click="$emit('normal-screen')"
                >
                  <MIcon name="exit-fullscreen" size="2x" />
                </span>
              </span>
              <Graph
                v-if="nodes.length > 0"
                ref="graphRef"
                :key="`${view}-${graphKey}`"
                :view="view"
                :layout="tab === 'other' ? 'breadthfirst' : 'preset'"
                :show-dashed-edges="showLayer3"
                center
                :root-key="String(target.id)"
                :search-term="searchTerm"
                :fit="nodes.length > 4"
                :edge-tooltip="EdgeToolTipComponent"
                :fullscreen="fullscreen"
                :nodes="nodes"
                :edges="edges"
                :tooltip-component="EdgeToolTipComponent"
                @selected="activeMonitor = $event"
                @clear="activeMonitor = null"
              />
              <FlotoNoData v-else class="h-full" />
              <template v-if="shouldShowSideBar">
                <MonitorSidebar
                  :monitor="activeMonitor"
                  @close="handleCloseSidebar"
                />
              </template>
            </MCol>
          </MRow>
        </div>
      </div>
    </FlotoContentLoader>
  </div>
</template>

<script>
import FindIndex from 'lodash/findIndex'
import UpperCase from 'lodash/upperCase'
import { makeFullScreen, exitFullScreen } from '@utils/fullscreen'
import { generateId } from '@utils/id'
import Bus from '@utils/emitter'
import Graph from '@components/monitor-graph/graph.vue'
import MonitorSidebar from '@components/monitor-sidebar/monitor-sidebar.vue'
import { SEVERITY_MAP, transformMonitorForLocalDb } from '@data/monitor'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import EdgeTooltip from './edge-tooltip.vue'
import { topologyWorker, objectDBWorker, severityDBWorker } from '@/src/workers'
import { getMonitorsApi } from '../../settings/monitoring/monitors-api'
import { getInstanceSeverityApi } from '@/src/utils/socket-event-as-api'

export default {
  name: 'TopologyGraph',
  components: {
    Graph,
    MonitorSidebar,
  },
  props: {
    target: { type: [Object], required: true },
    tab: { type: String, required: true },
    view: { type: String, default: 'full' },
    showLayer3: { type: Boolean, default: false },
    fullscreen: { type: Boolean, default: false },
    searchTerm: { type: String, default: undefined },
  },
  data() {
    this.__cache = {}
    return {
      loading: true,
      guid: generateId(),
      nodes: [],
      edges: {},
      activeMonitor: null,
      graphKey: 1,
    }
  },
  computed: {
    ...UserPreferenceComputed,
    EdgeToolTipComponent() {
      return EdgeTooltip
    },
    shouldShowSideBar() {
      if (this.activeMonitor && this.activeMonitor.hasPermission === false) {
        return false
      }
      const excludedTypes = ['region', 'vm', 'unknown', 'other', 'ap']
      if (
        this.activeMonitor &&
        excludedTypes.includes(this.activeMonitor.resourceType) === false
      ) {
        return true
      }
      return false
    },
  },
  watch: {
    async theme(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (this.$refs.graphRef) {
          this.$refs.graphRef.updateTheme(newValue)
        }
      }
    },
    target(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.loading = true
        this.__cache = {}
        this.startTopologyStreaming()
      }
    },
    fullscreen(newValue) {
      if (newValue) {
        makeFullScreen(this.$refs.mainContainer)
      } else {
        exitFullScreen()
      }
    },
    view(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.activeMonitor = null
        if (this.__cache[newValue]) {
          this.nodes = this.__cache[newValue].nodes
          this.edges = this.__cache[newValue].edges
        } else {
          this.loading = true
          this.buildNodeAndEdges()
        }
      }
    },
  },
  async mounted() {
    const fullscreenHandler = (e) => {
      if (!document.fullscreen) {
        this.$emit('normal-screen')
      }
    }
    document.addEventListener('fullscreenchange', fullscreenHandler)
    Bus.$on('topology:position:update', this.updatePosition)
    Bus.$on(
      this.$currentModule.getConfig().TOPOLOGY_RENDER_EVENT,
      this.onLinkReceived
    )
    Bus.$on(this.$constants.EVENT_SEVERITY_UPDATED, this.onSeverityUpdated)
    Bus.$on('monitor.deleted', this.onMonitorDeleted)
    let refreshInterval = setInterval(() => {
      this.buildNodeAndEdges()
      this.graphKey++
    }, 30000)
    this.$once('hook:beforeDestroy', () => {
      clearInterval(refreshInterval)
      Bus.$off(
        this.$currentModule.getConfig().TOPOLOGY_RENDER_EVENT,
        this.onLinkReceived
      )
      Bus.$off('topology:position:update', this.updatePosition)
      Bus.$off(this.$constants.EVENT_SEVERITY_UPDATED, this.onSeverityUpdated)
      document.removeEventListener('fullscreenchange', fullscreenHandler)
      if (this.$_resizeObserver) {
        this.$_resizeObserver.disconnect()
        this.$_resizeObserver = undefined
      }
      Bus.$off(
        this.$constants.EVENT_SEVERITY_COUNTER_DB_CHANGED,
        this.buildNodeAndEdges
      )
    })

    this.$_resizeObserver = new ResizeObserver(() => {
      setTimeout(async () => {
        if (this.__originalEventData) {
          await this.buildNodeAndEdges()
          this.graphKey++
        }
      })
    })

    this.$_resizeObserver.observe(this.$refs.mainContainer)
    this.startTopologyStreaming()

    Bus.$on(
      this.$constants.EVENT_SEVERITY_COUNTER_DB_CHANGED,
      this.buildNodeAndEdges
    )
  },
  methods: {
    updatePosition(event) {
      this.nodes.map((node) => {
        if (node.data.ip === event.id || Number(node.data.id) === event.id) {
          node.data.position.x = event.x
          node.data.position.y = event.y
        }
        return { ...node }
      })
    },
    async onSeverityUpdated(monitorId, severity) {
      if (!this.$refs.graphRef) {
        return
      }
      setTimeout(async () => {
        if (this.tab === 'network') {
          this.$refs.graphRef.updateSeverity(monitorId, severity)
        } else {
          const updateNeeded =
            await topologyWorker.calculateTopologySeverityUpwards(
              monitorId,
              severity,
              this.nodes
            )
          Object.keys(updateNeeded.updatedNodes).forEach((key) => {
            const index = FindIndex(
              this.nodes,
              (node) => String(node.data.id) === String(key)
            )
            if (index !== -1) {
              this.nodes = Object.freeze([
                ...this.nodes.slice(0, index),
                {
                  ...this.nodes[index],
                  data: {
                    ...this.nodes[index].data,
                    severity,
                    severityNumber: SEVERITY_MAP[severity],
                  },
                },
                ...this.nodes.slice(index + 1),
              ])
            }
            this.$refs.graphRef.updateSeverity(
              key,
              updateNeeded.updatedNodes[key]
            )
          })
        }
      })
    },
    onMonitorDeleted(monitor) {
      if (this.$refs.graphRef) {
        this.$refs.graphRef.removeNode(monitor)
      }
    },
    async onLinkReceived(payload) {
      if (payload[this.$constants.UI_EVENT_UUID] !== this.guid) {
        return
      }
      this.__originalEventData = payload
      this.buildNodeAndEdges()
    },
    async buildNodeAndEdges() {
      if (!this.__originalEventData) {
        return
      }
      const payload = this.__originalEventData

      if (Object.keys(payload.result || {}).length === 0) {
        this.nodes = []
        this.edges = []
        this.loading = false
        return
      }
      let result
      let includedInstances = await topologyWorker.getAllInstancesFromHierachy(
        payload
      )
      let instanceSeverityMap = {}
      if ((includedInstances.instances || []).length) {
        instanceSeverityMap = await getInstanceSeverityApi({
          instance: true,
          'event.context': includedInstances.instances,
        })
      }
      let applicationSeverityMap = {}
      if ((includedInstances.applications || []).length) {
        applicationSeverityMap = await getInstanceSeverityApi({
          instance: false,
          'event.context': includedInstances.applications,
        })
      }
      let severityMap = await severityDBWorker.getSeverityMap({})
      severityMap = {
        ...severityMap,
        ...instanceSeverityMap,
        ...applicationSeverityMap,
      }
      let objectCache = await objectDBWorker.getObjectsAsMap({}, ['id', 'name'])
      let getAllObjectFromApi = await getMonitorsApi(
        {
          params: {
            'admin.role': 'yes',
          },
        },
        false
      )
      let allObjects = getAllObjectFromApi.result.map((object) =>
        transformMonitorForLocalDb(object, { transformTag: true })
      )

      let objectMap = {}
      for (let i = 0; i < allObjects.length; i++) {
        objectMap[allObjects[i].id] = {
          ...allObjects[i],
          severity: severityMap[allObjects[i].id],
          severityNumber: SEVERITY_MAP[severityMap[allObjects[i].id]],
          hasPermission: !!objectCache[allObjects[i].id],
        }
      }
      allObjects = objectMap

      if (['OTHER'].includes(payload['object.category'])) {
        result = await topologyWorker.makeOtherTopologyNodes(
          payload,
          {
            view: 'full',
            theme: this.theme,
            filledNode: true,
            height: this.$refs.mainContainer.offsetHeight,
            width: this.$refs.mainContainer.offsetWidth,
            preference: this.topologyPreference[this.target.id]
              ? this.topologyPreference[this.target.id][this.view] ||
                (this.view === 'full'
                  ? this.topologyPreference[this.target.id]
                  : {})
              : {},
          },
          allObjects,
          severityMap
        )
      } else {
        result = await topologyWorker.makeTopologyNodes(
          payload,
          {
            view: this.view,
            theme: this.theme,
            link: true,
            filledNode: true,
            height: this.$refs.mainContainer.offsetHeight,
            width: this.$refs.mainContainer.offsetWidth,
            preference: this.topologyPreference[this.target.id]
              ? this.topologyPreference[this.target.id][this.view] ||
                (this.view === 'full'
                  ? this.topologyPreference[this.target.id]
                  : {})
              : {},
          },
          allObjects,
          severityMap
        )
      }

      objectMap = null
      severityMap = null

      this.nodes = Object.freeze(result.nodes)

      this.edges = Object.freeze(result.edges)

      this.__cache = {
        ...this.__cache,
        [this.view]: {
          nodes: Object.freeze(result.nodes),
          edges: Object.freeze(result.edges),
        },
      }
      this.$nextTick(() => {
        this.loading = false
      })
    },
    startTopologyStreaming() {
      const target = this.target
      const tab = this.tab
      Bus.$emit('server:event', {
        'event.type': this.$currentModule.getConfig().TOPOLOGY_RENDER_EVENT,
        'event.context': {
          'entity.id': target.resourceId || target.id,
          'object.category': UpperCase(tab),
          'dependency.format': 'top.bottom.hierarchy',
          ...(this.target.viewId
            ? {
                'explorer.id': this.target.viewId,
                'entity.id': target.savedViewMonitorId,
              }
            : {}),
          [this.$constants.UI_EVENT_UUID]: this.guid,
        },
      })
    },
    handleCloseSidebar() {
      this.activeMonitor = null
      if (this.$refs.graphRef) {
        this.$refs.graphRef.clearSelection()
      }
    },
  },
}
</script>
