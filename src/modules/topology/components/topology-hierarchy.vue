<template>
  <div class="flex flex-1 flex-col min-h-0 mt-2">
    <FlotoContentLoader :loading="loading">
      <div class="flex flex-1 min-h-0 flex-col">
        <div class="px-2">
          <MInput
            v-model="searchTerm"
            class="search-box"
            placeholder="Search"
            name="search"
            style="max-width: unset"
          >
            <template v-slot:prefix>
              <MIcon name="search" />
            </template>
          </MInput>
        </div>
        <div
          class="flex-1 flex-col min-h-0 flex min-w-0 monitor-hierarchy-view hierarchy-explorer pt-2 overflow-auto"
        >
          <InfiniteTree
            v-if="defaultHierarchy && defaultHierarchy.length"
            v-model="innerValue"
            :row-height="27"
            :search-term="searchTerm"
            :filter-fn="filterNode"
            :data="defaultHierarchy"
            :open-ids="defaultExpandedKeys"
            @change="handleTargetChange"
            @mouseenter-infinite-tree-node="handleMouseEnter"
            @mouseleave-infinite-tree-node="handleMouseLeave"
          >
            <template v-slot="{ item }">
              <template v-if="item.resourceType === 'savedViewMonitor'">
                <div class="flex flex-1 cursor-pointer items-center">
                  <InlineNameEdit
                    :item="item"
                    :update-fn="updateView"
                    :delete-fn="deleteView"
                    :can-show-actions-btn="(hoveredItem || {}).key === item.key"
                    :delete-permission="$constants.TOPOLOGY_DELETE_PERMISSION"
                  >
                    <template v-slot:beforeName>
                      <Severity
                        disable-tooltip
                        :severity="item.severity"
                        class="mr-2"
                      />
                    </template>
                  </InlineNameEdit>
                </div>
              </template>

              <template v-else>
                <div
                  v-if="
                    item.resourceType === 'monitor' ||
                    item.resourceType === 'application' ||
                    item.resourceType === 'vm'
                  "
                  :title="`${item.name}${item.ip ? ` - (${item.ip})` : ''}`"
                  class="flex cursor-pointer flex-1 items-center"
                  style="white-space: nowrap"
                >
                  <Severity
                    disable-tooltip
                    :severity="item.severity"
                    class="mr-2"
                  />
                  <MonitorType
                    width="15px"
                    :type="
                      item.resourceType === 'application'
                        ? item.name
                        : item.resourceType === 'vm'
                        ? $constants.VIRTUAL_MACHINE
                        : item.type
                    "
                    disable-tooltip
                    class="mr-1"
                  />
                  {{ item.name }}
                  {{ item.ip ? ` - (${item.ip})` : '' }}
                </div>
                <div
                  v-else
                  class="flex flex-1 cursor-pointer items-center"
                  style="white-space: nowrap"
                >
                  <Severity
                    disable-tooltip
                    :severity="item.severity"
                    class="mr-2"
                  />
                  {{ item.name }}
                </div>
              </template>
            </template>
          </InfiniteTree>
          <FlotoNoData
            v-else
            hide-svg
            icon="exclamation-triangle"
            header-tag="h5"
            variant="neutral"
          />
        </div>
      </div>
    </FlotoContentLoader>
  </div>
</template>

<script>
import UpperCase from 'lodash/upperCase'
import CloneDeep from 'lodash/cloneDeep'
import Uniq from 'lodash/uniq'
import { generateId } from '@utils/id'
import { objectDBWorker, severityDBWorker } from '@/src/workers'
import Bus from '@utils/emitter'
import Severity from '@components/severity.vue'
import InfiniteTree from '@components/hierarchy/infinite-tree.vue'
import MonitorType from '@components/monitor-type.vue'
import { SEVERITY_MAP } from '@/src/data/monitor'
import { fetchGroupsApi } from '@modules/settings/group-settings/group-api'
import { getMonitorsApi } from '@modules/settings/monitoring/monitors-api'
import { transformMonitorForLocalDb } from '@data/monitor'
import { transformGroupRecursive } from '@modules/settings/group-settings/helpers/group.js'
import {
  getExplorerSavedViewApi,
  EXPLORER_TYPE,
  deleteExplorerApi,
  updateExplorerApi,
} from '@api/explorer-api'
import { authComputed } from '@state/modules/auth'
import InlineNameEdit from '@components/common/Inline-name-edit.vue'

export default {
  name: 'TopologyHierarchy',
  components: {
    Severity,
    InfiniteTree,
    MonitorType,
    InlineNameEdit,
  },
  inject: {
    SocketContext: { defualt: {} },
  },
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default: undefined,
    },
    category: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      objectIds: undefined,
      searchTerm: undefined,
      defaultHierarchy: undefined,
      defaultExpandedKeys: [],
      loading: true,
      guid: generateId(),
      groupOptions: [],
      objects: [],
      hoveredItem: null,
    }
  },
  computed: {
    ...authComputed,

    innerValue: {
      get() {
        return this.value
      },
      set(v) {
        if (!v) {
          return
        }
        if (v.disabled) {
          return
        }
        let value = CloneDeep(v)
        value.id = value.resourceId || value.id
        this.$emit('change', value)
      },
    },
  },
  watch: {
    'value.id': function (newValue, oldValue) {
      if (newValue !== oldValue) {
        this.findValueObject()
      }
    },
  },
  async created() {
    await this.getGroupContext()
  },
  mounted() {
    Bus.$on(
      this.$currentModule.getConfig().TOPOLOGY_PARENT_EVENT,
      this.handleHierarchyReceived
    )

    this.$once('hook:beforeDestroy', () => {
      Bus.$off(
        this.$currentModule.getConfig().TOPOLOGY_PARENT_EVENT,
        this.handleHierarchyReceived
      )
      Bus.$off('socket:connected', this.askForParents)
    })
    if (this.SocketContext.connected) {
      this.askForParents()
    }
    Bus.$on('socket:connected', this.askForParents)
  },
  methods: {
    handleMouseEnter(item) {
      this.hoveredItem = item
    },
    handleMouseLeave(item) {
      this.hoveredItem = null
    },
    deleteView(item) {
      return deleteExplorerApi(item.viewId, EXPLORER_TYPE.TOPOLOGY).then(() => {
        this.buildHierarchy(true)
      })
    },
    updateView(item, name) {
      return updateExplorerApi(
        { ...item, id: item.viewId },
        EXPLORER_TYPE.TOPOLOGY,
        { name }
      ).then(() => {
        this.buildHierarchy(true)
      })
    },
    getGroupContext() {
      return fetchGroupsApi(
        {
          params: {
            'admin.role': 'yes',
          },
        },
        false
      ).then((data) => {
        this.groupOptions = transformGroupRecursive(data)
        this.buildHierarchy()
      })
    },
    handleTargetChange(node, tree) {
      if (!node.disabled) {
        tree.selectNode(node)
        tree.openNode(node)
      } else {
        tree.toggleNode(node)
      }
    },
    filterNode(node, term) {
      return (
        (node.ip || '').toLowerCase().indexOf(term.toLowerCase()) >= 0 ||
        (node.name || '').toLowerCase().indexOf(term.toLowerCase()) >= 0 ||
        (node.severity || '').toLowerCase().indexOf(term.toLowerCase()) >= 0
      )
    },
    askForParents() {
      Bus.$emit('server:event', {
        'event.type': this.$currentModule.getConfig().TOPOLOGY_PARENT_EVENT,
        'event.context': {
          'object.category': UpperCase(this.category),
          [this.$constants.UI_EVENT_UUID]: this.guid,
        },
      })
    },
    handleHierarchyReceived(payload) {
      this.$emit('hierarchy-event-received', payload)
      if (payload[this.$constants.UI_EVENT_UUID] === this.guid) {
        const ids = payload['dependency.parent'] || []
        if (this.category.toLowerCase() === 'network') {
          this.objectIds = Object.freeze(ids)
        } else {
          this.objectIds = Object.freeze(ids || [])
        }
        this.buildHierarchy()
      }
    },
    async findValueObject() {
      if (
        !this.value ||
        !this.value.id ||
        !(this.defaultHierarchy || []).length
      ) {
        return
      }
      if (
        this.defaultExpandedKeys.includes(this.value.id) ||
        this.defaultExpandedKeys.includes(this.value.parentId)
      ) {
        return
      }
      const defaultHierarchy = this.defaultHierarchy
      const path = await objectDBWorker.getPathById(
        defaultHierarchy,
        this.value.id
      )
      this.defaultExpandedKeys = Uniq([...this.defaultExpandedKeys, ...path])
    },
    async buildHierarchy(shouldPersistExpandedKeys = false) {
      if (this.objectIds && this.groupOptions.length) {
        let severityMap = await severityDBWorker.getSeverityMap({})
        let getAllObjectFromApi = await getMonitorsApi(
          {
            params: {
              'admin.role': 'yes',
            },
          },
          false
        )
        let objects = getAllObjectFromApi.result.map(transformMonitorForLocalDb)
        const ids = this.objectIds

        let savedViewData
        if (ids) {
          if (this.category.toLowerCase() === 'network') {
            savedViewData = await getExplorerSavedViewApi(
              null,
              EXPLORER_TYPE.TOPOLOGY
            )
            let objMap = {}
            objects.forEach((object) => {
              objMap[object.id] = object
            })
            let selectedObjects = []
            Object.keys(ids).forEach((id) => {
              let foundObj = objMap[id]
              if (foundObj) {
                foundObj.topologyChildren = ids[id].map((child) => ({
                  ...(objMap[child] || {}),
                  severity: severityMap[child],
                  severityNumber: SEVERITY_MAP[severityMap[child]],
                }))
                selectedObjects.push(foundObj)
              }
            })
            objects = selectedObjects
          } else {
            objects = objects.filter((i) => ids.includes(i.id))
          }
        }
        objects = objects.map((m) => ({
          ...m,
          severity: severityMap[m.id],
          severityNumber: SEVERITY_MAP[severityMap[m.id]],
        }))
        const hierarchy = await objectDBWorker.buildObjectHieararchyByGroup(
          this.groupOptions,
          objects,
          {
            removeInstances: true,
            severities: severityMap,
            disableGroups: true,
            savedViewData,
            user: this.user,
          }
        )
        this.defaultHierarchy = Object.freeze(hierarchy)
        this.defaultExpandedKeys = shouldPersistExpandedKeys
          ? Uniq(
              Object.freeze(hierarchy.map(({ id }) => id)).concat(
                this.defaultExpandedKeys
              )
            )
          : Object.freeze(hierarchy.map(({ id }) => id))
        this.findValueObject()
        severityMap = null
        savedViewData = null
        if (this.defaultHierarchy) {
          this.$emit('hierarchy-received', this.defaultHierarchy)
          this.loading = false
        }
      }
    },
  },
}
</script>
