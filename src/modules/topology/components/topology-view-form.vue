<template>
  <ObjectTagProvider>
    <UserProvider transform-data-for-picker>
      <div>
        <MPermissionChecker :permission="$constants.TOPOLOGY_CREATE_PERMISSION">
          <MButton
            class="mx-2 btn btn-primary"
            :shadow="false"
            :loading="loading"
            :disabled="disableView"
            @click="handleMountDrilldown"
          >
            Create Topology View
          </MButton>
        </MPermissionChecker>
        <FlotoDrawerForm
          v-if="mountDrilldown"
          :open="isDrawerOpen"
          width="40%"
          @cancel="handleCancel"
          @submit="handleFormSubmit"
          @reset="resetForm"
        >
          <template v-slot:header> Create Topology View</template>
          <MRow>
            <MCol :size="6">
              <FlotoFormItem
                v-model="formData.name"
                label="Topology View Name"
                rules="required"
              >
              </FlotoFormItem>
            </MCol>
          </MRow>
          <MRow>
            <MCol :size="6">
              <FlotoFormItem
                v-model="formData.viewGroup"
                label="Topology View Group"
                rules="required|max_char_occurrences:1__@@__:"
                :info-tooltip="$message('topology_view_group')"
                placeholder="<standalone> or <key:value>"
              >
              </FlotoFormItem>
            </MCol>
          </MRow>
          <MRow>
            <MCol :size="12">
              <FlotoFormItem label=" ">
                <MRadioGroup
                  v-model="formData.includeOrExclude"
                  as-button
                  :options="topologyOptions"
                />
              </FlotoFormItem>
            </MCol>
          </MRow>
          <MRow>
            <MCol :size="6">
              <FlotoFormItem
                label="Monitor Filter"
                class="mr-4"
                :required="true"
              >
                <FlotoDropdownPicker
                  :value="formData.includeExcludeTargetType"
                  :options="currentValidTargetTypes"
                  allow-clear
                  @change="handleTargetTypeChange"
                />
              </FlotoFormItem>
            </MCol>
            <MCol :size="6">
              <FlotoFormItem
                v-if="formData.includeExcludeTargetType"
                :label="
                  formData.includeExcludeTargetType === 'exclude-ip.address' ||
                  formData.includeExcludeTargetType === 'include-ip.address'
                    ? 'Enter one or more IPs'
                    : formData.includeExcludeTargetType ===
                        'exclude-ip.address.range' ||
                      formData.includeExcludeTargetType ===
                        'include-ip.address.range'
                    ? 'Enter one or more IP Ranges'
                    : (formData.includeExcludeTargetType || '').indexOf(
                        'tag'
                      ) >= 0
                    ? 'Select Tags'
                    : 'Select Groups'
                "
                rules="required"
              >
                <!-- if loose tags is required use loose tag input-->
                <ObjectTagPicker
                  v-if="
                    (formData.includeExcludeTargetType || '').indexOf('tag') >=
                    0
                  "
                  v-model="formData.includeExcludeTargets"
                  :full-width="true"
                  as-dropdown
                  placeholder="Select Tags"
                  title="Tag"
                  variant="default"
                  rounded
                  class="w-full"
                />
                <GroupPicker
                  v-else-if="
                    (formData.includeExcludeTargetType || '').indexOf(
                      'group'
                    ) >= 0
                  "
                  id="group-parent"
                  v-model="formData.includeExcludeTargets"
                  multiple
                  placeholder="Select Groups"
                  :included-groups-by-name="[$constants.NETWORK]"
                />
                <FlotoTagsPicker
                  v-else-if="
                    formData.includeExcludeTargetType ===
                      'exclude-ip.address' ||
                    formData.includeExcludeTargetType ===
                      'exclude-ip.address.range' ||
                    formData.includeExcludeTargetType ===
                      'include-ip.address' ||
                    formData.includeExcludeTargetType ===
                      'include-ip.address.range'
                  "
                  id="exclude-ip"
                  v-model="formData.includeExcludeTargets"
                  class="mt-3"
                  variant="default"
                  :title="
                    formData.includeExcludeTargetType ===
                      'exclude-ip.address' ||
                    formData.includeExcludeTargetType === 'include-ip.address'
                      ? 'e.g. ***********'
                      : 'e.g. **********-10'
                  "
                  rounded
                  disable-justify-around
                  :full-width="true"
                  always-text-mode
                  :type="
                    formData.includeExcludeTargetType ===
                      'exclude-ip.address' ||
                    formData.includeExcludeTargetType === 'include-ip.address'
                      ? 'ip'
                      : 'ip_range'
                  "
                />
              </FlotoFormItem>
            </MCol>
          </MRow>
          <MRow>
            <MCol :size="12">
              <FlotoFormItem label="Security" rules="required">
                <MRadioGroup
                  v-model="formData.security"
                  as-button
                  :options="securityOptions"
                />
              </FlotoFormItem>
            </MCol>
          </MRow>
          <MCol v-if="formData.security === 'private'" :size="6">
            <FlotoFormItem id="user-id" label="User" rules="required">
              <UserPicker
                id="user-dropdown"
                v-model="formData.users"
                class="w-full"
                searchable
                type="user"
                multiple
                allow-clear
              />
            </FlotoFormItem>
          </MCol>

          <template v-slot:actions="{ submit, reset }">
            <span class="mandatory">
              <span class="text-secondary-red">*</span> fields are
              mandatory</span
            >
            <MButton
              id="reset-btn-id"
              variant="default"
              class="mr-2"
              @click="reset"
              >Reset</MButton
            >
            <MButton id="submit-btn" :loading="loading" @click="submit"
              >Create Topology View</MButton
            >
          </template>
        </FlotoDrawerForm>
      </div>
    </UserProvider>
  </ObjectTagProvider>
</template>

<script>
import UserPicker from '@components/data-picker/user-picker.vue'
import UserProvider from '@/src/components/data-provider/user-provider.vue'
import ObjectTagPicker from '@components/data-picker/object-tag-picker.vue'
import ObjectTagProvider from '@components/data-provider/object-tag-provider.vue'
import { createExplorerApi, EXPLORER_TYPE } from '@api/explorer-api'

export default {
  name: 'TopologyViewForm',
  components: {
    UserPicker,
    UserProvider,
    ObjectTagPicker,
    ObjectTagProvider,
  },
  props: {
    tab: {
      type: String,
      required: true,
    },
    disableView: {
      type: Boolean,
      default: false,
    },
    selectedTarget: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      formData: {
        // includeExcludeTargetType: 'group',
        selectGroup: 'server',
        security: 'public',
        includeOrExclude: 'include',
      },
      loading: false,
      mountDrilldown: false,
      isDrawerOpen: false,
      selectGroupOptions: [
        { key: 'server', text: 'Server' },
        { key: 'switch', text: 'Switch' },
      ],
      securityOptions: [
        { value: 'public', label: 'Public' },
        { value: 'private', label: 'Private' },
      ],
      topologyOptions: [
        { value: 'include', text: 'Include' },
        { value: 'exclude', text: 'Exclude' },
      ],
      allTargetTypeOptions: [
        { key: 'include-tag', text: 'Tag' },
        { key: 'include-group', text: 'Group' },
        { key: 'include-ip.address', text: 'IP' },
        { key: 'include-ip.address.range', text: 'IP Range' },
        { key: 'exclude-tag', text: 'Tag' },
        { key: 'exclude-group', text: 'Group' },
        { key: 'exclude-ip.address', text: 'IP' },
        { key: 'exclude-ip.address.range', text: 'IP Range' },
      ],
    }
  },
  computed: {
    currentValidTargetTypes() {
      return this.allTargetTypeOptions.filter(
        (i) => i.key.indexOf(this.formData.includeOrExclude) >= 0
      )
    },
  },
  methods: {
    handleTargetTypeChange(value) {
      this.formData = {
        ...this.formData,
        includeExcludeTargetType: value,
        includeExcludeTargets: [],
      }
    },
    handleMountDrilldown() {
      this.mountDrilldown = true
      this.$nextTick(() => {
        this.isDrawerOpen = true
      })
    },
    handleCancel() {
      this.isDrawerOpen = false
      this.formData = {
        security: 'public',
        includeOrExclude: 'include',
      }
      setTimeout(() => {
        this.mountDrilldown = false
      }, 400)
    },
    resetForm() {
      this.formData = {
        security: 'public',
        includeOrExclude: 'include',
      }
    },
    handleFormSubmit() {
      this.loading = true

      if (!this.selectedTarget) {
        this.$errorNotification({
          message: 'Please select a target',
          description: `Please select a target to create a topology view`,
        })

        return
      }

      return createExplorerApi(
        { ...this.formData, selectedTarget: this.selectedTarget },
        EXPLORER_TYPE.TOPOLOGY
      )
        .then((res) => {
          this.$emit('submit', res)
          this.loading = false
          this.handleCancel()
        })
        .catch(() => {
          this.loading = false
          this.handleCancel()
        })
    },
  },
}
</script>
