<template>
  <GroupProvider v-if="shouldShowModule">
    <FlotoFixedView>
      <div class="flex flex-col h-full flex-1 content-inner-panel">
        <FlotoPageHeader title="Topology" use-divider>
          <template v-slot:before-title>
            <MenuToggleButton
              class="ml-2"
              :visible="menuVisible"
              @click="toggleSidebar"
            />
            <div class="mx-2 menu-divider" />
            <MIcon name="topology" class="text-primary-alt mr-3" />
          </template>
          <div class="flex justify-end flex-1 items-center">
            <template v-if="selectedTarget">
              <MInput
                v-model="searchTerm"
                class="search-box"
                placeholder="Search"
                name="search"
              >
                <template v-slot:prefix>
                  <MIcon name="search" />
                </template>
                <template v-if="searchTerm" v-slot:suffix>
                  <MIcon
                    name="times-circle"
                    class="text-neutral-light cursor-pointer"
                    @click="searchTerm = undefined"
                  />
                </template>
              </MInput>
            </template>
            <div v-if="tab === 'network'" class="inline-flex items-center mx-2">
              <span class="mr-2">Layer 3</span>
              <MSwitch
                v-model="showLayer3"
                checked-children="ON"
                un-checked-children="OFF"
              />
            </div>
            <TopologyMapInfoTooltip v-if="tab === 'network'" />
            <div v-if="tab !== 'other'" class="inline-flex mx-2">
              <FlotoDropdownPicker
                :value="view"
                :options="viewOptions"
                :searchable="false"
                :as-input="false"
                @change="handleLayoutChange"
              >
                <template v-slot:trigger="{ toggle }">
                  <div
                    class="cursor-pointer text-neutral-light"
                    @click="toggle"
                  >
                    <MIcon :name="currentViewIcon" size="sm" class="mr-2" />
                    <MIcon name="chevron-down" />
                  </div>
                </template>
                <template v-slot:menu-item="{ item, selectItem }">
                  <div class="cursor-pointer" @click="selectItem(item)">
                    <MIcon :name="item.icon" size="lg" class="mr-2" />
                    {{ item.text }}
                  </div>
                </template>
              </FlotoDropdownPicker>
            </div>
            <span
              class="mx-2 text-neutral-light cursor-pointer"
              @click="fullscreen = true"
            >
              <MIcon name="fullscreen" size="sm" />
            </span>
            <TopologyViewForm
              v-if="tab === 'network'"
              :disable-view="
                !selectedTarget ||
                dependencyParentIds.length === 0 ||
                !dependencyParentIds.includes((selectedTarget || {}).id) ||
                (selectedTarget || {}).resourceType === 'savedViewMonitor'
              "
              :tab="tab"
              :selected-target="selectedTarget"
              @submit="handleViewSubmit"
            />
          </div>
        </FlotoPageHeader>
        <MonitorHierarchyLayout
          ref="monitorHierarchyLayoutRef"
          v-model="selectedTarget"
          :category="currentCategories"
          :disable-instance="true"
          :default-menu-visible="true"
          @change="handleTargetChanged"
          @hierarchy-received="handleHierarchyReceived"
          @visible-change="menuVisible = $event"
        >
          <template v-slot:before-hierarchy>
            <MTab
              :value="tab"
              class="topology-hierarchy-tab mx-2"
              @change="handleTabChange"
            >
              <MTabPane v-for="category in categoryTabs" :key="category.key">
                <template v-slot:tab>
                  <div class="px-4" :title="category.tab">
                    <MIcon :name="category.icon" size="lg" class="m-0" />
                  </div>
                </template>
              </MTabPane>
            </MTab>
          </template>
          <template v-slot:left-side>
            <TopologyHierarchy
              ref="topologyHierarchyRef"
              :key="tab"
              v-model="selectedTarget"
              :category="tab"
              :disable-instance="true"
              @change="handleTargetChanged"
              @hierarchy-received="handleHierarchyReceived"
              @hierarchy-event-received="handleHierarchyEventReceived"
            />
          </template>
          <div class="flex flex-1 flex-col min-h-0">
            <FlotoContentLoader :loading="!isPreferenceLoaded">
              <TopologyGraph
                v-if="selectedTarget"
                :tab="tab"
                :target="selectedTarget"
                :view="view"
                :search-term="trimedSearchTerm"
                :show-layer3="showLayer3"
                :fullscreen="fullscreen"
                @normal-screen="fullscreen = false"
              />
              <FlotoNoData
                v-else
                header-tag="h4"
                message="Please select entry point to view Topology"
              />
            </FlotoContentLoader>
          </div>
        </MonitorHierarchyLayout>
      </div>
    </FlotoFixedView>
  </GroupProvider>

  <FlotoModuleNoData v-else module="topology" />
</template>

<script>
import Trim from 'lodash/trim'

import Capitalize from 'lodash/capitalize'
import LowerCase from 'lodash/lowerCase'
import GroupProvider from '@components/data-provider/group-provider.vue'
import MonitorHierarchyLayout from '@views/layouts/monitor-hierarchy-layout.vue'
import MenuToggleButton from '@components/menu-toggle-button.vue'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import { authComputed } from '@/src/state/modules/auth'
import { UserPreferenceMethods } from '@/src/state/modules/user-preference'

import TopologyMapInfoTooltip from '../components/topology-map-info-tooltip.vue'
import TopologyGraph from '../components/topology-graph.vue'
import { TAB_CATEGORY_MAP } from '../helpers/topology'
import TopologyHierarchy from '../components/topology-hierarchy.vue'
import { objectDBWorker } from '@/src/workers'
import TopologyViewForm from '../components/topology-view-form.vue'

export default {
  name: 'TopologyMap',
  components: {
    MonitorHierarchyLayout,
    TopologyMapInfoTooltip,
    TopologyGraph,
    TopologyHierarchy,
    GroupProvider,
    MenuToggleButton,
    TopologyViewForm,
  },
  data() {
    this.viewOptions = [
      { key: 'full', text: 'Full View', icon: 'bulls-eye' },
      { key: 'tree', text: 'Tree View', icon: 'tree-view' },
    ]
    return {
      showLayer3: false,
      view: 'full',
      selectedTarget: null,
      fullscreen: false,
      menuVisible: true,
      tab: null,
      monitorHierarchy: [],
      searchTerm: '',
      shouldShowModule: true,
      showTopologyViewForm: false,
      dependencyParentIds: [],
    }
  },
  computed: {
    ...UserPreferenceComputed,
    ...authComputed,
    categoryTabs() {
      return Object.keys(TAB_CATEGORY_MAP).map((category) => {
        return {
          tab: ['hci', 'sdn'].includes(category)
            ? category.toUpperCase()
            : Capitalize(category),
          key: category,
          icon: `${LowerCase(category)}-topology`,
        }
      })
    },
    currentCategories() {
      return TAB_CATEGORY_MAP[this.tab]
    },
    hasLeftMenu() {
      return true
    },
    currentViewIcon() {
      const view = this.view
      return this.viewOptions.find((v) => view === v.key).icon
    },
    trimedSearchTerm() {
      return Trim(this.searchTerm)
    },
  },
  watch: {
    $route(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (!Object.keys(TAB_CATEGORY_MAP).includes(this.$route.params.tab)) {
          this.$router.replace({ name: '404' })
          return
        }
        this.tab = newValue.params.tab
        if (newValue.params.tab !== oldValue.params.tab) {
          this.selectedTarget = null
        }
        // this.view = newValue.params.tab !== 'network' ? 'tree' : 'full'
        this.view = 'full'
        if (this.tab !== 'network') {
          this.showLayer3 = false
        }
      }
    },
  },
  async created() {
    this.tab = this.$route.params.tab || 'network'
    await this.getObjectsAndUpdatePrefrence()

    if (!Object.keys(TAB_CATEGORY_MAP).includes(this.tab)) {
      this.$router.replace({ name: '404' })
      return
    }
    // this.view = this.tab !== 'network' ? 'tree' : 'full'
    this.view = 'full'
    if (this.$route.params.monitorId || this.$route.params.groupId) {
      this.selectedTarget = {
        id: this.$route.params.monitorId
          ? +this.$route.params.monitorId
          : isNaN(this.$route.params.groupId)
          ? this.$route.params.groupId
          : +this.$route.params.groupId,
        resourceType: this.$route.params.monitorId ? 'monitor' : 'group',
      }
    }
  },
  methods: {
    ...UserPreferenceMethods,
    handleViewSubmit() {
      if (this.$refs.topologyHierarchyRef) {
        this.$refs.topologyHierarchyRef.buildHierarchy(true)
      }
    },
    toggleSidebar() {
      this.$refs.monitorHierarchyLayoutRef.toggleMenu()
    },
    handleTabChange(tab) {
      this.$router.push(
        this.$currentModule.getRoute('', { params: { tab: tab } })
      )
    },
    handleHierarchyEventReceived($event) {
      this.dependencyParentIds = Object.keys(
        $event['dependency.parent'] || {}
      ).map((id) => +id)
    },
    handleHierarchyReceived($event) {
      this.monitorHierarchy = Object.freeze($event)
      this.$refs.monitorHierarchyLayoutRef.setLoading(false)
    },
    handleLayoutChange(view) {
      const showLayer3 = this.showLayer3
      this.showLayer3 = false
      this.$nextTick(() => {
        this.view = view
        if (showLayer3) {
          setTimeout(() => {
            this.showLayer3 = showLayer3
          }, 100)
        }
      })
    },
    handleTargetChanged(value) {
      if (value.disabled === false) {
        return
      }
      let route
      if (value && value.resourceType === 'monitor') {
        if (+this.$route.params.monitorId !== value.id) {
          route = this.$currentModule.getRoute('monitor-topology', {
            params: {
              tab: this.tab,
              monitorId: value.id,
            },
          })
        }
      }
      // else if (value && value.resourceType === 'group') {
      //   if (+this.$route.params.groupId !== value.id) {
      //     route = this.$currentModule.getRoute('group-topology', {
      //       params: {
      //         tab: this.tab,
      //         groupId: value.id,
      //       },
      //     })
      //   }
      // }
      if (route) {
        const { href } = this.$router.resolve(route)
        if (href !== this.$route.path) {
          this.$router.push(route)
        }
      }
    },
    async getObjectsAndUpdatePrefrence() {
      this.shouldShowModule = await this.getStaticLendingPagePreferenceByModule(
        {
          module: 'object',
        }
      )

      if (!this.shouldShowModule) {
        const objects = await objectDBWorker.getObjects({})

        const hasObjects = !!objects.length

        if (this.shouldShowModule !== hasObjects) {
          await this.updateStaticLendingPagePreferences({
            lendingPagePreferences: { object: hasObjects },
          })
        }

        this.shouldShowModule =
          await this.getStaticLendingPagePreferenceByModule({
            module: 'object',
          })
      }
    },
  },
}
</script>
