import api from '@api'
import Constants from '@constants'
import buildWidgetResult from '@components/widgets/result-builder'

import { transformWidget } from '@/src/components/widgets/translator'

import {
  getWidgetResponseApi,
  buildWidgetContext,
  makeCounter,
} from '@utils/socket-event-as-api'

import {
  WidgetTypeConstants,
  AVAILABLE_GROUP_TYPES,
} from '@components/widgets/constants'
import { SLO_STATUS_FLAP_MAP_COLORS, SLO_STATUS_FLAP_MAP } from './helpers/slo'
import Omit from 'lodash/omit'

function transformSloForList(context) {
  return {
    id: context[Constants.ID_PROPERTY],
    pathName: context['slo.name'],
    source: context['slo.source'],
    destination: context['slo.destination'],
    port: context['slo.destination.port'],
    intervalTime: context['slo.polling.time'],
    lastPolledTime: context['last.poll.time'],
    tags: context['slo.tags'] || [],
  }
}

export function fetchSlosApi() {
  return api.get('/settings/slos').then((data) => {
    return (data.result || []).map(transformSloForList)
  })
}

export function fetchSloApi(id) {
  return api.get(`/settings/slos/${id}`).then(({ result }) => {
    return transformSloForList(result)
  })
}

export function fetchSloMetadataApi(id) {
  return api.get(`/settings/slos/${id}/metadata`).then(({ result }) => {
    return result
  })
}

export const SLO_AVAILABLE_COLUMNS = [
  {
    key: 'name',
    name: 'Name',
    sortable: true,
    searchable: true,
  },
  {
    key: 'type',
    name: 'Type',
    sortable: true,
    searchable: true,
  },
  {
    key: 'frequency',
    name: 'Frequency',
    sortable: true,
    searchable: true,
  },
  {
    key: 'target',
    name: 'Target',
    sortable: true,
    searchable: true,
  },
  {
    key: 'achieved',
    name: 'Achieved',
    sortable: true,
    searchable: true,
  },
  {
    key: 'violations',
    name: 'Violations',
    sortable: true,
    searchable: true,
  },
  {
    key: 'errorBudgetLeft',
    name: 'Error Budget Left',
    sortable: true,
    searchable: true,
  },
  {
    key: 'mttr',
    name: 'MTTR',
    sortable: true,
    searchable: true,
  },
  {
    key: 'mtbf',
    name: 'MTBF',
    sortable: true,
    searchable: true,
  },
  {
    key: 'status',
    name: 'Status',
    sortable: true,
    searchable: true,
  },
  {
    key: 'sloAlertName',
    name: 'SLO Alert Name',
    sortable: true,
    searchable: true,
  },
  {
    key: 'tags',
    name: 'Tags',
    sortable: true,
    searchable: true,
  },
  {
    key: 'businessServices',
    name: 'Business Services',
    sortable: true,
    searchable: true,
  },
]

export function fetchActiveSloApi(asMap = false) {
  return api
    .get('/settings/slo-profiles', {
      params: {
        active: 'yes',
      },
    })
    .then(({ result }) => {
      const map = new Map()
      result.forEach((item) => {
        map.set(item.id, transformActiveSloForList(item))
      })
      return asMap ? map : Array.from(map.values())
    })
}

function transformActiveSloForList(context) {
  const sloContext = context['slo.profile.context']

  return {
    id: context.id,
    _type: context._type,
    cycleId: context['slo.cycle.id'],
    name: context['slo.profile.name'],
    type: context['slo.profile.type'],
    state: context['slo.profile.state'],
    metric: sloContext.metric,
    // filters: sloContext.filters,
    entities: sloContext.entities,
    target: sloContext['slo.target'],
    entityType: sloContext['entity.type'],
    warning: sloContext['slo.warning'],
    frequency: sloContext['slo.frequency'],
    startTime: context['slo.profile.start.time'],
    businessServiceName: context['slo.profile.business.service.name'],
  }
}

export function getVisualizationDataApi() {
  return fetchActiveSloApi(true).then((sloMap) => {
    //  it is possible to delete the item if not cycle id ?

    const sloIds = Array.from(sloMap.values())
      .map((item) => {
        return item.cycleId ? item.id : null
      })
      .filter(Boolean)

    const sloProfiles = Array.from(sloMap.values()).filter(
      (item) => item.cycleId
    )
    const entityKeys = sloProfiles.map((item) => `${item.id}^${item.cycleId}`)

    if (sloIds.length === 0) {
      return Promise.resolve([])
    }
    return getWidgetResponseApi(
      buildWidgetContext({
        groupType: AVAILABLE_GROUP_TYPES.SLO,
        timeline: { selectedKey: 'today' },
        counters: [
          makeCounter('slo~status', 'last', 'Monitor', sloIds, entityKeys),
          makeCounter(
            'slo~achieved.percent',
            'last',
            'Monitor',
            sloIds,
            entityKeys
          ),
          makeCounter(
            'slo~violated.percent',
            'last',
            'Monitor',
            sloIds,
            entityKeys
          ),
          makeCounter(
            'slo~error.budget.left.seconds',
            'last',
            'Monitor',
            sloIds,
            entityKeys
          ),
          makeCounter(
            'slo~mttr.seconds',
            'last',
            'Monitor',
            sloIds,
            entityKeys
          ),
          makeCounter(
            'slo~mtbf.seconds',
            'last',
            'Monitor',
            sloIds,
            entityKeys
          ),
        ],
        category: WidgetTypeConstants.GRID,
        widgetType: WidgetTypeConstants.GRID,
        resultBy: ['slo.id', 'slo'],
      }).generateWidgetDefinition(),
      {
        checkWidgetProgress: true,
        useResultBuilder: true,
        ignoreTimeout: true,
      }
    ).then((data) => {
      return data.rows.map((row) => {
        const slo = sloMap.get(+row.slo_id)

        return {
          ...row,
          ...(slo || {}),
        }
      })
    })
  })
}

export function getGlobalSLOCalculationDataApi(slo) {
  const sloIds = [slo.id]
  const entityKeys = [`${slo.id}^${slo.cycleId}`]

  return getWidgetResponseApi(
    buildWidgetContext({
      groupType: AVAILABLE_GROUP_TYPES.SLO,
      timeline: { selectedKey: 'today' },
      counters: [
        makeCounter(
          'slo~achieved.percent',
          'last',
          'Monitor',
          sloIds,
          entityKeys
        ),
        makeCounter(
          'slo~violated.percent',
          'last',
          'Monitor',
          sloIds,
          entityKeys
        ),
        makeCounter(
          'slo~error.budget.left.seconds',
          'last',
          'Monitor',
          sloIds,
          entityKeys
        ),
        makeCounter('slo~mttr.seconds', 'last', 'Monitor', sloIds, entityKeys),
        makeCounter('slo~mtbf.seconds', 'last', 'Monitor', sloIds, entityKeys),
        makeCounter(
          'slo~error.budget.left.percent',
          'last',
          'Monitor',
          sloIds,
          entityKeys
        ),
        makeCounter(
          'slo~violated.seconds',
          'last',
          'Monitor',
          sloIds,
          entityKeys
        ),
        makeCounter('slo~status', 'last', 'Monitor', sloIds, entityKeys),
        makeCounter(
          'slo.object.error.budget.left.percent',
          'last',
          'Monitor',
          sloIds,
          entityKeys
        ),
      ],
      category: WidgetTypeConstants.GRID,
      widgetType: WidgetTypeConstants.GRID,
      resultBy: ['slo.id', 'slo'],
    }).generateWidgetDefinition(),
    {
      checkWidgetProgress: true,
      useResultBuilder: true,
      ignoreTimeout: true,
    }
  ).then((data) => {
    return data.rows
  })
}

export function getGlobalSLOCalculationDataApiForHistory(cycleContext) {
  const sloIds = cycleContext.map((item) => item.sloProfileId)
  const entityKeys = cycleContext.map(
    (item) => `${item.sloProfileId}^${item.cycleId}`
  )

  return getWidgetResponseApi(
    buildWidgetContext({
      groupType: AVAILABLE_GROUP_TYPES.SLO,
      timeline: { selectedKey: '-356d' },
      counters: [
        makeCounter(
          'slo~achieved.percent',
          'last',
          'Monitor',
          sloIds,
          entityKeys
        ),
        makeCounter(
          'slo~violated.percent',
          'last',
          'Monitor',
          sloIds,
          entityKeys
        ),
        makeCounter(
          'slo~error.budget.left.seconds',
          'last',
          'Monitor',
          sloIds,
          entityKeys
        ),
        makeCounter('slo~mttr.seconds', 'last', 'Monitor', sloIds, entityKeys),
        makeCounter('slo~mtbf.seconds', 'last', 'Monitor', sloIds, entityKeys),
        makeCounter(
          'slo~error.budget.left.percent',
          'last',
          'Monitor',
          sloIds,
          entityKeys
        ),
        makeCounter(
          'slo~violated.seconds',
          'last',
          'Monitor',
          sloIds,
          entityKeys
        ),
        makeCounter('slo~status', 'last', 'Monitor', sloIds, entityKeys),
        makeCounter(
          'slo.object.error.budget.left.percent',
          'last',
          'Monitor',
          sloIds,
          entityKeys
        ),
      ],
      category: WidgetTypeConstants.GRID,
      widgetType: WidgetTypeConstants.GRID,
      resultBy: ['slo.id', 'slo'],
    }).generateWidgetDefinition(),
    {
      checkWidgetProgress: true,
      useResultBuilder: true,
      ignoreTimeout: true,
    }
  ).then((data) => {
    return data.rows.map((item) => {
      const currentCycle = cycleContext.find(
        (cycle) => cycle.cycleId === +item['slo']
      )

      return {
        ...item,
        ...(currentCycle || {}),
      }
    })
  })
}

export function getSLOTrendDataApi(slo, timeline) {
  const sloId = slo.id
  const cycleId = slo.cycleId
  const sloCycleEndTime = slo.sloCycleEndTime * 1000

  return getWidgetResponseApi(
    buildWidgetContext({
      groupType: AVAILABLE_GROUP_TYPES.SLO_FLAP,
      timeline,
      counters: [
        makeCounter(
          'slo~status.flap',
          '__NONE__',
          'Monitor',
          Array.isArray(sloId) ? sloId : [sloId]
        ),
        makeCounter(
          'slo~duration',
          '__NONE__',
          'Monitor',
          Array.isArray(sloId) ? sloId : [sloId]
        ),
      ],
      category: WidgetTypeConstants.CHART,
      widgetType: WidgetTypeConstants.AREA,
      preFilters: {
        condition: 'and',
        inclusion: 'include',
        conditions: [
          {
            operand: 'slo~instance.name',
            operator: '=',
            value: `${cycleId}`,
          },
        ],
      },
      granularity: {
        value: 5,
        unit: 'm',
      },
    }).generateWidgetDefinition(),
    {
      checkWidgetProgress: true,
      useResultBuilder: false,
      ignoreTimeout: true,
    }
  ).then((data) => {
    const xTrendData = data.map((item) => {
      return {
        x: item['timestamp'],
        x2: item['timestamp'] + item['slo.duration.value'] * 1000,

        y: 0.4, // Changed from 1 to 0.5 for consistency
        color: SLO_STATUS_FLAP_MAP_COLORS[item['slo.status.flap.value']],
        status: SLO_STATUS_FLAP_MAP[item['slo.status.flap.value']],
      }
    })

    // const totalDuration = xTrendData.reduce((acc, item) => {
    //   return acc + (item.x2 - item.x)
    // }, 0)

    // const duration = 86400000 - totalDuration

    const lastItem = xTrendData[0]

    if (lastItem && lastItem.x2 < sloCycleEndTime) {
      xTrendData.push({
        x: lastItem.x2,
        x2: sloCycleEndTime,
        y: 0.4,
        color: 'var(--code-tag-background-color)',
        status: 'question',
      })
    }

    return xTrendData
  })
}

export function getConfiguredMonitorsApi(slo, entity, instance) {
  const isInstanceSlo = slo.sloFor !== 'monitor'
  const counterPrefix = isInstanceSlo ? 'slo.instance~' : 'slo.object.'

  const sloId = slo.id
  const cycleId = slo.cycleId

  const entities = entity || slo.entities || []

  return getWidgetResponseApi(
    buildWidgetContext({
      groupType: AVAILABLE_GROUP_TYPES.SLO_INSTANCE,
      timeline: { selectedKey: 'today' },
      counters: [
        makeCounter(`${counterPrefix}status`, 'last', 'Monitor', entities),
        makeCounter(
          `${counterPrefix}achieved.percent`,
          'last',
          'Monitor',
          entities
        ),
        makeCounter(
          `${counterPrefix}violated.percent`,
          'last',
          'Monitor',
          entities
        ),
        makeCounter(
          `${counterPrefix}violated.seconds`,
          'last',
          'Monitor',
          entities
        ),
        makeCounter(
          `${counterPrefix}error.budget.left.percent`,
          'last',
          'Monitor',
          entities
        ),
      ],
      category: WidgetTypeConstants.GRID,
      widgetType: WidgetTypeConstants.GRID,
      resultBy: ['monitor', isInstanceSlo ? 'slo.instance' : 'instance'],
      ...(instance
        ? {
            preFilters: {
              condition: 'and',
              inclusion: 'include',
              conditions: [
                {
                  operand: 'slo.instance~instance.name',
                  operator: '=',
                  value: `${instance}`,
                },
              ],
            },
          }
        : {}),
    })
      .appendToGroup(AVAILABLE_GROUP_TYPES.SLO_INSTANCE, {
        resultBy: ['monitor', ...(isInstanceSlo ? ['slo.instance'] : [])],
      })
      .generateWidgetDefinition({
        'store.suffix': `${sloId}###${cycleId}`,
      }),
    {
      checkWidgetProgress: true,
      useResultBuilder: true,
      ignoreTimeout: true,
    }
  ).then((data) => {
    return data.rows
  })
}

export function getSLOTrendDataApiForInstance(slo, entity, instance, timeline) {
  const isInstanceSlo = slo.sloFor !== 'monitor'

  const counterPrefix = isInstanceSlo ? 'slo.instance~' : 'slo.object.'
  const sloId = slo.id
  const cycleId = slo.cycleId
  const sloCycleEndTime = slo.sloCycleEndTime * 1000
  const entities = entity || slo.entities || []

  return getWidgetResponseApi(
    buildWidgetContext({
      groupType: AVAILABLE_GROUP_TYPES.SLO_INSTANCE_FLAP,
      timeline,
      counters: [
        makeCounter(
          `${counterPrefix}duration`,
          '__NONE__',
          'Monitor',
          entities
        ),
        makeCounter(
          `${counterPrefix}status.flap`,
          '__NONE__',
          'Monitor',
          entities
        ),
      ],
      category: WidgetTypeConstants.CHART,
      widgetType: WidgetTypeConstants.AREA,
      resultBy: [
        ...(!instance
          ? ['monitor', isInstanceSlo ? 'slo.instance' : 'instance']
          : []),
      ],
      ...(instance
        ? {
            preFilters: {
              condition: 'and',
              inclusion: 'include',
              conditions: [
                {
                  operand: 'slo.instance~instance.name',
                  operator: '=',
                  value: `${instance}`,
                },
              ],
            },
          }
        : {}),
    })
      .appendToGroup(AVAILABLE_GROUP_TYPES.SLO_INSTANCE_FLAP, {
        resultBy: [
          ...(!instance
            ? ['monitor', isInstanceSlo ? 'slo.instance' : 'instance']
            : []),
        ],
      })
      .generateWidgetDefinition({
        'store.suffix': `${sloId}###${cycleId}`,
      }),
    {
      checkWidgetProgress: true,
      useResultBuilder: false,
      ignoreTimeout: true,
    }
  ).then((data) => {
    const counterPrefixForKey = counterPrefix.replace(/[~^]/g, '.')

    const xTrendData = data.map((item) => {
      return {
        x: item['timestamp'],
        x2:
          item['timestamp'] +
          item[`${counterPrefixForKey}duration.value`] * 1000,

        y: 0.4, // Changed from 1 to 0.5 for consistency
        color:
          SLO_STATUS_FLAP_MAP_COLORS[
            item[`${counterPrefixForKey}status.flap.value`]
          ],
        status:
          SLO_STATUS_FLAP_MAP[item[`${counterPrefixForKey}status.flap.value`]],
      }
    })

    const lastItem = xTrendData[0]

    if (lastItem.x2 < sloCycleEndTime) {
      xTrendData.push({
        x: lastItem.x2,
        x2: sloCycleEndTime,
        y: 0.4,
        color: 'var(--code-tag-background-color)',
        status: 'question',
      })
    }

    return xTrendData
  })
}

export function getConfiguredMonitorSessionHistoryApi(slo, item, timeline) {
  const instance = item['slo_instance']

  const isInstanceSlo = slo.sloFor !== 'monitor'
  const counterPrefix = isInstanceSlo ? 'slo.instance~' : 'slo.object.'

  const sloId = slo.id
  const cycleId = slo.cycleId

  const entities = [item['entity.id']]

  return getWidgetResponseApi(
    buildWidgetContext({
      category: WidgetTypeConstants.CHART,
      widgetType: WidgetTypeConstants.GRID,
      groupType: AVAILABLE_GROUP_TYPES.SLO_INSTANCE,
      timeline,
      counters: [
        makeCounter(`${counterPrefix}status`, 'last', 'Monitor', entities),
        makeCounter(
          `${counterPrefix}achieved.percent`,
          '__NONE__',
          'Monitor',
          entities
        ),
        makeCounter(
          `${counterPrefix}violated.percent`,
          '__NONE__',
          'Monitor',
          entities
        ),
        makeCounter(
          `${counterPrefix}violated.seconds`,
          '__NONE__',
          'Monitor',
          entities
        ),
        makeCounter(
          `${counterPrefix}burn.rate.seconds`,
          '__NONE__',
          'Monitor',
          entities
        ),
      ],

      ...(instance
        ? {
            preFilters: {
              condition: 'and',
              inclusion: 'include',
              conditions: [
                {
                  operand: 'slo.instance~instance.name',
                  operator: '=',
                  value: `${instance}`,
                },
              ],
            },
          }
        : {}),
      granularity: {
        value: 5,
        unit: 'm',
      },
    })
      .appendToGroup(AVAILABLE_GROUP_TYPES.SLO_INSTANCE, {})
      .generateWidgetDefinition({
        'store.suffix': `${sloId}###${cycleId}`,
      }),
    {
      fullResponse: true,
      checkWidgetProgress: true,
      ignoreTimeout: true,
    }
  ).then(async (data) => {
    const grid = await buildWidgetResult(
      {
        ...transformWidget(Omit(data, ['result'])),
        category: 'Grid',
        widgetProperties: {
          columnSettings: [],
        },
      },
      {
        ...data,
      }
    )

    return grid
  })
}

export function getSloHistoryApi(slo) {
  return getWidgetResponseApi(
    buildWidgetContext({
      groupType: AVAILABLE_GROUP_TYPES.SLO,
      timeline: { selectedKey: 'today' },
    })
  )
}
