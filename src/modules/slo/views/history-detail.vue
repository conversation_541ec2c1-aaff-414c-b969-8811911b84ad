<template>
  <FlotoContentLoader :loading="loading">
    <FlotoFixedView :gutter="0">
      <MRow :gutter="0" class="content-inner-panel w-full">
        <MCol>
          <FlotoPageHeader
            :title="sloContext.sloCycleName"
            use-divider
            :back-link="
              $currentModule.getRoute('detail', {
                params: {
                  id: sloId,
                  tab: 'slo-history',
                },
              })
            "
          />
        </MCol>
      </MRow>

      <FlotoScrollView class="overflow-x-hidden">
        <div
          class="flex flex-1 flex-col"
          style="background: var(--dashboard-background)"
        >
          <SloOverview
            :slo="sloContext"
            :slo-timeline="sloTimeline"
            for-slo-history-drill-down
          />
        </div>
      </FlotoScrollView>
    </FlotoFixedView>
  </FlotoContentLoader>
</template>

<script>
import SloOverview from '../components/slo-overview.vue'
import Moment from 'moment'

import {
  getSLOProfileApi,
  getSLOProfileCyclesApi,
} from '@modules/settings/service-level-objective/slo-profile-api'

import { DATE_FORMAT, TIME_FORMAT } from '@components/widgets/constants'

export default {
  name: 'SloDetails',
  components: {
    SloOverview,
  },
  data() {
    return {
      sloId: this.$route.params.id,
      cycleId: this.$route.params.cycleId,
      loading: true,
      sloContext: {},
      sloTimeline: {},
    }
  },
  created() {
    this.fetchSloData()
  },
  methods: {
    fetchSloData() {
      this.loading = true

      return getSLOProfileApi(this.sloId).then((slo) => {
        return getSLOProfileCyclesApi(this.cycleId)
          .then((cycleContext) => {
            this.sloContext = { ...slo, ...cycleContext }

            const startTime = Moment.unix(cycleContext.sloCycleStartTime)
            const endTime = Moment.unix(cycleContext.sloCycleEndTime)

            this.sloTimeline = {
              selectedKey: 'custom',
              startDate: startTime.format(DATE_FORMAT),
              endDate: endTime.format(DATE_FORMAT),
              startTime: startTime.format(TIME_FORMAT),
              endTime: endTime.format(TIME_FORMAT),
            }
            this.loading = false
          })
          .catch(() => {
            this.loading = true
          })
      })
    },
  },
}
</script>
