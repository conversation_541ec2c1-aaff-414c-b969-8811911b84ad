<template>
  <GroupProvider>
    <ObjectTagProvider use-key-value-pair-only>
      <AgentProvider>
        <FlotoContentLoader v-if="loading" loading />
        <div v-else class="flex flex-col min-h-0 flex-1 h-full">
          <div>
            <div
              id="monitor-detail-element"
              class="border-bot flex py-1 page-background items-center"
            >
              <div class="flex items-center justify-between flex-1">
                <MButton variant="transparent" @click="$router.back()">
                  <MIcon
                    name="chevron-left"
                    class="text-neutral-light text-xl"
                    style="font-size: 1.5rem"
                  />
                </MButton>
                <div class="inline-flex items-center flex-1">
                  <Severity
                    :severity="slo.severity || 'UNKNOWN'"
                    class="mr-2"
                  />
                  <div class="text-lg text-primary">{{ slo.pathName }}</div>
                  <div class="mx-2 text-neutral-light">|</div>
                  <div class="flex items-center">
                    <span style="color: var(--neutral-theme-color)">
                      Source:
                      <!-- {{ slo.source }} -->
                    </span>
                    <AgentPicker class="ml-2" :value="slo.source" disabled />
                  </div>
                  <div class="mx-2 text-neutral-light">|</div>
                  <div>
                    <span style="color: var(--neutral-theme-color)"
                      >Destination:</span
                    >
                    {{ slo.destination }}
                  </div>
                  <div class="mx-2 text-neutral-light">|</div>
                  <div>
                    <span style="color: var(--neutral-theme-color)">Port:</span>
                    {{ slo.port }}
                  </div>
                </div>
                <div>
                  <TimerangePicker
                    v-model="timeline"
                    class="mr-2"
                    :hide-custom-time-range="false"
                  />
                  <!-- <SaveAsWidget :save-widget="handleSaveAsWidget">
                <template v-slot:trigger>
                  <MButton class="mr-2">Save as Widget</MButton>
                </template>
              </SaveAsWidget> -->
                </div>
              </div>
            </div>
          </div>

          <SloDrillDownNoDate v-if="!currentTimestamp" :theme="theme" />

          <div v-else class="flex flex-1 min-h-0 flex-col relative">
            <div class="flex flex-col min-h-0 flex-1">
              <div class="flex flex-col flex-1 h-full bg-neutral-lightest">
                <GraphView
                  :key="currentTimestamp"
                  :slo="slo"
                  :timestamp="currentTimestamp"
                  :metadata="metadata"
                  :timeline="timeline"
                  @set-active-item="activeItem = $event"
                />
              </div>
            </div>
            <div class="slo-border-bot"></div>
            <SloHistoryChartDataProvider
              :chart-series="chartSeries"
              :offset="offset"
              :current-timestamp="currentTimestamp"
            >
              <template v-slot="{ currentSeriesChunk }">
                <div class="flex flex-col min-h-0 flex-shrink-0">
                  <MCollapse
                    :bordered="false"
                    :default-active-key="['history']"
                    :accordion="false"
                    class="no-pb-collapse collapse-witout-border"
                    @change="handleCollapseChange"
                  >
                    <template v-slot:expandIcon="{ isActive }">
                      <MIcon
                        :name="`chevron-${isActive ? 'down' : 'right'}`"
                        size="lg"
                        class="text-neutral-light"
                      />
                    </template>
                    <MCollapsePanel key="history">
                      <template v-slot:header>
                        <MCol class="list-title flex items-center mt-1">
                          Path History
                        </MCol>
                      </template>
                      <div
                        class="flex flex-col h-full"
                        @mouseout="hideChartTooltips"
                      >
                        <div class="my-1" style="height: 90px">
                          <SloHistoryChart
                            :slo="slo"
                            counter="status"
                            :timeline="timeline"
                            :data="currentSeriesChunk.statusSeries"
                            :current-timestamp="currentTimestamp + offset"
                            @point-click="handlePointClick"
                            @range-changed="handleRangeChanged"
                        /></div>
                        <div class="mt-1" style="height: 70px">
                          <SloHistoryChart
                            :slo="slo"
                            counter="slo.latency.ms"
                            :timeline="timeline"
                            :data="currentSeriesChunk.latencySeries"
                            :current-timestamp="currentTimestamp + offset"
                            @point-click="handlePointClick"
                            @range-changed="handleRangeChanged"
                          />
                        </div>
                        <div class="mt-1" style="height: 70px">
                          <SloHistoryChart
                            :slo="slo"
                            counter="slo.packet.lost.percent"
                            :timeline="timeline"
                            enable-x-axis
                            :data="currentSeriesChunk.packetLostSeries"
                            :current-timestamp="currentTimestamp + offset"
                            @point-click="handlePointClick"
                            @range-changed="handleRangeChanged"
                          />
                        </div>
                        <div class="mt-4">
                          <TimelineScrollbar />
                        </div>
                      </div>
                    </MCollapsePanel>
                  </MCollapse>
                  <div
                    v-if="!isHistoryVisible"
                    class="my-1"
                    style="height: 70px"
                  >
                    <SloHistoryChart
                      :slo="slo"
                      counter="status"
                      :timeline="timeline"
                      disable-syncing
                      :data="currentSeriesChunk.statusSeries"
                      :current-timestamp="currentTimestamp + offset"
                      @point-click="handlePointClick"
                      @range-changed="handleRangeChanged"
                    />
                  </div>
                  <div v-if="!isHistoryVisible" class="mt-4">
                    <TimelineScrollbar />
                  </div>
                </div>
              </template>
            </SloHistoryChartDataProvider>
            <GraphSidebar
              v-if="activeItem"
              :timeline="timeline"
              :data="activeItem"
              :metadata="metadata"
              :type="activeItem.boxType"
              @close="activeItem = null"
            />
          </div>
        </div>
      </AgentProvider>
    </ObjectTagProvider>
  </GroupProvider>
</template>

<script>
import Highcharts from 'highcharts'
import Moment from 'moment'
import { objectDBWorker } from '@/src/workers'
import { fetchSloRouteApi, fetchSloRouteMetadataApi } from '../slo-api'
import Severity from '@components/severity.vue'
import TimerangePicker from '@components/widgets/time-range-picker.vue'
// import SaveAsWidget from '@components/widgets/save-as-widget.vue'
import GraphView from '../components/graph-view.vue'
import SloHistoryChart from '../components/slo-history-chart.vue'
import AgentProvider from '@components/data-provider/agent-provider.vue'
import AgentPicker from '@components/data-picker/agent-picker.vue'
import WidgetContextBuilder from '@components/widgets/widget-context-builder'
import { WidgetTypeConstants } from '@components/widgets/constants'
import { getWidgetResponseApi } from '@/src/utils/socket-event-as-api'
import { UserPreferenceComputed } from '@/src/state/modules/user-preference/helpers'
import GroupProvider from '@components/data-provider/group-provider.vue'
import ObjectTagProvider from '@components/data-provider/object-tag-provider.vue'
import SortBy from 'lodash/sortBy'
import Bus from '@utils/emitter'
import GraphSidebar from '../components/graph-sidebar.vue'
import TimelineScrollbar from '../components/timeline-scrollbar.vue'
import SloHistoryChartDataProvider from '../components/slo-history-chart-data-provider.vue'
import SloDrillDownNoDate from './slo-drill-down-no-date.vue'

export default {
  name: 'SloDrilldown',
  components: {
    Severity,
    TimerangePicker,
    // SaveAsWidget,
    GraphView,
    SloHistoryChart,
    AgentProvider,
    AgentPicker,
    GraphSidebar,
    TimelineScrollbar,
    SloHistoryChartDataProvider,
    GroupProvider,
    ObjectTagProvider,
    SloDrillDownNoDate,
  },
  data() {
    return {
      loading: true,
      timeline: {
        selectedKey: 'today',
      },
      slo: {},
      monitor: {},
      isHistoryVisible: true,
      chartSeries: {},
      currentTimestamp: this.$route.query.c ? +this.$route.query.c : undefined,
      activeItem: null,
      metadata: {},
    }
  },
  computed: {
    ...UserPreferenceComputed,
    offset() {
      return Moment().tz(this.timezone).utcOffset() * 60 * 1000
    },
  },
  created() {
    const t = this.$route.query.t
    if (t) {
      try {
        this.timeline = JSON.parse(atob(decodeURIComponent(t)))
      } catch (e) {
        this.timeline = {
          selectedKey: 'today',
        }
      }
    } else {
      this.timeline = {
        selectedKey: 'today',
      }
    }
    this.getSloRouteItem()
      .then(() => {
        return this.getChartData()
      })
      .then(() => {
        this.$watch('timeline', () => {
          this.currentTimestamp = undefined
          this.loading = true
          this.getChartData()
        })
      })
      .catch((error) => {
        console.error(error)
      })
  },
  methods: {
    hideChartTooltips() {
      Highcharts.charts.filter(Boolean).forEach((chart) => chart.tooltip.hide())
    },
    getSeverities() {
      return new Promise((resolve) => {
        Bus.$once('severity.query', (data) => {
          resolve(data)
        })
        Bus.$emit('server:event', {
          'event.type': 'severity.query',
          'event.context': {
            type: 'SLO',
          },
        })
      })
    },
    getMetadata() {
      return fetchSloRouteMetadataApi(this.$route.params.id)
        .then((data) => {
          this.metadata = Object.freeze(data)
        })
        .catch(() => {
          this.metadata = {}
        })
    },
    getChartData() {
      const context = new WidgetContextBuilder()
      context.addGroup('slo.metric')
      context.setCategory(WidgetTypeConstants.CHART)
      context.setWidgetType(WidgetTypeConstants.COLUMN)
      context.setTimeLine(this.timeline)
      context.addCounterToGroup({
        counter: 'status',
        aggrigateFn: '__NONE__',
        entityType: 'SLO',
        entities: this.slo.id,
      })
      context.addCounterToGroup({
        counter: 'slo.packet.lost.percent',
        aggrigateFn: '__NONE__',
        entityType: 'SLO',
        entities: this.slo.id,
      })
      context.addCounterToGroup({
        counter: 'slo.latency.ms',
        aggrigateFn: '__NONE__',
        entityType: 'SLO',
        entities: this.slo.id,
      })
      context.addCounterToGroup({
        counter: 'severity',
        aggrigateFn: '__NONE__',
        entityType: 'SLO',
        entities: this.slo.id,
      })
      getWidgetResponseApi(context.generateWidgetDefinition())
        .then((data) => {
          let statusSeries = [
            {
              name: 'Availability',
              data: [],
              formattedValues: [],
              states: {
                hover: {
                  opacity: 1,
                },
                select: {
                  opactiy: 1,
                },
              },
            },
          ]
          let packetLostSeries = [
            {
              name: 'slo.packet.lost.percent',
              counter: 'slo.packet.lost.percent',
              data: [],
              formattedValues: [],
              states: {
                hover: {
                  opacity: 1,
                },
                select: {
                  opactiy: 1,
                },
              },
            },
          ]
          let latencySeries = [
            {
              name: 'slo.latency.ms',
              counter: 'slo.latency.ms',
              data: [],
              formattedValues: [],
              states: {
                hover: {
                  opacity: 1,
                },
                select: {
                  opactiy: 1,
                },
              },
            },
          ]

          const severityMap = {
            7: 'down',
            6: 'unreachable',
            5: 'critical',
            4: 'major',
            3: 'warning',
            2: 'clear',
            1: 'maintenance',
            0: 'unknown',
          }
          SortBy(data, 'timestamp').forEach((item) => {
            let color = `var(--severity-${severityMap[item['severity.value']]})`

            statusSeries[0].data.push({
              x: item.timestamp + this.offset,
              y: 1,
              opacity: 0.5,
              color:
                item['status.value'] === 1
                  ? 'var(--severity-down)'
                  : 'var(--severity-clear)',
            })
            statusSeries[0].formattedValues.push(
              item['status.value'] === 0 ? 'Up' : 'Down'
            )
            packetLostSeries[0].data.push({
              x: item.timestamp + this.offset,
              y: item['slo.packet.lost.percent.value'],
              color,
              opacity: 0.5,
            })
            packetLostSeries[0].formattedValues.push(
              item['slo.packet.lost.percent.value.formatted']
            )
            if (!this.$route.query.c) {
              this.currentTimestamp = item.timestamp
            }
            latencySeries[0].data.push({
              x: item.timestamp + this.offset,
              y: item['slo.latency.ms.value'],
              color,
              opacity: 0.5,
            })
            latencySeries[0].formattedValues.push(
              item['slo.latency.ms.value.formatted']
            )
          })
          if (
            packetLostSeries[0].data.length === 0 &&
            latencySeries[0].data.length === 0 &&
            statusSeries[0].data.length === 0
          ) {
            this.currentTimestamp = undefined
          }
          this.loading = false
          this.chartSeries = {
            statusSeries,
            packetLostSeries,
            latencySeries,
          }
        })
        .catch((error) => {
          console.error(error)
        })
    },
    handleCollapseChange(data) {
      if (data.includes('history')) {
        this.isHistoryVisible = true
      } else {
        this.isHistoryVisible = false
      }
    },
    getSloRouteItem() {
      return fetchSloRouteApi(this.$route.params.id)
        .then(async (data) => {
          let severities = await this.getSeverities()
          this.slo = Object.freeze({
            ...data,
            severity: severities[data.id] || 'UNKNOWN',
          })
          return objectDBWorker.getObjectById(data.source)
        })
        .then((monitor) => {
          this.monitor = Object.freeze(monitor)
          return this.getMetadata()
        })
    },
    handleSaveAsWidget() {
      // @TODO provide appi call to save as widget
      // console.log('Handle save widget call')
    },
    handlePointClick(timestamp) {
      const currentTimestamp =
        typeof timestamp === 'number' ? timestamp : +timestamp
      this.currentTimestamp = currentTimestamp - this.offset
    },
    handleRangeChanged(range) {
      // Update the current visible range
      if (range && range.startTime && range.endTime) {
        // This can be used to zoom the charts to a specific range
        this.$emit('update:timeline', {
          ...this.timeline,
          startTime: range.startTime,
          endTime: range.endTime,
          selectedKey: 'custom',
        })
      }
    },
  },
}
</script>
<style lang="less" scoped>
.slo-border-bot {
  border-bottom: 1px solid var(--border-color) !important;
}
</style>
