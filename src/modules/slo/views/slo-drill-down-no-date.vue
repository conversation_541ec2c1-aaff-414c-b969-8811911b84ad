<template>
  <div
    class="flex justify-center items-center min-h-0 flex-col relative flex-1"
  >
    <div
      class="flex justify-end items-center h-3/5 bg-neutral-lightest w-full flex-col"
      style="height: 60%"
    >
      <component :is="noDateGraph" style="max-width: 100%; height: auto" />
      <div
        class="text-neutral-light my-6 flex items-center justify-center flex-col text-center text-base"
      >
        <MIcon name="slo" class="text-neutral-light my-2 mr-2" size="lg" />
        S<PERSON> is tracing the route quietly in the background

        <br />
        your end-to-end network visibility is on the way.
      </div>
    </div>

    <div
      class="flex justify-center items-center h-2/5 w-full"
      style="height: 40%"
    >
      <component :is="noDateChart" style="max-width: 100%; height: auto" />
    </div>
  </div>
</template>

<script>
// import NoDataBlackGraph from '@assets/images/no-data-image/slo-drill-down-graph-black.svg'
// import NoDataBlackChart from '@assets/images/no-data-image/slo-drill-down-chart-black.svg'
// import NoDataWhiteGraph from '@assets/images/no-data-image/slo-drill-down-graph-white.svg'
// import NoDataWhiteChart from '@assets/images/no-data-image/slo-drill-down-chart-white.svg'
export default {
  name: 'SloDrillDownNoDate',
  components: {
    // NoDataBlackGraph,
    // NoDataBlackChart,
    // NoDataWhiteGraph,
    // NoDataWhiteChart,
  },
  props: {
    theme: {
      type: String,
      default: 'black',
    },
  },
  computed: {
    //     noDateGraph() {
    //       return this.theme === 'black' ? NoDataBlackGraph : NoDataWhiteGraph
    //     },
    // noDateChart() {
    //   return this.theme === 'black' ? NoDataBlackChart : NoDataWhiteChart
    // },
  },
}
</script>
