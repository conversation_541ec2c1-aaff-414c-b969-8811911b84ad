<template>
  <FlotoContentLoader :loading="loading">
    <FlotoFixedView :gutter="0">
      <MRow :gutter="0" class="content-inner-panel w-full">
        <MCol>
          <FlotoPageHeader
            :title="sloContext.name"
            use-divider
            :back-link="$currentModule.getRoute('')"
          />
        </MCol>
      </MRow>
      <MTab :value="tabType" class="pl-2" @change="handleTabChange">
        <MTabPane key="overview" tab="Overview" />
        <MTabPane key="slo-history" tab="SLO History" />
      </MTab>
      <FlotoScrollView class="overflow-x-hidden">
        <div
          class="flex flex-1 flex-col"
          style="background: var(--dashboard-background)"
        >
          <SloOverview
            v-if="tabType === 'overview'"
            :slo="sloContext"
            :slo-timeline="sloTimeline"
          />
          <SloHistory
            v-if="tabType === 'slo-history'"
            :slo="sloContext"
            :slo-timeline="sloTimeline"
          />
        </div>
      </FlotoScrollView>
    </FlotoFixedView>
  </FlotoContentLoader>
</template>

<script>
import SloOverview from '../components/slo-overview.vue'
import SloHistory from '../components/slo-history.vue'
import Moment from 'moment'

import {
  getSLOProfileApi,
  getSLOProfileCyclesApi,
} from '@modules/settings/service-level-objective/slo-profile-api'

import { DATE_FORMAT, TIME_FORMAT } from '@components/widgets/constants'

export default {
  name: 'SloDetails',
  components: {
    SloOverview,
    SloHistory,
  },
  data() {
    return {
      sloId: this.$route.params.id,
      loading: true,
      sloContext: {},
      tabType: this.$route.params.tab || 'overview',
      sloTimeline: {},
    }
  },
  created() {
    this.fetchSloData()
  },
  methods: {
    fetchSloData() {
      this.loading = true

      return getSLOProfileApi(this.sloId).then((slo) => {
        return getSLOProfileCyclesApi(slo.cycleId)
          .then((cycleContext) => {
            this.sloContext = { ...slo, ...cycleContext }
            this.tabType = this.$route.params.tab || 'overview'

            const startTime = Moment.unix(cycleContext.sloCycleStartTime)
            const endTime = Moment.unix(cycleContext.sloCycleEndTime)

            this.sloTimeline = {
              selectedKey: 'custom',
              startDate: startTime.format(DATE_FORMAT),
              endDate: endTime.format(DATE_FORMAT),
              startTime: startTime.format(TIME_FORMAT),
              endTime: endTime.format(TIME_FORMAT),
            }
            this.loading = false
          })
          .catch(() => {
            this.loading = true
          })
      })
    },
    handleTabChange(tab) {
      // when use change the tab the detail route will be updated
      this.$router.push({
        name: 'slo.detail',
        params: {
          id: this.sloId,
          tab,
        },
      })
    },
  },
}
</script>
