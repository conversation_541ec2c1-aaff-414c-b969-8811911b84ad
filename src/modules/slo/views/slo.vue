<template>
  <FlotoContentLoader :loading="loading">
    <FlotoModule>
      <FlotoFixedView>
        <div class="flex flex-col h-full flex-1 content-inner-panel">
          <MRow v-if="!hideTitle" :gutter="0">
            <MCol>
              <FlotoPageHeader use-divider title="SLO" main-header>
                <template v-slot:before-title>
                  <MIcon
                    id="slo-icon"
                    name="slo"
                    class="text-primary-alt mr-3 pl-2"
                  />
                </template>
                <template v-slot:title>
                  <div class="flex-1 ml-4 justify-between flex items-center">
                    <SloStatusSwitch
                      v-model="selectedStatus"
                      :count="statusCount"
                    />
                  </div>
                </template>
              </FlotoPageHeader>
            </MCol>
          </MRow>
          <MPersistedColumns
            v-model="columns"
            :default-value="availableColumns"
            :module-key="`sla-slo-grid`"
            :available-columns="availableColumns"
          >
            <template
              v-slot="{
                columns: persistedColumns,
                setColumns: updatePersistedColumns,
              }"
            >
              <div
                class="flex flex-1 min-h-0 flex-col"
                :class="{ 'dashboard-container': view !== 'grid-view' }"
              >
                <div
                  class="flex justify-between items-center ml-2 mt-3"
                  :class="{ 'mb-2': view !== 'grid-view' }"
                >
                  <MInput
                    v-model="searchTerm"
                    class="search-box"
                    placeholder="Search"
                    name="search"
                  >
                    <template v-slot:prefix>
                      <MIcon name="search" />
                    </template>
                    <template v-if="searchTerm" v-slot:suffix>
                      <MIcon
                        name="times-circle"
                        class="text-neutral-light cursor-pointer"
                        @click="searchTerm = undefined"
                      />
                    </template>
                  </MInput>
                  <div class="flex items-center ml-2">
                    <MButton
                      class="squared-button mr-2"
                      variant="neutral-lightest"
                      @click="fetchSloData"
                    >
                      <MIcon name="sync" class="excluded-header-icon" />
                    </MButton>

                    <ColumnSelector
                      v-model="columns"
                      :columns="availableColumns"
                      @change="updatePersistedColumns"
                    >
                      <template v-slot:trigger="{ toggle }">
                        <MButton
                          class="squared-button mr-2"
                          variant="neutral-lightest"
                          @click="toggle"
                        >
                          <MIcon name="eye" class="excluded-header-icon" />
                        </MButton>
                      </template>
                    </ColumnSelector>
                    <MButton
                      shape="circle"
                      :title="view === 'dashboard-view' ? 'Grid' : 'Dashboard'"
                      class="squared-button mr-2"
                      variant="neutral-lightest"
                      @click="toggleView"
                    >
                      <MIcon
                        :name="
                          view === 'dashboard-view' ? 'grid' : 'widget-view'
                        "
                        class="excluded-header-icon"
                      />
                    </MButton>
                  </div>
                </div>

                <div class="flex flex-col flex-1 min-h-0">
                  <Transition name="placeholder" mode="out-in">
                    <MGrid
                      v-if="view === 'grid-view'"
                      :key="view"
                      :data="sloData"
                      :columns="persistedColumns"
                      :search-term="searchTerm"
                      :filters="filters"
                    >
                      <template v-slot:name="{ item }">
                        <a class="slo-name" @click="navigateToSloDetail(item)">
                          {{ item.name }}
                        </a>
                      </template>

                      <template v-slot:status="{ item }">
                        <SloStatusBadge
                          :status="item.slo_status_last"
                          :use-server-status="false"
                        />
                      </template>

                      <template v-slot:frequency="{ item }">
                        <MTag
                          :closable="false"
                          variant="primary"
                          rounded
                          class="tag-unknown inline-flex items-center"
                        >
                          {{ item.frequency }}
                        </MTag>
                      </template>

                      <template v-slot:target="{ item }">
                        <MTag
                          :closable="false"
                          variant="primary"
                          rounded
                          class="tag-primary inline-flex items-center"
                        >
                          {{ item.target }}%
                        </MTag>
                      </template>

                      <template v-slot:slo_achieved_percent_last="{ item }">
                        <MTag
                          :closable="false"
                          variant="primary"
                          rounded
                          class="tag-green inline-flex items-center"
                        >
                          {{ item.slo_achieved_percent_last }}
                        </MTag>
                      </template>

                      <template v-slot:slo_violated_percent_last="{ item }">
                        <MTag
                          :closable="false"
                          variant="primary"
                          rounded
                          class="tag-red inline-flex items-center"
                        >
                          {{ item.slo_violated_percent_last }}
                        </MTag>
                      </template>
                      <template v-slot:slo_mttr_seconds_last="{ item }">
                        {{
                          item.type === AVAILABLE_SLO_TYPES.AVAILABILITY
                            ? item.slo_mttr_seconds_last
                            : '-'
                        }}
                      </template>
                      <template v-slot:slo_mtbf_seconds_last="{ item }">
                        {{
                          item.type === AVAILABLE_SLO_TYPES.AVAILABILITY
                            ? item.slo_mtbf_seconds_last
                            : '-'
                        }}
                      </template>
                      <template
                        v-slot:slo_error_budget_left_seconds_last="{ item }"
                      >
                        {{ calculateErrorBudgetLeft(item) }}
                      </template>
                    </MGrid>
                    <SloKpiView
                      v-else
                      :slo-data="sloData"
                      :columns="availableColumns"
                      :search-term="searchTerm"
                      :filters="filters"
                      @drillDown="navigateToSloDetail"
                    />
                  </Transition>
                </div>
              </div>
            </template>
          </MPersistedColumns>
        </div>
      </FlotoFixedView>
    </FlotoModule>
  </FlotoContentLoader>
</template>

<script>
import groupBy from 'lodash/groupBy'
import Duration from '@src/filters/duration'

import {
  AVAILABLE_COLUMNS,
  SLO_STATUS,
  // SLO_STATUS_MAP,
  // REVERSE_SLO_STATUS_MAP,
  AVAILABLE_SLO_TYPES,
} from '../helpers/slo'

import Bus from '@utils/emitter'
import SloKpiView from '../components/slo-kpi-view.vue'
// import {
//   getWidgetResponseApi,
//   buildWidgetContext,
//   makeCounter,
// } from '../../../utils/socket-event-as-api'
// import { WidgetTypeConstants } from '../../../components/widgets/constants'
import ColumnSelector from '@components/column-selector.vue'
import { getVisualizationDataApi } from '../slo-api'
import SloStatusSwitch from '../components/slo-status-switch.vue'
import SloStatusBadge from '../components/slo-status-badge.vue'

export default {
  name: 'SLO',
  components: {
    SloKpiView,
    ColumnSelector,
    SloStatusBadge,
    SloStatusSwitch,
  },
  page() {
    return {
      title: 'SLO',
    }
  },
  data() {
    this.AVAILABLE_SLO_TYPES = AVAILABLE_SLO_TYPES
    return {
      searchTerm: undefined,
      loading: false,

      view: this.$route.params.view || 'grid-view',
      sloData: [],
      // shouldShowModule: true,
      statusCount: {},
      selectedStatus: 'total',
    }
  },

  computed: {
    availableColumns() {
      return AVAILABLE_COLUMNS
    },
    hideTitle() {
      return this?.$route?.meta?.isDrillDown
    },
    filters() {
      if (this.selectedStatus !== 'total') {
        const statusColumn = 'slo_status_last'

        return [
          {
            field: statusColumn,
            operator: 'eq',
            value: this.selectedStatus,
          },
        ]
      }
      return []
    },
  },
  watch: {
    $route(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.currentTab = newValue.name.split('.').pop()
      }
    },
  },
  created() {
    this.fetchSloData()
    this.columns = AVAILABLE_COLUMNS
  },
  methods: {
    calculateErrorBudgetLeft(item) {
      const isNegative = item.slo_error_budget_left_seconds_last_sort < 0

      return `${isNegative ? '-' : ''}${Duration(
        item.slo_error_budget_left_seconds_last_sort || 0,
        'seconds',
        false
      )}`
    },
    getSeverities() {
      return new Promise((resolve) => {
        Bus.$once('severity.query', (data) => {
          resolve(data)
        })
        Bus.$emit('server:event', {
          'event.type': 'severity.query',
          'event.context': {
            type: 'SLO',
          },
        })
      })
    },
    toggleView() {
      if (this.view === 'grid-view') {
        this.view = 'dashboard-view'
      } else {
        this.view = 'grid-view'
      }
    },

    async fetchSloData() {
      this.loading = true
      return getVisualizationDataApi().then((data) => {
        const groupByColumn = 'slo_status_last'
        const groupByStatus = groupBy(data, groupByColumn)

        this.statusCount = {
          [SLO_STATUS.BREACHED]:
            groupByStatus[SLO_STATUS.BREACHED]?.length || 0,
          [SLO_STATUS.WARNING]: groupByStatus[SLO_STATUS.WARNING]?.length || 0,
          [SLO_STATUS.OK]: groupByStatus[SLO_STATUS.OK]?.length || 0,
          // [SLO_STATUS.NOT_CALCULATED]: groupByStatus['3']?.length || 0,
          total: data.length,
        }

        this.sloData = data
        this.loading = false
      })
    },

    navigateToSloDetail(row) {
      this.$router.push(
        this.$currentModule.getRoute('detail', {
          params: {
            id: row.id,
            tab: 'overview',
          },
        })
      )
    },
  },
}
</script>
