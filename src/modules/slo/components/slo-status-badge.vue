<template>
  <MTag class="status-badge" :class="statusClass" rounded :closable="false">
    {{ statusText }}
  </MTag>
</template>

<script>
import Capitalize from 'lodash/capitalize'
import { SLO_STATUS, SLO_STATUS_MAP } from '../helpers/slo'

export default {
  name: 'SloStatusBadge',
  props: {
    status: {
      type: [String, Number],
      required: true,
    },
    useServerStatus: {
      type: Boolean,
      // eslint-disable-next-line
      default: true,
    },
  },
  computed: {
    statusClass() {
      const convertedStatus = this.useServerStatus
        ? SLO_STATUS_MAP[this.status]
        : this.status
      if ([convertedStatus].includes(SLO_STATUS.BREACHED)) {
        return 'tag-red'
      }

      if ([convertedStatus].includes(SLO_STATUS.WARNING)) {
        return 'tag-yellow'
      }

      if ([convertedStatus].includes(SLO_STATUS.OK)) {
        return 'tag-green'
      }

      if ([convertedStatus].includes(SLO_STATUS.NOT_CALCULATED)) {
        return 'tag-blue'
      }

      return 'tag-gray'
    },
    statusText() {
      return this.useServerStatus
        ? Capitalize(SLO_STATUS_MAP[this.status])
        : Capitalize(this.status)
    },
  },
}
</script>
