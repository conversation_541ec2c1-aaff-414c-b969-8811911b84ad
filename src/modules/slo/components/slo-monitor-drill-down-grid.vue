<template>
  <FlotoFixedView :gutter="0" class="h-full">
    <FlotoContentLoader :loading="loading">
      <div class="flex flex-1 min-h-0 flex-col">
        <div class="flex flex-col flex-1 min-h-0">
          <Transition name="placeholder" mode="out-in">
            <MGrid
              ref="gridRef"
              :data="rows"
              :columns="availableColumns"
              :search-term="searchTerm"
              :paging="false"
            >
              <template v-slot:Timestamp="{ item }">
                {{ Math.round(item.Timestamp / 1000) | datetime }}
              </template>

              <template v-slot:slo_object_achieved_percent_value="{ item }">
                <MTag class="status-badge tag-green" rounded :closable="false">
                  {{ item.slo_object_achieved_percent_value }}
                </MTag>
              </template>
              <template v-slot:slo_instance_achieved_percent_value="{ item }">
                <MTag class="status-badge tag-green" rounded :closable="false">
                  {{ item.slo_instance_achieved_percent_value }}
                </MTag>
              </template>

              <template v-slot:slo_object_violated_percent_value="{ item }">
                <MTag class="status-badge tag-red" rounded :closable="false">
                  {{ item.slo_object_violated_percent_value }}
                </MTag>
              </template>
              <template v-slot:slo_instance_violated_percent_value="{ item }">
                <MTag class="status-badge tag-red" rounded :closable="false">
                  {{ item.slo_instance_violated_percent_value }}
                </MTag>
              </template>

              <template v-slot:slo_object_burn_rate_seconds_value="{ item }">
                {{ item.slo_object_burn_rate_seconds_value }}
              </template>
              <template v-slot:slo_instance_burn_rate_seconds_value="{ item }">
                {{ item.slo_instance_burn_rate_seconds_value }}
              </template>
            </MGrid>
          </Transition>
        </div>
      </div>
    </FlotoContentLoader>
  </FlotoFixedView>
</template>

<script>
import { getConfiguredMonitorSessionHistoryApi } from '../slo-api'
// import MonitorName from '@components/widgets/views/grid/view-more/monitor-name.vue'
// import LooseTags from '@components/loose-tags.vue'
// import SloStatusBadge from './slo-status-badge.vue'

export default {
  name: 'SloMonitorDrillDownGrid',
  components: {
    // MonitorName,
    // LooseTags,
    // SloStatusBadge,
  },
  props: {
    slo: {
      type: Object,
      required: true,
    },
    globalSloData: {
      type: Object,
      required: true,
    },
    gridItem: {
      type: Object,
      required: true,
    },
    sloTimeline: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      searchTerm: undefined,
      loading: true,
      drillDownDevice: null,
      rows: [],
    }
  },
  computed: {
    isInstanceSlo() {
      return this.slo.sloFor !== 'monitor'
    },
    availableColumns() {
      return this.isInstanceSlo
        ? [
            {
              key: 'Timestamp',
              name: 'Time stamp',
              searchable: true,
              sortable: true,
              align: 'left',
            },
            {
              key: 'slo_instance_achieved_percent_value',
              name: 'Achieved',
              searchable: true,
              sortable: true,
              align: 'left',
            },

            {
              key: 'slo_instance_violated_percent_value',
              name: 'Violation',
              sortable: true,
              searchable: true,
              align: 'center',
              minWidth: '100px',
            },
            {
              key: 'slo_instance_burn_rate_seconds_value',
              name: 'Error budget used',
              searchable: true,
              sortable: true,
              align: 'center',
            },
          ]
        : [
            {
              key: 'Timestamp',
              name: 'Time stamp',
              searchable: true,
              sortable: true,
              align: 'left',
            },
            {
              key: 'slo_object_achieved_percent_value',
              name: 'Achieved',
              searchable: true,
              sortable: true,
              align: 'left',
            },

            {
              key: 'slo_object_violated_percent_value',
              name: 'Violation',
              sortable: true,
              searchable: true,
              align: 'center',
              minWidth: '100px',
            },
            {
              key: 'slo_object_burn_rate_seconds_value',
              name: 'Error budget used',
              searchable: true,
              sortable: true,
              align: 'center',
            },
          ]
    },
  },

  created() {
    this.getConfiguredMonitors()
  },
  methods: {
    getConfiguredMonitors() {
      return getConfiguredMonitorSessionHistoryApi(
        this.slo,
        this.gridItem,
        this.sloTimeline
      ).then((data) => {
        this.rows = data.rows || []

        this.loading = false
      })
    },
    handleOpenMonitorSlo(item) {
      this.drillDownDevice = item
    },
  },
}
</script>
