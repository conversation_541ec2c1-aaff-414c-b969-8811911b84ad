<template>
  <div>
    <div
      class="flex flex-col flex-1 min-h-0 min-w-0 cursor-auto absolute pl-2 h-full overflow-auto border-left"
      :style="currentStyle"
    >
      <MRow class="min-h-0" :gutter="0">
        <MCol class="flex justify-between items-center min-w-0 mt-2">
          <div v-if="type === 'edge'">
            <div class="flex items-center">
              <h6 class="m-0 inline-flex">
                <Severity class="mr-1" :severity="data.severity" />
                {{ data.source.name }}
                <span class="mx-4" style="color: var(--neutral-theme-color)">
                  ---------►
                </span>
                {{ data.target.name }}
              </h6>
            </div>
          </div>
          <template v-else>
            <div v-if="data.monitor.id" class="flex flex-col">
              <div class="flex">
                <MonitorType disable-tooltip :type="data.monitor.type" />
                <div class="ml-2 flex flex-col">
                  <div class="flex items-center">
                    <h6 class="m-0 inline-flex">
                      <Severity class="mr-1" :severity="data.severity" />
                      {{ data.monitor.name }}
                    </h6>
                    <div class="mx-2" style="color: var(--neutral-theme-color)"
                      >|</div
                    >
                    <div>
                      <span style="color: var(--neutral-theme-color)">{{
                        data.monitor.ip
                      }}</span>
                    </div>
                    <div class="mx-2" style="color: var(--neutral-theme-color)"
                      >|</div
                    >
                    <div>
                      <span style="color: var(--neutral-theme-color)">{{
                        data.monitor.type
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="my-2">
                <div
                  class="inline-flex flex-grow-0 min-w-0 flex-shrink-0 w-full"
                >
                  <div class="group-picker-container">
                    <GroupPicker
                      :value="data.monitor.groups"
                      disabled
                      :wrap="false"
                    />
                  </div>
                  <div class="loose-tags-container">
                    <LooseTags
                      :value="data.monitor.tags"
                      disabled
                      :wrap="false"
                    />
                  </div>
                </div>
              </div>
            </div>
            <div v-else>
              <div class="flex items-center">
                <h6 class="m-0 inline-flex">
                  <Severity class="mr-1" :severity="data.severity" />
                  {{ data.name }}
                </h6>
              </div>
            </div>
          </template>
          <MButton
            :shadow="false"
            variant="transparent"
            @click="$emit('close')"
          >
            <MIcon name="times" size="lg" />
          </MButton>
        </MCol>
        <MDivider class="my-1" />
      </MRow>
      <div class="min-h-0 flex-1">
        <FlotoContentLoader :loading="loading" class="h-full">
          <div class="flex flex-col w-full py-2 border-bot">
            <div v-if="!data.root" class="flex items-center mb-4">
              <div class="w-1/3" style="color: var(--neutral-theme-color)"
                >Latency</div
              >
              <div class="w-2/3">
                <div class="flex items-center">
                  <div class="flex flex-col flex-1">
                    <div style="color: var(--neutral-theme-color)">Min</div>
                    <div>{{ data.hop['slo.min.latency.ms'] }}ms</div>
                  </div>
                  <div class="flex flex-col flex-1">
                    <div style="color: var(--neutral-theme-color)">Avg.</div>
                    <div>{{ data.hop['slo.latency.ms'] }}ms</div>
                  </div>
                  <div class="flex flex-col flex-1">
                    <div style="color: var(--neutral-theme-color)">Max</div>
                    <div>{{ data.hop['slo.max.latency.ms'] }}ms</div>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="!data.root" class="flex items-center mb-4">
              <div class="w-1/3" style="color: var(--neutral-theme-color)"
                >Packet Loss</div
              >
              <div class="w-2/3">
                <MTag
                  :closable="false"
                  rounded
                  class="cursor-auto inline-flex items-center"
                >
                  {{ data.hop['slo.packet.lost.percent'] }}%
                </MTag>
              </div>
            </div>
            <MDivider v-if="type === 'node'" class="my-1" />
            <MonitorAlertCountProvider
              v-if="type === 'node' && data.monitor.id"
              ref="alertCountProvider"
              :selected-target="widgetParams"
            >
              <template v-slot="{ data: alerts }">
                <MCollapse
                  :bordered="false"
                  :default-active-key="['alerts']"
                  class="flex-1"
                  :accordion="false"
                >
                  <MCollapsePanel key="alerts">
                    <template v-slot:header>
                      <MCol
                        class="text-primary font-bold flex items-center relative"
                      >
                        Alert(s)
                        <MTag
                          :closable="false"
                          rounded
                          class="tag-red text-ellipsis cursor-auto ml-2"
                          :title="((alerts || {}).rows || []).length"
                        >
                          {{
                            ((alerts || {}).rows || []).filter(
                              (item) => item.severity !== 'CLEAR'
                            ).length
                          }}
                        </MTag>
                      </MCol>
                    </template>
                    <MGrid
                      class="min-w-0 relative"
                      style="margin-top: -15px"
                      :columns="alertColumns"
                      :data="
                        ((alerts || {}).rows || []).filter(
                          (item) => item.severity !== 'CLEAR'
                        )
                      "
                      :paging="false"
                      :expandable="true"
                      :detail="'detailRow'"
                    >
                      <!-- <template v-slot:detailRow="{ item }">
                        <div>
                          <span class="text-primary">Message:</span>
                          {{ JSON.stringify(item) }}
                        </div>
                      </template> -->
                      <template v-slot:policy_name="{ item }">
                        <div class="flex items-center">
                          <!-- <MButton
                            variant="transparent"
                            shape="circle"
                            :shadow="false"
                            @click="toggleExpand"
                          >
                            <MIcon
                              :name="
                                item.expanded ? 'chevron-down' : 'chevron-right'
                              "
                            />
                          </MButton> -->
                          <Severity
                            disable-tooltip
                            :severity="item.severity"
                            :center="false"
                            class="mr-1"
                          />
                          <AlertDrilldown
                            :alert="item || {}"
                            traget-blank
                            :field="item.policy_name"
                          />
                        </div>
                      </template>
                      <template v-slot:value="{ item }">
                        <MStatusTag
                          v-if="(item.metric || '').includes('status')"
                          :status="item.value"
                        />
                        <MTag
                          v-else
                          :closable="false"
                          rounded
                          class="tag-primary text-ellipsis cursor-auto"
                          style="max-width: 200px"
                          :title="item.value"
                          >{{ item.value }}</MTag
                        >
                      </template>
                    </MGrid>
                  </MCollapsePanel>
                </MCollapse>
              </template>
            </MonitorAlertCountProvider>
            <div class="mt-4"></div>
            <div
              v-for="item in appliedMetadata"
              :key="item.label"
              class="flex items-center mb-4"
            >
              <div class="w-1/3" style="color: var(--neutral-theme-color)"
                >{{ item.label }}:</div
              >
              <div class="w-2/3">
                <template v-if="Array.isArray(item.value)">
                  <div v-for="subItem in item.value" :key="subItem">
                    <a
                      :href="
                        key === 'phones'
                          ? `tel:${subItem}`
                          : `mailto:${subItem}`
                      "
                    >
                      {{ subItem }}
                    </a>
                  </div>
                </template>
                <template v-else>
                  {{ item.value }}
                </template>
              </div>
            </div>
          </div>
          <div
            v-if="
              type === 'node' &&
              !data.monitor.id &&
              data.name.indexOf('Timeout') === -1
            "
            class="flex w-full py-1"
          >
            <span style="color: var(--neutral-theme-color)" class="pt-2">
              This device is not Monitored with AIOps platform.
            </span>
            <a
              href="/settings/network-discovery/network-discovery-profiles"
              target="_blank"
              class="ml-1 pt-2"
            >
              Discover Now
              <MIcon name="external-link" class="ml-1 text-primary" />
            </a>
          </div>
        </FlotoContentLoader>
      </div>
    </div>
  </div>
</template>

<script>
import Severity from '@components/severity.vue'
import MonitorType from '@components/monitor-type.vue'
import MonitorAlertCountProvider from '@/src/components/data-provider/monitor-alert-count-provider.vue'
import AlertDrilldown from '@components/widgets/views/grid/view-more/alert-drilldown.vue'
import LooseTags from '@/src/components/loose-tags.vue'

export default {
  name: 'GraphSidebar',
  components: {
    Severity,
    MonitorType,
    MonitorAlertCountProvider,
    AlertDrilldown,
    LooseTags,
  },
  props: {
    type: {
      type: String,
      default: 'node',
    },
    data: {
      type: Object,
      required: true,
    },
    timeline: {
      type: Object,
      required: true,
    },
    metadata: {
      type: Object,
      default() {
        return {}
      },
    },
    topOffset: {
      type: Number,
      default: 0,
    },
    rightOffset: {
      type: Number,
      default: 0,
    },
  },
  data() {
    this.defaultOpenPanels = ['alert_trend']
    this.metadataKeyMap = {
      'originated.by': 'Origniated By',
      message: 'Message',
      prefix: 'Prefix',
      'owned.by': 'Owned By',
      phones: 'Phone',
      emails: 'E-mail',
      'object.ip': 'IP',
    }
    return {
      loading: false,
      isOpen: false,
    }
  },
  computed: {
    alertColumns() {
      return [
        {
          key: 'policy_name',
          name: 'Policy Name',
          searchable: true,
          sortable: true,
        },
        {
          key: 'value',
          name: 'Value',
          searchable: true,
          sortable: true,
        },
      ]
    },
    widgetParams() {
      if (this.data.monitor.id) {
        return {
          resourceType: 'Monitor',
          id: this.data.monitor.id,
        }
      } else {
        return {}
      }
    },
    appliedMetadata() {
      let data = []
      if (this.type === 'edge') {
        return data
      }
      let metadata =
        this.metadata[
          (this.data.monitor || {}).id ? this.data.monitor.ip : this.data.ip
        ] || {}
      let order = ['owned.by', 'prefix', 'originated.by', 'phones', 'emails']
      order.map((key) => {
        if (metadata[key]) {
          data.push({
            label: this.metadataKeyMap[key] || key,
            value: metadata[key],
          })
        }
      })

      Object.keys(metadata)
        .filter((key) => order.includes(key) === false)
        .forEach((key) => {
          data.push({
            label: this.metadataKeyMap[key] || key,
            value: metadata[key],
          })
        })
      return data
    },
    currentStyle() {
      return {
        top: `0px`,
        right: `0px`,
        zIndex: 100,
        width: '550px',
        willChange: 'transform',
        transition: 'transform 0.1s linear',
        borderWidth: '2px',
        background: 'var(--drawer-background-color)',
        boxShadow: `-6px 0px 10px -5px ${
          this.theme === 'black' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.2)'
        }`,
        ...(this.topOffset || this.rightOffset
          ? {
              transform: `translate(${this.rightOffset}px, ${this.topOffset}px)`,
            }
          : {}),
      }
    },
  },
  watch: {
    data: {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.isOpen = true
        }
      },
    },
  },
  beforeDestroy() {
    this.handleDrawerHide()
  },
  methods: {
    handleDrawerHide() {
      this.isOpen = false
      this.$emit('close')
    },
  },
}
</script>

<style lang="less" scoped>
.group-picker-container {
  min-width: 0; /* Prevents overflow issues */
  max-width: 50%; /* Limits the width to 50% when content overflows */
}

.loose-tags-container {
  min-width: 0; /* Prevents overflow issues */
  max-width: 50%; /* Limits the width to 50% when content overflows */
}
</style>
