<template>
  <div :key="key" class="flex h-full items-end">
    <div
      v-if="!hideLabel"
      class="mb-4 text-right"
      :style="{
        width: '100px',
        fontSize: '0.65rem',
        color: 'var(--chart-legend-color)',
        position:
          counter === 'slo.packet.lost.percent' ? 'relative' : undefined,
        bottom: counter === 'slo.packet.lost.percent' ? '25px' : undefined,
      }"
    >
      {{
        counter === 'status'
          ? 'Availability'
          : counter === 'slo.latency.ms'
          ? 'Latency'
          : counter === 'slo.packet.lost.percent'
          ? 'Packet Loss'
          : counter
      }}
    </div>
    <div class="flex flex-col h-full flex-1">
      <ChartView
        :data="{ series: data }"
        :sync-group="disableSyncing ? undefined : 'slo-history-chart'"
        :tooltip-out-side="tooltipOutSide"
        :time-range="timeline"
        :widget="widget"
        :disable-server-zoom="true"
        :max-y="counter === 'slo.packet.lost.percent' ? 100 : undefined"
        :y-axis-label-width="80"
      />
    </div>
    <TimelineScrollbar
      v-if="showTimelineScrollbar"
      :start-time="timeline.startTime"
      :end-time="timeline.endTime"
      :current-time="currentTimestamp"
      :time-step="30 * 60 * 1000"
      class="timeline-scrollbar-container"
      @time-changed="onTimeChanged"
      @range-changed="onRangeChanged"
    />
  </div>
</template>

<script>
import WidgetContextBuilder from '@components/widgets/widget-context-builder'
import { WidgetTypeConstants } from '@components/widgets/constants'
import ChartView from '@/src/components/widgets/views/chart-view.vue'
import TimelineScrollbar from './timeline-scrollbar.vue'

export default {
  name: 'SloHistoryChart',
  components: {
    ChartView,
    TimelineScrollbar,
  },
  props: {
    currentTimestamp: {
      type: Number,
      default: undefined,
    },
    timeline: {
      type: Object,
      required: true,
    },
    disableSyncing: {
      type: Boolean,
      default: false,
    },
    slo: {
      type: Object,
      required: true,
    },
    hideLabel: {
      type: Boolean,
      default: false,
    },
    disableState: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Array,
      required: true,
    },
    counter: {
      type: String,
      required: true,
    },
    enableXAxis: {
      type: Boolean,
      default: false,
    },
    disableYAxis: {
      type: Boolean,
      default: false,
    },
    tooltipOutSide: {
      type: Boolean,
      default: false,
    },
    chartOptions: {
      type: Object,
      default: () => ({}),
    },
    showTimelineScrollbar: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      key: 1,
    }
  },
  computed: {
    widget() {
      const _that = this
      const context = new WidgetContextBuilder()
      context.addGroup('slo.metric')
      context.setCategory(WidgetTypeConstants.CHART)
      context.setWidgetType(WidgetTypeConstants.VERTICAL_BAR)
      context.setTimeLine(this.timeline)
      context
        .addCounterToGroup({
          counter: this.counter,
          aggrigateFn: '__NONE__',
          entityType: 'Slo',
          entities: this.slo.id,
        })
        .setWidgetProperties({
          styleSetting: {
            chartOptions: {
              ...this.chartOptions,
              boost: {
                enabled: false,
                allowForce: false,
              },
              chart: {
                ...((this.chartOptions || {}).chart || {}),
                events: {
                  load: function () {
                    _that._chart = this
                    if (_that.disableState) {
                      return
                    }
                    this.series[0].points.forEach((point) => {
                      if (point.category === _that.currentTimestamp) {
                        point.graphic.css({
                          opacity: 1,
                          color: point.color,
                        })
                      } else {
                        point.graphic.css({
                          opacity: 0.5,
                          color: point.color,
                        })
                      }
                    })
                  },
                },
              },
              ...(this.enableXAxis
                ? {}
                : {
                    xAxis: {
                      enabled: false,
                      lineWidth: 0,
                      minorGridLineWidth: 0,
                      lineColor: 'transparent',
                      gridLineWidth: 0,
                      minorTickLength: 0,
                      tickLength: 0,
                      labels: {
                        enabled: false,
                      },
                      title: {
                        text: null,
                      },
                    },
                  }),
              yAxis: {
                allowDecimal: false,
                ...(this.counter === 'status'
                  ? {
                      labels: {
                        format: ' ',
                        style: {
                          opacity: 0,
                        },
                      },
                      title: {
                        text: null,
                      },
                      gridLineWidth: 0,
                    }
                  : {}),
                ...(this.disableYAxis
                  ? {
                      lineWidth: 0,
                      minorGridLineWidth: 0,
                      lineColor: 'transparent',
                      minorTickLength: 0,
                      tickLength: 0,
                      labels: {
                        enabled: false,
                      },
                      enabled: false,
                      title: {
                        text: null,
                      },
                    }
                  : {}),
              },
              legend: {
                enabled: false,
              },
              plotOptions: {
                series: {
                  allowPointSelect: true,
                  turboThreshold: 0,
                  events: {
                    click: function () {},
                  },
                  point: {
                    events: {
                      select: function (event) {
                        setTimeout(() => {
                          this.graphic.css({
                            opacity: 1,
                            color: this.color,
                          })
                        }, 0)
                        _that.$emit('point-click', this.category)
                      },
                      unselect: function (event) {
                        setTimeout(() => {
                          this.graphic.css({
                            opacity: 0.5,
                            color: this.color,
                          })
                        }, 0)
                      },
                    },
                  },
                },
              },
            },
          },
        })
      return context.getContext()
    },
  },
  watch: {
    currentTimestamp(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (this.disableState) {
          return
        }
        if (this._chart) {
          this._chart.series[0].points.forEach((point) => {
            if (point.category === newValue) {
              point.graphic.css({
                opacity: 1,
                color: point.color,
              })
            } else {
              point.graphic.css({
                opacity: 0.5,
                color: point.color,
              })
            }
          })
        }
      }
    },
    timeline: {
      handler(newValue, oldValue) {
        if (
          newValue &&
          oldValue &&
          JSON.stringify(newValue) !== JSON.stringify(oldValue)
        ) {
          this.key += 1
        }
      },
    },
  },
  methods: {
    onTimeChanged(timestamp) {
      this.$emit('point-click', timestamp)
    },
    onRangeChanged(range) {
      this.$emit('range-changed', range)
    },
  },
}
</script>

<style scoped>
.timeline-scrollbar-container {
  position: absolute;
  bottom: -40px;
  left: 0;
  width: 100%;
}
</style>
