<template>
  <FlotoDrawer
    :open="open"
    wrap-class-name="ant-drawer-close-btn-center"
    width="60%"
    @hide="handleHide"
  >
    <template v-slot:title>
      <GroupProvider>
        <div v-if="sloMonitor" class="w-full flex justify-between items-center">
          <div class="flex items-center">
            <MButton
              shape="circle"
              class="squared-button mr-2"
              variant="neutral-lightest"
            >
              <MonitorType
                :type="sloMonitor['type']"
                disable-tooltip
                :center="false"
                class="p-1"
              />
            </MButton>

            <div class="flex flex-col w-11/12">
              <div class="flex items-center">
                <h5
                  class="text-ellipsis mb-0 mr-2 page-text-color"
                  :title="item.monitor"
                  >{{ item.monitor }}
                </h5>
                <span class="text-neutral-light font-500 text-xs mt-1">
                  {{ ` | ${sloMonitor.ip} | ${sloMonitor.type} | ` }}
                </span>

                <SloStatusBadge
                  class="ml-1"
                  :status="
                    item.slo_object_status_last || item.slo_instance_status_last
                  "
                  :use-server-status="false"
                />
              </div>

              <div class="inline-flex flex-grow-0 min-w-0 flex-shrink-0">
                <GroupPicker
                  :value="sloMonitor.groups"
                  disabled
                  :wrap="false"
                  style="flex: none"
                />
                <LooseTags :value="sloMonitor.tags" disabled />
              </div>
            </div>
          </div>
        </div>
        <span v-else />
      </GroupProvider>
    </template>
    <template v-if="item && sloMonitor && slo">
      <FlotoContentLoader :loading="loading" class="flex flex-col flex-1">
        <MRow :gutter="0" class="w-full mt-2" style="min-height: 170px">
          <MCol :size="6" class="h-full">
            <MRow
              class="vue-grid-item rounded py-2 h-full flex flex-col mr-1 px-4"
              :gutter="0"
            >
              <MCol :size="12" class="flex">
                <h6 class="text-xs slo-summary-label ml-2">SLO Achieved </h6>
              </MCol>
              <div
                :size="12"
                class="w-full flex flex-col justify-around flex-1"
              >
                <div class="text-5xl text-secondary-green font-extrabold mb-2">
                  {{
                    sloStatistics.slo_object_achieved_percent_last_sort ||
                    sloStatistics.slo_instance_achieved_percent_last_sort
                  }}
                  %
                </div>

                <div class="flex flex-col w-full">
                  <div class="w-full flex justify-end mb-1"> &nbsp; </div>
                  <TargetIndicatorProgressBar
                    :achieved-percentage="
                      sloStatistics.slo_object_achieved_percent_last_sort ||
                      sloStatistics.slo_instance_achieved_percent_last_sort ||
                      0
                    "
                    :target-percentage="slo.sloTarget"
                    :violated-at="slo.sloWarning"
                    left-percentage-color="var(--secondary-red)"
                    achieved-color="var(--secondary-green)"
                  />
                  <div class="w-full flex justify-between mb-4">
                    <div class=""> &nbsp; </div>
                    <div class=""> &nbsp; </div>
                  </div>
                </div>
              </div>
            </MRow>
          </MCol>
          <MCol
            v-if="!forSloHistoryDrillDown"
            :size="6"
            style="padding-right: 0"
            class="h-full"
          >
            <MRow
              class="vue-grid-item rounded py-2 h-full flex flex-col ml-1 px-4"
              :gutter="0"
            >
              <MCol :size="12" class="flex">
                <h6 class="text-xs slo-summary-label ml-2 mb-0"
                  >Error Budget Left</h6
                >
              </MCol>
              <div
                :size="12"
                class="w-full flex flex-col justify-around flex-1"
              >
                <div
                  class="text-5xl font-extrabold"
                  :class="{
                    'text-secondary-red': errorBudgetRemainingTime <= 0,
                    'text-secondary-green': errorBudgetRemainingTime > 0,
                  }"
                >
                  {{
                    sloStatistics.slo_object_error_budget_left_percent_last ||
                    sloStatistics.slo_instance_error_budget_left_percent_last
                  }}
                </div>
                <div class="flex flex-col w-full">
                  <div class="w-full flex justify-end mb-1">
                    <div class="text-sm">
                      Remaining Time:
                      <span class="font-bold">{{
                        errorBudgetRemainingTime | duration
                      }}</span>
                    </div>
                  </div>
                  <TargetIndicatorProgressBar
                    :achieved-percentage="
                      sloStatistics.slo_object_error_budget_left_percent_last_sort ||
                      sloStatistics.slo_instance_error_budget_left_percent_last_sort ||
                      0
                    "
                  />
                  <div class="w-full flex justify-between">
                    <div class="text-sm text-ellipsis">
                      Violated Time:
                      <span class="font-bold">{{
                        (sloStatistics.slo_object_violated_seconds_last_sort ||
                          sloStatistics.slo_instance_violated_seconds_last_sort)
                          | duration
                      }}</span>
                    </div>
                    <div class="text-sm text-ellipsis">
                      Acceptable Violation Time:
                      <span class="font-bold">{{
                        slo.sloAcceptableViolationTime | duration
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </MRow>
          </MCol>
          <MCol
            v-if="forSloHistoryDrillDown"
            :size="6"
            style="padding-right: 0"
            class="h-full"
          >
            <MRow
              class="vue-grid-item rounded py-2 h-full flex flex-col ml-1 px-4"
              :gutter="0"
            >
              <MCol :size="12" class="flex">
                <h6 class="text-xs slo-summary-label ml-2 mb-0"
                  >Error Budget Left</h6
                >
              </MCol>
              <div
                :size="12"
                class="w-full flex flex-col justify-around flex-1"
              >
                <div
                  class="text-5xl font-extrabold"
                  :class="{ 'text-secondary-red': true }"
                >
                  {{
                    sloStatistics.slo_object_violated_percent_last ||
                    sloStatistics.slo_instance_violated_percent_last
                  }}
                </div>
                <div class="flex flex-col w-full">
                  <TargetIndicatorProgressBar
                    :achieved-percentage="
                      sloStatistics.slo_object_violated_percent_last_sort ||
                      sloStatistics.slo_instance_violated_percent_last_sort ||
                      0
                    "
                    achieved-color="var(--secondary-red)"
                  />
                  <div class="w-full flex justify-between">
                    <div class="text-sm text-ellipsis">
                      Violated Time:
                      <span class="font-bold">{{
                        (sloStatistics.slo_object_violated_seconds_last_sort ||
                          sloStatistics.slo_instance_violated_seconds_last_sort)
                          | duration
                      }}</span>
                    </div>
                    <div class="text-sm text-ellipsis">
                      Acceptable Violation Time:
                      <span class="font-bold">{{
                        slo.sloAcceptableViolationTime | duration
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </MRow>
          </MCol>
        </MRow>

        <MRow :gutter="0" class="w-full mt-2">
          <MCol :size="12">
            <MRow class="vue-grid-item rounded py-2" :gutter="0">
              <MCol :size="12">
                <h6 class="text-xs slo-summary-label ml-2">SLO Trend</h6>
              </MCol>
              <MCol :size="12" style="padding-right: 0">
                <div
                  :style="{ width: `100%` }"
                  class="self-center flex"
                  style="flex-shrink: 0; height: 100%; min-height: 80px"
                >
                  <SloXRangeTrend
                    :slo="slo"
                    :slo-monitor="{
                      entity: item['entity.id'],
                    }"
                    for-monitor-drill-down
                    :item="item"
                    :slo-timeline="sloTimeline"
                  />
                </div>
              </MCol>
            </MRow>
          </MCol>
        </MRow>

        <MRow v-if="!isAvailabilitySlo" :gutter="0" class="w-full mt-2">
          <MCol :size="12">
            <MRow class="vue-grid-item rounded py-2" :gutter="0">
              <MCol :size="12">
                <h6 class="text-xs slo-summary-label ml-2">SLO Metric</h6>
              </MCol>
              <MCol :size="12" style="padding-right: 0">
                <div
                  :style="{ width: `100%` }"
                  class="self-center flex"
                  style="flex-shrink: 0; height: 100%; min-height: 280px"
                >
                  <WidgetContainer
                    :widget="lineChart"
                    is-preview
                    :height="280"
                    watch-widget
                  />
                </div>
              </MCol>
            </MRow>
          </MCol>
        </MRow>
        <MRow :gutter="0" class="w-full mt-2" style="min-height: 190px">
          <MCol :size="6">
            <MRow class="vue-grid-item rounded py-2 mr-1 h-full" :gutter="0">
              <MCol :size="12">
                <h6 class="text-xs slo-summary-label ml-2"
                  >Error Budget Burndown
                </h6>
              </MCol>
              <MCol :size="12" style="padding-right: 0">
                <div
                  :style="{ width: `100%` }"
                  class="self-center flex"
                  style="flex-shrink: 0; height: 100%"
                >
                  <WidgetContainer
                    :widget="burndownWidget"
                    is-preview
                    :height="190"
                    watch-widget
                  />
                </div>
              </MCol>
            </MRow>
          </MCol>
          <MCol :size="6" style="padding-right: 0">
            <MRow class="vue-grid-item rounded py-2 ml-1 h-full" :gutter="0">
              <MCol :size="12">
                <h6 class="text-xs slo-summary-label ml-2 mb-0">Burn Rate</h6>
              </MCol>
              <MCol :size="12" style="padding-right: 0">
                <div
                  :style="{ width: `100%` }"
                  class="self-center flex"
                  style="flex-shrink: 0; height: 100%"
                >
                  <WidgetContainer
                    :widget="burnRateWidget"
                    is-preview
                    :height="190"
                    watch-widget
                  />
                </div>
              </MCol>
            </MRow>
          </MCol>
        </MRow>
        <MRow
          :gutter="0"
          class="w-full mt-2 flex flex-1"
          style="min-height: 280px"
        >
          <MCol :size="12">
            <SloMonitorDrillDownGrid
              :slo="slo"
              :slo-monitor="sloMonitor"
              :global-slo-data="globalSloData"
              :grid-item="item"
              :slo-timeline="sloTimeline"
            />
          </MCol>
        </MRow>
      </FlotoContentLoader>
    </template>
  </FlotoDrawer>
</template>

<script>
import { objectDBWorker } from '@/src/workers'
import MonitorType from '@components/monitor-type.vue'
import GroupProvider from '@components/data-provider/group-provider.vue'
import LooseTags from '@components/loose-tags.vue'
import WidgetContainer from '@components/widgets/views/container.vue'
import TargetIndicatorProgressBar from '@components/common/slo-progress-bar.vue'

import SloXRangeTrend from './slo-x-range-trend.vue'
import SloMonitorDrillDownGrid from './slo-monitor-drill-down-grid.vue'
import SloStatusBadge from './slo-status-badge.vue'

import { getConfiguredMonitorsApi } from '../slo-api'

import {
  getErrorBudgetBurndownWidgetDef,
  getBurnRateWidgetDef,
  getSloMetricWidgetDef,
} from '../helpers/slo-overview'
import { AVAILABLE_SLO_TYPES } from '../helpers/slo'

export default {
  name: 'SloMonitorDrillDown',
  components: {
    TargetIndicatorProgressBar,
    SloXRangeTrend,
    WidgetContainer,
    SloMonitorDrillDownGrid,
    MonitorType,
    GroupProvider,
    LooseTags,
    SloStatusBadge,
  },
  props: {
    item: {
      type: Object,
      required: true,
    },
    open: {
      type: Boolean,
      default: false,
    },
    globalSloData: {
      type: Object,
      required: true,
    },
    slo: {
      type: Object,
      required: true,
    },
    sloTimeline: {
      type: Object,
      required: true,
    },
    forSloHistoryDrillDown: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      sloMonitor: {},
      sloStatistics: {},
      loading: true,
    }
  },
  computed: {
    burndownWidget() {
      return getErrorBudgetBurndownWidgetDef(
        this.slo,
        this.sloTimeline,
        this.item,
        true
      )
    },
    burnRateWidget() {
      return getBurnRateWidgetDef(this.slo, this.sloTimeline, this.item, true)
    },

    errorBudgetRemainingTime() {
      const violatedSecondsKey = this.isInstanceSlo
        ? 'slo_instance_violated_seconds_last_sort'
        : 'slo_object_violated_seconds_last_sort'
      const diff =
        this.slo.sloAcceptableViolationTime -
        this.sloStatistics[violatedSecondsKey]

      return diff < 0 ? 0 : diff
    },
    isAvailabilitySlo() {
      return this.slo.sloType === AVAILABLE_SLO_TYPES.AVAILABILITY
    },
    lineChart() {
      return getSloMetricWidgetDef(
        this.slo,
        this.item,
        this.item['slo_instance'],
        this.sloTimeline
      )
    },
    isInstanceSlo() {
      return this.slo.sloFor !== 'monitor'
    },
  },
  created() {
    this.getSloData()
  },

  methods: {
    handleHide() {
      this.loading = true
      this.$emit('hide')
    },
    async getSloData() {
      this.loading = true

      const monitor = await objectDBWorker.getObjectById(this.item['entity.id'])
      this.sloMonitor = monitor

      const sloStatistics = await getConfiguredMonitorsApi(
        this.slo,
        this.item['entity.id'],
        this.item['slo_instance']
      )

      this.sloStatistics = Object.freeze(sloStatistics[0] || {})

      this.loading = false
    },
  },
}
</script>
