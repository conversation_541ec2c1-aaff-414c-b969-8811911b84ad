<template>
  <div class="px-2 flex flex-col h-full min-h-0">
    <RenderLessSearching
      v-if="sloData.length"
      :data="sloData"
      :columns="columns"
      :search-term="searchTerm"
      :filters="filters"
    >
      <template v-slot="{ processingData, dataItems }">
        <FlotoFixedView :gutter="0" class="h-full w-full">
          <FlotoContentLoader v-if="processingData" loading />

          <RecycleScroller
            v-else-if="(dataItems.data || []).length"
            :items="chunkedData(dataItems.data)"
            :item-size="200"
            class="card-scroller"
            key-field="guid"
          >
            <template v-slot="{ item }">
              <MRow class="card-row" :gutter="0">
                <MCol
                  v-for="(card, index) in item.items"
                  :key="index"
                  class="pr-2"
                  :size="3"
                >
                  <SloCard :card="card" @drill-down="navigateToSloDetail" />
                </MCol>
              </MRow>
            </template>
          </RecycleScroller>

          <FlotoNoData
            v-else
            hide-svg
            header-tag="h5"
            icon="exclamation-triangle"
            variant="neutral"
            message="No records available"
          />
        </FlotoFixedView>
      </template>
    </RenderLessSearching>
    <FlotoNoData v-else />
  </div>
</template>

<script>
import { generateId } from '@utils/id'
import RenderLessSearching from '@components/crud/render-less-searching.vue'
import SloCard from './slo-card.vue'
import Chunk from 'lodash/chunk'

export default {
  name: 'SloKpiView',
  components: {
    RenderLessSearching,
    SloCard,
  },
  props: {
    sloData: {
      type: Array,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
    },

    searchTerm: {
      type: String,
      default: '',
    },
    filters: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {}
  },
  computed: {},
  methods: {
    navigateToSloDetail(row) {
      this.$emit('drillDown', row)
    },

    chunkedData(data) {
      return Chunk(data, 4).map((chunk) => ({
        guid: generateId(),
        items: chunk,
      }))
    },
  },
}
</script>
