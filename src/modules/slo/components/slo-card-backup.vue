<template>
  <MCol
    :size="3"
    class="flex flex-col h-full cursor-pointer"
    @click="handelDrilldown"
  >
    <div
      class="p-2 h-full flex flex-col"
      :style="{
        background: 'var(--widget-background)',
        border: '1px solid var(--widget-border-color)',
        borderRadius: '2px',
        boxShadow: '0 0 0 1px var(--widget-box-shadow)',
      }"
    >
      <div class="flex items-center">
        <Severity :severity="slo.severity || 'UNKNOWN'" class="mr-2" />
        <h6 class="mb-0 text-ellipsis overflow-hidden">
          {{ slo.pathName }}
        </h6>
      </div>
      <MRow :gutter="8" class="flex items-center mt-6">
        <MCol :size="6">
          <div class="text-xs" style="color: var(--neutral-theme-color)"
            >Source</div
          >
          <div>
            <AgentPicker :value="slo.source" disabled text-only />
          </div>
        </MCol>
        <MCol :size="6">
          <div class="text-xs" style="color: var(--neutral-theme-color)"
            >Destination</div
          >
          <div>{{ slo.destination }}</div>
        </MCol>
      </MRow>
      <MRow :gutter="8" class="flex items-center mt-6">
        <MCol :size="6">
          <div style="font-size: 1.25rem; font-weight: 600">
            {{ slo.latency }}
          </div>
          <div class="text-xs" style="color: var(--neutral-theme-color)"
            >Latency</div
          >
        </MCol>
        <MCol :size="6">
          <div style="font-size: 1.25rem; font-weight: 600">{{
            slo.packetLoss
          }}</div>
          <div class="text-xs" style="color: var (--neutral-theme-color)"
            >Packet Loss</div
          >
        </MCol>
      </MRow>
      <MDivider class="my-2" />
      <MRow :gutter="8" class="flex flex-1">
        <MCol :size="6" class="flex flex-col">
          <div class="text-xs" style="color: var(--neutral-theme-color)"
            >Availability</div
          >
          <div class="flex items-end flex-1">
            <div class="flex-1 h-full">
              <SloHistoryChart
                v-if="availabilityChart.length"
                disable-syncing
                :slo="slo"
                :chart-options="{
                  chart: { margin: [7, 5, 7, 0], borderColor: 'transparent' },
                }"
                hide-label
                counter="status"
                tooltip-out-side
                disable-y-axis
                disable-x-axis
                disable-state
                :timeline="timeline"
                :data="availabilityChart"
              />
            </div>
            <div class="flex-shrink-0 text-base">{{
              slo.availabilityPercent !== undefined
                ? `${slo.availabilityPercent}%`
                : '---'
            }}</div>
          </div>
        </MCol>
        <MCol :size="6">
          <div class="text-xs" style="color: var(--neutral-theme-color)"
            >Last Polled at</div
          >
          <div class="text-xs">{{ slo.lastPolledTime | datetime }}</div>
        </MCol>
      </MRow>
    </div>
  </MCol>
</template>

<script>
import SortBy from 'lodash/sortBy'
import Severity from '@components/severity.vue'
import AgentPicker from '@components/data-picker/agent-picker.vue'
import Moment from 'moment'
import SloHistoryChart from './slo-history-chart.vue'
import { UserPreferenceComputed } from '@/src/state/modules/user-preference/helpers'

export default {
  name: 'SloCard',
  components: {
    Severity,
    AgentPicker,
    SloHistoryChart,
  },
  props: {
    slo: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      availabilityChart: [],
      timeline: { selectedKey: '-1h' },
    }
  },
  computed: {
    ...UserPreferenceComputed,
    offset() {
      return Moment().tz(this.timezone).utcOffset() * 60 * 1000
    },
  },
  created() {
    this.getAvailabilityData()
  },
  methods: {
    async getAvailabilityData() {
      let statusSeries = [
        {
          name: 'Availability',
          data: [],
          formattedValues: [],
        },
      ]
      SortBy(this.slo.availabilityData, 'timestamp').forEach((item) => {
        statusSeries[0].data.push({
          x: item.timestamp + this.offset,
          y: 1,
          color:
            item['status.value'] === 1
              ? 'var(--severity-down)'
              : 'var(--severity-clear)',
        })
        statusSeries[0].formattedValues.push(
          item['status.value'] === 1 ? 'Down' : 'Up'
        )
      })
      this.availabilityChart = statusSeries
    },
    handelDrilldown() {
      this.$router.push(
        this.$currentModule.getRoute('graph', {
          params: {
            id: this.slo.id,
          },
        })
      )
    },
  },
}
</script>
