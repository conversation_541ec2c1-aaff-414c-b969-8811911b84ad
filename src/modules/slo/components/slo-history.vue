<template>
  <div
    class="min-h-0 flex flex-col flex-1 w-full px-2 pt-2"
    style="background: var(--page-background-color)"
  >
    <FlotoContentLoader v-if="loading" class="flex flex-col flex-1" loading />
    <template v-else>
      <div class="mr-2 flex justify-between">
        <MInput v-model="searchTerm" class="search-box" placeholder="Search">
          <template v-slot:prefix>
            <MIcon name="search" />
          </template>
          <template v-if="searchTerm" v-slot:suffix>
            <MIcon
              name="times-circle"
              class="text-neutral-light cursor-pointer"
              @click="searchTerm = undefined"
            />
          </template>
        </MInput>
      </div>
      <MGrid
        class="min-w-0"
        :search-term="searchTerm"
        :columns="columns"
        :data="data"
      >
        <template v-slot:sloCycleName="{ item }">
          <a class="slo-name" @click="navigateToSloDetail(item)">
            {{ slo.sloCycleName }}
          </a>
        </template>
        <template v-slot:status="{ item }">
          <SloStatusBadge
            :status="item.slo_status_last"
            :use-server-status="false"
          />
        </template>
        <template v-slot:target>
          <MTag
            :closable="false"
            variant="primary"
            rounded
            class="tag-primary inline-flex items-center"
          >
            {{ slo.sloTarget }}%
          </MTag>
        </template>
        <template v-slot:slo_achieved_percent_last="{ item }">
          <MTag
            :closable="false"
            variant="primary"
            rounded
            class="tag-green inline-flex items-center"
          >
            {{ item.slo_achieved_percent_last }}
          </MTag>
        </template>
        <template v-slot:slo_violated_percent_last="{ item }">
          <MTag
            :closable="false"
            variant="primary"
            rounded
            class="tag-red inline-flex items-center"
          >
            {{ item.slo_violated_percent_last }}
          </MTag>
        </template>
      </MGrid>
    </template>
  </div>
</template>

<script>
import { getSLOProfileCyclesApiByFilter } from '@src/modules/settings/service-level-objective/slo-profile-api'
import { getGlobalSLOCalculationDataApiForHistory } from '../slo-api'
import SloStatusBadge from './slo-status-badge.vue'

export default {
  name: 'SloHistory',
  components: {
    SloStatusBadge,
  },
  props: {
    slo: {
      type: Object,
      required: true,
    },
    sloTimeline: {
      type: Object,
      required: true,
    },
  },
  data() {
    const columns = [
      {
        key: 'sloCycleName',
        name: 'Name',

        searchable: true,
        sortable: true,
        disable: true,
      },
      {
        key: 'target',
        name: 'Target',

        searchable: true,
        sortable: true,
      },
      {
        key: 'slo_achieved_percent_last',
        name: 'Achieved',
        searchable: true,
        sortable: true,
      },
      {
        key: 'slo_violated_percent_last',
        name: 'Violation',

        searchable: true,
        sortable: true,
      },
      {
        key: 'slo_error_budget_left_seconds_last',
        name: 'Error Budget Left',
        searchable: true,
        sortable: true,
      },
      {
        key: 'status',
        name: 'Status',

        searchable: true,
        sortable: true,
      },
    ]

    return {
      searchTerm: '',
      columns,
      data: [],
      loading: true,
    }
  },
  async created() {
    await this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true
      return getSLOProfileCyclesApiByFilter(this.slo.sloProfileId)
        .then((data) => {
          const filteredData = data.filter(
            (item) => item.cycleId !== this.slo.cycleId
          )
          return getGlobalSLOCalculationDataApiForHistory(filteredData)
        })
        .then((data) => {
          this.data = data
          this.loading = false
        })
    },
    navigateToSloDetail(item) {
      // todo: use  this.currentRoute.getRoute function
      this.$router.push({
        name: 'slo.history-detail',
        params: { id: +item.slo_id, cycleId: +item.slo },
      })
    },
  },
}
</script>
