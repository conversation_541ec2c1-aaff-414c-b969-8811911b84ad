<template>
  <div :style="{ width: width }">
    <GroupProvider v-if="type === 'node'">
      <template v-slot="{ loading }">
        <FlotoContentLoader :loading="loading" style="min-height: 100px">
          <NodeTooltip :data="options" />
        </FlotoContentLoader>
      </template>
    </GroupProvider>
    <EdgeTooltip v-else :data="options" :source="source" :target="target" />
  </div>
</template>

<script>
import GroupProvider from '@/src/components/data-provider/group-provider.vue'
import NodeTooltip from './node-tooltip.vue'
import EdgeTooltip from './edge-tooltip.vue'
export default {
  name: 'Tooltip',
  components: {
    NodeTooltip,
    EdgeTooltip,
    GroupProvider,
  },
  props: {
    type: {
      type: String,
      required: true,
    },
    source: {
      type: Object,
      default: undefined,
    },
    target: {
      type: Object,
      default: undefined,
    },
    options: {
      type: Object,
      required: true,
    },
  },
  computed: {
    width() {
      return this.type === 'edge' ? '400px' : '400px'
    },
  },
}
</script>
