<template>
  <div class="slo-card vue-grid-item" @click="$emit('drill-down', card)">
    <div class="card-header">
      <div class="card-title">{{ card.name }}</div>
      <SloStatusBadge
        :status="card.slo_status_last_sort || card.slo_status_last"
      />
    </div>

    <div class="card-content">
      <div class="info-row">
        <div class="info-item">
          <div class="info-label">Type</div>
          <div class="info-value">{{ card.type }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Frequency</div>
          <div class="frequency-badge">
            <MTag
              :closable="false"
              variant="primary"
              rounded
              class="tag-unknown inline-flex items-center"
            >
              {{ card.frequency }}
            </MTag>
          </div>
        </div>
      </div>

      <div class="metrics-row">
        <div class="metric-item">
          <div class="metric-value text-primary">{{ card.target }}%</div>
          <div class="metric-label">Target</div>
        </div>
        <div class="metric-item">
          <div class="metric-value text-secondary-green">{{
            card.slo_achieved_percent_last
          }}</div>
          <div class="metric-label">Achieved</div>
        </div>
        <div class="metric-item">
          <div class="metric-value text-secondary-red">{{
            card.slo_violated_percent_last
          }}</div>
          <div class="metric-label">Violation</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SloStatusBadge from './slo-status-badge.vue'

export default {
  name: 'SloCard',
  components: {
    SloStatusBadge,
  },
  props: {
    card: {
      type: Object,
      required: true,
    },
  },
}
</script>

<style lang="less" scoped>
.slo-card {
  display: flex;
  flex-direction: column;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 16px;
}

.card-title {
  flex: 1;
  width: 70%;
  margin-right: 8px;
  overflow: hidden;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.43;
  text-overflow: ellipsis;
  white-space: nowrap;
  opacity: 0.8;
}

.status-badge {
  padding: 2px 8px;
  font-size: 11px;
  font-weight: 500;
  line-height: 1.5;
  white-space: nowrap;
  border-radius: 16px;
}

.card-content {
  display: flex;
  flex: 1;
  flex-direction: column;
}

.info-row {
  display: flex;
  justify-content: space-between;
  width: 65%;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  width: 50%;
}

.info-label {
  font-size: 12px;
  font-weight: 400;
  line-height: 1.5;
  color: var(--neutral-light);
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  line-height: 1;
  opacity: 0.8;
}

.frequency-badge {
  padding: 1px 8px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.5;
  white-space: nowrap;
  border-radius: 16px;
}

.metrics-row {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 16px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
  width: 33%;
}

.metric-value {
  font-size: 20px;
  font-weight: 600;
  line-height: 1;
}

.metric-label {
  font-size: 12px;
  font-weight: 400;
  line-height: 1.5;
  color: var(--neutral-light);
}
</style>
