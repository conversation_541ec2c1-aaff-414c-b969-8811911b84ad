<template>
  <div class="flex flex-col w-full">
    <div class="flex flex-col my-2 border-bot py-1 w-full">
      <div>
        <div class="flex items-center">
          <h6 class="m-0 flex items-center w-full overflow-hidden">
            <Severity class="mr-1 flex-shrink-0" :severity="data.severity" />
            <!-- {{ source.name }} -->
            <span
              :title="source.name"
              class="truncate text-ellipsis"
              style="width: 37.5%"
              >{{ source.name }}</span
            >
            <span
              class="text-neutral-light mx-4 flex-shrink-0 whitespace-nowrap"
              style="width: 25%"
            >
              ---------►
            </span>
            <!-- {{ target.name }} -->
            <span
              :title="target.name"
              class="truncate text-ellipsis"
              style="width: 37.5%"
              >{{ target.name }}</span
            >
          </h6>
        </div>
      </div>
    </div>
    <div class="flex flex-col w-full py-2 border-bot">
      <div class="flex items-center mb-4">
        <div class="w-1/3 text-neutral-light">Latency</div>
        <div class="w-2/3">
          <div class="flex items-center">
            <div class="flex flex-col flex-1">
              <div class="text-neutral-light">Min</div>
              <div>{{ data.hop['slo.min.latency.ms'] }}ms</div>
            </div>
            <div class="flex flex-col flex-1">
              <div class="text-neutral-light">Avg.</div>
              <div>{{ data.hop['slo.latency.ms'] }}ms</div>
            </div>
            <div class="flex flex-col flex-1">
              <div class="text-neutral-light">Max</div>
              <div>{{ data.hop['slo.max.latency.ms'] }}ms</div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex items-center mb-4">
        <div class="w-1/3 text-neutral-light">Packet Loss</div>
        <div class="w-2/3">
          <MTag
            :closable="false"
            rounded
            class="cursor-auto inline-flex items-center"
          >
            {{ data.hop['slo.packet.lost.percent'] }}%
          </MTag>
        </div>
      </div>
      <!-- <div class="flex items-center mb-4">
        <div class="w-1/3 text-neutral-light">Transit Liklihood</div>
        <div class="w-2/3"> STATIC </div>
      </div> -->
    </div>
  </div>
</template>

<script>
import Severity from '@/src/components/severity.vue'

export default {
  name: 'EdgeTooltip',
  components: {
    Severity,
  },
  props: {
    data: {
      type: Object,
      required: true,
    },
    source: {
      type: Object,
      default: undefined,
    },
    target: {
      type: Object,
      default: undefined,
    },
  },
}
</script>
