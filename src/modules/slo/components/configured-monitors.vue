<template>
  <FlotoFixedView :gutter="0">
    <FlotoContentLoader :loading="loading">
      <div class="flex flex-1 min-h-0 flex-col">
        <div class="flex justify-between items-center mt-2">
          <h6 class="text-xs pl-1 ml-2 mb-0">Configured {{ sloFor }}</h6>

          <MInput
            v-model="searchTerm"
            class="search-box mr-2"
            placeholder="Search"
            name="search"
          >
            <template v-slot:prefix>
              <MIcon name="search" />
            </template>
            <template v-if="searchTerm" v-slot:suffix>
              <MIcon
                name="times-circle"
                class="text-neutral-light cursor-pointer"
                @click="searchTerm = undefined"
              />
            </template>
          </MInput>
        </div>

        <div class="flex flex-col flex-1 min-h-0">
          <Transition name="placeholder" mode="out-in">
            <MGrid
              ref="gridRef"
              :data="rows"
              :columns="availableColumns"
              :search-term="searchTerm"
              :paging="false"
            >
              <template v-slot:monitor="{ item }">
                <SloDrillDownSlot
                  v-if="!isInstanceSlo"
                  :item="item"
                  :global-slo-data="globalSloData"
                  :slo="slo"
                  :slo-timeline="sloTimeline"
                  :display-value="item.monitor"
                  :for-slo-history-drill-down="forSloHistoryDrillDown"
                />

                <span v-else>
                  {{ item.monitor }}
                </span>

                <!-- <span class="flex">
                  <MonitorName :value="item.monitor" :row="item" />
                </span> -->
              </template>

              <template v-slot:slo_instance="{ item }">
                <SloDrillDownSlot
                  :item="item"
                  :global-slo-data="globalSloData"
                  :slo="slo"
                  :slo-timeline="sloTimeline"
                  :display-value="item.slo_instance"
                  :for-slo-history-drill-down="forSloHistoryDrillDown"
                />

                <!-- <span class="flex">
                  <MonitorName :value="item.monitor" :row="item" />
                </span> -->
              </template>

              <template v-slot:tag="{ item }">
                <LooseTags :tags="item.tag" :disabled="true" />
              </template>
              <template v-slot:slo_object_status_last="{ item }">
                <SloStatusBadge
                  :status="item.slo_object_status_last"
                  :use-server-status="false"
                />
              </template>
              <template v-slot:slo_instance_status_last="{ item }">
                <SloStatusBadge
                  :status="item.slo_instance_status_last"
                  :use-server-status="false"
                />
              </template>

              <template v-slot:slo_object_achieved_percent_last="{ item }">
                <MTag class="status-badge tag-green" rounded :closable="false">
                  {{ item.slo_object_achieved_percent_last }}
                </MTag>
              </template>
              <template v-slot:slo_instance_achieved_percent_last="{ item }">
                <MTag class="status-badge tag-green" rounded :closable="false">
                  {{ item.slo_instance_achieved_percent_last }}
                </MTag>
              </template>

              <template v-slot:slo_object_violated_percent_last="{ item }">
                <MTag class="status-badge tag-red" rounded :closable="false">
                  {{ item.slo_object_violated_percent_last }}
                </MTag>
              </template>
              <template v-slot:slo_instance_violated_percent_last="{ item }">
                <MTag class="status-badge tag-red" rounded :closable="false">
                  {{ item.slo_instance_violated_percent_last }}
                </MTag>
              </template>
            </MGrid>
          </Transition>
        </div>
      </div>
    </FlotoContentLoader>
  </FlotoFixedView>
</template>

<script>
import Capitalize from 'lodash/capitalize'

import { getConfiguredMonitorsApi } from '../slo-api'
// import MonitorName from '@components/widgets/views/grid/view-more/monitor-name.vue'
import LooseTags from '@components/loose-tags.vue'
import SloStatusBadge from './slo-status-badge.vue'
import GroupBy from 'lodash/groupBy'
import { SLO_STATUS } from '../helpers/slo'
import SloDrillDownSlot from './slo-drill-down-slot.vue'

export default {
  name: 'ConfiguredMonitors',
  components: {
    // MonitorName,
    LooseTags,
    SloStatusBadge,
    SloDrillDownSlot,
  },
  props: {
    slo: {
      type: Object,
      required: true,
    },
    globalSloData: {
      type: Object,
      required: true,
    },
    sloTimeline: {
      type: Object,
      required: true,
    },
    forSloHistoryDrillDown: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.gridItemActions = [
      { key: 'edit', name: 'Edit Role', icon: 'pencil' },
      { key: 'delete', name: 'Delete Role', icon: 'trash-alt', isDanger: true },
    ]
    return {
      searchTerm: undefined,
      loading: true,
      drillDownDevice: null,
      rows: [],
    }
  },

  computed: {
    sloFor() {
      return Capitalize(this.slo.sloFor)
    },
    isInstanceSlo() {
      return this.slo.sloFor !== 'monitor'
    },
    availableColumns() {
      const counterPrefix = this.isInstanceSlo ? 'slo_instance' : 'slo_object'

      return [
        ...(this.isInstanceSlo
          ? [
              {
                key: 'slo_instance',
                name: 'instance',
                searchable: true,
                sortable: true,
              },
            ]
          : []),
        {
          key: 'monitor',
          name: 'Monitor Name',
          searchable: true,
          sortable: true,
          align: 'left',
        },
        {
          key: 'object_ip',
          name: 'IP',
          searchable: true,
          sortable: true,
          align: 'left',
        },

        // {
        //   key: 'group',
        //   name: 'Group',
        //   searchable: false,
        //   sortable: false,
        //   // width: '200px',
        //   minWidth: '100px',
        //   align: 'center',
        // },

        {
          key: 'tag',
          name: 'Tag',
          sortable: true,
          searchable: true,
          align: 'center',
          minWidth: '100px',
        },
        {
          key: `${counterPrefix}_status_last`,
          name: 'Status',
          searchable: true,
          sortable: true,
          align: 'center',
          searchKey: `${counterPrefix}_status_last`,
        },
        {
          key: `${counterPrefix}_achieved_percent_last`,
          name: 'Achieved',
          searchable: true,
          sortable: true,
          align: 'center',
        },
        {
          key: `${counterPrefix}_violated_percent_last`,
          name: 'Violated',
          searchable: true,
          sortable: true,
          align: 'center',
        },

        // {
        //   key: 'actions',
        //   name: 'Actions',
        //   width: '120px',
        //   align: 'right',
        //   minWidth: '100px',
        // },
      ]
    },
  },
  created() {
    this.getConfiguredMonitors()
  },
  methods: {
    getConfiguredMonitors() {
      return getConfiguredMonitorsApi(this.slo).then((data) => {
        this.rows = data

        const groupBy = GroupBy(data, 'slo_object_status_last')

        const statusCount = {
          [SLO_STATUS.BREACHED]: groupBy[SLO_STATUS.BREACHED]?.length || 0,
          [SLO_STATUS.WARNING]: groupBy[SLO_STATUS.WARNING]?.length || 0,
          [SLO_STATUS.OK]: groupBy[SLO_STATUS.OK]?.length || 0,
          total: data.length,
        }

        this.$emit('slo-configured-monitors-data-loaded', statusCount)
        this.loading = false
      })
    },
    handleOpenMonitorSlo(item) {
      this.drillDownDevice = item
    },
  },
}
</script>
