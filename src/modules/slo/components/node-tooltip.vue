<template>
  <div class="flex flex-col w-full">
    <div class="flex flex-col my-2 border-bot py-1 w-full">
      <div v-if="data.monitor.id" class="flex">
        <MonitorType disable-tooltip :type="data.monitor.type" />
        <div class="ml-2 flex flex-col">
          <div class="flex items-center">
            <h6 class="m-0 inline-flex">
              <Severity class="mr-1" :severity="data.severity" />
              {{ data.monitor.name }}
            </h6>
            <div class="mx-2 text-neutral-light">|</div>
            <div>
              <span class="text-neutral-light">{{ data.monitor.ip }}</span>
            </div>
            <div class="mx-2 text-neutral-light">|</div>
            <div>
              <span class="text-neutral-light">{{ data.monitor.type }}</span>
            </div>
          </div>
          <div class="inline-flex flex-grow-0 min-w-0 flex-shrink-0 w-full">
            <div class="group-picker-container __panel relative">
              <GroupPicker
                use-popover
                :value="data.monitor.groups"
                disabled
                :wrap="false"
              />
            </div>
            <div class="loose-tags-container">
              <LooseTags :value="data.monitor.tags" disabled :wrap="false" />
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <div class="flex items-center">
          <h6 class="m-0 inline-flex">
            <Severity class="mr-1" :severity="data.severity" />
            {{ data.name }}
          </h6>
        </div>
      </div>
    </div>
    <div v-if="!data.root" class="flex flex-col w-full py-2 border-bot">
      <div class="flex items-center mb-4">
        <div class="w-1/3 text-neutral-light">Latency</div>
        <div class="w-2/3">
          <div class="flex items-center">
            <div class="flex flex-col flex-1">
              <div class="text-neutral-light">Min</div>
              <div>{{ data.hop['slo.min.latency.ms'] }}ms</div>
            </div>
            <div class="flex flex-col flex-1">
              <div class="text-neutral-light">Avg.</div>
              <div>{{ data.hop['slo.latency.ms'] }}ms</div>
            </div>
            <div class="flex flex-col flex-1">
              <div class="text-neutral-light">Max</div>
              <div>{{ data.hop['slo.max.latency.ms'] }}ms</div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex items-center mb-4">
        <div class="w-1/3 text-neutral-light">Packet Loss</div>
        <div class="w-2/3">
          <MTag
            :closable="false"
            rounded
            class="cursor-auto inline-flex items-center"
          >
            {{ data.hop['slo.packet.lost.percent'] }}%
          </MTag>
        </div>
      </div>
    </div>
    <div
      class="flex flex-col w-full py-2"
      :class="{ 'border-bot': !data.monitor.id }"
    >
      <div class="flex items-center">
        <div class="w-1/3 text-neutral-light">Alert(s)</div>
        <div class="w-2/3">
          <MTag
            :closable="false"
            rounded
            class="cursor-auto inline-flex items-center tag-red"
          >
            {{ data.alerts || 0 }}
          </MTag>
        </div>
      </div>
    </div>
    <div v-if="!data.monitor.id" class="flex flex-col w-full py-1">
      <div class="text-neutral-light mt-2">
        This device is not Monitored with AIOps platform.
      </div>
    </div>
  </div>
</template>

<script>
import MonitorType from '@/src/components/monitor-type.vue'
import Severity from '@/src/components/severity.vue'
import LooseTags from '@/src/components/loose-tags.vue'

export default {
  name: 'NodeTooltip',
  components: {
    MonitorType,
    Severity,
    LooseTags,
  },
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
}
</script>

<style lang="less" scoped>
.loose-tags-container {
  min-width: 0; /* Prevents overflow issues */
  max-width: 50%; /* Limits the width to 50% when content overflows */
}

.group-picker-container {
  min-width: 0; /* Prevents overflow issues */
  max-width: 50%; /* Limits the width to 50% when content overflows */
}
</style>
