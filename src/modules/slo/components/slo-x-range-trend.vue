<template>
  <div class="flex flex-col h-full flex-1">
    <FlotoContentLoader :loading="loading">
      <ChartView
        :data="{ series: xRangeDataSeries }"
        :tooltip-out-side="true"
        :widget="widget"
        :disable-server-zoom="true"
        :y-axis-label-width="0"
      />
    </FlotoContentLoader>
  </div>
</template>

<script>
import WidgetContextBuilder from '@components/widgets/widget-context-builder'
import { WidgetTypeConstants } from '@components/widgets/constants'
import ChartView from '@/src/components/widgets/views/chart-view.vue'
import Datetime from '@src/filters/datetime'
import Duration from '@src/filters/duration'
import Moment from 'moment'
import { getSLOTrendDataApi, getSLOTrendDataApiForInstance } from '../slo-api'
import { COLOR_TO_STATUS_FLAP_MAP, SLO_STATUS_FLAP_MAP } from '../helpers/slo'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import { SLO_FREQUENCY_OPTIONS } from '@src/modules/settings/service-level-objective/helpers/slo-profile'

export default {
  name: 'SloXRangeTrend',
  components: {
    ChartView,
  },
  props: {
    slo: {
      type: Object,
      required: true,
    },
    forMonitorDrillDown: {
      type: Boolean,
      default: false,
    },
    sloMonitor: {
      type: Object,
      default: null,
    },
    item: {
      type: Object,
      default: null,
    },
    sloTimeline: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      loading: true,
      disableYAxis: true,
      enableXAxis: true,
      seriesData: undefined,
    }
  },
  computed: {
    ...UserPreferenceComputed,

    widget() {
      const userTimezone = this.timezone
      const context = new WidgetContextBuilder()
      context.addGroup('netroute.metric')
      context.setCategory(WidgetTypeConstants.CHART)
      context.setWidgetType(WidgetTypeConstants.X_RANGE)
      context.setTimeLine(this.sloTimeline)
      context
        .addCounterToGroup({
          counter: 'netroute.metric',
          aggrigateFn: '__NONE__',
          entityType: 'NetRoute',
          entities: '1',
        })
        .setWidgetProperties({
          styleSetting: {
            chartOptions: {
              boost: {
                enabled: false,
                allowForce: false,
              },
              chart: {
                // spacing: [10, 10, 10, 10], // Add spacing to prevent overlap
                // margin: [10, 10, 10, 10], // Add margin for better positioning
              },
              xAxis: {
                crosshair: false,
                ...(this.slo?.frequency?.toLowerCase() ===
                SLO_FREQUENCY_OPTIONS[0].key.toLowerCase()
                  ? {
                      tickInterval:
                        this.slo?.frequency?.toLowerCase() ===
                        SLO_FREQUENCY_OPTIONS[0].key.toLowerCase()
                          ? 3600 * 1000
                          : undefined, // 1 hour in milliseconds
                      // Custom label formatter to convert to user timezone
                      labels: {
                        formatter: function () {
                          // Convert UTC timestamp to user's timezone
                          const userTime = Moment(this.value)
                            .tz(userTimezone)
                            .format('HH:mm')
                          return userTime
                        },
                      },
                    }
                  : {}),
              },
              yAxis: {
                allowDecimal: false,
                min: 0,
                max: 1,
                lineWidth: 0, // Hide y-axis line
                minorGridLineWidth: 0,
                lineColor: 'transparent',
                minorTickLength: 0,
                tickLength: 0,
                gridLineWidth: 0, // Remove horizontal grid lines
                labels: {
                  enabled: false, // Hide y-axis labels
                },
                enabled: true, // Keep enabled for proper scaling
                title: {
                  text: null,
                },
              },
              legend: {
                enabled: false,
              },
              plotOptions: {
                series: {
                  allowPointSelect: true,
                  turboThreshold: 0,
                },
                xrange: {
                  // X-range specific options
                  pointPadding: 0,
                  groupPadding: 0,
                  borderWidth: 0,
                  borderRadius: 2,
                },
              },
              tooltip: {
                enabled: true,
                useHTML: true,
                backgroundColor: 'var(--chart-tooltip-background)',
                borderWidth: 0,
                shadow: false,

                formatter: function () {
                  const startTime = this.points[0].x
                  const endTime = this.points[0].x2
                  const duration = endTime - startTime
                  const color = this.points[0].color
                  let status = COLOR_TO_STATUS_FLAP_MAP[color]
                  status = SLO_STATUS_FLAP_MAP[status]

                  if (!status) {
                    return `<div style="font-size: 11px; margin-bottom: 4px; color: #cccccc;">
                          End Time: ${Datetime(endTime / 1000)}
                        </div>
                        <div style="font-size: 11px; font-weight: 600; color: white;">
                          Duration: ${Duration(duration / 1000)}
                        </div>`
                  } else {
                    return `
                    <div style="display: flex; border-radius: 10px; overflow: hidden" class="shadow-lg hc-tooltip-bg highcharts-tooltip-container-class -m-2" >
                      <div style="flex: 2" class="left-side-container">
                        <div style="font-size: 11px; margin-bottom: 4px; color: #cccccc;">
                          Start Time: ${Datetime(startTime / 1000)}
                        </div>
                        <div style="font-size: 11px; margin-bottom: 4px; color: #cccccc;">
                          End Time: ${Datetime(endTime / 1000)}
                        </div>
                        <div style="font-size: 11px; font-weight: 600; color: white;">
                          Duration: ${Duration(duration / 1000)}
                        </div>
                      </div>
                      <div style="flex: 1; background-color: ${color}; padding: 12px; display: flex; align-items: center; justify-content: center;">
                        <span style="color: white; font-weight: bold; font-size: 12px; font-family: sans-serif;">
                          ${status}
                        </span>
                      </div>
                    </div>
                  `
                  }
                },
              },
            },
          },
        })
      return context.getContext()
    },

    xRangeDataSeries() {
      return [
        {
          name: 'Trend',
          pointWidth: 18, // Increased from 10 to 25 for taller bars
          pointPadding: 2, // Add padding between bars
          groupPadding: 0,
          data: this.seriesData || [],
          dataLabels: {
            enabled: false, // Disabled to prevent clutter
          },
        },
      ]
    },
  },

  async created() {
    await this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true
      let data = []

      if (this.forMonitorDrillDown) {
        data = await getSLOTrendDataApiForInstance(
          this.slo,
          this.forMonitorDrillDown ? [this.sloMonitor.entity] : undefined,
          this.item?.['slo_instance'],
          this.sloTimeline
        )
      } else {
        data = await getSLOTrendDataApi(this.slo, this.sloTimeline)
      }

      this.loading = false
      this.seriesData = data
    },
  },
}
</script>
