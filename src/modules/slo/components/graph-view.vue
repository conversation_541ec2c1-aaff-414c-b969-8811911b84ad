<template>
  <FlotoContentLoader v-if="loading" loading />
  <div
    v-else
    ref="mainContainer"
    class="flex flex-col h-full px-2 py-2 flex-1 has-bg"
  >
    <div class="flex flex-col min-h-0 flex-1">
      <Graph
        ref="graphRef"
        layout="slo"
        :center="true"
        auto-resize
        :fit="true"
        tooltip-trigger="hover"
        :graph-style="graphStyle"
        omit-default-style
        disable-preference-persist
        root-key="empty"
        :nodes="nodes"
        disable-bg
        :edges="edges"
        :animate-edges="false"
        :tooltip-component="ToolTipComponent"
        :node-html-labels="nodeHtmlLabels"
        @selected-edge="
          $emit('set-active-item', {
            ...$event,
            boxType: 'edge',
          })
        "
        @selected="$emit('set-active-item', { ...$event, boxType: 'node' })"
        @clear="$emit('set-active-item', null)"
        @node-expanded="handleNodeExpanded"
      />
    </div>
    <div
      class="flex-shrink-0 py-1 text-right overflow-x-auto min-w-0 relative z-50"
    >
      <MTag
        v-for="node in expandedNodes"
        :key="node.id"
        :closable="true"
        rounded
        class="cursor-auto inline-flex items-center"
        @close="handleCloseExpandedTag(node)"
      >
        {{ node.label }}
      </MTag>
    </div>
  </div>
</template>

<script>
import UniqBy from 'lodash/uniqBy'
import Graph from '@components/monitor-graph/graph.vue'
import Tooltip from './tooltip.vue'
import { getNodesAndEdges } from '../helpers/slo-drilldown'
import { UserPreferenceComputed } from '@/src/state/modules/user-preference/helpers'
import { DefaultStyle } from './graph-style'
import WidgetContextBuilder from '@components/widgets/widget-context-builder'
import { WidgetTypeConstants } from '@components/widgets/constants'
import { getWidgetResponseApi } from '@/src/utils/socket-event-as-api'
import { objectDBWorker, severityDBWorker } from '@/src/workers/index'
import Bus from '@/src/utils/emitter'
import { getMonitorsApi } from '../../settings/monitoring/monitors-api'
import { SEVERITY_MAP, transformMonitorForLocalDb } from '@/src/data/monitor'

export default {
  name: 'SloGraphView',
  components: {
    Graph,
  },
  props: {
    slo: {
      type: Object,
      required: true,
    },
    timestamp: {
      type: Number,
      required: true,
    },
    timeline: {
      type: Object,
      required: true,
    },
    metadata: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      loading: true,
      fullscreen: false,
      graph: [],
      nodes: [],
      edges: {},
      activeMonitor: null,
      expandedNodes: [],
    }
  },
  computed: {
    ...UserPreferenceComputed,
    nodeHtmlLabels() {
      return [
        {
          query: 'node',
          cssClass: 'cy-title',
          valign: 'top',
          halign: 'right',
          valignBox: 'top',
          halignBox: 'right',
          tpl: function (data) {
            if (data.alerts) {
              return `<p class="cy-title__alert px-1 rounded relative" style="background-color: var(--severity-critical); color: white;top: 15px;right: 10px;font-size: 0.5rem;">${data.alerts}</p>`
            }
            return ''
          },
        },
        {
          query: 'node.cy-expand-collapse-collapsed-node',
          cssClass: 'cy-title',
          valign: 'top',
          halign: 'right',
          valignBox: 'top',
          halignBox: 'right',
          tpl: function (data) {
            if (data.collapsedChildren) {
              let count = data.collapsedChildren.filter(
                (i) => !i.isEdge()
              ).length
              return `<p class="cy-title__alert px-1 rounded relative" style="top: 27px;right: 22px;font-size: 0.5rem;">(${count})</p>`
            }
            return ''
          },
        },
      ]
    },
    ToolTipComponent() {
      return Tooltip
    },
    graphStyle() {
      return DefaultStyle(this.theme)
    },
  },
  created() {
    this.fetchData()
  },
  methods: {
    getAlertCounts(monitorIds) {
      return new Promise((resolve, reject) => {
        Bus.$once('severity.query', (data) => {
          resolve(data)
        })
        Bus.$emit('server:event', {
          'event.type': 'severity.count.query',
          'event.context': {
            entities: monitorIds,
          },
        })
      })
    },
    fetchData() {
      this.loading = true
      const context = new WidgetContextBuilder()
      context.addGroup('slo.metric')
      context.setCategory(WidgetTypeConstants.CHART)
      context.setWidgetType(WidgetTypeConstants.COLUMN)
      context.setTimeLine(this.timeline)
      context.addCounterToGroup({
        counter: 'slo.event',
        aggrigateFn: '__NONE__',
        entityType: 'Slo',
        entities: this.slo.id,
        entityKeys: `${this.slo.id}^${this.timestamp / 1000}^slo.event`,
      })
      getWidgetResponseApi(context.generateWidgetDefinition())
        .then(async (data) => {
          if (data && data.length) {
            let objectCache = await objectDBWorker.getObjectsAsMap({}, [
              'id',
              'name',
            ])
            let getAllObjectFromApi = await getMonitorsApi(
              {
                params: {
                  'admin.role': 'yes',
                },
              },
              false
            )
            let allObjects = getAllObjectFromApi.result.map((object) =>
              transformMonitorForLocalDb(object, { transformTag: true })
            )

            let severityMap = await severityDBWorker.getSeverityMap({})

            let objectMap = {}
            for (let i = 0; i < allObjects.length; i++) {
              objectMap[allObjects[i].ip] = {
                ...allObjects[i],
                severity: severityMap[allObjects[i].id],
                severityNumber: SEVERITY_MAP[severityMap[allObjects[i].id]],
                hasPermission: !!objectCache[allObjects[i].id],
              }
            }
            const { nodes, edges, monitorIds } = getNodesAndEdges(
              this.slo,
              JSON.parse(data[0]['slo.event.value']),
              {
                theme: this.theme,
                monitors: objectMap,
                metadata: this.metadata,
              }
            )
            let alerts = await this.getAlertCounts(monitorIds)
            this.nodes = Object.freeze(
              nodes.map((node) => ({
                ...node,
                ...(node.data.monitor.id && alerts[node.data.monitor.id]
                  ? {
                      data: {
                        ...node.data,
                        alerts: Object.keys(alerts[node.data.monitor.id])
                          .filter((key) => key.toLowerCase() !== 'clear')
                          .reduce((acc, key) => {
                            return acc + alerts[node.data.monitor.id][key]
                          }, 0),
                      },
                    }
                  : {}),
              }))
            )
            this.edges = Object.freeze(edges)
          }
          this.loading = false
        })
        .catch((error) => {
          console.error(error)
        })
    },
    handleNodeExpanded(data) {
      // let label = `${data.ownedBy} (${
      //   data.collapsedChildren.filter((d) => !d.isEdge()).length
      // })`
      this.expandedNodes = UniqBy(
        [
          ...this.expandedNodes,
          {
            id: data.ownedBy,
            label: data.ownedBy,
          },
        ],
        'id'
      )
    },
    handleCloseExpandedTag(tag) {
      this.expandedNodes = this.expandedNodes.filter((t) => t.id !== tag.id)
      if (this.$refs.graphRef) {
        this.$refs.graphRef.collapseCollection(
          this.$refs.graphRef.cy.cy
            .nodes(':parent')
            .filter((node) => node.data('ownedBy') === tag.id)
        )
        setTimeout(() => {
          this.$refs.graphRef.cy.fit()
        }, 100)
      }
    },
  },
}
</script>
