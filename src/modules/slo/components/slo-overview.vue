<template>
  <FlotoContentLoader :loading="loading" class="flex flex-col flex-1">
    <div class="min-h-0 flex flex-col flex-1">
      <div class="flex flex-col min-h-0 flex-1 w-full mt-2 px-2">
        <MRow :gutter="8">
          <MCol
            v-if="!forSloHistoryDrillDown"
            :size="isAvailabilitySlo ? 6 : 9"
          >
            <MRow class="vue-grid-item rounded py-2" :gutter="0">
              <MCol :size="12">
                <h6 class="text-xs slo-summary-label ml-2"> SLO Summary</h6>
              </MCol>

              <MCol :size="isAvailabilitySlo ? 6 : 4" class="pl-2">
                <table class="item-list-table with-out-border">
                  <colgroup>
                    <col style="width: 150px" />
                    <col />
                  </colgroup>
                  <tbody>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details"
                          >Business Service Name</span
                        >
                      </td>
                      <td
                        class="text-ellipsis px-4 pt-2 pb-2"
                        style="font-size: 0.8rem"
                        >{{ slo.businessServiceName }}</td
                      >
                    </tr>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details">Status</span>
                      </td>
                      <td
                        class="px-4 pt-2 pb-2 text-ellipsis"
                        style="font-size: 0.8rem"
                      >
                        <SloStatusBadge
                          v-if="globalSloData.slo_status_last"
                          :status="globalSloData.slo_status_last"
                          :use-server-status="false"
                        />
                      </td>
                    </tr>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details"
                          >SLO Frequency
                        </span>
                      </td>
                      <td
                        class="px-4 pt-2 pb-2 text-ellipsis"
                        style="font-size: 0.8rem"
                        >{{ slo.frequency }}</td
                      >
                    </tr>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details"> Type </span>
                      </td>
                      <td
                        class="px-4 pt-2 pb-2 text-ellipsis"
                        style="font-size: 0.8rem"
                        >{{ slo.sloType }}</td
                      >
                    </tr>
                  </tbody>
                </table>
              </MCol>

              <MCol
                v-if="isAvailabilitySlo"
                :size="6"
                class="pl-10 border-left"
              >
                <table class="item-list-table with-out-border">
                  <colgroup>
                    <col style="width: 150px" />
                    <col />
                  </colgroup>
                  <tbody>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details"
                          >SLO Start Date/Time</span
                        >
                      </td>
                      <td
                        class="text-ellipsis px-4 pt-2 pb-2"
                        style="font-size: 0.8rem"
                        >{{ (slo.startDate / 1000) | datetime }}</td
                      >
                    </tr>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details">Tags</span>
                      </td>
                      <td
                        class="px-4 pt-2 pb-2 text-ellipsis"
                        style="font-size: 0.8rem"
                      >
                        <LooseTags
                          v-if="slo.tags"
                          :value="slo.tags"
                          disabled
                          rounded
                        />
                      </td>
                    </tr>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details"
                          >Elapsed Time
                        </span>
                      </td>
                      <td
                        v-if="elapsedTime"
                        class="px-4 pt-2 pb-2 text-ellipsis"
                        style="font-size: 0.8rem"
                        >{{ elapsedTime | duration }}</td
                      >
                    </tr>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details">
                          Remaining Time
                        </span>
                      </td>
                      <td
                        class="px-4 pt-2 pb-2 text-ellipsis"
                        style="font-size: 0.8rem"
                        >{{ remainingTime | duration }}</td
                      >
                    </tr>
                  </tbody>
                </table>
              </MCol>

              <template v-if="!isAvailabilitySlo">
                <MCol :size="4" class="pl-10 border-left">
                  <table class="item-list-table with-out-border">
                    <colgroup>
                      <col style="width: 150px" />
                      <col />
                    </colgroup>
                    <tbody>
                      <tr>
                        <td
                          class="text-neutral px-4 pt-2 pb-2 slo-summary-label"
                        >
                          <span class="text-ellipsis metric-details"
                            >SLO Start Date/Time
                          </span>
                        </td>
                        <td
                          class="text-ellipsis px-4 pt-2 pb-2"
                          style="font-size: 0.8rem"
                          >{{ (slo.startDate / 1000) | datetime }}</td
                        >
                      </tr>
                      <tr>
                        <td
                          class="text-neutral px-4 pt-2 pb-2 slo-summary-label"
                        >
                          <span class="text-ellipsis metric-details"
                            >SLO Metric</span
                          >
                        </td>
                        <td
                          class="px-4 pt-2 pb-2 text-ellipsis"
                          style="font-size: 0.8rem"
                          >{{ slo.counter || {} }}</td
                        >
                      </tr>
                      <tr>
                        <td
                          class="text-neutral px-4 pt-2 pb-2 slo-summary-label"
                        >
                          <span class="text-ellipsis metric-details"
                            >SLO Trigger Condition
                          </span>
                        </td>
                        <td
                          class="px-4 pt-2 pb-2 text-ellipsis"
                          style="font-size: 0.8rem"
                          :title="slo.triggerCondition"
                          >{{ slo.triggerCondition }}</td
                        >
                      </tr>
                      <tr>
                        <td
                          class="text-neutral px-4 pt-2 pb-2 slo-summary-label"
                        >
                          <span class="text-ellipsis metric-details">
                            Tags
                          </span>
                        </td>
                        <td
                          class="px-4 pt-2 pb-2 text-ellipsis"
                          style="font-size: 0.8rem"
                        >
                          <LooseTags
                            v-if="slo.tags"
                            :value="slo.tags"
                            disabled
                            rounded
                          />
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </MCol>
                <MCol :size="4" class="pl-10 border-left">
                  <table class="item-list-table with-out-border">
                    <colgroup>
                      <col style="width: 150px" />
                      <col />
                    </colgroup>
                    <tbody>
                      <tr>
                        <td
                          class="text-neutral px-4 pt-2 pb-2 slo-summary-label"
                        >
                          <span class="text-ellipsis metric-details"
                            >Elapsed Time</span
                          >
                        </td>
                        <td
                          class="text-ellipsis px-4 pt-2 pb-2"
                          style="font-size: 0.8rem"
                          >{{ elapsedTime | duration }}</td
                        >
                      </tr>
                      <tr>
                        <td
                          class="text-neutral px-4 pt-2 pb-2 slo-summary-label"
                        >
                          <span class="text-ellipsis metric-details"
                            >Remaining Time</span
                          >
                        </td>
                        <td
                          class="px-4 pt-2 pb-2 text-ellipsis"
                          style="font-size: 0.8rem"
                          >{{ remainingTime | duration }}</td
                        >
                      </tr>
                    </tbody>
                  </table>
                </MCol>
              </template>
            </MRow>
          </MCol>
          <MCol v-if="forSloHistoryDrillDown" :size="isAvailabilitySlo ? 6 : 9">
            <MRow class="vue-grid-item rounded py-2" :gutter="0">
              <MCol :size="12">
                <h6 class="text-xs slo-summary-label ml-2"> SLO Summary</h6>
              </MCol>

              <MCol :size="isAvailabilitySlo ? 6 : 4" class="pl-2">
                <table class="item-list-table with-out-border">
                  <colgroup>
                    <col style="width: 150px" />
                    <col />
                  </colgroup>
                  <tbody>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details"
                          >Business Service Name</span
                        >
                      </td>
                      <td
                        class="text-ellipsis px-4 pt-2 pb-2"
                        style="font-size: 0.8rem"
                        >{{ slo.businessServiceName }}</td
                      >
                    </tr>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details">Status</span>
                      </td>
                      <td
                        class="px-4 pt-2 pb-2 text-ellipsis"
                        style="font-size: 0.8rem"
                      >
                        <SloStatusBadge
                          v-if="globalSloData.slo_status_last"
                          :status="globalSloData.slo_status_last"
                          :use-server-status="false"
                        />
                      </td>
                    </tr>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details"
                          >SLO Frequency
                        </span>
                      </td>
                      <td
                        class="px-4 pt-2 pb-2 text-ellipsis"
                        style="font-size: 0.8rem"
                        >{{ slo.frequency }}</td
                      >
                    </tr>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details"> Type </span>
                      </td>
                      <td
                        class="px-4 pt-2 pb-2 text-ellipsis"
                        style="font-size: 0.8rem"
                        >{{ slo.sloType }}</td
                      >
                    </tr>
                    <tr v-if="isAvailabilitySlo">
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details"
                          >SLO Start Date/Time
                        </span>
                      </td>
                      <td
                        class="text-ellipsis px-4 pt-2 pb-2"
                        style="font-size: 0.8rem"
                        >{{ (slo.startDate / 1000) | datetime }}</td
                      >
                    </tr>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details">Tags</span>
                      </td>
                      <td
                        class="px-4 pt-2 pb-2 text-ellipsis"
                        style="font-size: 0.8rem"
                      >
                        <LooseTags
                          v-if="slo.tags"
                          :value="slo.tags"
                          disabled
                          rounded
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </MCol>

              <MCol
                v-if="isAvailabilitySlo"
                :size="6"
                class="pl-10 border-left"
              >
                <table class="item-list-table with-out-border">
                  <colgroup>
                    <col style="width: 150px" />
                    <col />
                  </colgroup>
                  <tbody>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details">
                          Start Time
                        </span>
                      </td>
                      <td
                        class="px-4 pt-2 pb-2 text-ellipsis"
                        style="font-size: 0.8rem"
                      >
                        {{ slo.sloCycleStartTime | datetime }}
                      </td>
                    </tr>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details">
                          End Time
                        </span>
                      </td>
                      <td
                        class="px-4 pt-2 pb-2 text-ellipsis"
                        style="font-size: 0.8rem"
                      >
                        {{ slo.sloCycleEndTime | datetime }}
                      </td>
                    </tr>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details"
                          >Correction Profile
                        </span>
                      </td>
                      <td
                        class="text-ellipsis px-4 pt-2 pb-2"
                        style="font-size: 0.8rem"
                      >
                        -
                      </td>
                    </tr>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details"
                          >Penalty Profile</span
                        >
                      </td>
                      <td
                        class="px-4 pt-2 pb-2 text-ellipsis"
                        style="font-size: 0.8rem"
                        >-</td
                      >
                    </tr>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details"
                          >Contract Amount</span
                        >
                      </td>
                      <td
                        class="px-4 pt-2 pb-2 text-ellipsis"
                        style="font-size: 0.8rem"
                        >-</td
                      >
                    </tr>
                    <tr>
                      <td class="text-neutral px-4 pt-2 pb-2 slo-summary-label">
                        <span class="text-ellipsis metric-details"
                          >Penalty Amount</span
                        >
                      </td>
                      <td
                        class="px-4 pt-2 pb-2 text-ellipsis"
                        style="font-size: 0.8rem"
                        >-</td
                      >
                    </tr>
                  </tbody>
                </table>
              </MCol>

              <template v-if="!isAvailabilitySlo">
                <MCol :size="4" class="pl-10 border-left">
                  <table class="item-list-table with-out-border">
                    <colgroup>
                      <col style="width: 150px" />
                      <col />
                    </colgroup>
                    <tbody>
                      <tr>
                        <td
                          class="text-neutral px-4 pt-2 pb-2 slo-summary-label"
                        >
                          <span class="text-ellipsis metric-details"
                            >SLO Start Date/Time
                          </span>
                        </td>
                        <td
                          class="text-ellipsis px-4 pt-2 pb-2"
                          style="font-size: 0.8rem"
                          >{{ (slo.startDate / 1000) | datetime }}</td
                        >
                      </tr>
                      <tr>
                        <td
                          class="text-neutral px-4 pt-2 pb-2 slo-summary-label"
                        >
                          <span class="text-ellipsis metric-details"
                            >SLO Metric</span
                          >
                        </td>
                        <td
                          class="px-4 pt-2 pb-2 text-ellipsis"
                          style="font-size: 0.8rem"
                          >{{ slo.counter || {} }}</td
                        >
                      </tr>
                      <tr>
                        <td
                          class="text-neutral px-4 pt-2 pb-2 slo-summary-label"
                        >
                          <span class="text-ellipsis metric-details"
                            >SLO Trigger Condition
                          </span>
                        </td>
                        <td
                          class="px-4 pt-2 pb-2 text-ellipsis"
                          style="font-size: 0.8rem"
                          :title="slo.triggerCondition"
                          >{{ slo.triggerCondition }}</td
                        >
                      </tr>
                      <tr>
                        <td
                          class="text-neutral px-4 pt-2 pb-2 slo-summary-label"
                        >
                          <span class="text-ellipsis metric-details">
                            Start Time
                          </span>
                        </td>
                        <td
                          class="px-4 pt-2 pb-2 text-ellipsis"
                          style="font-size: 0.8rem"
                        >
                          {{ slo.sloCycleStartTime | datetime }}
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="text-neutral px-4 pt-2 pb-2 slo-summary-label"
                        >
                          <span class="text-ellipsis metric-details">
                            End Time
                          </span>
                        </td>
                        <td
                          class="px-4 pt-2 pb-2 text-ellipsis"
                          style="font-size: 0.8rem"
                        >
                          {{ slo.sloCycleEndTime | datetime }}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </MCol>
                <MCol :size="4" class="pl-10 border-left">
                  <table class="item-list-table with-out-border">
                    <colgroup>
                      <col style="width: 150px" />
                      <col />
                    </colgroup>
                    <tbody>
                      <tr>
                        <td
                          class="text-neutral px-4 pt-2 pb-2 slo-summary-label"
                        >
                          <span class="text-ellipsis metric-details"
                            >Correction Profile
                          </span>
                        </td>
                        <td
                          class="text-ellipsis px-4 pt-2 pb-2"
                          style="font-size: 0.8rem"
                        >
                          -
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="text-neutral px-4 pt-2 pb-2 slo-summary-label"
                        >
                          <span class="text-ellipsis metric-details"
                            >Penalty Profile</span
                          >
                        </td>
                        <td
                          class="px-4 pt-2 pb-2 text-ellipsis"
                          style="font-size: 0.8rem"
                          >-</td
                        >
                      </tr>
                      <tr>
                        <td
                          class="text-neutral px-4 pt-2 pb-2 slo-summary-label"
                        >
                          <span class="text-ellipsis metric-details"
                            >Contract Amount</span
                          >
                        </td>
                        <td
                          class="px-4 pt-2 pb-2 text-ellipsis"
                          style="font-size: 0.8rem"
                          >-</td
                        >
                      </tr>
                      <tr>
                        <td
                          class="text-neutral px-4 pt-2 pb-2 slo-summary-label"
                        >
                          <span class="text-ellipsis metric-details"
                            >Penalty Amount</span
                          >
                        </td>
                        <td
                          class="px-4 pt-2 pb-2 text-ellipsis"
                          style="font-size: 0.8rem"
                          >-</td
                        >
                      </tr>
                    </tbody>
                  </table>
                </MCol>
              </template>
            </MRow>
          </MCol>

          <MCol v-if="isAvailabilitySlo" :size="3">
            <MRow class="vue-grid-item rounded py-2 h-full" :gutter="0">
              <MCol :size="12">
                <h6 class="text-xs slo-summary-label ml-2">
                  Service Reliability Metrics</h6
                >
              </MCol>
              <MCol :size="12" class="flex flex-col">
                <MRow
                  :gutter="0"
                  class="p-2 flex justify-between pl-3 pb-4 h-full"
                  :class="{
                    'items-end': !forSloHistoryDrillDown,
                    'items-start': forSloHistoryDrillDown,
                  }"
                >
                  <div class="flex flex-col w-1/2">
                    <span class="">MTTR</span>
                    <span
                      class="text-4xl text-ellipsis font-semibold"
                      :title="globalSloData.slo_mttr_seconds_last"
                    >
                      {{ globalSloData.slo_mttr_seconds_last }}
                    </span>
                  </div>

                  <div class="flex flex-col w-1/2">
                    <span class="">MTBF</span>
                    <span
                      class="text-4xl text-ellipsis font-semibold"
                      :title="globalSloData.slo_mtbf_seconds_last"
                    >
                      {{ globalSloData.slo_mtbf_seconds_last }}
                    </span>
                  </div>
                </MRow>
              </MCol>
            </MRow>
          </MCol>
          <MCol :size="3">
            <MRow class="vue-grid-item rounded py-2 h-full" :gutter="0">
              <MCol :size="12">
                <h6 class="text-xs slo-summary-label ml-2">
                  SLO Monitor Overview</h6
                >
              </MCol>

              <MCol :size="12">
                <MRow
                  v-if="configuredMonitorsData"
                  :gutter="0"
                  class="slo-status-card-row mx-2"
                >
                  <div
                    :size="6"
                    class="flex flex-col items-center slo-status-card bg-red-lightest"
                  >
                    <span class="card-value text-secondary-red">{{
                      configuredMonitorsData[SLO_STATUS.BREACHED]
                    }}</span>
                    <span class="card-label text-neutral">Breached</span>
                  </div>
                  <div
                    :size="6"
                    class="flex flex-col items-center slo-status-card"
                  >
                    <span class="card-value text-secondary-yellow">{{
                      configuredMonitorsData[SLO_STATUS.WARNING]
                    }}</span>
                    <span class="card-label text-neutral">Warning</span>
                  </div>
                  <div
                    :size="6"
                    class="flex flex-col items-center slo-status-card"
                  >
                    <span class="card-value text-secondary-green">{{
                      configuredMonitorsData[SLO_STATUS.OK]
                    }}</span>
                    <span class="card-label text-neutral">Ok</span>
                  </div>
                  <div
                    :size="6"
                    class="flex flex-col items-center slo-status-card"
                  >
                    <span class="card-value text-primary">{{
                      configuredMonitorsData.total
                    }}</span>
                    <span class="card-label text-neutral">Total</span>
                  </div>
                </MRow>
                <MRow v-else>
                  <FlotoContentLoader
                    :loading="true"
                    class="flex flex-col flex-1"
                  />
                </MRow>
              </MCol>
            </MRow>
          </MCol>
        </MRow>

        <MRow :gutter="0" class="w-full mt-2" style="min-height: 220px">
          <MCol :size="6" class="h-full">
            <MRow
              class="vue-grid-item rounded py-2 h-full flex flex-col mr-1"
              :gutter="0"
            >
              <MCol :size="12" class="flex">
                <h6 class="text-xs slo-summary-label ml-2">SLO Achieved </h6>
              </MCol>
              <div
                :size="12"
                class="w-full flex flex-col justify-around flex-1 px-4"
              >
                <div class="text-6xl text-secondary-green font-extrabold mb-2">
                  {{ globalSloData.slo_achieved_percent_last }}
                </div>

                <div class="flex flex-col w-full">
                  <div class="w-full flex justify-end mb-1"> &nbsp; </div>
                  <TargetIndicatorProgressBar
                    :achieved-percentage="
                      globalSloData.slo_achieved_percent_last_sort || 0
                    "
                    :target-percentage="slo.sloTarget"
                    :violated-at="slo.sloWarning"
                    left-percentage-color="var(--secondary-red)"
                    achieved-color="var(--secondary-green)"
                  />
                  <div class="w-full flex justify-between mb-4">
                    <div class=""> &nbsp; </div>
                    <div class=""> &nbsp; </div>
                  </div>
                </div>
              </div>
            </MRow>
          </MCol>
          <MCol
            v-if="!forSloHistoryDrillDown"
            :size="6"
            style="padding-right: 0"
            class="h-full"
          >
            <MRow
              class="vue-grid-item rounded py-2 h-full flex flex-col ml-1"
              :gutter="0"
            >
              <MCol :size="12" class="flex">
                <h6 class="text-xs slo-summary-label ml-2 mb-0"
                  >Error Budget Left</h6
                >
              </MCol>
              <div
                :size="12"
                class="w-full flex flex-col justify-around flex-1 px-4"
              >
                <div
                  class="text-6xl font-extrabold"
                  :class="{
                    'text-secondary-red': errorBudgetRemainingTime <= 0,
                    'text-secondary-green': errorBudgetRemainingTime > 0,
                  }"
                >
                  {{ globalSloData.slo_error_budget_left_percent_last }}
                </div>
                <div class="flex flex-col w-full">
                  <div class="w-full flex justify-end mb-1">
                    <div class="text-sm">
                      Remaining Time:
                      <span class="font-bold">{{
                        errorBudgetRemainingTime | duration
                      }}</span>
                    </div>
                  </div>
                  <TargetIndicatorProgressBar
                    :achieved-percentage="
                      globalSloData.slo_error_budget_left_percent_last_sort || 0
                    "
                  />
                  <div class="w-full flex justify-between">
                    <div class="text-sm">
                      Violated Time:
                      <span class="font-bold">{{
                        globalSloData.slo_violated_seconds_last_sort | duration
                      }}</span>
                    </div>
                    <div class="text-sm">
                      Acceptable Violation Time:
                      <span class="font-bold">{{
                        slo.sloAcceptableViolationTime | duration
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </MRow>
          </MCol>
          <MCol
            v-if="forSloHistoryDrillDown"
            :size="6"
            style="padding-right: 0"
            class="h-full"
          >
            <MRow
              class="vue-grid-item rounded py-2 h-full flex flex-col ml-1"
              :gutter="0"
            >
              <MCol :size="12" class="flex">
                <h6 class="text-xs slo-summary-label ml-2 mb-0"
                  >Violation Time</h6
                >
              </MCol>
              <div
                :size="12"
                class="w-full flex flex-col justify-around flex-1 px-4"
              >
                <div
                  class="text-6xl font-extrabold"
                  :class="{ 'text-secondary-red': true }"
                >
                  {{ globalSloData.slo_violated_percent_last }}
                </div>
                <div class="flex flex-col w-full">
                  <TargetIndicatorProgressBar
                    :achieved-percentage="
                      globalSloData.slo_violated_percent_last_sort || 0
                    "
                    achieved-color="var(--secondary-red)"
                  />
                  <div class="w-full flex justify-between">
                    <div class="text-sm">
                      Violated Time:
                      <span class="font-bold">{{
                        globalSloData.slo_violated_seconds_last_sort | duration
                      }}</span>
                    </div>
                    <div class="text-sm">
                      Acceptable Violation Time:
                      <span class="font-bold">{{
                        slo.sloAcceptableViolationTime | duration
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </MRow>
          </MCol>
        </MRow>

        <MRow :gutter="0" class="w-full mt-2">
          <MCol :size="12">
            <MRow class="vue-grid-item rounded py-2" :gutter="0">
              <MCol :size="12">
                <h6 class="text-xs slo-summary-label ml-2">SLO Trend</h6>
              </MCol>
              <MCol :size="12" style="padding-right: 0">
                <div
                  :style="{ width: `100%` }"
                  class="self-center flex"
                  style="flex-shrink: 0; height: 100%; min-height: 100px"
                >
                  <SloXRangeTrend :slo="slo" :slo-timeline="sloTimeline" />
                </div>
              </MCol>
            </MRow>
          </MCol>
        </MRow>

        <!-- <MRow v-if="!isAvailabilitySlo" :gutter="0" class="w-full mt-2">
          <MCol :size="12">
            <MRow class="vue-grid-item rounded py-2" :gutter="0">
              <MCol :size="12">
                <h6 class="text-xs slo-summary-label ml-2">SLO Metric</h6>
              </MCol>
              <MCol :size="12" style="padding-right: 0">
                <div
                  :style="{ width: `100%` }"
                  class="self-center flex"
                  style="flex-shrink: 0; height: 100%; min-height: 280px"
                >
                  <WidgetContainer
                    :widget="lineChart"
                    is-preview
                    :height="280"
                    watch-widget
                  />
                </div>
              </MCol>
            </MRow>
          </MCol>
        </MRow> -->
        <MRow :gutter="0" class="w-full mt-2">
          <MCol :size="6">
            <MRow class="vue-grid-item rounded pt-2 mr-1" :gutter="0">
              <MCol :size="12">
                <h6 class="text-xs slo-summary-label ml-2"
                  >Error Budget Burndown
                </h6>
              </MCol>
              <MCol :size="12" style="padding-right: 0">
                <div
                  :style="{ width: `100%` }"
                  class="self-center flex"
                  style="flex-shrink: 0; height: 100%; min-height: 280px"
                >
                  <WidgetContainer
                    :widget="burndownWidget"
                    is-preview
                    :height="280"
                    watch-widget
                  />
                </div>
              </MCol>
            </MRow>
          </MCol>
          <MCol :size="6" style="padding-right: 0">
            <MRow class="vue-grid-item rounded pt-2 ml-1 h-full" :gutter="0">
              <MCol :size="12">
                <h6 class="text-xs slo-summary-label ml-2 mb-0">Burn Rate</h6>
              </MCol>
              <MCol :size="12" style="padding-right: 0">
                <div
                  :style="{ width: `100%` }"
                  class="self-center flex"
                  style="flex-shrink: 0; height: 100%; min-height: 280px"
                >
                  <WidgetContainer
                    :widget="burnRateWidget"
                    is-preview
                    :height="280"
                    watch-widget
                  />
                </div>
              </MCol>
            </MRow>
          </MCol>
        </MRow>

        <MRow
          class="mt-2 vue-grid-item flex-1 min-h-0"
          style="min-height: 280px"
          :gutter="0"
        >
          <ConfiguredMonitors
            :slo="slo"
            :global-slo-data="globalSloData"
            :slo-timeline="sloTimeline"
            :for-slo-history-drill-down="forSloHistoryDrillDown"
            @slo-configured-monitors-data-loaded="
              handleSloConfiguredMonitorsDataLoaded
            "
          />
        </MRow>
      </div>
    </div>
  </FlotoContentLoader>
</template>

<script>
import Moment from 'moment'

import WidgetContainer from '@components/widgets/views/container.vue'
import { AVAILABLE_SLO_TYPES } from '@modules/slo/helpers/slo'
import LooseTags from '@components/loose-tags.vue'

import ConfiguredMonitors from './configured-monitors.vue'
import TargetIndicatorProgressBar from '@components/common/slo-progress-bar.vue'
import SloXRangeTrend from './slo-x-range-trend.vue'
import SloStatusBadge from './slo-status-badge.vue'
import { getGlobalSLOCalculationDataApi } from '../slo-api'
import {
  getErrorBudgetBurndownWidgetDef,
  getBurnRateWidgetDef,
} from '../helpers/slo-overview'

import { SLO_STATUS } from '../helpers/slo'

export default {
  name: 'SloOverview',
  components: {
    WidgetContainer,
    ConfiguredMonitors,
    TargetIndicatorProgressBar,
    SloXRangeTrend,
    SloStatusBadge,
    LooseTags,
  },
  props: {
    slo: {
      type: Object,
      required: true,
    },
    sloTimeline: {
      type: Object,
      required: true,
    },
    forSloHistoryDrillDown: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.SLO_STATUS = SLO_STATUS
    return {
      targetPercentage: 5,
      achievedPercentage: 90,
      violatedAt: 98,
      loading: true,
      globalSloData: {},
      configuredMonitorsData: {},
    }
  },
  computed: {
    errorBudgetRemainingTime() {
      const diff =
        this.slo.sloAcceptableViolationTime -
        this.globalSloData.slo_violated_seconds_last_sort
      return diff < 0 ? 0 : diff
    },
    elapsedTime() {
      const currentTime = Moment().unix()
      return currentTime - this.slo.sloCycleStartTime
    },
    remainingTime() {
      const currentTime = Moment().unix()
      return this.slo.sloCycleEndTime - currentTime
    },
    burndownWidget() {
      return getErrorBudgetBurndownWidgetDef(this.slo, this.sloTimeline)
    },
    burnRateWidget() {
      return getBurnRateWidgetDef(this.slo, this.sloTimeline)
    },

    isAvailabilitySlo() {
      return this.slo.sloType === AVAILABLE_SLO_TYPES.AVAILABILITY
    },
    isInstanceSlo() {
      return this.slo.sloFor !== 'monitor'
    },
  },
  async created() {
    await this.fetchData()
  },
  methods: {
    async fetchData() {
      this.loading = true
      const data = await getGlobalSLOCalculationDataApi(this.slo)
      this.globalSloData = data?.[0] || {}
      this.loading = false
    },
    handleSloConfiguredMonitorsDataLoaded(data) {
      this.configuredMonitorsData = data
    },
  },
}
</script>

<style lang="less" scoped>
.metric-details {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--neutral-regular);
}

.slo-summary-label {
  padding-left: 5px !important;
}

.slo-status-card-row {
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: space-around;

  .slo-status-card {
    flex: 1;
    min-width: 75px;
    padding: 12px;
    background: var(--code-tag-background-color);
    border-radius: 4px;

    .card-value {
      font-size: 24px;
      font-style: normal;
      font-weight: 600;
      line-height: 100%; /* 24px */
    }

    .card-label {
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
}
</style>
