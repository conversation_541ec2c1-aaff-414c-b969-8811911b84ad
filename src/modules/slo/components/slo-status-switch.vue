<template>
  <div class="inline-flex">
    <div
      v-for="key in Object.keys(count)"
      :key="key"
      class="rounded-widget mr-2 inline-flex px-4"
      :class="{
        [`${key.toLowerCase()}-status active`]: value === key,
        [`${key.toLowerCase()}-status  text-neutral`]: value !== key,
      }"
      @click="onClickStatus(key)"
    >
      <div class="pt-2 pb-2 w-title pr-2">
        {{ capitalize(key) }}
      </div>
      <div class="pt-2 pb-2 text-right w-count text total" :class="key">
        {{ count[key] | numberFormat }}
      </div>
    </div>
  </div>
</template>

<script>
import Capitalize from 'lodash/capitalize'

export default {
  name: 'SloStatusSwitch',
  model: { event: 'change' },
  props: {
    count: {
      type: Object,
      default() {
        return {}
      },
    },
    value: {
      type: String,
      default: undefined,
    },
  },
  methods: {
    onClickStatus(status) {
      if (status === this.value) {
        this.$emit('change', 'total')
      } else {
        this.$emit('change', status)
      }
    },
    capitalize(key) {
      return Capitalize(key)
    },
  },
}
</script>

<style lang="less" scoped>
.rounded-widget {
  cursor: pointer;
  border: 1px solid transparent;
  border-radius: 4px;
  transition: all 0.2s ease;

  &.breached-status {
    .total {
      color: var(--severity-critical);
    }

    &.active {
      color: var(--white-regular);
      background-color: var(--severity-critical);

      .total {
        color: var(--white-regular);
      }
    }

    &:hover {
      color: var(--white-regular);
      background-color: var(--severity-critical);

      .total {
        color: var(--white-regular);
      }
    }
  }

  &.warning-status {
    .total {
      color: var(--severity-warning);
    }

    &.active {
      color: var(--white-regular);
      background-color: var(--severity-warning);

      .total {
        color: var(--white-regular);
      }
    }

    &:hover {
      color: var(--white-regular);
      background-color: var(--severity-warning);

      .total {
        color: var(--white-regular);
      }
    }
  }

  &.ok-status {
    .total {
      color: var(--severity-clear);
    }

    &.active {
      color: var(--white-regular);
      background-color: var(--severity-clear);

      .total {
        color: var(--white-regular);
      }
    }

    &:hover {
      color: var(--white-regular);
      background-color: var(--severity-clear);

      .total {
        color: var(--white-regular);
      }
    }
  }

  &.total-status {
    .total {
      color: var(--severity-maintenance);
    }

    &.active {
      color: var(--white-regular);
      background-color: var(--severity-maintenance);

      .total {
        color: var(--white-regular);
      }
    }

    &:hover {
      color: var(--white-regular);
      background-color: var(--severity-maintenance);

      .total {
        color: var(--white-regular);
      }
    }
  }

  // &.total {
  //   &.active {
  //     background-color: rgba(0, 157, 220, 0.2);
  //     border-color: #009ddc;
  //     color: #009ddc;
  //   }

  //   &.hovered {
  //     background-color: rgba(0, 157, 220, 0.1);
  //     border-color: #009ddc;
  //     color: #009ddc;
  //   }
  // }
}

.w-title {
  min-width: auto;
}

.w-count {
  min-width: 30px;
  font-weight: 600;
}
</style>
