import { colorMap } from '@data/monitor'
import Mem from 'mem'
import {
  arrowScale,
  bgColor,
  borderColor,
  edgeColor,
  makeSvg,
  nodeSizeCalculator,
  selectedNodeSizeCalculator,
  textColor,
} from '@/src/components/monitor-graph/graph-style'

const edgeWidth = Mem(
  (ele) => {
    return `${2 * (1 / ele.data('zoom'))}`
  },
  {
    cacheKey: (arguments_) =>
      `${arguments_[0].data('zoom')}-${arguments_[0].data('severity')}`,
  }
)

export const DefaultStyle = (colorScheme) => {
  return [
    {
      selector: 'node',
      shape: 'circle',
      style: {
        label: 'data(name)',
        'font-family': 'Poppins',
        'text-halign': 'center',
        'text-valign': 'bottom',
        'text-max-width': 100,
        'text-wrap': 'ellipsis',
        color: textColor,
        'background-color': bgColor,
        'border-width': '2px',
        'background-image': (ele) => makeSvg(ele, true, true).svg,
        'border-color': borderColor,
        width: nodeSizeCalculator,
        height: nodeSizeCalculator,
      },
    },
    {
      selector: 'node.cy-expand-collapse-collapsed-node',
      style: {
        'text-wrap': 'wrap',
        'text-justification': 'center',
      },
    },
    {
      selector: ':parent',
      style: {
        shape: 'round-rectangle',
        label: '',
        'background-color': '#099dd9',
        'background-opacity': 0,
        'border-width': 0,
        'border-color': '#099dd9',
      },
    },
    ...Object.keys(colorMap).map((key) => ({
      selector: `node.${key}`,
      style: {
        'border-color': colorMap[key],
      },
    })),
    {
      selector: 'node.selected',
      style: {
        'z-index': 2,
        'font-weight': 'bold',
        opacity: 1,
        'overlay-opacity': 0,
        width: selectedNodeSizeCalculator,
        height: selectedNodeSizeCalculator,
      },
    },
    {
      selector: 'node.destination',
      style: {
        shape: 'square',
      },
    },
    {
      selector: 'edge',
      style: {
        // 'edge-text-rotation': 'autorotate',
        'min-zoomed-font-size': 10,
        label: 'data(edgelabel)',
        'font-family': 'Poppins',
        'text-halign': 'center',
        // 'text-valign': 'bottom',
        'text-margin-x': 10,
        'text-margin-y': 5,
        // 'text-rotation': 250,
        'text-max-width': 100,
        'text-wrap': 'ellipsis',
        color: textColor,
        width: edgeWidth,
        'line-style': 'solid',
        'radius-type': 'influence-radius',
        // 'curve-style': 'straight',
        'line-color': edgeColor,
        opacity: 1,
        'curve-style': 'round-taxi',
        'taxi-direction': 'rightward',
        'taxi-turn': 40,
        'taxi-turn-min-distance': 20,
        'taxi-radius': 40,
      },
    },
    {
      selector: 'edge.has-arrow',
      style: {
        'target-arrow-shape': 'triangle-backcurve',
        'arrow-scale': arrowScale,
      },
    },
    {
      selector: 'edge.selected',
      style: {
        'line-style': 'dashed',
      },
    },
    ...Object.keys(colorMap).map((key) => ({
      selector: `edge.${key}`,
      style: {
        'line-color': colorMap[key],
        'target-arrow-color': colorMap[key],
      },
    })),
    {
      selector: 'edge.cy-expand-collapse-meta-edge',
      style: {
        'target-label': (ele) => {
          return ''
        },
        label: (ele) => {
          return ''
        },
        'source-label': (ele) => {
          return ''
        },
        width: edgeWidth,
        'line-color': '#099dd9',
        'target-arrow-color': '#099dd9',
        'target-arrow-shape': 'triangle-backcurve',
      },
    },
  ]
}
