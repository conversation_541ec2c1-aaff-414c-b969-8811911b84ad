<script>
// import api from '@api'
// import Bus from '@utils/emitter'
// import Constants from '@constants'
import datetime from '@src/filters/datetime'

export default {
  name: 'SloHistoryChartDataProvider',
  provide() {
    const sloHistoryChartContext = {
      oneBatchShift: this.oneBatchShift, // Keep oneBatchShift for potential separate use
      onePontShift: this.onePontShift, // Retain for direct use if needed
    }
    Object.defineProperty(sloHistoryChartContext, 'startTime', {
      enumerable: true,
      get: () => {
        return this.startTime
      },
    })
    Object.defineProperty(sloHistoryChartContext, 'endTime', {
      enumerable: true,
      get: () => {
        return this.endTime
      },
    })
    return { sloHistoryChartContext }
  },
  props: {
    chartSeries: {
      type: Object,
      default: undefined,
    },
    offset: {
      type: Number,
      default: 0,
    },
    currentTimestamp: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      dataChunkSize: 98,
      currentSeriesChunk: {},
      chunkStartIndex: 0, // Tracks the start index of the current chunk
      lastShiftType: null, // Tracks the last shift type ('point' or 'batch')
    }
  },
  computed: {
    startTime() {
      return this.currentSeriesChunk?.statusSeries?.[0]?.data?.[0]?.x
        ? datetime(
            (this.currentSeriesChunk.statusSeries[0].data[0].x - this.offset) /
              1000
          )
        : ''
    },
    endTime() {
      const index = this.currentSeriesChunk?.statusSeries?.[0]?.data?.length - 1
      return this.currentSeriesChunk?.statusSeries?.[0]?.data?.[index]?.x
        ? datetime(
            (this.currentSeriesChunk.statusSeries[0].data[index].x -
              this.offset) /
              1000
          )
        : ''
    },
  },
  watch: {
    chartSeries() {
      this.initializeCurrentSeriesChunk()
    },
  },
  created() {
    this.initializeCurrentSeriesChunk()
  },
  methods: {
    initializeCurrentSeriesChunk() {
      if (!this.chartSeries || !this.chartSeries.statusSeries?.[0]?.data) return

      const totalDataPoints = this.chartSeries.statusSeries[0].data.length
      const drilldownTimestamp = this.$route.query.c
        ? this.$route.query.c + this.offset
        : null

      // If drilldown timestamp is present, center the view around that timestamp
      if (drilldownTimestamp) {
        // Find the index closest to the drilldown timestamp
        let drilldownIndex = -1
        let closestDistance = Infinity

        for (let i = 0; i < totalDataPoints; i++) {
          const pointTimestamp = this.chartSeries.statusSeries[0].data[i].x

          const distance = Math.abs(pointTimestamp - drilldownTimestamp)
          if (distance < closestDistance) {
            closestDistance = distance
            drilldownIndex = i
          }
        }

        if (drilldownIndex !== -1) {
          // Calculate start and end indices to show 40 points before and 40 points after
          const pointsBeforeAfter = 40
          const startIndex = Math.max(0, drilldownIndex - pointsBeforeAfter)
          const endIndex = Math.min(
            totalDataPoints,
            drilldownIndex + pointsBeforeAfter + 1
          )

          this.chunkStartIndex = startIndex

          this.currentSeriesChunk = {
            statusSeries: [
              {
                ...this.chartSeries.statusSeries[0],
                data: this.chartSeries.statusSeries[0].data.slice(
                  startIndex,
                  endIndex
                ),
                formattedValues:
                  this.chartSeries.statusSeries[0].formattedValues.slice(
                    startIndex,
                    endIndex
                  ),
              },
            ],
            packetLostSeries: [
              {
                ...this.chartSeries.packetLostSeries[0],
                data: this.chartSeries.packetLostSeries[0].data.slice(
                  startIndex,
                  endIndex
                ),
                formattedValues:
                  this.chartSeries.packetLostSeries[0].formattedValues.slice(
                    startIndex,
                    endIndex
                  ),
              },
            ],
            latencySeries: [
              {
                ...this.chartSeries.latencySeries[0],
                data: this.chartSeries.latencySeries[0].data.slice(
                  startIndex,
                  endIndex
                ),
                formattedValues:
                  this.chartSeries.latencySeries[0].formattedValues.slice(
                    startIndex,
                    endIndex
                  ),
              },
            ],
          }
          return
        }
      }

      // Default behavior: show the last 98 points
      this.chunkStartIndex = Math.max(0, totalDataPoints - this.dataChunkSize)
      const endIndex = totalDataPoints

      this.currentSeriesChunk = {
        statusSeries: [
          {
            ...this.chartSeries.statusSeries[0],
            data: this.chartSeries.statusSeries[0].data.slice(
              this.chunkStartIndex,
              endIndex
            ),
            formattedValues:
              this.chartSeries.statusSeries[0].formattedValues.slice(
                this.chunkStartIndex,
                endIndex
              ),
          },
        ],
        packetLostSeries: [
          {
            ...this.chartSeries.packetLostSeries[0],
            data: this.chartSeries.packetLostSeries[0].data.slice(
              this.chunkStartIndex,
              endIndex
            ),
            formattedValues:
              this.chartSeries.packetLostSeries[0].formattedValues.slice(
                this.chunkStartIndex,
                endIndex
              ),
          },
        ],
        latencySeries: [
          {
            ...this.chartSeries.latencySeries[0],
            data: this.chartSeries.latencySeries[0].data.slice(
              this.chunkStartIndex,
              endIndex
            ),
            formattedValues:
              this.chartSeries.latencySeries[0].formattedValues.slice(
                this.chunkStartIndex,
                endIndex
              ),
          },
        ],
      }
    },
    onePontShift(direction) {
      if (!this.chartSeries || !this.chartSeries.statusSeries?.[0]?.data) return

      const totalDataPoints = this.chartSeries.statusSeries[0].data.length
      let newStartIndex = this.chunkStartIndex

      if (direction === 'forward') {
        if (this.chunkStartIndex + this.dataChunkSize < totalDataPoints) {
          newStartIndex += 1
        } else {
          return
        }
      } else if (direction === 'backward') {
        if (this.chunkStartIndex > 0) {
          newStartIndex -= 1
        } else {
          return
        }
      } else {
        return
      }

      this.chunkStartIndex = newStartIndex
      let endIndex = Math.min(
        this.chunkStartIndex + this.dataChunkSize,
        totalDataPoints
      )
      if (endIndex > totalDataPoints) {
        endIndex = totalDataPoints - 1
      }
      this.currentSeriesChunk = {
        statusSeries: [
          {
            ...this.chartSeries.statusSeries[0],
            data: this.chartSeries.statusSeries[0].data.slice(
              this.chunkStartIndex,
              endIndex
            ),
            formattedValues:
              this.chartSeries.statusSeries[0].formattedValues.slice(
                this.chunkStartIndex,
                endIndex
              ),
          },
        ],
        packetLostSeries: [
          {
            ...this.chartSeries.packetLostSeries[0],
            data: this.chartSeries.packetLostSeries[0].data.slice(
              this.chunkStartIndex,
              endIndex
            ),
            formattedValues:
              this.chartSeries.packetLostSeries[0].formattedValues.slice(
                this.chunkStartIndex,
                endIndex
              ),
          },
        ],
        latencySeries: [
          {
            ...this.chartSeries.latencySeries[0],
            data: this.chartSeries.latencySeries[0].data.slice(
              this.chunkStartIndex,
              endIndex
            ),
            formattedValues:
              this.chartSeries.latencySeries[0].formattedValues.slice(
                this.chunkStartIndex,
                endIndex
              ),
          },
        ],
      }
      this.lastShiftType = 'point'
    },
    oneBatchShift(direction) {
      if (!this.chartSeries || !this.chartSeries.statusSeries?.[0]?.data) return

      const totalDataPoints = this.chartSeries.statusSeries[0].data.length
      let newStartIndex = this.chunkStartIndex

      if (direction === 'forward') {
        newStartIndex += this.dataChunkSize
        if (newStartIndex + this.dataChunkSize > totalDataPoints) {
          // newStartIndex = totalDataPoints - this.dataChunkSize
        }

        if (newStartIndex >= totalDataPoints) {
          return
        }
      } else if (direction === 'backward') {
        newStartIndex -= this.dataChunkSize
        if (newStartIndex < 0) {
          newStartIndex = 0
        }
      } else {
        return
      }

      this.chunkStartIndex = newStartIndex
      let endIndex = Math.min(
        this.chunkStartIndex + this.dataChunkSize,
        totalDataPoints
      )
      if (endIndex > totalDataPoints) {
        endIndex = totalDataPoints - 1
      }
      this.currentSeriesChunk = {
        statusSeries: [
          {
            ...this.chartSeries.statusSeries[0],
            data: this.chartSeries.statusSeries[0].data.slice(
              this.chunkStartIndex,
              endIndex
            ),
            formattedValues:
              this.chartSeries.statusSeries[0].formattedValues.slice(
                this.chunkStartIndex,
                endIndex
              ),
          },
        ],
        packetLostSeries: [
          {
            ...this.chartSeries.packetLostSeries[0],
            data: this.chartSeries.packetLostSeries[0].data.slice(
              this.chunkStartIndex,
              endIndex
            ),
            formattedValues:
              this.chartSeries.packetLostSeries[0].formattedValues.slice(
                this.chunkStartIndex,
                endIndex
              ),
          },
        ],
        latencySeries: [
          {
            ...this.chartSeries.latencySeries[0],
            data: this.chartSeries.latencySeries[0].data.slice(
              this.chunkStartIndex,
              endIndex
            ),
            formattedValues:
              this.chartSeries.latencySeries[0].formattedValues.slice(
                this.chunkStartIndex,
                endIndex
              ),
          },
        ],
      }
      this.lastShiftType = 'batch'
    },
  },
  render() {
    return this.$scopedSlots.default({
      currentSeriesChunk: this.currentSeriesChunk,
    })
  },
}
</script>
