<template>
  <div class="timeline-scrollbar">
    <div class="timeline-controls">
      <div class="flex items-center">
        <MButton
          class="timeline-nav-button timeline-button-back"
          variant="neutral-lightest"
          @click="moveBatchBackwardSide"
        >
          <MIcon name="chevron-left" />
          <MIcon name="chevron-left" class="double-icon ml-1" />
        </MButton>
        <MButton
          class="timeline-nav-button mr-2 ml-2"
          variant="neutral-lightest"
          @click="moveBackwardSide"
        >
          <MIcon name="chevron-left" />
        </MButton>
        {{ batchStartTime }}
      </div>

      <div class="flex items-center">
        {{ batchEndTime }}

        <MButton
          class="timeline-nav-button ml-2 mr-2"
          variant="neutral-lightest"
          @click="moveForwardSide"
        >
          <MIcon name="chevron-right" />
        </MButton>
        <MButton
          class="timeline-nav-button timeline-button-forward"
          variant="neutral-lightest"
          @click="moveBatchForwardSide"
        >
          <MIcon name="chevron-right" class="mr-2" />
          <MIcon name="chevron-right" class="double-icon" />
        </MButton>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TimelineScrollbar',
  inject: { sloHistoryChartContext: { default: {} } },

  props: {},
  data() {
    return {
      currentTimeInternal: null,
      isDragging: false,
    }
  },
  computed: {
    batchStartTime() {
      if (this.sloHistoryChartContext.startTime) {
        return this.sloHistoryChartContext.startTime
      }
      return undefined
    },
    batchEndTime() {
      if (this.sloHistoryChartContext.endTime) {
        return this.sloHistoryChartContext.endTime
      }
      return undefined
    },
  },

  methods: {
    moveBackwardSide() {
      if (this.sloHistoryChartContext.onePontShift) {
        this.sloHistoryChartContext.onePontShift('backward')
      }
    },
    moveForwardSide() {
      if (this.sloHistoryChartContext.onePontShift) {
        this.sloHistoryChartContext.onePontShift('forward')
      }
    },
    moveBatchBackwardSide() {
      if (this.sloHistoryChartContext.oneBatchShift) {
        this.sloHistoryChartContext.oneBatchShift('backward')
      }
    },
    moveBatchForwardSide() {
      if (this.sloHistoryChartContext.oneBatchShift) {
        this.sloHistoryChartContext.oneBatchShift('forward')
      }
    },
  },
}
</script>

<style scoped>
.timeline-scrollbar {
  width: 100%;
  margin-top: 8px;
  background-color: var(--timeline-scrollbar-background-color);

  /* border: 1px solid var(--border-dark, #2a2a2a); */

  /* border-radius: 4px; */
  border-top: 1px solid var(--border-color);

  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15); */
}

.timeline-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  padding: 0 12px;
}

.timestamp-display {
  flex: 1;
  padding: 0 12px;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-primary, #fff);
  text-align: center;
  white-space: nowrap;
}

.timeline-nav-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 25px;

  /* color: var(--text-secondary, rgba(255, 255, 255, 0.7));
  cursor: pointer;
  background-color: var(--surface-darker, rgba(26, 26, 26, 0.8));
  border: 1px solid var(--border-light, rgba(60, 60, 60, 0.8));
  border-radius: 4px;
  outline: none;
  transition: all 0.2s ease; */
}

/*
.timeline-nav-button:hover {
  color: var(--text-primary, #fff);
  background-color: var(--surface-hover, rgba(50, 50, 50, 0.8));
  border-color: var(--border-active, rgba(90, 90, 90, 0.9));
} */

.timeline-slider {
  height: 20px;
  padding: 0 12px 8px;
}

.timeline-slider-handle:hover {
  transform: scale(1.15);
}

.timeline-slider-handle:active {
  cursor: grabbing;
}

.timeline-button-back,
.timeline-button-forward {
  position: relative;
  width: 36px;
}

.double-icon {
  position: absolute;
  left: 8px;
}
</style>
