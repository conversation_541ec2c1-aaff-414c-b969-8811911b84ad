<template>
  <div>
    <a @click="handleOpenMonitorSlo">
      {{ displayValue }}
    </a>

    <SloMonitorDrillDown
      v-if="mountDrillDown"
      :open="isDrawerOpen"
      :item="item"
      :global-slo-data="globalSloData"
      :slo="slo"
      :slo-timeline="sloTimeline"
      :for-slo-history-drill-down="forSloHistoryDrillDown"
      @hide="handleHide"
    />
  </div>
</template>

<script>
import SloMonitorDrillDown from './slo-monitor-drill-down.vue'
export default {
  name: 'SloDrillDownSlot',
  components: {
    SloMonitorDrillDown,
  },
  props: {
    item: {
      type: Object,
      required: true,
    },
    globalSloData: {
      type: Object,
      required: true,
    },
    slo: {
      type: Object,
      required: true,
    },
    sloTimeline: {
      type: Object,
      required: true,
    },

    displayValue: {
      type: String,
      required: true,
    },
    forSloHistoryDrillDown: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      mountDrillDown: false,
      isDrawerOpen: false,
    }
  },
  methods: {
    handleOpenMonitorSlo() {
      this.mountDrillDown = true
      this.$nextTick(() => {
        this.isDrawerOpen = true
      })
    },
    handleHide() {
      this.isDrawerOpen = false
      setTimeout(() => {
        this.mountDrillDown = false
      }, 400)
    },
  },
}
</script>
