import Moment from 'moment'

import { buildWidgetContext, makeCounter } from '@utils/socket-event-as-api'
import {
  WidgetTypeConstants,
  AVAILABLE_GROUP_TYPES,
  DATE_FORMAT,
  TIME_FORMAT,
} from '@components/widgets/constants'

import { isUnitConvertible } from '@/src/utils/unit-checker'
import applyUnit from '@/src/utils/unit-applier'

export function getSloSummaryWidget(slo, timeline) {
  return buildWidgetContext({
    groupType: 'metric',
    category: WidgetTypeConstants.GRID,
    widgetType: WidgetTypeConstants.GRID,
    timeline,
    counters: [
      makeCounter('system.cpu.percent', 'avg', 'Monitor'),
      // makeCounter('system.memory.percent', 'avg', 'SLO', [slo.id]),
      // makeCounter('system.disk.percent', 'avg', 'SLO', [slo.id]),
      // makeCounter('system.network.percent', 'avg', 'SLO', [slo.id]),
    ],
    resultBy: ['status'],
  })
    .setWidgetProperties({
      styleSetting: {
        chartOptions: {
          yAxis: { allowDecimals: false },
        },
      },
    })
    .getContext()
}

export function getSloTrendWidget(slo, timeline) {
  return buildWidgetContext({
    groupType: 'slo',
    category: WidgetTypeConstants.CHART,
    widgetType: WidgetTypeConstants.LINE,
    timeline,
    counters: [makeCounter('slo.achieved_percent', 'avg', 'SLO', [slo.id])],
  })
    .setWidgetProperties({
      styleSetting: {
        chartOptions: {
          yAxis: {
            min: 0,
            max: 100,
            allowDecimals: false,
          },
        },
      },
    })
    .getContext()
}

export function getSloAchievedWidget(slo, timeline) {
  return buildWidgetContext({
    groupType: 'slo',
    category: WidgetTypeConstants.GAUGE,
    widgetType: WidgetTypeConstants.SOLID_GAUGE,
    timeline,
    counters: [makeCounter('slo.achieved_percent', 'last', 'SLO', [slo.id])],
  })
    .setWidgetProperties({
      styleSetting: {
        chartOptions: {
          yAxis: {
            min: 0,
            max: 100,
            plotBands: [
              {
                from: 0,
                to: 90,
                color: '#f04e3e',
                thickness: '20%',
              },
              {
                from: 90,
                to: 100,
                color: '#89c540',
                thickness: '20%',
              },
            ],
          },
        },
      },
    })
    .getContext()
}

export function getErrorBudgetLeftWidget(slo, timeline) {
  return buildWidgetContext({
    groupType: 'slo',
    category: WidgetTypeConstants.GAUGE,
    widgetType: WidgetTypeConstants.SOLID_GAUGE,
    timeline,
    counters: [
      makeCounter('slo.error_budget_left_percent', 'last', 'SLO', [slo.id]),
    ],
  })
    .setWidgetProperties({
      styleSetting: {
        chartOptions: {
          yAxis: {
            min: 0,
            max: 100,
            plotBands: [
              {
                from: 0,
                to: 30,
                color: '#f04e3e',
                thickness: '20%',
              },
              {
                from: 30,
                to: 100,
                color: '#89c540',
                thickness: '20%',
              },
            ],
          },
        },
      },
    })
    .getContext()
}

export function getSloMetricWidget(slo, timeline) {
  return buildWidgetContext({
    groupType: 'slo',
    category: WidgetTypeConstants.CHART,
    widgetType: WidgetTypeConstants.LINE,
    timeline,
    counters: [makeCounter(slo.metric, 'avg', 'Monitor', [slo.monitor])],
  })
    .setWidgetProperties({
      styleSetting: {
        chartOptions: {
          yAxis: {
            allowDecimals: false,
          },
        },
      },
    })
    .getContext()
}

export function getErrorBudgetBurndownWidget(slo, timeline) {
  return buildWidgetContext({
    groupType: 'slo',
    category: WidgetTypeConstants.CHART,
    widgetType: WidgetTypeConstants.LINE,
    timeline,
    counters: [
      makeCounter('slo.error_budget_burndown', 'avg', 'SLO', [slo.id]),
    ],
  })
    .setWidgetProperties({
      styleSetting: {
        chartOptions: {
          yAxis: {
            min: 0,
            max: 100,
            allowDecimals: false,
          },
        },
      },
    })
    .getContext()
}

export function getBurnRateWidget(slo, timeline) {
  return buildWidgetContext({
    groupType: 'slo',
    category: WidgetTypeConstants.CHART,
    widgetType: WidgetTypeConstants.LINE,
    timeline,
    counters: [makeCounter('slo.burn_rate', 'avg', 'SLO', [slo.id])],
  })
    .setWidgetProperties({
      styleSetting: {
        chartOptions: {
          yAxis: {
            min: 0,
            max: 100,
            allowDecimals: false,
          },
        },
      },
    })
    .getContext()
}

export function getConfiguredMonitorsWidget(slo, timeline) {
  return buildWidgetContext({
    groupType: 'slo',
    category: WidgetTypeConstants.GRID,
    widgetType: WidgetTypeConstants.GRID,
    timeline,
    counters: [
      makeCounter('monitor.name', 'count', 'Monitor', [slo.monitor]),
      makeCounter('monitor.ip', 'last', 'Monitor', [slo.monitor]),
      makeCounter('monitor.group', 'last', 'Monitor', [slo.monitor]),
      makeCounter('monitor.tag', 'last', 'Monitor', [slo.monitor]),
      makeCounter('monitor.status', 'last', 'Monitor', [slo.monitor]),
      makeCounter('slo.achieved_percent', 'last', 'SLO', [slo.id]),
      makeCounter('slo.violated_percent', 'last', 'SLO', [slo.id]),
      makeCounter('slo.trend', 'last', 'SLO', [slo.id]),
    ],
  })
    .setWidgetProperties({
      styleSetting: {
        chartOptions: {
          yAxis: { allowDecimals: false },
        },
      },
    })
    .getContext()
}

// Helper function to format SLO data for display
export function formatSloData(slo) {
  return {
    id: slo.id,
    name: slo.name,
    businessServiceName: slo.businessServiceName || 'N/A',
    status: slo.status || 'Unknown',
    frequency: slo.frequency || 'Daily',
    type: slo.type || 'Performance',
    startDateTime: slo.startDateTime,
    metric: slo.metric,
    triggerCondition: slo.triggerCondition,
    achieved: slo.achieved || 0,
    violated: slo.violated || 0,
    errorBudgetLeft: slo.errorBudgetLeft || 0,
    elapsedTime: slo.elapsedTime || '0 Hours 0 Minutes',
    remainingTime: slo.remainingTime || '0 Hours 0 Minutes',
    target: slo.target || 90,
    tags: slo.tags || [],
    monitors: slo.monitors || [],
  }
}

// Helper function to get status color
export function getStatusColor(status) {
  const statusColors = {
    breached: '#f04e3e',
    warning: '#f5bc18',
    ok: '#89c540',
    unknown: '#a5bad0',
  }
  return statusColors[status.toLowerCase()] || statusColors.unknown
}

// Helper function to format duration
export function formatDuration(seconds) {
  if (!seconds) return '0 Hours 0 Minutes'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  return `${hours} Hours ${minutes} Minutes`
}

// Helper function to format percentage
export function formatPercentage(value) {
  if (value === null || value === undefined) return '0%'
  return `${Math.round(value)}%`
}

export function getErrorBudgetBurndownWidgetDef(
  slo,
  timeline,
  item = {},
  forDrillDown = false
) {
  const forInstance = slo.sloFor !== 'monitor'
  const instance = item['slo_instance']

  const counterPrefix = !forDrillDown
    ? 'slo~'
    : forInstance
    ? 'slo.instance~'
    : 'slo.object.'
  const sloId = slo.id
  const cycleId = slo.cycleId
  const calculatedEntityId =
    instance || forDrillDown ? item['entity.id'] : sloId

  return buildWidgetContext({
    groupType:
      instance || forDrillDown
        ? AVAILABLE_GROUP_TYPES.SLO_INSTANCE
        : AVAILABLE_GROUP_TYPES.SLO,
    category: WidgetTypeConstants.CHART,
    widgetType: WidgetTypeConstants.LINE,
    timeline,
    counters: [
      makeCounter(
        `${counterPrefix}error.budget.left.percent`,
        'avg',
        'Monitor',
        Array.isArray(calculatedEntityId)
          ? calculatedEntityId
          : [calculatedEntityId]
      ),
    ],
    preFilters: {
      condition: 'and',
      inclusion: 'include',
      conditions:
        forDrillDown && !forInstance
          ? []
          : [
              ...(instance
                ? [
                    {
                      operand: 'slo.instance~instance.name',
                      operator: '=',
                      value: `${instance}`,
                    },
                  ]
                : [
                    {
                      operand: 'slo~instance.name',
                      operator: '=',
                      value: `${cycleId}`,
                    },
                  ]),
            ],
    },
    // ...(instance ? { resultBy: ['slo.instance', 'monitor'] } : {}),
    granularity: { value: 15, unit: 'm' },
  })
    .setExtraData({
      'store.suffix': `${sloId}###${cycleId}`,
    })
    .getContext()
}

export function getBurnRateWidgetDef(
  slo,
  timeline,
  item = {},
  forDrillDown = false
) {
  const forInstance = slo.sloFor !== 'monitor'
  const instance = item['slo_instance']

  const counterPrefix = !forDrillDown
    ? 'slo~'
    : forInstance
    ? 'slo.instance~'
    : 'slo.object.'
  const sloId = slo.id
  const cycleId = slo.cycleId

  const calculatedEntityId =
    instance || forDrillDown ? item['entity.id'] : sloId
  return buildWidgetContext({
    groupType:
      instance || forDrillDown
        ? AVAILABLE_GROUP_TYPES.SLO_INSTANCE
        : AVAILABLE_GROUP_TYPES.SLO,
    category: WidgetTypeConstants.CHART,
    widgetType: WidgetTypeConstants.LINE,
    timeline,
    counters: [
      makeCounter(
        `${counterPrefix}burn.rate.percent`,
        'sum',
        'Monitor',
        Array.isArray(calculatedEntityId)
          ? calculatedEntityId
          : [calculatedEntityId]
      ),
    ],
    preFilters: {
      condition: 'and',
      inclusion: 'include',
      conditions:
        forDrillDown && !forInstance
          ? []
          : [
              ...(instance
                ? [
                    {
                      operand: 'slo.instance~instance.name',
                      operator: '=',
                      value: `${instance}`,
                    },
                  ]
                : [
                    {
                      operand: 'slo~instance.name',
                      operator: '=',
                      value: `${cycleId}`,
                    },
                  ]),
            ],
    },
    granularity: { value: 15, unit: 'm' },
  })
    .setExtraData({
      'store.suffix': `${sloId}###${cycleId}`,
    })
    .getContext()
}

export function getSloMetricWidgetDef(slo, item, instance, timeline) {
  const startTime = Moment.unix(slo.sloCycleStartTime)
  const endTime = Moment.unix(slo.sloCycleEndTime)

  timeline = timeline || {
    selectedKey: 'custom',
    startDate: startTime.format(DATE_FORMAT),
    endDate: endTime.format(DATE_FORMAT),
    startTime: startTime.format(TIME_FORMAT),
    endTime: endTime.format(TIME_FORMAT),
  }
  const metric = slo.counter
  const operator = slo.operator

  const value = slo.value
  let unitConvertedValue = value

  if (isUnitConvertible(metric, value)) {
    unitConvertedValue = applyUnit(metric, value)
  }

  // const unitConvertedValue = applyUnit(metric, value)

  const counter = makeCounter(slo.counter, 'avg', 'Monitor', [
    item['entity.id'],
  ])

  const instanceType = metric.split('~')[0]
  return buildWidgetContext({
    groupType: AVAILABLE_GROUP_TYPES.METRIC,
    category: WidgetTypeConstants.CHART,
    widgetType: WidgetTypeConstants.LINE,
    counters: [counter],
    timeline,
    ...(instance && instanceType
      ? {
          preFilters: {
            condition: 'and',
            inclusion: 'include',
            conditions: [
              {
                operand: `${instanceType}~instance.name`,
                operator: '=',
                value: `${instance}`,
              },
            ],
          },
        }
      : {}),
  })
    .setWidgetProperties({
      styleSetting: {
        legendEnabled: true,
        chartOptions: {
          yAxis: {
            plotLines: [
              {
                zIndex: 3,
                dashStyle: 'ShortDash',

                value: +value,
                color: 'var(--primary)',
                width: 1,
                label: {
                  text: `${operator} ${unitConvertedValue}`,
                  style: {
                    color: `var(--primary)`,
                  },
                },
              },
            ],
          },
        },
      },
    })
    .getContext()
}
