import Invert from 'lodash/invert'

export const AVAILABLE_SLO_TYPES = {
  PERFORMANCE: 'Performance',
  AVAILABILITY: 'Availability',
}

export const AVAILABLE_COLUMNS = [
  {
    key: 'name',
    name: 'Name',

    searchable: true,
    sortable: true,
    disable: true,
  },
  {
    key: 'status',
    name: 'Status',

    searchable: true,
    sortable: true,
  },
  {
    key: 'type',
    name: 'SLO Type',
    searchable: true,
    sortable: true,
  },
  {
    key: 'frequency',
    name: 'Frequency',

    searchable: true,
    sortable: true,
  },
  {
    key: 'target',
    name: 'Target',

    searchable: true,
    sortable: true,
  },
  {
    key: 'slo_achieved_percent_last',
    name: 'Achieved',
    searchable: true,
    sortable: true,
  },
  {
    key: 'slo_violated_percent_last',
    name: 'Violation',

    searchable: true,
    sortable: true,
  },
  {
    key: 'slo_error_budget_left_seconds_last',
    name: 'Error Budget Left',
    searchable: true,
    sortable: true,
  },
  {
    key: 'slo_mttr_seconds_last',
    name: 'MTTR',
    searchable: true,
    sortable: true,
  },
  {
    key: 'slo_mtbf_seconds_last',
    name: '<PERSON><PERSON><PERSON>',
    searchable: true,
    sortable: true,
  },
]

export const SLO_STATUS = {
  BREACHED: 'Breached',
  WARNING: 'Warning',
  OK: 'Ok',
  // NOT_CALCULATED: 'not_calculated',
}

export const SLO_STATUS_MAP = {
  0: SLO_STATUS.OK,
  1: SLO_STATUS.BREACHED,
  2: SLO_STATUS.WARNING,
  // 3: SLO_STATUS.NOT_CALCULATED,
}

export const SLO_STATUS_FLAP = {
  HEALTHY: 'Healthy',
  DEGRADED: 'Degraded',
  NOT_CALCULATED: 'Not Calculated',
  CORRECTED: 'Corrected',
}

export const SLO_STATUS_FLAP_MAP = {
  0: SLO_STATUS_FLAP.HEALTHY,
  1: SLO_STATUS_FLAP.DEGRADED,
  2: SLO_STATUS_FLAP.NOT_CALCULATED,
  3: SLO_STATUS_FLAP.CORRECTED,
}

export const SLO_STATUS_MAP_INVERTED = Invert(SLO_STATUS_FLAP_MAP)

export const SLO_STATUS_FLAP_MAP_COLORS = {
  [SLO_STATUS_MAP_INVERTED[SLO_STATUS_FLAP.HEALTHY]]: 'var( --severity-clear)',
  [SLO_STATUS_MAP_INVERTED[SLO_STATUS_FLAP.DEGRADED]]:
    'var(--severity-critical)',
  [SLO_STATUS_MAP_INVERTED[SLO_STATUS_FLAP.NOT_CALCULATED]]:
    'var(--severity-unreachable)',
  [SLO_STATUS_MAP_INVERTED[SLO_STATUS_FLAP.CORRECTED]]:
    'var(--severity-unknown)',
}

export const COLOR_TO_STATUS_FLAP_MAP = Invert(SLO_STATUS_FLAP_MAP_COLORS)

export const REVERSE_SLO_STATUS_MAP = Invert(SLO_STATUS_MAP)
