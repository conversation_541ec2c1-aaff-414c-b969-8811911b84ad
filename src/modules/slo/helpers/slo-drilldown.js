import { SEVERITY_MAP, SEVERITY_NUMBER_MAP } from '@/src/data/monitor'
import Uniq from 'lodash/uniq'

/**
 *
 * sample data
 {
  "id": 10001,
  "slo.routes": [
    {
      "slo.source": "172.16.10.1",
      "slo.level": 6,
      "slo.destination": "172.16.10.2",
      "ping.lost.packet.percent": 20,
      "ping.min.latency.ms": 20,
      "severity": "WARNING",
      "slo.routes": [
        {
          "slo.source": "172.16.10.2",
          "slo.level": 6,
          "slo.destination": "***********",
          "ping.lost.packet.percent": 20,
          "ping.min.latency.ms": 20,
          "severity": "WARNING",
          "slo.routes": [

          ]
        }
      ]
    },
    {
      "slo.source": "172.16.10.1",
      "slo.level": 6,
      "slo.destination": "***********",
      "ping.lost.packet.percent": 20,
      "ping.min.latency.ms": 20,
      "severity": "WARNING",
      "slo.routes": [
        {
          "slo.source": "***********",
          "slo.level": 6,
          "slo.destination": "***********",
          "ping.lost.packet.percent": 20,
          "ping.min.latency.ms": 20,
          "severity": "WARNING",
          "slo.routes": [

          ]
        }
      ]
    }
  ]
}

 */

const keyMaps = {
  'slo.source': 0,
  'slo.destination': 1,
  'slo.destination.ip': 2,
  'slo.hop.ttl': 3,
  'slo.routes': 4,
  'slo.latency.ms': 5,
  'slo.min.latency.ms': 6,
  'slo.max.latency.ms': 7,
  'slo.packet.lost.percent': 8,
  'path.link.transit.likelihood': 9,
  severity: 10,
  status: 11,
}

function extractValue(row, key) {
  return row[keyMaps[key]]
}

function convertHopToKeys(hop) {
  return Object.keys(keyMaps).reduce((prev, key) => {
    prev[key] = extractValue(hop, key)
    return prev
  }, {})
}

export function getNodesAndEdges(slo, data, options) {
  let monitorsMap = options.monitors || {}
  let severityMap = {}
  let monitorIds = []
  let nodes = {}
  let edges = []
  let hops = extractValue(data, 'slo.routes') || []
  function traverseHop(hop, levelOptions = {}) {
    let sourceId = String(hop['slo.source']).replace(/[.\s]/g, '')
    let destinationId = String(hop['slo.destination.ip']).replace(/[.\s]/g, '')

    let sourceMonitor = monitorsMap[hop['slo.source']] || {}
    if (sourceMonitor.id) {
      monitorIds.push(sourceMonitor.id)
    }

    let sourceSeverity = String(
      sourceMonitor.severity || severityMap[sourceId] || 'UNKNOWN'
    )

    let sourceMetadata = options.metadata[hop['slo.source']] || {}
    let destinationMetadata = options.metadata[hop['slo.destination.ip']] || {}
    if (
      sourceMetadata['originated.by'] &&
      sourceMetadata['originated.by'] === destinationMetadata['originated.by']
    ) {
      nodes[`${sourceMetadata['originated.by']}-${levelOptions.level}`] = {
        grabbable: false,
        data: {
          id: `${sourceMetadata['originated.by']}-${levelOptions.level}`,
          name: `${sourceMetadata['object.ip']}\n\n${sourceMetadata['owned.by']}`,
          ownedBy: sourceMetadata['owned.by'],
          originatedBy: sourceMetadata['originated.by'],
          monitor: {},
          theme: options.theme,
          type: 'group',
          zoom: 1,
          ...(nodes[
            `${sourceMetadata['originated.by']}-${levelOptions.level - 1}`
          ]
            ? {
                parent: `${sourceMetadata['originated.by']}-${
                  levelOptions.level - 1
                }`,
              }
            : {}),
        },
        classes: `group ${options.filledNode ? 'filled' : ''}`,
      }
    }
    if (!nodes[sourceId]) {
      nodes[sourceId] = {
        grabbable: false,
        data: {
          root: hop.root,
          ip: hop['slo.source'],
          name:
            sourceMonitor.name ||
            (hop['slo.source'] || '').indexOf('Timeout') >= 0
              ? hop['slo.source'].split('-')[0]
              : hop['slo.source'],
          theme: options.theme,
          type: sourceMonitor.type,
          classes: `${sourceMonitor.type || ''} ${
            options.filledNode ? 'filled' : ''
          }`,
          severity: sourceSeverity,
          severityNumber: SEVERITY_MAP[sourceSeverity],
          monitor: sourceMonitor,
          id: sourceId,
          hop,
          tooltip: true,
          ...(sourceMetadata['originated.by']
            ? {
                origin: sourceMetadata['originated.by'],
              }
            : {}),
          ...(destinationMetadata['originated.by'] ===
          sourceMetadata['originated.by']
            ? {
                parent: `${sourceMetadata['originated.by']}-${levelOptions.level}`,
              }
            : {}),
          rawId: hop['slo.source'],
          zoom: 1,
        },
        classes: `${hop.type || ''} ${(sourceSeverity || '').toLowerCase()} ${
          options.filledNode ? 'filled' : ''
        }`,
      }
    } else {
      nodes[sourceId] = {
        ...nodes[sourceId],
        data: {
          ...nodes[sourceId].data,
          ...(sourceMetadata['originated.by']
            ? {
                origin: sourceMetadata['originated.by'],
              }
            : {}),
          ...(destinationMetadata['originated.by'] ===
          sourceMetadata['originated.by']
            ? {
                parent: `${sourceMetadata['originated.by']}-${levelOptions.level}`,
              }
            : {}),
        },
      }
    }

    let destinationMonitor = monitorsMap[hop['slo.destination.ip']] || {}
    if (destinationMonitor.id) {
      monitorIds.push(destinationMonitor.id)
    }
    let destinationSeverity = String(
      destinationMonitor.severity || hop.severity || 'UNKNOWN'
    )

    if (severityMap[destinationId]) {
      let existingSeverity = SEVERITY_MAP[severityMap[destinationId]]

      let maxSeverityNumber = Math.min(
        existingSeverity,
        SEVERITY_MAP[destinationSeverity]
      )
      severityMap[destinationId] = SEVERITY_NUMBER_MAP[maxSeverityNumber]
      destinationSeverity = severityMap[destinationId]
    } else {
      severityMap[destinationId] = destinationSeverity
    }

    nodes[destinationId] = {
      grabbable: false,
      data: {
        root: false,
        ip: hop['slo.destination.ip'],
        name:
          destinationMonitor.name ||
          (hop['slo.destination'] || '').indexOf('Timeout') >= 0
            ? hop['slo.destination'].split('-')[0]
            : hop['slo.destination'] || hop['slo.source'],
        theme: options.theme,
        type: destinationMonitor.type,
        tooltip: true,
        classes: `${destinationMonitor.type || ''} ${
          options.filledNode ? 'filled' : ''
        }`,
        ...((hop['slo.routes'] || []).length === 0
          ? {
              type: 'slo_destination',
              classes: `destination ${options.filledNode ? 'filled' : ''}`,
            }
          : {}),
        severity: destinationSeverity,
        severityNumber: SEVERITY_MAP[destinationSeverity],
        monitor: destinationMonitor,
        id: destinationId,
        hop,
        ...(destinationMetadata['originated.by']
          ? { origin: destinationMetadata['originated.by'] }
          : {}),
        ...(destinationMetadata['originated.by'] ===
        sourceMetadata['originated.by']
          ? { parent: destinationMetadata['originated.by'] }
          : {}),
        rawId: hop['slo.destination.ip'],
        zoom: 1,
      },
      classes: `${destinationMonitor.type || ''} ${(
        destinationSeverity || ''
      ).toLowerCase()} ${options.filledNode ? 'filled' : ''}`,
      ...((hop['slo.routes'] || []).length === 0
        ? {
            type: 'slo_destination',
            classes: `destination ${(
              destinationSeverity || ''
            ).toLowerCase()} ${options.filledNode ? 'filled' : ''}`,
          }
        : {}),
    }

    if (sourceId !== destinationId && !edges[`${sourceId}-${destinationId}`]) {
      edges[`${sourceId}-seperator-${destinationId}`] = {
        grabbable: false,
        data: {
          id: `${sourceId}-seperator-${destinationId}`,
          theme: options.theme,
          severity: String(hop.severity || ''),
          source: sourceId,
          target: destinationId,
          edgelabel: `${hop['slo.latency.ms']}ms`,
          tooltip: true,
          hop,
          level: hop['slo.level'],
          latency: hop['slo.latency.ms'],
          packetLossPercent: hop['slo.packet.lost.percent'],
          zoom: 1,
        },
        classes: [
          'has-arrow',
          String(hop.severity || '').toLowerCase(),
          ...(options.classes || []),
        ],
      }
    }

    if ((hop['slo.routes'] || []).length) {
      hop['slo.routes'].forEach((hop) =>
        traverseHop(convertHopToKeys(hop), {
          ...sourceMetadata,
          level: levelOptions.level + 1,
        })
      )
    }
  }

  hops.forEach((hop) =>
    traverseHop({ ...convertHopToKeys(hop), root: true }, { level: 1 })
  )
  return {
    nodes: Array.from(Object.values(nodes)),
    edges: Array.from(Object.values(edges)),
    monitorIds: Uniq(monitorIds),
  }
}
