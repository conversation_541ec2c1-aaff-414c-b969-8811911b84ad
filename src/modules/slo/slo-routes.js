import lazyLoadView from '@router/lazy-loader'
import ContainerView from './views/main'
import configs from './config'
// import Constants from '@constants'

const routePrefix = configs.routePrefix

const moduleName = configs.name

const routeNamePrefix = configs.routeNamePrefix

export default [
  {
    path: `/${routePrefix}`,
    component: ContainerView,
    meta: { moduleName },
    children: [
      {
        path: '',
        name: routeNamePrefix,
        component: () =>
          lazyLoadView(
            import(
              /* webpackChunkName: "alert-metric-explorer" */ './views/slo'
            )
          ),
        meta: {},
      },

      {
        path: 'detail/:id/:tab',
        name: `${routeNamePrefix}.detail`,
        component: () =>
          lazyLoadView(
            import(
              /* webpackChunkName: "alert-metric-explorer" */ './views/details.vue'
            )
          ),
        meta: {},
      },
      {
        path: 'history-detail/:id/:cycleId',
        name: `${routeNamePrefix}.history-detail`,
        component: () =>
          lazyLoadView(
            import(
              /* webpackChunkName: "alert-metric-explorer" */ './views/history-detail.vue'
            )
          ),
        meta: {},
      },
    ],
  },
]
