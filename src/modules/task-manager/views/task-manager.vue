<template>
  <MonitorProvider :search-params="monitorSearchParams">
    <div class="flex flex-col flex-1 content-inner-panel min-h-0">
      <MRow :gutter="0" class="main-title-panel">
        <MCol>
          <h4 class="text-primary-alt header-primery-inner-color">
            <MIcon name="task-manager" class="text-primary-alt pl-2 mt-2" />
            Task Manager
          </h4>
        </MCol>
        <MCol
          v-if="currentTab === 'running'"
          class="flex items-center justify-end header-spacer"
          :class="{
            'text-secondary-yellow': isSubscribed,
            'text-secondary-green': !isSubscribed,
          }"
        >
          <template v-if="isSubscribed">
            Pause
            <MIcon
              id="pause-task-manager"
              name="pause-circle"
              class="text-secondary-yellow cursor-pointer ml-1"
              @click="pauseSubscription"
            />
          </template>
          <template v-else>
            Resume
            <MIcon
              id="resume-task-manager"
              name="play-circle"
              class="text-secondary-green cursor-pointer ml-1"
              @click="resumeSubscription"
            />
          </template>
        </MCol>
      </MRow>
      <div class="flex flex-col min-h-0 flex-1 ml-2">
        <div class="pr-0">
          <MPersistedTab
            v-model="currentTab"
            default-value="running"
            module-key="task-manager"
          >
            <template v-slot="{ tab, setTab }">
              <MTab :value="tab" @change="setTab">
                <MTabPane key="running" tab="Running" />
                <MTabPane key="history" tab="History" />
              </MTab>
            </template>
          </MPersistedTab>
        </div>
        <MRow :gutter="0" class="mt-4">
          <MCol :size="5">
            <MInput
              v-model="searchTerm"
              class="search-box"
              placeholder="Search"
              name="search"
            >
              <template v-slot:prefix>
                <MIcon name="search" />
              </template>
              <template v-if="searchTerm" v-slot:suffix>
                <MIcon
                  name="times-circle"
                  class="text-neutral-light cursor-pointer"
                  @click="searchTerm = undefined"
                />
              </template>
            </MInput>
          </MCol>
          <MCol v-if="currentTab !== 'running'" :size="7" class="text-right">
            <MButton
              id="filter-btn"
              :shadow="false"
              :rounded="false"
              shape="circle"
              class="mr-2"
              :variant="showFilters ? 'neutral-lighter' : 'transparent'"
              @click="showFilters = !showFilters"
            >
              <MIcon name="filter" />
            </MButton>
          </MCol>
          <MCol
            v-if="showFilters && currentTab !== 'running'"
            :size="12"
            class="slide-filters mt-4"
          >
            <div class="px-4">
              <TaskFilters
                v-model="filters"
                :ip-address-options="ipAddressOptions"
                :users-options="usersOptions"
                :events="events"
                @hide="showFilters = !showFilters"
                @change="handleFilterChange"
              />
            </div>
          </MCol>
        </MRow>
        <TaskGrid
          ref="gridRef"
          :key="currentTab"
          :filters="filters"
          :search-term="searchTerm"
          :type="currentTab"
          :subscribed.sync="isSubscribed"
          :events.sync="events"
          @task-log="showLogForTask"
        />
        <TaskLog
          :open="showTaskLogForItem !== null"
          :task="showTaskLogForItem"
          @hide="showTaskLogForItem = null"
        />
      </div>
    </div>
  </MonitorProvider>
</template>

<script>
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import { fetchUsersApi } from '@modules/settings/users-settings/users-api'
import TaskFilters from '../components/task-filters.vue'
import TaskLog from '../components/task-log.vue'
import TaskGrid from '../components/task-grid.vue'
import { getEventContextFromId, transfromTask } from '../helpers/task'

export default {
  name: 'TaskManager',
  components: { TaskLog, TaskFilters, MonitorProvider, TaskGrid },
  data() {
    return {
      currentTab: 'running',
      showTaskLogForItem: null,
      showFilters: false,
      filters: {
        objectId: undefined,
        user: undefined,
        status: undefined,
        time: undefined,
        eventName: undefined,
      },
      searchTerm: undefined,
      isSubscribed: true,
      usersOptions: [],
      events: [],
      ipAddressOptions: [],
    }
  },
  computed: {
    monitorSearchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.WIRELESS,
          this.$constants.OTHER,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.SDN,
          this.$constants.STORAGE,
          this.$constants.CONTAINER_ORCHESTRATION,
        ],
      }
    },
  },
  watch: {
    currentTab(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.searchTerm = undefined
        this.showFilters = false
        this.filters = {
          objectId: undefined,
          user: undefined,
          status: this.currentTab === 'running' ? undefined : 'fail',
          time: undefined,
          eventName: undefined,
        }
      }
    },
  },
  created() {
    this.fetchUsers()
  },
  beforeDestroy() {
    this.pauseSubscription()
  },
  methods: {
    showLogForTask(item) {
      this.showTaskLogForItem = Object.freeze(item)
      getEventContextFromId(item.id).then((data) => {
        this.showTaskLogForItem = Object.freeze(transfromTask(data, true))
      })
    },
    fetchUsers() {
      fetchUsersApi().then((data) => {
        this.usersOptions = Object.freeze(
          data.map((u) => ({ key: u.userName, text: u.userName }))
        )
      })
    },
    handleFilterChange() {
      this.$nextTick(() => {
        this.$refs.gridRef.applyFilters()
      })
    },
    pauseSubscription() {
      this.$refs.gridRef.pauseSubscription()
    },
    resumeSubscription() {
      this.$refs.gridRef.subscribe()
    },
  },
}
</script>
