<template>
  <FlotoContentLoader v-if="shouldShowModule" :loading="loading">
    <MetricExplorerProvider ref="metricExplorerProviderRef">
      <CounterProvider :search-params="counterSearchParams">
        <MonitorProvider :search-params="monitorSearchParams">
          <FlotoFixedView>
            <div class="flex flex-col h-full flex-1 content-inner-panel">
              <FlotoPageHeader title="Metric Explorer" use-divider main-header>
                <template v-slot:before-title>
                  <MenuToggleButton
                    class="ml-2"
                    :visible="menuVisible"
                    @click="toggleSidebar"
                  />
                  <div class="mx-2 menu-divider" />
                  <MIcon
                    name="metric-explorer"
                    size="lg"
                    class="text-primary-alt mr-3 pl-2"
                  />
                </template>
                <template v-slot:after-title>
                  <div class="flex min-w-0 justify-end flex-1 items-start">
                    <div class="material-input flex items-center">
                      <MRadioGroup
                        v-model="chartType"
                        as-button
                        :options="chartTypeOptions"
                        class="chart-selector-radio mr-4 relative"
                      >
                        <template v-slot:option="{ option }">
                          <WidgetTypeIcon
                            :widget-type="option.icon"
                            :tooltip="option.text"
                            :size="28"
                            class="relative"
                            :selected="option.value === chartType"
                          />
                        </template>
                      </MRadioGroup>
                      <TimeRangePicker
                        v-model="timeline"
                        :hide-custom-time-range="false"
                      />
                    </div>
                    <GranularitySelection
                      v-model="granularity"
                      should-show-query-type
                    />
                    <MButton
                      variant="neutral-lightest"
                      title="Capture"
                      class="mx-2 squared-button"
                      @click="convertToImage"
                    >
                      <MIcon name="image" class="excluded-header-icon" />
                    </MButton>
                    <MButton
                      class="squared-button"
                      variant="neutral-lightest"
                      title="Clear All"
                      @click="removeAll"
                    >
                      <MIcon name="clear-all" class="excluded-header-icon" />
                    </MButton>
                    <MButton
                      class="ml-2 squared-button"
                      variant="neutral-lightest"
                      title="Share"
                      @click="shareMetricExplorer"
                    >
                      <MIcon name="share-alt" class="excluded-header-icon" />
                    </MButton>

                    <SaveViewForm
                      :disabled-save-button="disabledSaveButton"
                      :selected-view="selectedView"
                      :save-fn="createMetricExplorerView"
                      :update-fn="updateMetricExplorerView"
                    />
                  </div>
                </template>
              </FlotoPageHeader>

              <div class="flex flex-col min-h-0 flex-1">
                <MRow class="min-h-0 flex-1 relative" :gutter="0">
                  <Transition name="sidebar">
                    <MetricExplorerSavedViewProvider
                      @select-view="setSelectedMetrics"
                    >
                      <SavedMetricExplorersList
                        v-if="menuVisible"
                        :selected-metric-explorer-view="selectedView"
                        :menu-visible="menuVisible"
                        @visible-change="menuVisible = $event"
                        @selectionChange="selectedView = $event"
                        @select-view="setSelectedMetrics"
                        @create="onCreateMetricExplorerView"
                      />
                    </MetricExplorerSavedViewProvider>
                  </Transition>
                  <MCol :size="3" class="h-full border-right">
                    <div
                      class="flex-col flex-1 min-h-0 h-full flex pt-2 page-background metric-explorer-bg"
                    >
                      <div
                        class="flex justify-between items-center px-2 border-bot"
                      >
                        <div class="flex-1 min-w-0">
                          <MTab
                            :value="selectedTab"
                            class="metric-picker-tabs no-border"
                            @change="handleChangeTab"
                          >
                            <MTabPane
                              v-for="(m, index) in monitors"
                              :key="m.guid"
                            >
                              <template v-slot:tab>
                                <div
                                  class="flex min-w-0"
                                  style="max-width: 150px"
                                >
                                  <span
                                    class="text-ellipsis"
                                    :title="m.name || `Metric ${index + 1}`"
                                  >
                                    {{ m.name || `Metric ${index + 1}` }}
                                  </span>
                                  <MIcon
                                    v-if="monitors.length > 1"
                                    name="times"
                                    class="m-0 ml-1 text-neutral-light"
                                    style="margin-right: 0"
                                    @click.stop="removeTab(m.guid)"
                                  />
                                </div>
                              </template>
                            </MTabPane>
                          </MTab>
                        </div>
                        <div class="inline-block py-1" style="flex-shrink: 0">
                          <MButton
                            v-if="monitors.length < maxAllowedMonitors"
                            class="squared-button justify-center items-center"
                            variant="neutral-lightest"
                            style="
                              width: 25px !important;
                              height: 25px !important;
                            "
                            @click="handleChangeTab('add')"
                          >
                            <MIcon name="plus" class="text-primary" />
                          </MButton>
                        </div>
                      </div>
                      <div
                        v-if="currentMetricSelector"
                        class="flex min-h-0 flex-col flex-1 relative"
                      >
                        <KeepAlive>
                          <MetricPicker
                            :key="currentMetricSelector.guid"
                            :can-add-counter="addedCounters < maxAllowedCharts"
                            :initial-monitor-id="currentMetricSelector.id"
                            :default-selected-instance-data="
                              currentMetricSelector &&
                              currentMetricSelector.instance &&
                              currentMetricSelector.instanceType
                                ? {
                                    instance: currentMetricSelector.instance,
                                    instanceType:
                                      currentMetricSelector.instanceType,
                                  }
                                : undefined
                            "
                            @select-monitor="
                              handleSelectMonitor(
                                currentMetricSelector.guid,
                                $event
                              )
                            "
                            @add-counter="handleAddCounterFromMetricPicker"
                          />
                        </KeepAlive>
                      </div>
                    </div>
                  </MCol>
                  <MCol
                    :size="9"
                    class="flex-col min-h-0 h-full dashboard-container metric-explorer-bg"
                  >
                    <ScrollableExport ref="exportRef">
                      <!-- <template v-slot="{ convertToImage }"> -->
                      <div class="flex flex-col min-h-0 h-full flex-1 ml-2">
                        <div
                          ref="scrollContainerRef"
                          class="overflow-auto dashboard-container metric-explorer-bg"
                        >
                          <div
                            v-for="(chart, index) in selectedCharts"
                            :key="chart.guid"
                            :style="{
                              height: `${Math.max(singleChartHeight, 250)}px`,
                            }"
                            class="chart-placeholder"
                          >
                            <MetricExplorerChart
                              v-if="chart.counter"
                              :chart="chart"
                              :chart-type="
                                chartType.indexOf('stacked') >= 0
                                  ? chartType.split('-').pop()
                                  : chartType
                              "
                              :stacked="chartType.indexOf('stacked') >= 0"
                              :timeline="timeline"
                              :save-fn="createMetricExplorerView"
                              @add-counter-to-explorer="
                                handleAddCounterToExplorerAndMerge
                              "
                              @remove="handleRemoveChart(chart)"
                              @apply-chart-changes="
                                applyChartChanges(chart, $event, index)
                              "
                            />
                            <div
                              v-else
                              class="chart-placeholder flex items-center justify-center my-2 flex-col"
                              @drop="dropSeries($event, index)"
                              @dragover="allowDropSeries"
                            >
                              <h3 class="m-0 text-neutral-light">
                                <MIcon name="chart" size="2x" />
                              </h3>
                              <h3 class="m-0 text-neutral-light">
                                Drop metric here to view trend
                              </h3>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- </template> -->
                    </ScrollableExport>
                  </MCol>
                </MRow>
              </div>
            </div>
            <div
              v-if="tempChartCounter"
              class="chart-placeholder"
              style="display: none"
            >
              <MetricChart
                v-if="tempChartCounter"
                :counter="tempChartCounter.key"
                :object-id="tempChartCounter.objectId"
                :instance="tempChartCounter.instance"
                :instance-type="tempChartCounter.instanceType"
                :timeline="timeline"
                :color="tempChartCounter.color"
                :event="$constants.UI_WIDGET_RESULT_EVENT"
                :remove-series-on-remove="false"
                @series-registered="tempChartCounter = undefined"
              />
            </div>
            <ShareModal
              v-if="showShareModal"
              share-type="Metric"
              :processing="processing"
              @hide="showShareModal = false"
              @submit="handleShareCapture"
            />
          </FlotoFixedView>
        </MonitorProvider>
      </CounterProvider>
    </MetricExplorerProvider>
  </FlotoContentLoader>

  <FlotoModuleNoData v-else module="metric-explorer" />
</template>

<script>
import Uniq from 'lodash/uniq'
import LRange from 'lodash/range'
import FindIndex from 'lodash/findIndex'
import Bus from '@utils/emitter'
import api from '@api'
import Constants from '@constants'
import Moment from 'moment'
import { generateId } from '@utils/id'
import { colors } from '@utils/chart-colors'
import { objectDBWorker } from '@/src/workers'
import { authComputed } from '@/src/state/modules/auth'

import ScrollableExport from '@components/scrollable-export.vue'
import TimeRangePicker from '@components/widgets/time-range-picker.vue'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import MetricChart from '@components/metric-explorer/metric-chart.vue'
import MetricExplorerProvider from '@components/metric-explorer/metric-explorer-provider.vue'
import {
  WidgetTypeConstants,
  QUERY_TYPE_OPTIONS,
} from '@components/widgets/constants'
import WidgetTypeIcon from '@components/widgets/widget-type-icon/widget-type-icon.vue'
import MetricPicker from '../components/metric-picker.vue'
import MetricExplorerChart from '../components/metric-explorer-chart.vue'
import { UserPreferenceMethods } from '@/src/state/modules/user-preference'
import GranularitySelection from '@/src/components/widgets/granularity-selection.vue'
import CounterProvider from '@components/data-provider/counter-provider.vue'
import ShareModal from '@modules/alert/components/stream/share-model.vue'
import MenuToggleButton from '@components/menu-toggle-button.vue'
import SavedMetricExplorersList from '../components/saved-metric-explorers-list.vue'
import MetricExplorerSavedViewProvider from '../components/metric-explorer-saved-view-provider.vue'
import SaveViewForm from '../components/save-view-form.vue'
import { createExplorerApi, updateExplorerApi } from '@api/explorer-api'

const MAX_ALLOWED_MONITORS = 10
const MAX_ALLOWED_CHARTS = 10

export default {
  name: 'MetricExplorer',
  components: {
    GranularitySelection,
    MonitorProvider,
    MetricPicker,
    TimeRangePicker,
    MetricChart,
    MetricExplorerProvider,
    MetricExplorerChart,
    ScrollableExport,
    WidgetTypeIcon,
    CounterProvider,
    ShareModal,
    MenuToggleButton,
    SavedMetricExplorersList,
    SaveViewForm,
    MetricExplorerSavedViewProvider,
  },
  data() {
    this.chartTypeOptions = [
      { value: 'area', text: 'area', icon: WidgetTypeConstants.AREA },
      { value: 'line', text: 'line', icon: WidgetTypeConstants.LINE },
      {
        value: 'stacked-line',
        text: 'stacked line',
        icon: WidgetTypeConstants.STACKED_LINE,
      },
      {
        value: 'stacked-area',
        text: 'stacked area',
        icon: WidgetTypeConstants.STACKED_AREA,
      },
    ]
    const guid = generateId()
    return {
      loading: true,
      compareModalProps: null,
      monitors: [{ id: undefined, guid }],
      selectedCharts: LRange(0, MAX_ALLOWED_CHARTS).map(() => ({
        guid: generateId(),
      })),
      timeline: {
        selectedKey: 'today',
      },
      chartType: 'line',
      singleChartHeight: false,
      selectedTab: guid,
      tempChartCounter: undefined,
      lastUsedColorIndex: 0,
      shouldShowModule: true,
      granularity: {
        value: 5,
        unit: 'm',
        queryType: QUERY_TYPE_OPTIONS.AGGREGATION,
      },
      showShareModal: false,
      processing: false,
      menuVisible: false,
      selectedView: undefined,
    }
  },
  computed: {
    ...authComputed,

    counterSearchParams() {
      return {
        'visualization.group.type': 'metric',
      }
    },
    maxAllowedMonitors() {
      return MAX_ALLOWED_MONITORS
    },
    maxAllowedCharts() {
      return MAX_ALLOWED_CHARTS
    },
    addedMonitors() {
      return Uniq(
        this.selectedCharts.map(({ objectId }) => objectId).filter(Boolean)
      )
    },
    currentMetricSelector() {
      const selectedTab = this.selectedTab
      return this.monitors.find((m) => m.guid === selectedTab)
    },
    monitorSearchParams() {
      return {}
    },
    addedCounters() {
      return this.selectedCharts.filter((c) => c && c.key).length
    },
    disabledSaveButton() {
      return this.addedCounters === 0
    },
  },
  watch: {
    granularity: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.$refs.metricExplorerProviderRef.setGranularity(newValue)
        }
      },
    },
  },
  async created() {
    const q = this.$route.query

    if (q.instanceDrillDownContext && q.counters) {
      try {
        let counters = JSON.parse(atob(decodeURIComponent(q.counters)))

        let instanceDrillDownContext = JSON.parse(
          atob(decodeURIComponent(q.instanceDrillDownContext))
        )

        const monitor = await objectDBWorker.getObjectById(
          instanceDrillDownContext.monitor
        )

        this.monitors = Object.freeze([
          {
            ...(monitor || {}),

            instance: instanceDrillDownContext.instance,
            instanceType: instanceDrillDownContext.instanceType,
          },
        ])
        // this.selectedCharts = [
        //   ...[
        //     {
        //       ...this.monitors.map((monitor, index) => ({
        //         color: colors[index],
        //         counter: metrics.replace(/~^/g, '.'),
        //         counterName: metrics,
        //         dataType: ['Numeric'],
        //         guid: generateId(),
        //         isStatusCounter: false,
        //         key: metrics,
        //         metricPlugins: [],
        //         name: metrics,
        //         objectId: monitor.id,
        //         instance: monitor.instance,
        //         instanceType: instanceType,
        //       }))?.[0],
        //     },
        //   ],
        //   ...this.selectedCharts.slice(this.monitors.length),
        // ]

        setTimeout(() => {
          this.selectedCharts = [
            ...counters.map((counter, index) => ({
              color: colors[index],
              counter: counter.replace(/[~^]/g, '.'),
              counterName: counter,
              dataType: ['Numeric'],
              guid: generateId(),
              isStatusCounter: false,
              key: counter,
              metricPlugins: [],
              name: counter,
              objectId: monitor.id,
              instance: instanceDrillDownContext.instance,
              instanceType: instanceDrillDownContext.instanceType,
            })),
            ...this.selectedCharts.slice(counters.length),
          ]
        }, 0)

        this.selectedTab = this.monitors[0].guid
      } catch (e) {
        console.error('failed to parse monitor ids', e)
      }
    }

    if (q.counter && q.instanceMap) {
      try {
        let metrics = JSON.parse(atob(decodeURIComponent(q.counter)))

        let instanceMap = JSON.parse(atob(decodeURIComponent(q.instanceMap)))

        let instanceType = JSON.parse(atob(decodeURIComponent(q.instanceType)))

        let monitors = await objectDBWorker.getObjects({
          name: Object.values(instanceMap).map((i) => i),
        })

        monitors = monitors.reduce((acc, monitor) => {
          acc[monitor.name] = monitor
          return acc
        }, {})
        this.monitors = Object.freeze(
          Object.keys(instanceMap)
            .filter(Boolean)
            .slice(0, MAX_ALLOWED_MONITORS)
            .map((i, index) => {
              const instanceName = i?.split(
                this.$constants.SEPARATOR_WITH_SLASH
              )?.[0]

              const monitor = monitors[instanceMap[i]]
              return {
                guid: generateId(),
                ...(monitor || {}),
                instance: instanceName,
                instanceType,

                chartContext: {
                  color: colors[index],
                  counter: metrics.replace(/~^/g, '.'),
                  counterName: metrics,
                  dataType: ['Numeric'],
                  guid: generateId(),
                  isStatusCounter: false,
                  key: metrics,
                  metricPlugins: [],
                  name: metrics,
                  objectId: monitor.id,
                  instance: instanceName,
                  instanceType,
                },
              }
            })
        )
        // this.selectedCharts = [
        //   ...[
        //     {
        //       ...this.monitors.map((monitor, index) => ({
        //         color: colors[index],
        //         counter: metrics.replace(/~^/g, '.'),
        //         counterName: metrics,
        //         dataType: ['Numeric'],
        //         guid: generateId(),
        //         isStatusCounter: false,
        //         key: metrics,
        //         metricPlugins: [],
        //         name: metrics,
        //         objectId: monitor.id,
        //         instance: monitor.instance,
        //         instanceType: instanceType,
        //       }))?.[0],
        //     },
        //   ],
        //   ...this.selectedCharts.slice(this.monitors.length),
        // ]

        this.selectedCharts = [
          ...this.monitors.map((monitor, index) => ({
            color: colors[index],
            counter: metrics.replace(/~^/g, '.'),
            counterName: metrics,
            dataType: ['Numeric'],
            guid: generateId(),
            isStatusCounter: false,
            key: metrics,
            metricPlugins: [],
            name: metrics,
            objectId: monitor.id,
            instance: monitor.instance,
            instanceType: instanceType,
          })),
        ]

        this.selectedTab = this.monitors[0].guid
      } catch (e) {
        console.error('failed to parse monitor ids', e)
      }
    }
    if (q.metrics) {
      try {
        let metrics = JSON.parse(atob(decodeURIComponent(q.metrics)))
        let monitors = await objectDBWorker.getObjects({
          id: Object.keys(metrics).map((i) => +i),
        })

        let instanceContext = q.instanceContext
          ? JSON.parse(atob(decodeURIComponent(q.instanceContext)))
          : null
        this.monitors = Object.freeze(
          monitors
            .filter(Boolean)
            .slice(0, MAX_ALLOWED_MONITORS)
            .map((i) => ({
              ...i,
              ...(instanceContext
                ? {
                    instance: instanceContext.instance,
                    instanceType: instanceContext.instanceType,
                  }
                : {}),
              guid: generateId(),
            }))
        )
        this.selectedCharts = [
          ...this.monitors.map((monitor, index) => ({
            color: colors[index],
            counter: metrics[monitor.id].replace(/~^/g, '.'),
            counterName: metrics[monitor.id],
            dataType: ['Numeric'],
            guid: generateId(),
            isStatusCounter: false,
            key: metrics[monitor.id],
            metricPlugins: [],
            name: metrics[monitor.id],
            objectId: monitor.id,
            ...(instanceContext
              ? {
                  instance: instanceContext.instance,
                  instanceType: instanceContext.instanceType,
                }
              : {}),
          })),
          ...this.selectedCharts.slice(this.monitors.length),
        ]
        this.selectedTab = this.monitors[0].guid
      } catch (e) {
        console.error('failed to parse monitor ids', e)
      }
    }
    if (q.monitor && /^\d+$/.test(q.monitor)) {
      const m = await objectDBWorker.getObjectById(+q.monitor)
      if (m) {
        this.monitors = [
          { ...this.monitors[0], id: +q.monitor },
          ...this.monitors.slice(1),
        ]
      }
    }
    const t = this.$route.query.t
    if (t) {
      try {
        this.timeline = JSON.parse(atob(decodeURIComponent(t)))
      } catch (e) {
        this.timeline = {
          selectedKey: 'today',
        }
      }
    } else {
      this.timeline = {
        selectedKey: 'today',
      }
    }
    this.loading = false

    await this.getObjectsAndUpdatePrefrence()
  },
  mounted() {
    this.singleChartHeight = parseInt(getComputedStyle(this.$el).height) / 4
  },
  methods: {
    ...UserPreferenceMethods,
    convertToImage() {
      this.$refs.exportRef.capture(
        this.$refs.scrollContainerRef,
        'metric-explorer'
      )
    },
    handleAddCounterToExplorerAndMerge(counter) {
      const totalKeys = Object.keys(
        this.$refs.metricExplorerProviderRef.serieses
      ).filter(
        (key) => this.$refs.metricExplorerProviderRef.serieses[key]
      ).length
      this.tempChartCounter = {
        ...counter,
        color: colors[totalKeys % colors.length],
      }
    },
    removeAll() {
      this.selectedCharts = LRange(0, MAX_ALLOWED_CHARTS).map(() => ({
        guid: generateId(),
      }))
    },
    setCompareCounter(counterInfo) {
      this.compareModalProps = Object.freeze(counterInfo)
    },
    removeTab(guid) {
      if (this.monitors.length > 1) {
        this.monitors = this.monitors.filter((m) => m.guid !== guid)
        if (this.selectedTab === guid) {
          this.selectedTab = this.monitors[0].guid
        }
      }
    },
    handleChangeTab(key) {
      if (key !== 'add') {
        this.selectedTab = key

        const isInstanceLevenlDrilldown = this.monitors.find(
          (m) => m.guid === key
        )

        if (
          isInstanceLevenlDrilldown &&
          isInstanceLevenlDrilldown.instance &&
          isInstanceLevenlDrilldown.instanceType &&
          isInstanceLevenlDrilldown.chartContext
        ) {
          // this.selectedCharts = [isInstanceLevenlDrilldown.chartContext]
        }
      } else {
        const guid = generateId()
        this.monitors = [...this.monitors, { id: undefined, guid }]
        this.selectedTab = guid
      }
    },
    async handleSelectMonitor(guid, monitorId) {
      const index = FindIndex(this.monitors, { guid })
      if (index !== -1) {
        const monitor = await objectDBWorker.getObjectById(monitorId)
        if (monitor) {
          this.monitors = [
            ...this.monitors.slice(0, index),
            { ...this.monitors[index], id: monitorId, name: monitor.name },
            ...this.monitors.slice(index + 1),
          ]
        }
      }
    },
    handleRemoveMonitor(guid) {
      this.monitors = this.monitors.filter((m) => m.guid !== guid)
    },
    handleAddCounter(counter, indexToInsert) {
      const index = FindIndex(this.selectedCharts, {
        counter: counter.counter,
        objectId: counter.objectId,
        ...(counter.instance ? { instance: counter.instance } : {}),
      })
      if (index === -1) {
        // const indexToInsert = FindIndex(
        //   this.selectedCounters,
        //   (c) => c === undefined
        // )
        this.selectedCharts = Object.freeze([
          ...this.selectedCharts.slice(0, indexToInsert),
          {
            ...this.selectedCharts[indexToInsert],
            ...counter,
            counter: counter.counter,
            color: colors[indexToInsert],
          },
          ...this.selectedCharts.slice(indexToInsert + 1),
        ])
      }
    },
    handleRemoveChart(counter) {
      const index = FindIndex(this.selectedCharts, {
        guid: counter.guid,
      })
      if (index !== -1) {
        this.selectedCharts = [
          ...this.selectedCharts.slice(0, index),
          { guid: this.selectedCharts[index].guid },
          ...this.selectedCharts.slice(index + 1),
        ]
      }
    },
    allowDropSeries(ev) {
      if (this.$refs.metricExplorerProviderRef.draggingContext) {
        ev.preventDefault()
      }
    },
    handleAddCounterFromMetricPicker(counter) {
      const index = FindIndex(this.selectedCharts, (i) => !i.counter)
      this.handleAddCounter(counter, index)
    },
    dropSeries(ev, index) {
      ev.preventDefault()
      const data = this.$refs.metricExplorerProviderRef.draggingContext.data
      if (data.type !== 'add-series-to-metric-explorer') {
        return
      }
      this.handleAddCounter(data.counter, index)
    },
    async getObjectsAndUpdatePrefrence() {
      this.shouldShowModule = await this.getStaticLendingPagePreferenceByModule(
        {
          module: 'object',
        }
      )

      if (!this.shouldShowModule) {
        const objects = await objectDBWorker.getObjects({})
        const hasObjects = !!objects.length

        if (this.shouldShowModule !== hasObjects) {
          await this.updateStaticLendingPagePreferences({
            lendingPagePreferences: { object: hasObjects },
          })
        }

        this.shouldShowModule =
          await this.getStaticLendingPagePreferenceByModule({
            module: 'object',
          })
      }
    },
    shareMetricExplorer() {
      this.showShareModal = true
    },
    async handleShareCapture(formData, recipients) {
      this.processing = true
      setTimeout(async () => {
        const image = await this.$refs.exportRef.getScrollableElement(
          this.$refs.scrollContainerRef,
          'metric-explorer'
        )
        return api
          .getNewClient()
          .post(
            '/upload-image',
            { file: image },
            {
              notify: false,
              headers: { Authorization: `Bearer ${this.accessToken}` },
            }
          )
          .then(({ data }) => {
            Bus.$emit('server:event', {
              'event.type': Constants.UI_ACTION_WIDGET_SHARE,
              'event.context': {
                filename: data['file.name'],
                recipients,
                message: formData.message,
                'user.name': this.user.userName,
                type: 'Metric Explorer',
                Timestamp: Moment().unix() * 1000,
              },
            })
            // api.post(`/misc/widget-share`, {
            //   filename: data['file.name'],
            //   recipients: this.formData.users,
            //   message: this.formData.message,
            //   'user.name': this.user.userName,
            // })
            this.showShareModal = false

            this.processing = false
            this.$successNotification({
              message: 'Successful',
              description: `Shared successfully`,
            })
          })
      }, 1000)
    },
    applyChartChanges(chart, options = {}, index) {
      this.handleRemoveChart(chart)
      this.handleAddCounter(
        {
          ...chart,

          ...(options
            ? {
                overlayOptions: options.overlayOptions,
              }
            : {}),
          ...(options
            ? {
                selectedArithmeticOperation:
                  options.selectedArithmeticOperation,
              }
            : {}),
        },
        index
      )
    },
    toggleSidebar() {
      this.menuVisible = !this.menuVisible
    },
    createMetricExplorerView(formData) {
      const selectedMetricExplorerChart = this.selectedCharts.filter(
        (c) => c && c.key
      )

      const metricExplorerPreference = {
        timeline: this.timeline,
        chartType: this.chartType,
        granularity: this.granularity,
      }

      let metricExplorerMergedCounterContext

      if (this.$refs.metricExplorerProviderRef) {
        metricExplorerMergedCounterContext =
          this.$refs.metricExplorerProviderRef.getAllMergeCountersMap()
      }
      return createExplorerApi({
        ...formData,
        ...metricExplorerPreference,
        selectedCharts: selectedMetricExplorerChart,
        metricExplorerMergedCounterContext,
      })
        .then((res) => {
          // this.selectedView = res

          Bus.$emit('metric-explorer:view-saved', res)

          return res
        })
        .catch(() => {
          Bus.$emit('metric-explorer:view-saved')
        })
    },
    updateMetricExplorerView() {
      const selectedMetricExplorerChart = this.selectedCharts.filter(
        (c) => c && c.key
      )

      let metricExplorerMergedCounterContext

      if (this.$refs.metricExplorerProviderRef) {
        metricExplorerMergedCounterContext =
          this.$refs.metricExplorerProviderRef.getAllMergeCountersMap()
      }

      const metricExplorerPreference = {
        timeline: this.timeline,
        chartType: this.chartType,
        granularity: this.granularity,
      }

      return updateExplorerApi({
        ...(this.selectedView || {}),
        ...metricExplorerPreference,
        selectedCharts: selectedMetricExplorerChart,
        metricExplorerMergedCounterContext,
      })
        .then((res) => {
          Bus.$emit('metric-explorer:view-update', res)
          this.$successNotification({
            message: 'Successful',
            description: this.$message('metric_explorer_update'),
          })
        })
        .catch(() => {
          Bus.$emit('metric-explorer:view-update')
        })
    },
    setSelectedMetrics(savedViewContext) {
      if (this.$refs.metricExplorerProviderRef) {
        this.$refs.metricExplorerProviderRef.setMergeCountersData(
          savedViewContext.metricExplorerMergedCounterContext
        )
      }

      this.selectedView = savedViewContext
      if (savedViewContext) {
        if (savedViewContext?.timeline) {
          this.timeline = savedViewContext?.timeline
        }
        if (savedViewContext?.chartType) {
          this.chartType = savedViewContext?.chartType
        }
        if (savedViewContext?.granularity) {
          this.granularity = savedViewContext?.granularity
        }
      }
      this.removeAll()

      this.selectedCharts = [
        ...(savedViewContext.selectedCharts || []).map((chart, index) => ({
          color: colors[index],
          counter: chart.counterRawName.replace(/~^/g, '.'),
          counterName: chart.counterRawName,
          dataType: ['Numeric'],
          guid: generateId(),
          isStatusCounter: false,
          key: chart.counterRawName,
          metricPlugins: [],
          name: chart.counterRawName,
          objectId: chart.objectId,
          ...(chart.instanceType ? { instanceType: chart.instanceType } : {}),
          ...(chart.instance ? { instance: chart.instance } : {}),
          ...(chart.overlayOptions
            ? { overlayOptions: chart.overlayOptions }
            : {}),

          ...(chart.selectedArithmeticOperation
            ? { selectedArithmeticOperation: chart.selectedArithmeticOperation }
            : {}),
        })),
        ...this.selectedCharts.slice(
          (savedViewContext.selectedCharts || []).length
        ),
      ]
    },
    onCreateMetricExplorerView() {
      this.removeAll()
      this.selectedView = undefined
    },
  },
}
</script>

<style>
.metric-explorer-bg {
  background-color: var(--page-background-color) !important;
}
</style>
