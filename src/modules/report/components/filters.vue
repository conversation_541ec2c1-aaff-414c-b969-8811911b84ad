<template>
  <MRow class="slide-filters items-center pr-2">
    <MCol :size="3" class="alert-multi-spacer">
      <FlotoFormItem label="Type">
        <FlotoDropdownPicker
          id="type-picker"
          v-model="currentValue.reportType"
          class="mt-1"
          :options="typeOptions"
          placeholder="Type"
          multiple
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="3" class="alert-multi-spacer">
      <FlotoFormItem label="Report Type">
        <FlotoDropdownPicker
          id="report-type-picker"
          v-model="currentValue.reportTypeName"
          class="mt-1"
          :options="reportTypeOptions"
          placeholder="Report Type"
        />
      </FlotoFormItem>
    </MCol>
    <MCol class="flex justify-between ml-auto" :size="2">
      <div class="flex-1 items-center mt-2 flex justify-center">
        <MButton id="reset-btn" variant="default" @click="resetFilters">
          Reset
        </MButton>
        <MButton id="apply-btn" class="ml-2" @click="apply">Apply</MButton>
      </div>
    </MCol>
    <MButton
      id="close-filter"
      variant="transparent"
      :shadow="false"
      shape="circle"
      style="position: absolute; top: 0; right: 0"
      class="monitor-agent-filter-close"
      @click="$emit('hide')"
    >
      <MIcon name="times" class="text-neutral-light" />
    </MButton>
  </MRow>
</template>

<script>
import { ReportTypeNameMap, AvailableReportCategories } from '../helpers/report'

export default {
  name: 'Filters',
  model: {
    event: 'change',
  },
  data() {
    this.typeOptions = Object.keys(AvailableReportCategories)
      .map((key) => ({
        text: ReportTypeNameMap[AvailableReportCategories[key]],
        key: AvailableReportCategories[key],
      }))
      ?.filter((i) => i.key !== AvailableReportCategories.LOG_REPORT)

    // Object.values(AvailableReportCategories).map((i) => ({
    //   text: i,
    //   key: i,
    // }))
    this.reportTypeOptions = [
      { key: 'Default', text: 'Default' },
      { key: 'Custom', text: 'Custom' },
    ]
    return {
      currentValue: {},
    }
  },
  methods: {
    apply() {
      this.$emit('change', this.currentValue)
    },
    resetFilters() {
      this.currentValue = {}
      this.$emit('change', {
        reportType: [],
        reportTypeName: null,
      })
    },
  },
}
</script>
