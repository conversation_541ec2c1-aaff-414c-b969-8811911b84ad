<template>
  <FlotoFixedView>
    <div class="flex flex-col h-full flex-1 content-inner-panel">
      <FlotoPageHeader title="Report" use-divider main-header>
        <template v-slot:before-title>
          <MIcon name="report" size="lg" class="text-primary-alt mr-3 ml-2" />
        </template>
      </FlotoPageHeader>
      <div class="flex flex-1 flex-col min-h-0">
        <MRow :gutter="0">
          <MCol :size="12">
            <div class="flex px-2 border-bot items-center justify-between">
              <MTab v-model="tab">
                <MTabPane v-for="t in reportTypes" :key="t.key" :tab="t.text" />
              </MTab>
              <div class="flex items-center">
                <!-- <MButton
                      variant="neutral-lighter"
                      class="mr-2 squared-button"
                      @click="showFilter = !showFilter"
                    >
                      <MIcon name="filter" />
                    </MButton> -->

                <MPermissionChecker
                  :permission="$constants.REPORT_CREATE_PERMISSION"
                >
                  <MButton id="create-report-btn" @click="nevigateToCreate">
                    Create Custom Report</MButton
                  >
                </MPermissionChecker>
              </div>
            </div>
          </MCol>
        </MRow>
        <MRow class="flex-1 min-h-0" :gutter="0">
          <MCol :size="2" class="h-full">
            <div class="flex flex-col min-h-0 h-full rounded">
              <ReportSidebar
                v-model="category"
                :sidebar-items="reportCategory"
                :tab="tab"
                @add-new-category="addnewCatogery"
              />
            </div>
          </MCol>
          <MCol :size="10" class="h-full">
            <div class="flex flex-col min-h-0 h-full rounded mt-2 ml-2">
              <div class="flex items-center justify-between -mb-2">
                <MInput
                  v-model="searchTerm"
                  class="search-box"
                  placeholder="Search"
                  name="search"
                >
                  <template v-slot:prefix>
                    <MIcon name="search" />
                  </template>
                  <template v-if="searchTerm" v-slot:suffix>
                    <MIcon
                      name="times-circle"
                      class="text-neutral-light cursor-pointer"
                      @click="searchTerm = undefined"
                    />
                  </template>
                </MInput>
                <div>
                  <MButton
                    :shadow="false"
                    class="squared-button mr-2"
                    :rounded="false"
                    title="Export As PDF"
                    variant="neutral-lightest"
                    @click="exportGrid('pdf')"
                  >
                    <MIcon name="export-pdf" />
                  </MButton>
                  <MButton
                    :shadow="false"
                    class="squared-button mr-2"
                    :rounded="false"
                    title="Export As CSV"
                    variant="neutral-lightest"
                    @click="exportGrid('csv')"
                  >
                    <MIcon name="export-csv" />
                  </MButton>
                  <MButton
                    id="filter-btn"
                    class="squared-button mr-4"
                    variant="neutral-lightest"
                    @click="isFilterVisible = !isFilterVisible"
                  >
                    <MIcon name="filter" class="excluded-header-icon" />
                  </MButton>
                </div>
              </div>

              <div
                v-show="isFilterVisible"
                class="flex flex-0 min-h-0 flex-col mr-2"
                style="margin-bottom: -8px"
              >
                <div class="px-4 mt-4">
                  <Filters
                    ref="filterRef"
                    v-model="filters"
                    :category="tab"
                    @change="applyFilter"
                    @hide="isFilterVisible = !isFilterVisible"
                  />
                </div>
              </div>

              <div class="flex flex-1 min-h-0 flex-col">
                <FlotoContentLoader :loading="loading">
                  <FlotoPaginatedCrud
                    :key="`${category}-${tab}`"
                    ref="paginatedCrudRef"
                    default-sort="-name"
                    resource-name="Report"
                    :use-padding="true"
                    :columns="columns"
                    :fetch-fn="prepareFilteredData"
                    :delete-fn="deleteReport"
                    class="-mt-4 -mx-2"
                    @loaded="filterWithSearchTerm"
                  >
                    <!-- <template
                    v-slot:add-controls="{ filter, resetFilter, searchTerm }"
                  >
                    <MRow class="-mb-2">
                      <MCol>
                        <MInput
                          :value="searchTerm"
                          class="search-box"
                          placeholder="Search"
                          name="role-search"
                          @update="filter"
                        >
                          <template v-slot:prefix>
                            <MIcon name="search" />
                          </template>
                          <template v-if="searchTerm" v-slot:suffix>
                            <MIcon
                              name="times-circle"
                              class="text-neutral-light cursor-pointer"
                              @click="resetFilter"
                            />
                          </template>
                        </MInput>
                      </MCol>
                    </MRow>
                  </template> -->
                    <template v-slot:name="{ item }">
                      <div class="w-full flex min-w-0 items-center">
                        <MIcon
                          name="star"
                          class="relative cursor-pointer font-lg"
                          :class="{
                            'text-secondary-yellow':
                              preference.favouriteReports.includes(item.id),
                          }"
                          size="lg"
                          style="left: -8px; font-size: 0.9rem"
                          @click="markItemAsFavourite(item)"
                        />
                        <MPermissionChecker
                          :permission="$constants.REPORT_READ_PERMISSION"
                        >
                          <FlotoLink :to="nevigateToView(item)">
                            <span
                              class="font-medium text-primary text-ellipsis"
                              style="font-size: 12px"
                              :title="item.name"
                            >
                              {{ item.name }}
                            </span>
                          </FlotoLink>
                        </MPermissionChecker>
                      </div>
                    </template>
                    <template v-slot:description="{ item }">
                      <div class="w-full flex min-w-0 items-center">
                        <MIcon
                          v-if="item.description"
                          name="clipboard"
                          size="sm"
                          class="mr-2 text-neutral-light"
                        />
                        <div class="font-lg text-ellipsis text-neutral-light">
                          {{ item.description }}
                        </div>
                      </div>
                    </template>

                    <template v-slot:reportType="{ item }">
                      {{ ReportTypeNameMap[item.reportType] || '' }}
                    </template>

                    <template v-slot:type="{ item }">
                      <SelectedItemPills :value="[item.type]" />
                    </template>

                    <!-- <template v-slot:isSystemDefault="{ item }">
                        {{ item.isSystemDefault ? 'Default' : 'Custom' }}
                      </template> -->

                    <template v-slot:schedule="{ item }">
                      <div class="flex items-center">
                        <MSwitch
                          :id="item.id"
                          :checked="item.schedule"
                          checked-children="ON"
                          un-checked-children="OFF"
                          @change="scheduleChange(item, $event)"
                        />
                        <MButton
                          v-if="item.schedule"
                          variant="transparent"
                          shape="circle"
                          title="Edit Schedule"
                          class="ml-2"
                          @click="handleEditSchedule(item)"
                        >
                          <MIcon
                            name="schedule"
                            size="lg"
                            class="text-neutral-light font-lg"
                          />
                        </MButton>
                      </div>
                    </template>

                    <template v-slot:download="{ item }">
                      <MRow :gutter="0" class="items-center w-full">
                        <MCol :size="12">
                          <span v-if="!item.isDownloading"> </span>
                          <template v-else>
                            <div class="flex flex-col">
                              <Progress
                                class="mr-4"
                                :width="item.downloadingProgress || 100"
                                type="success"
                              />
                              <div
                                :title="item.downloadingState || ''"
                                class="text-neutral-light flex text-ellipsis"
                                style="font-size: 11px"
                              >
                                {{ item.downloadingProgress || 0 }}%
                                {{ item.downloadingState || '' }}
                              </div>
                            </div>
                          </template>
                        </MCol>
                      </MRow>
                    </template>
                    <template v-slot:actions="{ update, item }">
                      <FlotoGridActions
                        :actions="getActions(item)"
                        :resource="item"
                        :edit-permission-name="
                          $constants.REPORT_UPDATE_PERMISSION
                        "
                        :delete-permission-name="
                          $constants.REPORT_DELETE_PERMISSION
                        "
                        class="mr-3 action-btn-handle"
                        @update="update({ disabled: true })"
                        @edit="navigateToEdit(item)"
                        @clone="handleCloneReport(item)"
                        @email_this_report="emailThisReport(item)"
                        @export_pdf="downloadReport(item, 'pdf')"
                        @export_csv="downloadReport(item, 'csv')"
                      />
                    </template>
                  </FlotoPaginatedCrud>
                </FlotoContentLoader>
              </div>
            </div>
          </MCol>
        </MRow>
      </div>
      <FlotoDrawerForm
        :open="scheduleForItem !== null"
        @submit="handleScheduleFormSubmit"
        @reset="resetScheduleForm"
        @cancel="hideScheduleForm"
      >
        <template v-if="scheduleForItem" v-slot:header>
          {{ scheduleForItem.name }} Schedule Report
        </template>
        <ScheduleInput v-model="reportScheduleForm" />
        <FlotoFormItem
          id="add-email-btn"
          label="Notify via Email"
          rules="required"
        >
          <FlotoTagsPicker
            v-model="reportScheduleForm.email"
            :full-width="true"
            always-text-mode
            type="email"
            placeholder="Email Recipients"
            title="Email Recipients"
          />
        </FlotoFormItem>
        <template v-slot:actions="{ submit, reset }">
          <MButton variant="default" @click="reset"> Reset </MButton>
          <MButton
            id="btn-schedule"
            class="ml-2"
            :loading="processingScheduleForm"
            @click="submit"
          >
            Schedule
          </MButton>
        </template>
      </FlotoDrawerForm>
      <EmailDispatchFormDrawer
        :open="emailDispatchItem !== null"
        :item="emailDispatchItem"
        @closeEmailDispatchForm="emailDispatchItem = null"
      />
    </div>
  </FlotoFixedView>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import UniqBy from 'lodash/uniqBy'
import Bus from '@utils/emitter'
import config from '../config'
import FindIndex from 'lodash/findIndex'
import Constants from '@constants'
import { generateId } from '@utils/id'
import exportData from '@modules/settings/monitoring/helpers/export-pdf-csv'
import { downloadFile } from '@utils/download'
import Filters from '../components/filters.vue'
import {
  UserPreferenceComputed,
  UserPreferenceMethods,
} from '@state/modules/user-preference'
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import ScheduleInput from '@components/schedule-input/index.vue'
import { authMethods, authComputed } from '@state/modules/auth'
import ReportSidebar from '../components/report-sidebar.vue'
import EmailDispatchFormDrawer from '../components/email-dispatch-form-drawer.vue'
import {
  getAllReportsApi,
  deleteReportApi,
  getScheduler,
  createSchedulerApi,
  stateChangeApi,
  updateSchedulerApi,
  exportOrSendEmailApi,
  updateReportScheduleApi,
} from '../report-api'

import {
  defaultReportCategories,
  REPORT_TYPES,
  REPORT_TYPE_FILTER_MAP,
  ReportTypeNameMap,
  AvailableReportCategories,
} from '../helpers/report'
import Progress from '@components/progress.vue'

export default {
  name: 'ReportList',
  components: {
    ReportSidebar,
    SelectedItemPills,
    ScheduleInput,
    EmailDispatchFormDrawer,
    Progress,
    Filters,
  },
  inject: { SocketContext: { default: {} } },
  data() {
    this.ReportTypeNameMap = ReportTypeNameMap
    this.reportActions = [
      { key: 'edit', name: 'Edit', icon: 'pencil' },
      { key: 'clone', icon: 'clone', name: 'Clone Report' },
      { key: 'email_this_report', name: 'Email This Report', icon: 'email' },
      { key: 'export_pdf', name: 'Export PDF', icon: 'export-pdf' },
      { key: 'export_csv', name: 'Export XLSX', icon: 'export-xlsx' },
      { key: 'delete', name: 'Delete', icon: 'trash-alt', isDanger: true },
    ]
    this.columns = [
      {
        key: 'name',
        name: 'Name',
        searchable: true,
        sortable: true,
      },
      {
        key: 'description',
        name: 'Description',
        searchable: true,
        sortable: true,
      },
      {
        key: 'reportType',
        name: 'Type',
        searchable: true,
        sortable: true,
        width: '150px',
      },
      {
        key: 'reportTypeName',
        name: 'Report Type',
        searchable: true,
        sortable: true,
        width: '150px',
      },
      {
        key: 'schedule',
        name: 'Schedule',
        searchable: false,
        sortable: false,
        exportKey: 'schedule',
        exportType: 'boolean',
        width: '120px',
      },
      {
        key: 'download',
        name: 'Download',
        searchable: false,
        sortable: false,
        align: 'center',
        width: '200px',
        export: false,
      },
      {
        key: 'actions',
        name: 'Actions',
        width: '120px',
        export: false,
      },
    ]

    return {
      loading: true,
      category: 'all',
      searchTerm: undefined,
      showFilter: false,
      filters: {},
      tab: 'metric',
      allReports: [],
      reportScheduleForm: {
        scheduleType: 'Once',
        scheduleInfo: {},
      },
      scheduleForItem: null,
      processingScheduleForm: false,
      emailDispatchItem: null,
      reportCategory: [],
      uuid: generateId(),
      isFilterVisible: false,
    }
  },
  computed: {
    ...authComputed,
    ...UserPreferenceComputed,
    favorites() {
      const favouriteReports = this.preference.favouriteReports
      return this.allReports.filter((report) =>
        favouriteReports.includes(report.id)
      )
    },
    reportTypes() {
      return REPORT_TYPES.filter((t) => {
        if (t['text'] === 'NCM') {
          return this.hasLicensePermission(
            this.$constants.NCM_LICENSE_PERMISSION
          )
        }
        return true
      })
    },
    currentCategoryReports() {
      if (this.category === 'all') {
        return this.allReports.filter((report) =>
          REPORT_TYPE_FILTER_MAP[this.tab].includes(report.reportType)
        )
      }
      if (this.category === 'favorites') {
        const favouriteReports = this.preference.favouriteReports
        return this.allReports
          .filter((report) => favouriteReports.includes(report.id))
          .filter((report) =>
            REPORT_TYPE_FILTER_MAP[this.tab].includes(report.reportType)
          )
      }
      return this.allReports
        .filter((report) => report.reportCategory === this.category)
        .filter((report) =>
          REPORT_TYPE_FILTER_MAP[this.tab].includes(report.reportType)
        )
    },

    allReportCategories() {
      return this.reportCategory.filter((r) => r.key !== 'new_category')
    },
  },
  watch: {
    searchTerm(newValue, oldValue) {
      if (this.$refs.paginatedCrudRef && newValue !== oldValue) {
        this.$refs.paginatedCrudRef.filterResults(newValue)
      }
    },
    tab(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.category = 'all'
        this.filters = {}
        this.$refs.filterRef.resetFilters()
        this.isFilterVisible = false
      }
    },
  },
  created() {
    this.getAllReports()
    this.category = this.$route.params.category || 'all'
    Bus.$on(config.REPORT_DOWNLOADING_PROGRESS, this.handleDownloadingProgress)
  },
  beforeDestroy() {
    Bus.$off(config.REPORT_DOWNLOADING_PROGRESS, this.handleDownloadingProgress)
  },
  methods: {
    ...UserPreferenceMethods,
    ...authMethods,
    applyFilter() {
      this.isFilterVisible = false
      this.$refs?.paginatedCrudRef?.fetchData()
    },
    navigateToEdit(item) {
      this.$router.push(
        this.$currentModule.getRoute('edit', {
          params: {
            id: item.id,
            category: this.category,
          },
          query: {
            reportCategories: encodeURIComponent(
              btoa(
                JSON.stringify({
                  allReportCategories: this.allReportCategories.filter(
                    (c) => c.isNew
                  ),
                })
              )
            ),
          },
        })
      )
    },
    nevigateToCreate() {
      this.$router.push(
        this.$currentModule.getRoute('create', {
          query: {
            reportCategories: encodeURIComponent(
              btoa(
                JSON.stringify({
                  allReportCategories: this.allReportCategories.filter(
                    (c) => c.isNew
                  ),
                })
              )
            ),
          },
        })
      )
    },
    nevigateToView(item) {
      return this.$currentModule.getRoute('view', {
        params: {
          id: item.id,
          type: item.type,
          category: this.category,
          uuid: this.uuid,
        },
        query: {
          reportCategories: encodeURIComponent(
            btoa(
              JSON.stringify({
                allReportCategories: this.allReportCategories.filter(
                  (c) => c.isNew
                ),
              })
            )
          ),
        },
      })
    },
    getActions(item) {
      let actions = this.reportActions
      // if (!item.isSystemDefault) {
      //   // return this.reportActions
      // }

      // if (item.reportType === AvailableReportCategories.EVENT_HISTORY) {
      //   actions = actions.filter(
      //     (action) => !['edit', 'clone'].includes(action.key)
      //   )
      // }
      if (item.isCustomReport) {
        actions = actions.filter(
          (action) => !['delete', 'edit', 'clone'].includes(action.key)
        )
      }

      if (item.isSystemDefault) {
        actions = actions.filter(
          (action) => !['delete', 'edit', 'clone'].includes(action.key)
        )
      }

      if (item.isDownloading) {
        actions = actions.filter(
          (action) => !['export_pdf', 'export_csv'].includes(action.key)
        )
      }

      if (
        item.isLogToReport ||
        item.reportType === AvailableReportCategories.FORECAST
      ) {
        actions = actions.filter(
          (action) => !['edit', 'clone'].includes(action.key)
        )
      }

      return actions
    },
    async prepareFilteredData() {
      let filteredData = this.currentCategoryReports

      if (this.filters.reportTypeName) {
        filteredData = filteredData.filter(
          (item) => item.reportTypeName === this.filters.reportTypeName
        )
      }
      if (this.filters.reportType?.length) {
        filteredData = filteredData.filter((item) =>
          this.filters.reportType?.includes(item.reportType)
        )
      }
      return filteredData
    },
    getAllReports() {
      getAllReportsApi().then((data) => {
        this.allReports = Object.freeze(data)
        let groupedCategories = {}

        data.forEach((d) => {
          const category = d.reportCategory
          const type = d.reportType

          if (!groupedCategories[category]) {
            // If the category doesn't exist, create it with reportType as an array
            groupedCategories[category] = {
              key: category,
              text: category,
              reportTypes: [type],
            }
          } else if (!groupedCategories[category].reportTypes.includes(type)) {
            // If the category exists and the reportType is not already included, add it
            groupedCategories[category].reportTypes.push(type)
          }
        })

        this.reportCategory = UniqBy(
          [
            ...defaultReportCategories,
            ...Object.values(groupedCategories),
            { key: 'new_category', text: 'New Category' },
          ],
          'key'
        )
        this.loading = false
      })
    },
    markItemAsFavourite(item) {
      const index = FindIndex(this.allReports, { id: item.id })
      if (index !== -1) {
        this.updatePartialPreference({
          favouriteReports: this.preference.favouriteReports.includes(item.id)
            ? this.preference.favouriteReports.filter((id) => id !== item.id)
            : [...this.preference.favouriteReports, item.id],
        }).then(() => {
          this.refreshUser()
        })
      }
    },
    async exportGrid(type) {
      const columns = this.columns.filter((obj) => obj.key && !obj.hidden)
      let items = await this.$refs?.paginatedCrudRef?.getFilteredData()
      const contextData = this.$refs?.paginatedCrudRef?.getContextData()
      items = items.map((item) => {
        return {
          ...item,
          reportType: this.ReportTypeNameMap[item.reportType],
        }
      })
      this.$successNotification({
        message: 'Success',
        description: `The file will be downloaded once ready`,
      })
      exportData(columns, items, type, contextData).then((blob) => {
        downloadFile(blob, undefined, `Report_list.${type}`)
      })
    },
    deleteReport(payload) {
      this.allReports = this.allReports.filter((r) => r.id !== payload.id)
      return deleteReportApi(payload)
    },
    async scheduleChange(item, isChecked) {
      let scheduleData = await getScheduler(item.id)
      if (scheduleData.length === 0 && isChecked) {
        this.resetScheduleForm()
        this.scheduleForItem = item
        return
      }
      if (isChecked) {
        let upadatedItem = { ...item, schedule: isChecked }
        this.$refs.paginatedCrudRef.replaceItem(upadatedItem)
        try {
          await stateChangeApi(scheduleData[0].id, isChecked)
          await updateReportScheduleApi(upadatedItem.id)
          this.replaceSchedualKey(upadatedItem)
        } catch (e) {
          this.$refs.paginatedCrudRef.replaceItem({
            ...upadatedItem,
            schedule: item.schedule,
          })
        }
      } else {
        // turn off schedule
        const updatedItem = { ...item, schedule: isChecked }
        this.$refs.paginatedCrudRef.replaceItem(updatedItem)
        try {
          await stateChangeApi(scheduleData[0].id, updatedItem.schedule)
          await updateReportScheduleApi(updatedItem.id)
          this.replaceSchedualKey(updatedItem)
        } catch (e) {
          this.$refs.paginatedCrudRef.replaceItem({
            ...updatedItem,
            schedule: false,
          })
        }
      }
    },
    async handleEditSchedule(item) {
      let scheduleData = await getScheduler(item.id)
      if (scheduleData.length > 0) {
        const i = {
          ...item,
          scheduleForm: {
            ...scheduleData[0],
          },
        }
        this.scheduleForItem = i
        this.resetScheduleForm()
      }
    },
    hideScheduleForm() {
      this.scheduleForItem = null
    },
    resetScheduleForm() {
      if (this.scheduleForItem && this.scheduleForItem.scheduleForm) {
        this.reportScheduleForm = {
          ...CloneDeep(this.scheduleForItem.scheduleForm.schedule),
          email: this.scheduleForItem.scheduleForm.email,
        }
      } else {
        this.reportScheduleForm = {
          scheduleType: 'Once',
          scheduleInfo: {},
        }
      }
    },
    async handleScheduleFormSubmit() {
      let item = this.scheduleForItem
      item.schedule = true
      this.processingScheduleForm = true
      try {
        if (item.scheduleForm && item.scheduleForm.id) {
          await updateSchedulerApi(item.id, {
            ...this.reportScheduleForm,
            id: item.scheduleForm.id,
          })
        } else {
          await createSchedulerApi(item.id, this.reportScheduleForm)
        }
        await updateReportScheduleApi(item.id)
        this.replaceSchedualKey(item)
        this.hideScheduleForm()
        this.processingScheduleForm = false
        this.$refs.paginatedCrudRef.replaceItem(item)
      } catch (e) {}
    },
    downloadReport(item, exportType) {
      const uuid = generateId()
      this.SocketContext.addGuidForEvent(Constants.UI_EVENT_CSV_EXPORT, uuid)
      exportOrSendEmailApi(item.id, false, uuid, exportType).then(() => {
        this.$successNotification({
          message: this.$message('downloading_report', {
            reportName: item.name,
          }),
        })
      })
    },
    emailThisReport(item) {
      this.emailDispatchItem = item
    },
    handleDownloadingProgress(data) {
      const abort = !data.progress && !data.message
      const downlodingReport = this.allReports
        .filter((e) => e.id === data.id)
        .shift()

      const updatedItem = {
        ...downlodingReport,
        downloadingProgress: !abort ? data.progress : 0,
        isDownloading: data.progress !== 100 && !abort,
        downloadingState: !abort ? data.message : '',
      }
      if (this.$refs.paginatedCrudRef) {
        this.$refs.paginatedCrudRef.replaceItem(updatedItem)
        this.replaceSchedualKey(updatedItem)
      }
    },
    replaceSchedualKey(item) {
      const index = FindIndex(this.allReports, { id: item.id })
      this.allReports = Object.freeze([
        ...this.allReports.slice(0, index),
        item,
        ...this.allReports.slice(index + 1),
      ])
    },
    handleCloneReport(item) {
      return this.$router.push(
        this.$currentModule.getRoute('create', {
          query: {
            cloneId: item.id,
            reportCategories: encodeURIComponent(
              btoa(
                JSON.stringify({
                  allReportCategories: this.allReportCategories.filter(
                    (c) => c.isNew
                  ),
                })
              )
            ),
          },

          params: {
            category: this.category,
            reportCategory: this.allReportCategories,
          },
        })
      )
    },
    filterWithSearchTerm() {
      if (this.searchTerm) {
        this.$refs.paginatedCrudRef.filterResults(this.searchTerm)
      }
    },

    addnewCatogery(category) {
      this.reportCategory = UniqBy(
        [
          ...this.reportCategory.filter((k) => k.key !== 'new_category'),
          { key: category, text: category, isNew: true },
          { key: 'new_category', text: 'New Category' },
        ],
        'key'
      )
    },
  },
}
</script>

<style scoped lang="less">
.content-inner-panel {
  padding: 0 !important;
}
</style>
