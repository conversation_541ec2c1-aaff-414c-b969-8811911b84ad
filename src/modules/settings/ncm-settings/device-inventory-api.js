import api from '@api'
import Constants from '@constants'
import Config from './config'
import getMessage from '@constants/messages'
import {
  transformInventoryForList,
  transformInventoryForServer,
  transformInventoryForClient,
} from './helpers/device-inventory'

import {
  transformScheduleForServer,
  transformScheduleForClient,
  // transformBulkMonitorScheduleForServer,
} from '@data/scheduler'

export function fetchInventoriesApi() {
  return api
    .get('/settings/configurations/', {
      params: { 'object.category': 'Network' },
    })
    .then((data) =>
      (data.result || []).map((inventory) =>
        transformInventoryForList(inventory)
      )
    )
}

export function fetchInventoryApi(id) {
  return api
    .get('/settings/configurations/' + id)
    .then((data) => transformInventoryForList(data.result))
}

export function createInventoryApi(inventory) {
  return api
    .post('/settings/configurations', transformInventoryForServer(inventory))
    .then((response) => {
      return fetchInventoryApi(response[Constants.ID_PROPERTY])
    })
}

export function updateInventoryApi(inventory, updateAndFetch = true) {
  return api
    .put(
      '/settings/configurations/' + inventory.id,
      transformInventoryForServer(inventory)
    )
    .then((data) => {
      return updateAndFetch
        ? fetchInventoryApi(inventory.id)
        : transformInventoryForList(data)
    })
}

export function deleteInventoryApi(inventory) {
  return api.delete('/settings/configurations/' + inventory.id)
}

export function deleteInventoryBulkApi(selectedInventoryIds) {
  return api.delete('/settings/configurations', {
    data: {
      ids: selectedInventoryIds,
    },
  })
}

// Inventory schedule related apis

export function createNewInventoryScheduleApi(inventoryProfile, data) {
  return api
    .post('/settings/schedulers', {
      ...transformScheduleForServer({
        ...data.schedule,
        type: Config.NCM_SCHEDULER_JOB_TYPE,
        context: {
          objects: [inventoryProfile.id],
          'attach.report.status': data.attachReport ? 'yes' : 'no',
        },
      }),
      'scheduler.email.recipients': data.email,
      'scheduler.sms.recipients': data.sms,
    })
    .then((response) => {
      return getInventoryApi(inventoryProfile.id, true)
    })
}

export function getInventoryProfileSchedulesApi(inventoryId, isBackup = false) {
  return api
    .get('/settings/schedulers', {
      params: {
        filter: {
          id: inventoryId,
          'scheduler.job.type': isBackup
            ? Config.NCM_SCHEDULER_JOB_TYPE
            : Config.NCM_SCHEDULER_JOB_TYPE_UPGRADE,
        },
      },
    })
    .then((data) => {
      const result = (data.result || []).map((item) => ({
        ...transformScheduleForClient(item),
        attachReport:
          (item['scheduler.context'] || {})['attach.report.status'] === 'yes',
        email: item['scheduler.email.recipients'] || [],
        sms: item['scheduler.sms.recipients'] || [],
      }))
      return result
    })
}

export function updateInventoryScheduleApi(
  inventoryId,
  data,
  isBackup = false
) {
  if (data.stateChange) {
    return api
      .put(`/settings/schedulers/${data.id}/state`, {
        'scheduler.state': data.enabled ? 'yes' : 'no',
        'scheduler.job.type': isBackup
          ? Config.NCM_SCHEDULER_JOB_TYPE
          : Config.NCM_SCHEDULER_JOB_TYPE_UPGRADE,
      })
      .then(() => ({ ...data, stateChange: false }))
  }
  // Use the preserved objects array if available, otherwise fall back to single inventory ID
  const objects =
    data.objects && data.objects.length > 0 ? data.objects : [inventoryId]

  const transformedSchedular = transformScheduleForServer({
    ...data.schedule,
    type: isBackup
      ? Config.NCM_SCHEDULER_JOB_TYPE
      : Config.NCM_SCHEDULER_JOB_TYPE_UPGRADE,
    context: {
      objects: objects,
      'config.firmware.image.file.name':
        data.schedule?.scheduleContext?.['config.firmware.image.file.name'],
      'config.firmware.image.file.size':
        data.schedule?.scheduleContext?.['config.firmware.image.file.size'],
      'attach.report.status': data.attachReport ? 'yes' : 'no',
    },
  })

  const payload = {
    ...transformedSchedular,
    'scheduler.job.type': isBackup
      ? Config.NCM_SCHEDULER_JOB_TYPE
      : Config.NCM_SCHEDULER_JOB_TYPE_UPGRADE,
    'scheduler.email.recipients': data.email,
    'scheduler.sms.recipients': data.sms,
  }

  if (!isBackup) {
    payload['scheduler.date.time'] = [
      `${transformedSchedular['scheduler.start.date']} ${transformedSchedular['scheduler.times']}`,
    ]
  }

  return api
    .put('/settings/schedulers/' + data.id, payload)
    .then(() => getInventoryProfileSchedulesApi(inventoryId))
    .then((response) => {
      const schedule = response.find((r) => r.id === data.id)
      if (schedule) {
        return schedule
      }
      return data
    })
}

export function deleteInventoryScheduleApi(
  inventoryId,
  scheduleId,
  isBackup = false
) {
  return api.delete('/settings/schedulers/' + scheduleId, {
    params: {
      'scheduler.job.type': isBackup
        ? Config.NCM_SCHEDULER_JOB_TYPE
        : Config.NCM_SCHEDULER_JOB_TYPE_UPGRADE,
    },
  })
  // .then(() => getInventoryApi(inventoryId, true))
}

export function getInventoryApi(id, useListTransformer = false) {
  const transfromFn = useListTransformer
    ? transformInventoryForList
    : transformInventoryForClient
  return api
    .get('/settings/configurations/' + id, { handle404: true })
    .then((data) => transfromFn(data.result))
}

export function manageConfigStatusApi(item) {
  return api.put(`/settings/configurations/${item.id}/manage`, {
    'config.management.status': item.ncmStatus ? 'yes' : 'no',
  })
}

export function createInventoryScheduleBulkApi(
  selectedMonitorIds,
  scheduleData
) {
  if (scheduleData) {
    return api
      .post('/settings/schedulers', {
        ...transformScheduleForServer({
          ...scheduleData.schedule,
          type: Config.NCM_SCHEDULER_JOB_TYPE,
          context: {
            objects: selectedMonitorIds,
          },
        }),
        'scheduler.job.type': Config.NCM_SCHEDULER_JOB_TYPE,
        'scheduler.email.recipients': scheduleData.email,
        'scheduler.sms.recipients': scheduleData.sms,
      })
      .catch((e) => {
        return Promise.reject(new Error(e.response.data.message))
      })
  } else {
    return Promise.reject(new Error(getMessage('monitor_schedule_error')))
  }
}

export function updateInventoryBulkApi(data) {
  return api.post(
    '/settings/configurations/update',
    transformInventoryForServer(data)
  )
}
