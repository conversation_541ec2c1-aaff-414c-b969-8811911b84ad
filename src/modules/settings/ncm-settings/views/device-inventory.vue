<template>
  <GroupProvider>
    <StorageProfileProvider>
      <DeviceTemplateProvider>
        <CredentialProvider :search-params="credentialSearchParams">
          <div
            class="flex flex-col min-h-0 flex-1 py-2 px-2 page-background-color"
          >
            <MonitorTypeProvider
              :device-types="[$constants.NETWORK, $constants.WIRELESS]"
            >
              <VendorProvider>
                <MPersistedColumns
                  v-model="gridColumns"
                  :default-value="gridColumns"
                  module-key="device-inventory"
                  :available-columns="availableColumns"
                >
                  <template
                    v-slot="{
                      columns: persistedColumns,
                      setColumns: updatePersistedColumns,
                    }"
                  >
                    <FlotoPaginatedCrud
                      ref="paginatedCrudRef"
                      class="h-100"
                      as-table
                      resource-name="Inventory"
                      :use-padding="false"
                      :columns="persistedColumns"
                      :filters="filters"
                      :fetch-fn="fetchInventory"
                      :update-fn="updateCredential"
                      :create-fn="createInventory"
                      :delete-fn="deleteInventory"
                      selectable
                      :drawer-props="{
                        style: { 'z-index': 9999 },
                      }"
                      @column-change="updatePersistedColumns"
                      @selection-change="selectedItems = $event"
                      @loaded-once="onGridLoaded"
                    >
                      <template
                        v-slot:add-controls="{
                          filter,
                          resetFilter,
                          searchTerm,
                        }"
                      >
                        <MRow>
                          <MCol :size="5">
                            <MInput
                              :value="searchTerm"
                              class="search-box"
                              placeholder="Search"
                              name="search"
                              @update="filter"
                            >
                              <template v-slot:prefix>
                                <MIcon name="search" />
                              </template>
                              <template v-if="searchTerm" v-slot:suffix>
                                <MIcon
                                  name="times-circle"
                                  class="text-neutral-light cursor-pointer"
                                  @click="resetFilter"
                                />
                              </template>
                            </MInput>
                          </MCol>

                          <MCol class="text-right">
                            <MRow :gutter="0" class="justify-end">
                              <div>
                                <MPermissionChecker
                                  :permission="
                                    $constants.MY_ACCOUNT_UPDATE_PERMISSION
                                  "
                                >
                                  <ColumnSelector
                                    v-model="gridColumns"
                                    :columns="availableColumns"
                                    @change="updatePersistedColumns"
                                  /> </MPermissionChecker
                              ></div>

                              <MButton
                                id="filter-btn"
                                :shadow="false"
                                :rounded="false"
                                :variant="
                                  showFilters
                                    ? 'neutral-lighter'
                                    : 'neutral-lightest'
                                "
                                class="squared-button"
                                @click="showFilters = !showFilters"
                              >
                                <MIcon name="filter" />
                              </MButton>
                              <FlotoGridActions
                                v-if="selectedItems.length > 1"
                                :resource="{}"
                                :actions="bulkActions"
                                :edit-permission-name="
                                  $constants.NCM_UPDATE_PERMISSION
                                "
                                :delete-permission-name="
                                  $constants.NCM_DELETE_PERMISSION
                                "
                                :create-permission-name="
                                  $constants.NCM_CREATE_PERMISSION
                                "
                                :create-permission-keys="['schedule']"
                                :edit-permission-keys="[
                                  'bulk_update_template',
                                  'bulk_storage_profile_attach',
                                ]"
                                :delete-permission-keys="['bulk_delete']"
                                @bulk_delete="
                                  showConfirmDeleteModalForSelectedItems =
                                    selectedItems
                                "
                                @schedule="
                                  newScheduleFormForItem = {
                                    forBulkAction: true,
                                    selectedItems,
                                  }
                                "
                                @bulk_update_template="
                                  () => {
                                    showNcmDiscoveryForItem = {
                                      forBulkUpdateTemplate: true,
                                      selectedItems,
                                    }
                                    forUpdateTemplate = true
                                  }
                                "
                                @bulk_storage_profile_attach="
                                  () => {
                                    showItemForAttachStorageProfile = {
                                      forBulkAction: true,
                                      selectedItems,
                                    }
                                  }
                                "
                              >
                                <template v-slot:trigger>
                                  <MButton
                                    id="btn-show-hide-columns"
                                    :shadow="false"
                                    :rounded="false"
                                    variant="neutral-lightest"
                                    class="squared-button ml-2"
                                  >
                                    <MIcon
                                      name="ellipsis-v"
                                      class="excluded-header-icon"
                                    />
                                  </MButton>
                                </template>
                              </FlotoGridActions>
                            </MRow>
                          </MCol>

                          <MCol :size="12" :class="{ 'mt-4': showFilters }">
                            <Filters
                              v-if="showFilters"
                              v-model="appliedFilters"
                              @change="applyFilter"
                              @hide="showFilters = !showFilters"
                            />
                          </MCol>
                        </MRow>
                      </template>

                      <template v-slot:name="{ item }">
                        {{ item.name }}
                      </template>

                      <template v-slot:ip="{ item }">
                        {{ item.ip }}
                      </template>

                      <template v-slot:vendor="{ item }">
                        <VendorPicker :value="item.vendor" text-only disabled />
                      </template>

                      <template v-slot:oid="{ item }">
                        {{ item.oid }}
                      </template>
                      <template v-slot:groups="{ item }">
                        <GroupPicker :value="item.groups" multiple disabled />
                      </template>

                      <template v-slot:type="{ item }">
                        <MonitorType :type="item.type" />
                      </template>

                      <template v-slot:templateId="{ item }">
                        <DeviceTemplatePicker
                          :value="item.templateId"
                          use-formatted-name
                        />
                      </template>

                      <template v-slot:ncmStatus="{ item }">
                        <MSwitch
                          :id="item.ncmStatus"
                          :checked="item.ncmStatus"
                          checked-children="ON"
                          un-checked-children="OFF"
                          :disabled="!hasCreateDeletePermission"
                          @change="ncmStatusChange(item, $event)"
                        />
                      </template>

                      <template v-slot:credentialStatus="{ item }">
                        <template v-if="!item.isInProgress">
                          <div>
                            <MButton
                              v-if="!item.credentialProfile && item.ncmStatus"
                              variant="transparent"
                              :shadow="false"
                              :rounded="false"
                              :disabled="!hasCreateDeletePermission"
                              class="p-0"
                              @click="showNcmDiscoveryForItem = item"
                            >
                              <span class="text-primary">Add Credential</span>
                            </MButton>
                            <MButton
                              v-else-if="
                                item.credentialStatus === 'fail' ||
                                (item.credentialStatusError &&
                                  item.credentialStatusError.length)
                              "
                              variant="transparent"
                              :shadow="false"
                              :rounded="false"
                              class="p-0"
                              @click="showCredentialStatusDrawer = item"
                            >
                              <div
                                class="flex items-center text-ellipsis text-secondary-red"
                              >
                                <MIcon class="mr-1" name="times-circle" />
                                Failed
                              </div>
                            </MButton>

                            <NCMCredentialStatus
                              v-else
                              :status="item.credentialStatus"
                              use-status-map
                            />
                          </div>
                        </template>

                        <StatusProgress v-else />
                      </template>

                      <template v-slot:schedule="{ item }">
                        <a
                          v-if="item.scheduled === 'yes'"
                          id="scheduled-inventory-icon"
                          :class="{
                            'ml-3': item.scheduled,
                            'ml-2': !item.scheduled,
                          }"
                          @click="showScheduleLogForItem = item"
                        >
                          <MIcon
                            name="schedule"
                            size="lg"
                            class="text-neutral-light"
                          />
                        </a>
                        <span v-else></span>
                      </template>

                      <template v-slot:tags="{ item }">
                        <div class="flex items-center justify-center">
                          <LooseTags
                            v-if="(item.tags || []).length"
                            :value="item.tags || []"
                            :max-items="1"
                            disabled
                          />
                          <span v-else />
                        </div>
                      </template>

                      <template v-slot:form-header="{ item }"
                        >{{ item.id ? 'Update' : ' ' }} NCM Credential
                        Profile</template
                      >

                      <template v-slot:form-items="{ resetForm }">
                        <CredentialProfileForm
                          :value="credentialContextForEdit || {}"
                          :reset-form="resetForm"
                        />
                      </template>
                      <template
                        v-slot:form-actions="{
                          submit,
                          processing,
                          item,
                          resetForm,
                          validate,
                        }"
                      >
                        <span class="mandatory mt-5"
                          ><span class="text-secondary-red">*</span> fields are
                          mandatory</span
                        >
                        <MButton
                          id="credential-profile-reset-btn"
                          variant="default"
                          @click="resetForm(item.id ? undefined : {})"
                          >Reset</MButton
                        >
                        <CredentialProfileTest
                          :context="credentialContextForEdit || {}"
                          :validate="validate"
                        />
                        <MButton
                          id="credential-profile-submit-btn"
                          :loading="processing"
                          class="ml-2"
                          @click="submit"
                          >{{
                            item.id
                              ? 'Re-run Discovery'
                              : 'Add Credential Profile'
                          }}</MButton
                        >
                      </template>

                      <template v-slot:actions="{ edit, item }">
                        <!-- <MIcon
                          v-if="item.isInProgress"
                          name="sync"
                          class="text-secondary-yellow fa-spin"
                          size="lg"
                        /> -->

                        <FlotoGridActions
                          v-if="item.ncmStatus"
                          :actions="getActions(item)"
                          :resource="item"
                          :edit-permission-name="
                            $constants.NCM_UPDATE_PERMISSION
                          "
                          :delete-permission-name="
                            $constants.NCM_DELETE_PERMISSION
                          "
                          :create-permission-name="
                            $constants.NCM_CREATE_PERMISSION
                          "
                          :create-permission-keys="['schedule']"
                          :edit-permission-keys="[
                            'updateTemplate',
                            'attachStorageProfile',
                            'edit',
                          ]"
                          @schedule="handleShowScheduleForm(item)"
                          @edit="updateCredentialProfile(item, edit)"
                          @updateTemplate="
                            () => {
                              showNcmDiscoveryForItem = item
                              forUpdateTemplate = true
                            }
                          "
                          @attachStorageProfile="
                            showItemForAttachStorageProfile = item
                          "
                        />
                      </template>
                    </FlotoPaginatedCrud>
                  </template>
                </MPersistedColumns>
              </VendorProvider>
            </MonitorTypeProvider>

            <FlotoConfirmModal
              v-if="showConfirmDeleteModalForSelectedItems.length"
              open
              hide-icon
              no-icon-shadow
              @confirm="handleBulkDeleteInventory"
              @hide="showConfirmDeleteModalForSelectedItems = []"
            >
              <template v-slot:message
                >Are you sure,you want to delete selected inventory</template
              >
            </FlotoConfirmModal>

            <FlotoDrawerForm
              :open="newScheduleFormForItem !== null"
              @submit="createNewScheduleForInventory"
              @reset="resetNewSchedule"
              @cancel="hideNewScheduleForm"
            >
              <template v-if="newScheduleFormForItem" v-slot:header>
                {{ newScheduleFormForItem.name }} Schedule Backup
              </template>

              <FlotoFormItem
                v-if="
                  newScheduleFormForItem && newScheduleFormForItem.forBulkAction
                "
                rules="required"
                label="Devices"
              >
                <MonitorPicker
                  id="monitors-dropdown"
                  v-model="newScheduleFormForItem.selectedItems"
                  :options="monitorOptions"
                  :for-ncm-device="true"
                  class="w-full"
                  multiple
                  searchable
                  use-body-container
                />
              </FlotoFormItem>

              <ScheduleInput
                v-if="newScheduleFormForItem"
                v-model="newInventorySchedule.schedule"
              />
              <MCol v-if="newScheduleFormForItem" :size="12">
                <FlotoFormItem label="Notify via Email">
                  <FlotoTagsPicker
                    v-model="newInventorySchedule.email"
                    :full-width="true"
                    always-text-mode
                    type="email"
                    placeholder="Email Recipients"
                    title="Email Recipients"
                  />
                </FlotoFormItem>
              </MCol>
              <MCol v-if="newScheduleFormForItem" :size="12">
                <FlotoFormItem label="Notify via SMS">
                  <FlotoTagsPicker
                    v-model="newInventorySchedule.sms"
                    :full-width="true"
                    type="mobile_number"
                    always-text-mode
                    placeholder="SMS Recipients"
                    title="SMS Recipients"
                  />
                </FlotoFormItem>
              </MCol>

              <template v-slot:actions="{ submit, reset }">
                <MButton variant="default" @click="reset"> Reset </MButton>
                <MButton
                  id="btn-schedule"
                  class="ml-2"
                  :loading="creatingNewSchedule"
                  @click="submit"
                >
                  Schedule
                </MButton>
              </template>
            </FlotoDrawerForm>

            <DeviceInventoryScheduleBackup
              v-if="showScheduleLogForItem !== null"
              :inventory="showScheduleLogForItem"
              @refresh="handleRefreshPaginationCrud"
              @hide="
                () => {
                  showScheduleLogForItem = null
                }
              "
            />
            <NcmDiscoveryDrawer
              v-if="showNcmDiscoveryForItem !== null"
              :item="showNcmDiscoveryForItem"
              :for-update-template="forUpdateTemplate"
              :monitor-options="monitorOptions"
              @cancel="
                () => {
                  if (forUpdateTemplate) {
                    resetList()
                  }
                  showNcmDiscoveryForItem = null
                  forUpdateTemplate = false
                }
              "
            />
            <NcmCredentialStatusdrawer
              v-if="showCredentialStatusDrawer !== null"
              :data-item="showCredentialStatusDrawer"
              @hide="showCredentialStatusDrawer = null"
              @update-credential="openUpdateCredentialProfileForm"
            />
            <AttachStorageProfileDrawer
              v-if="showItemForAttachStorageProfile !== null"
              :item="showItemForAttachStorageProfile"
              :monitor-options="monitorOptions"
              @cancel="handelCancelAttachStorageProfileDrawer"
            />
          </div>
        </CredentialProvider>
      </DeviceTemplateProvider>
    </StorageProfileProvider>
  </GroupProvider>
</template>

<script>
import Bus from '@utils/emitter'
// import { arrayWorker } from '@/src/workers'

import ScheduleInput from '@components/schedule-input/index.vue'
import VendorPicker from '@components/data-picker/vendor-picker.vue'
import VendorProvider from '@components/data-provider/vendor-provider.vue'
import MonitorType from '@components/monitor-type.vue'
import MonitorTypeProvider from '@components/data-provider/monitor-type-provider.vue'
import DeviceTemplatePicker from '@components/data-picker/device-template-picker.vue'
import DeviceTemplateProvider from '@components/data-provider/device-template-provider.vue'
import StorageProfileProvider from '@components/data-provider/storage-profile-provider.vue'
import GroupProvider from '@components/data-provider/group-provider.vue'

import {
  fetchInventoriesApi,
  updateInventoryApi,
  createInventoryApi,
  deleteInventoryApi,
  deleteInventoryBulkApi,
  createNewInventoryScheduleApi,
  manageConfigStatusApi,
  createInventoryScheduleBulkApi,
} from '../device-inventory-api'

import { buildUpdatedContext } from '../helpers/device-inventory'
import {
  updateCredentialProfileApi,
  getCredentialProfileApi,
} from '@modules/settings/network-discovery/credential-profile-api'
import DeviceInventoryScheduleBackup from '../components/device-inventory-schedule-backup.vue'
import NCMCredentialStatus from '../components/ncm-credential-status.vue'
import LooseTags from '@components/loose-tags.vue'
import CredentialProfileForm from '@modules/settings/network-discovery/components/credential-profile-form.vue'
import NcmDiscoveryDrawer from '../components/ncm-discovery-drawer.vue'
import NcmCredentialStatusdrawer from '../components/ncm-credential-status-drawer.vue'
import CredentialProvider from '@components/data-provider/credential-provider.vue'
import CredentialProfileTest from '@modules/settings/network-discovery/components/credential-profile-test.vue'
import AttachStorageProfileDrawer from '../components/attach-storage-profile-drawer.vue'
import ColumnSelector from '@components/column-selector.vue'
import Filters from '../components/filter.vue'
import StatusProgress from '@modules/ncm/components/status-progress.vue'
import { authComputed } from '@state/modules/auth'
import { sendConfigRequest } from '@modules/ncm/ncm-api'
import MonitorPicker from '@components/data-picker/monitor-picker.vue'
import { ACTION_MAP } from '@modules/ncm/helpers/explorer'
import ExplorerConfigs from '@modules/ncm/config'

export default {
  name: 'Inventory',
  components: {
    ScheduleInput,
    VendorPicker,
    VendorProvider,
    MonitorType,
    MonitorTypeProvider,
    DeviceTemplateProvider,
    DeviceTemplatePicker,
    // SelectedItemPills,
    DeviceInventoryScheduleBackup,
    LooseTags,
    NCMCredentialStatus,
    CredentialProfileForm,
    NcmDiscoveryDrawer,
    CredentialProvider,
    NcmCredentialStatusdrawer,
    CredentialProfileTest,
    StorageProfileProvider,
    AttachStorageProfileDrawer,
    ColumnSelector,
    Filters,
    GroupProvider,
    StatusProgress,
    MonitorPicker,
  },

  data() {
    this.eventCache = {}

    this.ACTION_MAP = ACTION_MAP

    this.credentialSearchParams = {
      key: 'credential.profile.protocol',
      value: [
        this.$constants.SSH,
        // this.$constants.NCM_TELNET,
      ],
    }
    this.monitorOptions = []

    this.bulkActions = [
      { key: 'schedule', name: 'Schedule Backup', icon: 'schedule' },

      {
        key: 'bulk_storage_profile_attach',
        name: 'Attach Storage Profile',
        icon: 'disk',
      },

      {
        key: 'bulk_update_template',
        name: 'Change Template',
        icon: 'file-alt',
      },
    ]

    this.gridItemActions = [
      { key: 'schedule', name: 'Schedule Backup', icon: 'schedule' },
      { key: 'edit', name: 'Update Credential', icon: 'lock-alt' },
      { key: 'updateTemplate', name: 'Update Template', icon: 'file-alt' },
      {
        key: 'attachStorageProfile',
        name: 'Attach Storage Profile',
        icon: 'disk',
      },
    ]

    this.columns = [
      {
        key: 'name',
        name: 'Device',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        disable: true,
      },
      {
        key: 'ip',
        name: 'IP/Host',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        disable: true,
      },
      {
        key: 'oid',
        name: 'System OID',
        searchable: true,
        sortable: true,
        minWidth: '100px',
      },
      {
        key: 'vendor',
        name: 'Vendor',
        searchable: true,
        sortable: true,
        minWidth: '100px',
      },
      {
        key: 'templateId',
        name: 'Template',
        searchable: true,
        sortable: true,
        searchKey: 'template',
        sortKey: 'template',
      },

      {
        key: 'ncmStatus',
        name: 'Manage NCM Status',
        searchable: false,
        sortable: true,
        minWidth: '100px',
        disable: true,
      },

      {
        key: 'credentialStatus',
        name: 'Credential Status',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        disable: true,
        searchKey: 'credentialStatusSearchKey',
        sortKey: 'credentialStatusSearchKey',
      },
      {
        key: 'schedule',
        name: 'Scheduler',
        searchable: true,
        sortable: true,
        minWidth: '150px',
      },
      {
        key: 'tags',
        name: 'Tags',
        searchable: true,
        sortable: true,
        hidden: true,
        searchKey: 'tagsStr',
        sortKey: 'tagsStr',
        minWidth: '100px',
      },
      {
        key: 'groups',
        name: 'Groups',
        contextKey: 'groupContext',
        searchable: true,
        sortable: true,
        searchKey: 'groupsDisplay',
        sortKey: 'groupsDisplay',
        hidden: true,
        minWidth: '100px',
      },
      {
        key: 'type',
        name: 'Type',
        align: 'center',
        width: '75px',
        searchable: true,
        sortable: true,
        disable: true,
      },
      {
        key: 'actions',
        name: 'Actions',
        align: 'right',
        width: '120px',
        disable: true,
      },
    ]

    return {
      newInventorySchedule: {
        schedule: {
          scheduleType: 'Once',
          scheduleInfo: {},
        },
      },
      creatingNewSchedule: false,
      newScheduleFormForItem: null,
      showScheduleLogForItem: null,
      isExecuteConfigletFormOpened: null,
      shouldExecuteConfigletDrawerOpen: false,
      showFilters: false,
      appliedFilters: {
        groups: [],
        types: [],
      },
      selectedItems: [],
      showConfirmDeleteModalForSelectedItems: [],
      linkedRecordsDetailData: undefined,
      showNcmDiscoveryForItem: null,
      showCredentialStatusDrawer: null,
      credentialContextForEdit: undefined,
      forUpdateTemplate: false,
      showItemForAttachStorageProfile: null,
      gridColumns: this.columns,
      availableColumns: this.columns,
    }
  },
  computed: {
    ...authComputed,

    filters() {
      let filters
      const value = this.appliedFilters

      if (value.types && value.types.length) {
        filters = [
          ...(filters || []),
          {
            field: 'type',
            operator: 'array_contains',
            value: value.types,
          },
        ]
      }

      if (value.groups && value.groups.length) {
        filters = [
          ...(filters || []),
          {
            field: 'groups',
            operator: 'array_contains',
            value: value.groups,
          },
        ]
      }
      return filters
    },

    hasCreateDeletePermission() {
      return this.hasPermission([
        this.$constants.NCM_UPDATE_PERMISSION,
        this.$constants.NCM_DELETE_PERMISSION,
      ])
    },
  },

  created() {
    const handler = (payload) => {
      if (payload.id) {
        this.updateInventoryStatus(payload)
      }
    }
    Bus.$on(this.$currentModule.getConfig().STATE_CHANGE, handler)

    this.$once('hook:beforeDestroy', () => {
      this.clearCacheExecutionInterval()
      Bus.$off(this.$currentModule.getConfig().STATE_CHANGE, handler)
    })
    if (this.$route.params.scheduleItem) {
      this.handleShowScheduleForm(this.$route.params.scheduleItem)
    }
  },

  methods: {
    fetchInventory() {
      return fetchInventoriesApi().then((data) => {
        this.monitorOptions = data.map((device) => ({
          id: device.id,
          ip: device.ip,
          device: device.name,
          type: device.type,
          groups: device.groups,
          vendor: device.vendor,
          objectId: device.objectId,
          text: device.name,
          key: device.id,
        }))
        return data
      })
    },

    updateInventory(item) {
      return updateInventoryApi(item)
    },

    createInventory(item) {
      return createInventoryApi(item)
    },
    deleteInventory(item) {
      return deleteInventoryApi(item)
        .then(() => {
          this.monitorOptions = this.monitorOptions.filter(
            (n) => n.key !== item.id
          )
        })
        .finally(() => {
          this.$refs.paginatedCrudRef.resetList()
        })
    },
    handleShowScheduleForm(item) {
      this.newScheduleFormForItem = item
    },
    async updateInventoryStatus(payload) {
      if (
        this.$refs.paginatedCrudRef &&
        ['discovery'].includes(payload['config.operation'])
      ) {
        let plainItem = buildUpdatedContext(payload)

        this.eventCache[payload.id] = {
          ...(this.eventCache[payload.id] || {}),
          ...plainItem,
        }
        // let plainItem = await this.$refs.paginatedCrudRef.getItem(payload.id)
        // if (plainItem) {
        //   plainItem = {
        //     ...plainItem,
        //     ...buildUpdatedContext(payload),
        //   }
        //   await this.handleInventoryUpdated(plainItem)
        // }
      }
    },

    hideNewScheduleForm() {
      this.newScheduleFormForItem = null
      this.newInventorySchedule = {
        schedule: {
          scheduleType: 'Once',
          scheduleInfo: {},
        },
      }

      this.resetList()
    },
    resetNewSchedule() {
      this.newInventorySchedule = {
        schedule: {
          scheduleType: 'Once',
          scheduleInfo: {},
        },
      }
    },
    createNewScheduleForInventory() {
      this.creatingNewSchedule = true

      if (this.newScheduleFormForItem.forBulkAction) {
        createInventoryScheduleBulkApi(
          this.newScheduleFormForItem.selectedItems,
          this.newInventorySchedule
        )
          .then(() => {
            this.hideNewScheduleForm()
            this.$nextTick(() => {
              setTimeout(() => {
                this.$refs.paginatedCrudRef.resetList()
              }, 400)
            })
          })
          .finally(() => (this.creatingNewSchedule = false))
      } else {
        createNewInventoryScheduleApi(
          this.newScheduleFormForItem,
          this.newInventorySchedule
        )
          .then((data) => {
            this.$refs.paginatedCrudRef.handleUpdateItem(data)
            this.hideNewScheduleForm()
          })
          .finally(() => (this.creatingNewSchedule = false))
      }
    },

    handleInventoryUpdated(inventory) {
      if (this.$refs.paginatedCrudRef) {
        this.$refs.paginatedCrudRef.handleUpdateItem(inventory)
      }
    },

    handleRefreshPaginationCrud() {
      if (this.$refs.paginatedCrudRef) {
        this.$refs.paginatedCrudRef.resetList()
      }
    },
    async handleBulkDeleteInventory() {
      await deleteInventoryBulkApi(this.showConfirmDeleteModalForSelectedItems)
        .catch((e) => {
          this.linkedRecordsDetailData = e.response.data

          throw e
        })
        .finally(() => {
          this.showConfirmDeleteModalForSelectedItems = []
          this.$refs.paginatedCrudRef.resetList()
        })
    },

    applyFilter() {
      this.showFilters = false
    },
    async ncmStatusChange(item, isChecked) {
      try {
        let upadatedItem = { ...item, ncmStatus: isChecked }
        await manageConfigStatusApi(upadatedItem)
        this.$refs.paginatedCrudRef.replaceItem(upadatedItem)
      } catch (e) {
        this.$refs.paginatedCrudRef.replaceItem(item)
      }
    },

    updateCredential(item) {
      return updateCredentialProfileApi({
        ...(this.credentialContextForEdit || {}),
      }).then(() => {
        return sendConfigRequest(item.id, ACTION_MAP.DISCOVERY).then(() => {
          this.credentialContextForEdit = undefined
          return item
        })
      })
    },
    async updateCredentialProfile(item, edit) {
      if (item.credentialProfile) {
        const context = await getCredentialProfileApi(item.credentialProfile)
        this.credentialContextForEdit = { ...context, disabledCliEnable: true }
        if (this.credentialContextForEdit) {
          if (edit) edit()
        }
      }
    },
    getActions(item) {
      if (item.credentialProfile) {
        return this.gridItemActions
      }
      return this.gridItemActions.filter(
        (action) => !['updateTemplate'].includes(action.key)
      )
    },
    async openUpdateCredentialProfileForm(id) {
      if (this.$refs.paginatedCrudRef) {
        const item = await this.$refs.paginatedCrudRef.getItem(id)
        await this.updateCredentialProfile(item)
        this.$refs.paginatedCrudRef.showForm(item)
      }
    },

    handelCancelAttachStorageProfileDrawer() {
      this.showItemForAttachStorageProfile = null
      this.resetList()
    },

    resetList() {
      if (this.$refs.paginatedCrudRef) {
        this.$refs.paginatedCrudRef.resetList()
      }
    },
    getItems() {
      if (this.$refs.paginatedCrudRef) {
        return this.$refs.paginatedCrudRef.getData()
      } else {
        return []
      }
    },
    onGridLoaded() {
      this.startCacheExecutionInterval()
    },
    startCacheExecutionInterval() {
      this.clearCacheExecutionInterval()
      this.__cacheExecutionInterval = setInterval(
        this.executeCache,
        ExplorerConfigs.CACHE_EXECUTION_INTERVAL
      )
    },
    clearCacheExecutionInterval() {
      if (this.__cacheExecutionInterval) {
        clearInterval(this.__cacheExecutionInterval)
        this.__cacheExecutionInterval = null
      }
    },
    async executeCache() {
      if (this.$refs.paginatedCrudRef && Object.keys(this.eventCache).length) {
        let data = this.getItems()
        const dataMap = new Map(data.map((item) => [item.id, item])) // Use Map for fast lookups
        const keys = Object.keys(this.eventCache)
        const chunkSize = 100 // Define a chunk size
        const totalChunks = Math.ceil(keys.length / chunkSize)

        let currentIndex = 0

        // Helper function to process chunks concurrently
        const processChunk = async () => {
          const chunkPromises = []

          while (currentIndex < keys.length) {
            const startIndex = currentIndex
            const chunkKeys = keys.slice(startIndex, startIndex + chunkSize)

            // Create a promise for processing each chunk
            chunkPromises.push(
              new Promise((resolve) => {
                for (const id of chunkKeys) {
                  const itemId = Number(id)
                  if (dataMap.has(itemId)) {
                    const item = dataMap.get(itemId)
                    dataMap.set(itemId, {
                      ...item,
                      ...(this.eventCache[id] || {}),
                    })
                  }
                  delete this.eventCache[id] // Clear cache for processed item
                }
                resolve() // Resolve this chunk
              })
            )

            currentIndex += chunkSize

            // Yield to event loop every chunkSize iterations
            if (
              chunkPromises.length === totalChunks ||
              currentIndex % chunkSize === 0
            ) {
              await Promise.all(chunkPromises) // Wait for chunk processing to finish
              chunkPromises.length = 0 // Clear the array

              setTimeout(processChunk, 0) // Yield to event loop, continue processing
              return
            }
          }

          // Once all chunks are processed, update items once
          const updatedData = Array.from(dataMap.values())
          await this.$refs.paginatedCrudRef.setItems(updatedData)
        }

        // Start processing
        processChunk()
      }
    },
  },
}
</script>
