<template>
  <FlotoDrawerForm
    ref="drawer"
    :open="open"
    :scrolled-content="false"
    @submit="attachStorageProfile"
    @reset="resetForm"
    @cancel="$emit('cancel')"
  >
    <template v-slot:header> Select Storage Profile </template>

    <MRow class="px-4">
      <MCol v-if="item.forBulkAction" :size="12">
        <FlotoFormItem rules="required" label="Devices">
          <MonitorPicker
            id="monitors-dropdown"
            v-model="item.selectedItems"
            :options="monitorOptions"
            :for-ncm-device="true"
            class="w-full"
            multiple
            searchable
            use-body-container
          />
        </FlotoFormItem>
      </MCol>
      <MCol :size="12">
        <FlotoFormItem label="Storage Profile Name" rules="required">
          <StorageProfilePicker
            v-model="storageProfile"
            allow-create
            :default-form-data="defaultFormData"
          />
        </FlotoFormItem>
      </MCol>
    </MRow>

    <template v-slot:actions="{ submit, reset }">
      <span class="mandatory mt-5"
        ><span class="text-secondary-red">*</span> fields are mandatory</span
      >
      <MButton variant="default" class="mr-2" @click="reset"> Reset </MButton>
      <MButton id="btn-schedule" @click="submit"
        >Attach Storage Profile
      </MButton>
    </template>
  </FlotoDrawerForm>
</template>

<script>
import {
  updateInventoryApi,
  updateInventoryBulkApi,
} from '../device-inventory-api'
import StorageProfilePicker from '@components/data-picker/storage-profile-picker.vue'
import MonitorPicker from '@components/data-picker/monitor-picker.vue'
import { authComputed } from '@/src/state/modules/auth'

export default {
  name: 'AttachStorageProfileDrawer',
  components: {
    StorageProfilePicker,
    MonitorPicker,
  },
  props: {
    item: {
      type: Object,
      required: true,
    },
    monitorOptions: {
      type: Array,
      required: true,
    },
  },
  data() {
    this.defaultPath = '/motadata/motadata/config-management'
    return {
      storageProfile: this.item.storagePofile,
      open: true,
    }
  },
  computed: {
    ...authComputed,
    defaultFormData() {
      if (this.directoryMetadata) {
        return {
          localPath: `${
            this.directoryMetadata?.['current.directory.path'] +
            this.directoryMetadata?.['path.separator']
          }config-management`,
        }
      } else {
        return { localPath: '' }
      }
    },
  },
  methods: {
    attachStorageProfile() {
      const fn = this.item.forBulkAction
        ? updateInventoryBulkApi
        : updateInventoryApi
      return fn({
        ...(this.item.forBulkAction
          ? { ids: this.item.selectedItems }
          : { id: this.item.id }),
        storageProfile: this.storageProfile,
      }).then(() => {
        this.$successNotification({
          message: this.$message('storage_profile_attach'),
        })
        this.hideForm()
      })
    },
    resetForm() {
      this.storageProfile = undefined
    },

    hideForm() {
      this.open = false
      setTimeout(() => {
        this.$emit('cancel')
      }, 700)
    },
  },
}
</script>
