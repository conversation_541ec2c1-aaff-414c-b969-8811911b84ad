<template>
  <FlotoDrawer
    ref="drawer"
    open
    width="60%"
    :scrolled-content="false"
    @hide="$emit('hide')"
  >
    <template v-slot:title>NCM - Scheduler Details </template>

    <MRow :gutter="0" class="mx-4">
      <MCol :size="12">
        <div class="w-full flex justify-between items-center border-bot">
          <div class="flex-1">
            <MTab
              v-model="currentTab"
              class="no-border"
              @change="handleTabChange"
            >
              <MTabPane v-for="tab in tabs" :key="tab.key" :tab="tab.name">
                <template v-slot:tab>
                  {{ tab.text }}
                </template>
              </MTabPane>
            </MTab>
          </div>
        </div>
      </MCol>
    </MRow>

    <FlotoPaginatedCrud
      ref="paginatedCrud"
      :key="currentTab"
      resource-name="Schedule"
      :fetch-fn="fetchInventorySchedules"
      :update-fn="updateInventorySchedule"
      :delete-fn="deleteInventorySchedule"
      as-table
      :columns="columns"
      class="px-2"
      @empty-list="closeDrawer"
    >
      <template v-slot:form-header> Edit Scheduler </template>
      <template v-slot:form-items="{ item }">
        <ScheduleInput
          v-model="item.schedule"
          :show-only-once="currentTab === 'firmware_upgrade'"
        />
        <MRow>
          <MCol :size="12">
            <FlotoFormItem id="add-email-btn" label="Notify via Email">
              <FlotoTagsPicker
                :value="item.email"
                :full-width="true"
                always-text-mode
                type="email"
                placeholder="Email Recipients"
                title="Email Recipients"
                @change="item.email = $event"
              />
            </FlotoFormItem>
          </MCol>
          <MCol :size="12">
            <FlotoFormItem id="add-number-btn" label="Notify via SMS">
              <FlotoTagsPicker
                :value="item.sms"
                :full-width="true"
                type="mobile_number"
                always-text-mode
                placeholder="SMS Recipients"
                title="SMS Recipients"
                @change="item.sms = $event"
              />
            </FlotoFormItem>
          </MCol>
          <!-- <MCol :size="12">
            <MCheckbox v-model="item.attachReport">
              Attach PDF report with Email.
            </MCheckbox>
          </MCol> -->
        </MRow>
      </template>
      <template v-slot:form-actions="{ submit, resetForm, processing }">
        <MButton
          id="reset-schedule-btn"
          variant="default"
          @click="resetForm(null)"
        >
          Reset
        </MButton>
        <MButton class="ml-2" :loading="processing" @click="submit">
          Update Scheduler
        </MButton>
      </template>

      <!-- column slots -->
      <template v-slot:runSchedules="{ item }">
        <SelectedItemPills :value="item.runSchedules" :max-items="3" />
      </template>

      <!-- <template v-slot:message="{ item }">
        <a @click="navigateToLog(item)">
          <small>View Details</small>
        </a>
      </template> -->

      <template v-slot:action="{ item, edit, update }">
        <MPermissionChecker :permission="$constants.NCM_UPDATE_PERMISSION">
          <MSwitch
            :checked="item.enabled"
            checked-children="ON"
            un-checked-children="OFF"
            @change="update({ ...item, enabled: $event, stateChange: true })"
          />
        </MPermissionChecker>

        <FlotoGridActions
          :actions="gridItemActions"
          :resource="item"
          :edit-permission-name="$constants.NCM_UPDATE_PERMISSION"
          :delete-permission-name="$constants.NCM_DELETE_PERMISSION"
          :create-permission-name="$constants.NCM_CREATE_PERMISSION"
          class="mr-3 action-btn-handle text-center"
          @edit="edit"
        />
      </template>
    </FlotoPaginatedCrud>
  </FlotoDrawer>
</template>
<script>
import ScheduleInput from '@components/schedule-input/index.vue'
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import {
  getInventoryProfileSchedulesApi,
  updateInventoryScheduleApi,
  deleteInventoryScheduleApi,
} from '../device-inventory-api'

export default {
  name: 'InventoryScheduleBackup',
  components: { ScheduleInput, SelectedItemPills },
  props: {
    inventory: { type: Object, required: true },
  },
  data() {
    this.gridItemActions = [
      { key: 'edit', name: 'Edit Scheduler', icon: 'pencil' },
      {
        key: 'delete',
        name: 'Delete Scheduler',
        icon: 'trash-alt',
        isDanger: true,
      },
    ]
    this.columns = [
      {
        key: 'scheduleType',
        name: 'Scheduler Type',
        width: '180px',
        sortable: true,
      },
      {
        key: 'startDate',
        name: 'Start Date',
      },
      {
        key: 'runSchedules',
        name: 'Triggers',
        sortable: true,
      },
      // {
      //   key: 'message',
      //   name: 'Result',
      // },
      { key: 'action', name: 'Actions', align: 'right' },
    ]
    this.editingItem = {}
    return {
      currentTab: 'backup',
    }
  },
  async created() {
    this.getTabs()
  },
  methods: {
    handleTabChange() {},
    closeDrawer() {
      this.$refs.drawer.hide()
      setTimeout(() => {
        this.$emit('hide')
      }, 350)
    },
    navigateToLog(item) {
      this.$refs.drawer.hide()
      setTimeout(() => {
        this.$router.push(
          this.$currentModule.getRoute('inventory-log', {
            params: {
              id: this.inventory.id,
              scheduleId: item.id,
            },
          })
        )
      }, 350)
    },

    fetchInventorySchedules() {
      return getInventoryProfileSchedulesApi(
        this.inventory.id,
        this.currentTab === 'backup'
      )
    },

    async updateInventorySchedule(data) {
      const response = await updateInventoryScheduleApi(
        this.inventory.id,
        data,
        this.currentTab === 'backup'
      )

      this.$refs.paginatedCrud.refresh()
      return response
    },

    deleteInventorySchedule(data) {
      return deleteInventoryScheduleApi(
        this.inventory.id,
        data.id,
        this.currentTab === 'backup'
      ).then((inventory) => {
        this.$emit('refresh')
      })
    },

    getTabs() {
      let tabs = [
        {
          key: 'backup',
          text: 'Backup',
        },
        {
          key: 'firmware_upgrade',
          text: 'Firmware Upgrade',
        },
      ]

      this.tabs = tabs
    },
  },
}
</script>
