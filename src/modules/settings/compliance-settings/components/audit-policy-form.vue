<template>
  <NcmMonitorProvider>
    <UserProvider>
      <GroupProvider>
        <BenchmarkProvider>
          <MonitorProvider :search-params="searchParams">
            <div class="mt-4">
              <MRow>
                <MCol :size="6">
                  <FlotoFormItem
                    id="audit-policy-name-id"
                    v-model="value.name"
                    placeholder="Must be unique"
                    rules="required"
                    label="Policy Name"
                    auto-focus
                    name="audit-policy-name"
                    :disabled="isView"
                  />
                </MCol>
                <MCol :size="6">
                  <FlotoFormItem
                    id="audit-policy-description-id"
                    v-model="value.description"
                    placeholder="Write Description here"
                    label="Description"
                    name="audit-policy-description"
                    :disabled="isView"
                  />
                </MCol>

                <MCol :size="6">
                  <FlotoFormItem label="Tags">
                    <LooseTags
                      id="tags"
                      v-model="value.tags"
                      always-text-mode
                      title="Tag"
                      variant="default"
                      rounded
                      class="w-full"
                      :disabled="isView"
                      :tag-type="AvailableTagType.COMPLIANCE"
                    />
                  </FlotoFormItem>
                </MCol>

                <MCol :size="6">
                  <FlotoFormItem label="Config File Type" rules="required">
                    <MRadioGroup
                      v-model="value.configType"
                      as-button
                      :options="configTypeOptions"
                      :disabled="isView"
                    />
                  </FlotoFormItem>
                </MCol>

                <!-- <MCol :size="6">
                  <FlotoFormItem label="Benchmark" rules="required"> -->

                <MCol :size="12">
                  <BenchmarkPicker
                    id="benchmark-options-id"
                    v-model="value.benchmarkContext"
                    :disabled="isView || !!value.id"
                    use-tag-filter
                  />
                </MCol>

                <!-- </FlotoFormItem>
                </MCol> -->
                <MCol :size="12">
                  <MonitorGroupSelection
                    v-model="value.target"
                    rules="required"
                    :labels-props="deviceLabels"
                    :source-required="true"
                    :disabled="isView"
                    use-ncm-monitor-picker
                  />
                </MCol>

                <!-- <MCol :size="12">
                  <FlotoFormItem>
                    <MCheckbox
                      v-model="value.generateReport"
                      :disabled="isView"
                    >
                      Generate report
                    </MCheckbox>
                  </FlotoFormItem>
                </MCol> -->
                <MCol :size="12">
                  <FlotoFormItem label="Notify Team">
                    <UserOrEmailPicker
                      v-model="value.notificationEmail"
                      :full-width="true"
                      class="w-full"
                      name="parameter-name"
                      title="@User or Email or /Handle or Mobile Number"
                      always-text-mode
                      disable-justify-around
                      type="email"
                      :disabled="isView"
                    />
                  </FlotoFormItem>
                </MCol>

                <MCol :size="12">
                  <span class="text-neutral"> For more information: </span>
                  <a
                    href="https://docs.motadata.com/motadata-aiops-docs/compliance/audit-policy "
                    target="_blank"
                    >Compliance Policy</a
                  >
                  <MIcon name="external-link" class="ml-1 text-primary" />
                </MCol>
              </MRow>
            </div>
          </MonitorProvider>
        </BenchmarkProvider>
      </GroupProvider>
    </UserProvider>
  </NcmMonitorProvider>
</template>

<script>
import LooseTags from '@components/loose-tags.vue'
import BenchmarkPicker from '@src/components/data-picker/benchmark-picker.vue'
import BenchmarkProvider from '@src/components/data-provider/benchmark-provider.vue'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import UserOrEmailPicker from '@components/data-picker/user-or-email-picker.vue'
import MonitorGroupSelection from '@components/widgets/monitor-or-group-selection.vue'
import UserProvider from '@/src/components/data-provider/user-provider.vue'
import NcmMonitorProvider from '@/src/components/data-provider/ncm-monitor-provider.vue'
import GroupProvider from '@components/data-provider/group-provider.vue'
import { AvailableTagType } from '@modules/settings/monitoring/helpers/tag-helper'

export default {
  name: 'AuditPolicyForm',
  components: {
    LooseTags,
    MonitorProvider,
    MonitorGroupSelection,
    UserOrEmailPicker,
    UserProvider,
    GroupProvider,
    BenchmarkPicker,
    BenchmarkProvider,
    NcmMonitorProvider,
  },

  props: {
    value: { type: Object, required: true },
    resetForm: { type: Function, required: false, default: () => ({}) },
    isView: { type: Boolean, required: false },
  },
  data() {
    // this.benchmarkFilterOptions = []
    this.AvailableTagType = AvailableTagType

    this.deviceLabels = {
      entityTypeLabel: 'Device Filter',
      entitiesLabel: 'Select Device',
    }
    this.configTypeOptions = [
      { text: 'Startup', value: 'startup.config' },
      { text: 'Running', value: 'running.config' },
    ]

    this.entityOptions = [
      { key: 'Monitor', text: 'Monitor', inputType: 'monitor' },
      { key: 'Group', text: 'Group', inputType: 'group' },
    ]

    return {}
  },
  computed: {
    searchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.OTHER,
          this.$constants.CLOUD,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.SDN,
          this.$constants.STORAGE,
          this.$constants.CONTAINER_ORCHESTRATION,
        ],
      }
    },
  },
  watch: {
    'value.credentialProfileProtocol': function (newValue) {
      if ([this.$constants.HTTP_HTTPS].indexOf(newValue) >= 0) {
        this.resetForm({
          ...this.value,
          authenticationType: 'basic',
        })
      }
    },
  },
}
</script>
