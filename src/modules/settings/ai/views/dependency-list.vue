<template>
  <GroupProvider>
    <FlotoPaginatedCrud
      ref="crudRef"
      class="h-100"
      form-width="50%"
      as-table
      default-sort="-parentMonitor"
      resource-name="Relationship"
      :columns="columns"
      :default-item="defaultItem"
      :fetch-fn="getAllDependency"
      :update-fn="updateDependency"
      :delete-fn="deleteDependency"
      :create-fn="createDependency"
    >
      <template
        v-slot:add-controls="{
          create,
          filter,
          resetFilter,
          searchTerm,
          processing,
        }"
      >
        <MRow>
          <MCol>
            <MInput
              :value="searchTerm"
              class="search-box"
              placeholder="Search"
              name="role-search"
              @update="filter"
            >
              <template v-slot:prefix>
                <MIcon name="search" />
              </template>
              <template v-if="searchTerm" v-slot:suffix>
                <MIcon
                  name="times-circle"
                  class="text-neutral-light cursor-pointer"
                  @click="resetFilter"
                />
              </template>
            </MInput>
          </MCol>
          <MPermissionChecker
            :permission="$constants.AIOPS_SETTINGS_CREATE_PERMISSION"
          >
            <MCol class="text-right">
              <MButton :loading="processing" @click="create">
                Create Relationship
              </MButton>
            </MCol>
          </MPermissionChecker>
        </MRow>
      </template>

      <template v-slot:form-header="{ item }">
        {{ item.id ? 'Edit' : 'Create' }} Relationship
      </template>

      <template v-slot:form-items="{ item, resetForm }">
        <DependecyForm ref="formRef" :value="item" :reset-form="resetForm" />
      </template>

      <template v-slot:form-actions="{ resetForm, submit, processing, item }">
        <span class="mandatory mt-5"
          ><span class="text-secondary-red">*</span> fields are mandatory</span
        >
        <MButton
          id="reset-btn"
          variant="default"
          class="mr-2"
          @click="
            resetForm(
              item.id ? undefined : { selectionMethod: getSelectionMethod() }
            )
          "
        >
          Reset
        </MButton>
        <MButton id="role-submit-btn" :loading="processing" @click="submit">
          {{ item.id ? 'Update' : 'Create' }} Relationship
        </MButton>
      </template>

      <template v-slot:parentInterface="{ item }">
        {{ (item.parentInterface || {}).name }}
      </template>
      <template v-slot:childInterface="{ item }">
        {{ (item.childInterface || {}).name }}
      </template>
      <template v-slot:isL3="{ item }">
        {{ item.isL3 ? 'L3' : 'L2' }}
      </template>
      <template v-slot:status="{ item, update }">
        <MSwitch
          :checked="!item.archived"
          checked-children="ON"
          un-checked-children="OFF"
          @change="update({ ...item, archived: !$event })"
        />
      </template>
      <template v-slot:actions="{ edit, item }">
        <FlotoGridActions
          :actions="gridItemActions"
          :resource="item"
          :edit-permission-name="$constants.AIOPS_SETTINGS_UPDATE_PERMISSION"
          :delete-permission-name="$constants.AIOPS_SETTINGS_DELETE_PERMISSION"
          @edit="edit"
        />
      </template>
    </FlotoPaginatedCrud>
  </GroupProvider>
</template>

<script>
import GroupProvider from '@components/data-provider/group-provider.vue'
import {
  getAllDependencyApi,
  createDependencyApi,
  updateDependencyApi,
  deleteDependencyApi,
} from '../dependency-mapper-api'
import DependecyForm from '../components/depedency-form.vue'

export default {
  name: 'DependecyList',
  components: { DependecyForm, GroupProvider },
  data() {
    this.defaultItem = {
      selectionMethod: 'manual',
      parentGroups: [],
      childGroups: [],
    }
    this.gridItemActions = [
      { key: 'edit', name: 'Edit Relationship', icon: 'pencil' },
      {
        key: 'delete',
        name: 'Delete Relationship',
        icon: 'trash-alt',
        isDanger: true,
      },
    ]
    return {
      columns: [
        {
          key: 'parentMonitor',
          name: 'Parent Monitor',
          searchable: true,
          sortable: true,
        },
        {
          key: 'parentInterface',
          name: 'Parent Interface',
          searchable: true,
          sortable: true,
          searchKey: 'parentInterfaceName',
        },
        {
          key: 'childMonitor',
          name: 'Child Monitor',
          searchable: true,
          sortable: true,
        },
        {
          key: 'childInterface',
          name: 'Child Interface',
          searchable: true,
          sortable: true,
          searchKey: 'childInterfaceName',
        },
        {
          key: 'type',
          name: 'Type',
          searchable: true,
          sortable: true,
        },
        {
          key: 'isL3',
          name: 'Link Layer',
          searchable: true,
          sortable: true,
          searchKey: 'isL3Str',
        },
        {
          key: 'status',
          name: 'Status',
          searchable: true,
          sortable: true,
          searchKey: 'isArchivedStr',
        },
        {
          name: 'Actions',
          key: 'actions',
          align: 'right',
          width: '120px',
        },
      ],
    }
  },
  methods: {
    createDependency(payload) {
      return createDependencyApi(payload).then((data) => {
        if (payload.selectionMethod === 'csv') {
          this.$refs.crudRef.resetList()
        }
        return data
      })
    },
    getAllDependency() {
      return getAllDependencyApi()
    },
    updateDependency(payload) {
      return updateDependencyApi(payload)
    },
    deleteDependency(payload) {
      return deleteDependencyApi(payload)
    },
    getSelectionMethod() {
      if (this.$refs.formRef) {
        return this.$refs.formRef.value.selectionMethod
      }
      return 'manual'
    },
  },
}
</script>
