import api from '@api'

function transformDependecyMapperForServer(payload) {
  const data = {
    'dependency.mapper.parent': payload.parentMonitor,
    'dependency.mapper.child': payload.childMonitor,
    'dependency.mapper.type': 'Network',
    'dependency.mapper.archived': payload.archived ? 'yes' : 'no',
    'dependency.mapper.context': {
      ...((payload.parentInterface || {}).index
        ? {
            'dependency.mapper.parent.interface': payload.parentInterface.index,
            'dependency.mapper.parent.interface.name':
              payload.parentInterface.name,
          }
        : {}),
      ...((payload.childInterface || {}).index
        ? {
            'dependency.mapper.child.interface': payload.childInterface.index,
            'dependency.mapper.child.interface.name':
              payload.childInterface.name,
          }
        : {}),
      'dependency.mapper.link.layer': payload.isL3 ? 'L3' : 'L2',
    },
  }

  if (payload.selectionMethod === 'csv') {
    data['dependency.mapper.parent'] = 'test'
    data['dependency.mapper.child'] = 'test'
    data['dependency.mapper.type'] = 'Network'
    data['dependency.mapper.archived'] = 'no'
    data['dependency.mapper.file'] = payload.csv[0].result
    data['dependency.mapper.context'] = {
      'dependency.mapper.parent.interface': '1',
      'dependency.mapper.parent.interface.name': 'test',
      'dependency.mapper.child.interface': '2',
      'dependency.mapper.child.interface.name': 'test',
      'dependency.mapper.link.layer': 'L3',
    }
  }

  return data
}

function transformDependecyMapperForClient(payload) {
  return {
    id: payload.id,
    selectionMethod: 'manual',
    parentMonitor: payload['dependency.mapper.parent'],
    childMonitor: payload['dependency.mapper.child'],
    parentInterface: {
      index: (payload['dependency.mapper.context'] || {})[
        'dependency.mapper.parent.interface'
      ],
      name: (payload['dependency.mapper.context'] || {})[
        'dependency.mapper.parent.interface.name'
      ],
    },
    parentInterfaceName: (payload['dependency.mapper.context'] || {})[
      'dependency.mapper.parent.interface.name'
    ],
    childInterface: {
      index: (payload['dependency.mapper.context'] || {})[
        'dependency.mapper.child.interface'
      ],
      name: (payload['dependency.mapper.context'] || {})[
        'dependency.mapper.child.interface.name'
      ],
    },
    childInterfaceName: (payload['dependency.mapper.context'] || {})[
      'dependency.mapper.child.interface.name'
    ],
    isL3:
      (payload['dependency.mapper.context'] || {})[
        'dependency.mapper.link.layer'
      ] === 'L3',
    isL3Str: (payload['dependency.mapper.context'] || {})[
      'dependency.mapper.link.layer'
    ],
    type: payload._type === '1' ? 'Manual' : 'Auto',
    archived: payload['dependency.mapper.archived'] === 'yes',
    isArchivedStr:
      payload['dependency.mapper.archived'] === 'yes' ? 'yes' : 'no',
    canEdit: payload._type === '1',
  }
}

export function getAllDependencyApi() {
  return api.get(`/settings/dependency-mappers`).then((data) => {
    return (data.result || []).map(transformDependecyMapperForClient)
  })
}

export function getAllInstancesApi(monitor, transformFn) {
  return api
    .post(`/settings/dependency-mappers/instances`, {
      'object.ip': monitor,
      'dependency.mapper.type': 'Network',
    })
    .then((data) => {
      if (data.result && data.result['metric.context']) {
        return (data.result['metric.context']['objects'] || []).map((i) => {
          return transformFn
            ? transformFn(i)
            : {
                key: i['interface.index'],
                text: `${i['interface']}`,
                name: i['interface.name'],
              }
        })
      }
      return []
    })
}

export function createDependencyApi(payload) {
  return api
    .post(
      `/settings/dependency-mappers`,
      transformDependecyMapperForServer(payload)
    )
    .then(({ id }) => {
      if (payload.selectionMethod === 'csv') {
        return undefined
      }
      return getDependencyApi(id)
    })
}

export function getDependencyApi(id) {
  return api
    .get(`/settings/dependency-mappers/${id}`)
    .then(({ result }) => transformDependecyMapperForClient(result))
}

export function updateDependencyApi(payload) {
  return api
    .put(
      `/settings/dependency-mappers/${payload.id}`,
      transformDependecyMapperForServer(payload)
    )
    .then(() => getDependencyApi(payload.id))
}

export function deleteDependencyApi(payload) {
  return api.delete(`/settings/dependency-mappers/${payload.id}`)
}

export function wanLinkFilter(data = []) {
  return data.filter((i) => i.ipAddress && i.ipAddress !== '')
}

export function wanLinkTransformer(item = {}) {
  return {
    key: item['interface.index'],
    text: `${item['interface.name']}`,
    name: item['interface.name'],
    ipAddress: item['interface.ip.address'],
  }
}
