<template>
  <div class="flex flex-col flex-1 min-h-0">
    <MRow class="my-4">
      <MCol :size="12">
        <MRadioGroup
          v-model="value.selectionMethod"
          :options="selectionMethodOptions"
          as-button
        >
        </MRadioGroup>
      </MCol>
    </MRow>
    <template v-if="value.selectionMethod === 'manual'">
      <h5 class="text-primary">Parent</h5>
      <MRow>
        <MCol :size="6">
          <MonitorProvider
            :search-params="parentMonitorSearchParams"
            use-ip
            :excluded-types="parentExcludedTypes"
          >
            <FlotoFormItem
              class="flex-form-control"
              rules="required"
              label="Select Parent Device"
            >
              <DependencyMonitorPicker v-model="value.parentMonitor" />
            </FlotoFormItem>
          </MonitorProvider>
        </MCol>
        <MCol :size="6">
          <FlotoFormItem
            v-if="value.parentMonitor"
            rules="required"
            class="flex-form-control"
            label="Select Parent Interface"
          >
            <DependencyInterfacePicker
              v-model="value.parentInterface"
              :monitor="value.parentMonitor"
            />
          </FlotoFormItem>
        </MCol>
      </MRow>
      <MDivider />
      <h5 class="text-primary">Child</h5>
      <MRow>
        <MCol :size="6">
          <MonitorProvider :search-params="childMonitorSearchParams" use-ip>
            <FlotoFormItem
              class="flex-form-control"
              rules="required"
              label="Select Child Device"
            >
              <DependencyMonitorPicker
                v-model="value.childMonitor"
                :excluded-options="
                  value.parentMonitor ? [value.parentMonitor] : undefined
                "
              />
            </FlotoFormItem>
          </MonitorProvider>
        </MCol>
        <MCol :size="6">
          <FlotoFormItem
            v-if="value.childMonitor"
            vid="childInterface"
            label="Select Child Interface"
            :rules="{ required: Boolean(value.isL3) }"
          >
            <DependencyInterfacePicker
              v-model="value.childInterface"
              :monitor="value.childMonitor"
            />
          </FlotoFormItem>
        </MCol>
      </MRow>
      <MRow>
        <MCol :size="12">
          <MCheckbox v-model="value.isL3"> Select if L3. </MCheckbox>
        </MCol>
      </MRow>
    </template>
    <template v-else>
      <MRow>
        <MCol :size="5">
          <FlotoFormItem
            id="csv-select-id"
            rules="required"
            label="CSV"
            validation-label="CSV"
          >
            <FileDropper
              id="csv-upload"
              v-model="value.csv"
              mode="attachment"
              button-text="Upload CSV"
              as-link
              :allowed-extensions="['csv']"
              :multiple="false"
              :max-files="1"
            />
            <div
              id="sample-csv-download-id"
              class="text-right"
              style="margin-top: -8px"
            >
              <a
                href="/samples/dependency-mapper-sample.csv"
                target="_blank"
                class="flex align-center justify-end"
              >
                <MIcon name="download" class="mr-1 flex items-center" />
                Sample CSV
              </a>
            </div>
          </FlotoFormItem>
        </MCol>
      </MRow>
    </template>
  </div>
</template>

<script>
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import DependencyMonitorPicker from './dependency-monitor-picker.vue'
import FileDropper from '@components/file-dropper.vue'
import DependencyInterfacePicker from './dependency-interface-picker.vue'

export default {
  name: 'DependencyForm',
  components: {
    DependencyMonitorPicker,
    DependencyInterfacePicker,
    MonitorProvider,
    FileDropper,
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    resetForm: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      selectionMethodOptions: [
        {
          label: 'Select Manually',
          value: 'manual',
        },
        {
          label: 'Upload CSV',
          value: 'csv',
          disabled: !!this.value.id,
        },
      ],
    }
  },
  computed: {
    parentExcludedTypes() {
      return [
        this.$constants.ARUBA_WIRELESS,
        this.$constants.CISCO_WIRELESS,
        this.$constants.RUCKUS_WIRELESS,
      ]
    },
    parentMonitorSearchParams() {
      return {
        category: [this.$constants.NETWORK],
      }
    },
    childMonitorSearchParams() {
      if (this.value.isL3) {
        return {
          category: [this.$constants.NETWORK],
        }
      }
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.OTHER,
          this.$constants.VIRTUALIZATION,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.SDN,
          // this.$constants.STORAGE,
        ],
      }
    },
  },
  watch: {
    'value.selectionMethod': function (newValue, oldValue) {
      if (newValue !== oldValue && newValue === 'manual') {
        this.resetForm({
          selectionMethod: 'manual',
        })
      }
    },
    'value.isL3': function (newValue, oldValue) {
      if (newValue !== oldValue && newValue) {
        this.resetForm({
          ...this.value,
          childMonitor: undefined,
          childInterface: undefined,
        })
      }
    },
  },
}
</script>
