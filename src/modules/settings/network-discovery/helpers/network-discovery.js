/**
 * network discovery
 */
import Reverse from 'lodash/reverse'
import SortBy from 'lodash/sortBy'
import GroupBy from 'lodash/groupBy'
import Lowercase from 'lodash/lowerCase'
import Invert from 'lodash/invert'
import { generateId } from '@utils/id'
import Uniq from 'lodash/uniq'
import Constants from '@constants'

export function transformNetworkDiscoveryForList(discovery) {
  return {
    id: discovery[Constants.ID_PROPERTY],
    type: getDiscoveryCategoryForClient(discovery),
    subType: getObjectTypeForClient(discovery),
    objectType: discovery['discovery.object.type'],
    name: discovery['discovery.name'],
    ip: discovery['discovery.target.name'],
    group: discovery['discovery.groups'],
    discoveredObjects: discovery['discovery.discovered.objects'],
    rpe: discovery['discovery.event.processors'],
    status: discovery['discovery.status'], // in miliseconds
    scheduled: discovery['discovery.scheduler'],
    isInProgress: discovery.state === 'Running',
    scheduleCount: discovery['discovery.scheduler.count'],
    sourceAgent: discovery['discovery.method'] === 'AGENT',
    tags: discovery['discovery.user.tags'],
    monitorsError: discovery['discovery.failed.objects'] || 0,
  }
}

const DISCOVERY_TYPE_MAP = {
  IPRange: 'ip.address.range',
  IP: 'ip.address',
  CSV: 'csv',
  CIDR: 'cidr',
  OBJECT: 'object',
  OTHER: 'other',
  URL: 'url',
}

/** server side translation **/

export function transformNetworkDiscoveryForServer(discovery) {
  const discoveryInfo = discovery.discoveryInfo
  return {
    'discovery.category': getDiscoveryCategoryForServer(discovery),
    'discovery.object.type': getObjectTypeForServer(discovery),
    'discovery.name': discoveryInfo.name,
    ...transformIPTypeOptionForServer(discoveryInfo, discovery.type),
    ...transformExcludeIPTypeOptionForServer(
      discoveryInfo,
      discovery.excludeIpType
    ),
    ...(discoveryInfo.credentials
      ? {
          'discovery.credential.profiles': Array.isArray(
            discoveryInfo.credentials
          )
            ? discoveryInfo.credentials
            : [discoveryInfo.credentials],
        }
      : {}),

    'discovery.config.management.status': discoveryInfo.manageNCM
      ? 'yes'
      : 'no',
    'discovery.event.processors': discoveryInfo.rpe,
    'discovery.groups': discoveryInfo.group,
    'aws.discovery.regions': discoveryInfo.region,
    'discovery.email.recipients': (discoveryInfo.notification || {}).email,
    'discovery.sms.recipients': (discoveryInfo.notification || {}).sms,
    'discovery.user.tags': discoveryInfo.tags || [],
    ...getDiscoveryAdditionalParamsForServer(discovery),
    'discovery.context': getTypeSpecificParamsForServer(discovery),
  }
}

function getDiscoveryCategoryForServer(discovery) {
  if (discovery.type === Constants.WIRELESS) {
    return Constants.NETWORK
  }
  return discovery.type
}

function getObjectTypeForServer(discovery) {
  const discoveryInfo = discovery.discoveryInfo
  if (discoveryInfo.subType === Constants.WINDOWS) {
    return discoveryInfo.windowsType
  }
  if (discovery.type === Constants.VIRTUALIZATION) {
    return discoveryInfo.virtualizationType
  }
  if (discovery.type === Constants.SERVICE_CHECK) {
    return discoveryInfo.serviceType
  }
  if (discovery.type === Constants.STORAGE) {
    return discoveryInfo.deviceModel
  }
  return discoveryInfo.subType
}

export function transformMapForServer(data) {
  let result = (data || []).reduce((obj, item) => {
    obj[item.name] = item.value
    return obj
  }, {})
  return result
}

function getTypeSpecificParamsForServer(discovery) {
  const type = discovery.type
  const discoveryInfo = discovery.discoveryInfo
  switch (type) {
    case Constants.SERVER:
    case Constants.NETWORK:
    case Constants.VIRTUALIZATION:
    case Constants.HYPERCONVERGED_INFRASTRUCTURE:
    case Constants.WIRELESS:
    case Constants.SDN:
    case Constants.STORAGE:
    case Constants.CONTAINER_ORCHESTRATION:
      return {
        'ping.check.status': discoveryInfo.pingCheck ? 'yes' : 'no',
        port: discoveryInfo.port,
        ...(discoveryInfo.subType === Constants.RUCKUS_WIRELESS
          ? {
              'controller.model': discoveryInfo.controllerModel,
            }
          : {}),
        ...(discoveryInfo.subType === Constants.RUCKUS_WIRELESS
          ? {
              'url.protocol': discoveryInfo.urlType,
            }
          : {}),
        ...(discoveryInfo.subType === Constants.CITRIX_XEN
          ? {
              'url.protocol': discoveryInfo.urlType,
            }
          : {}),
        ...(discoveryInfo.subType === Constants.PRISM
          ? {
              'url.protocol': discoveryInfo.urlType,
            }
          : {}),
        ...(discoveryInfo.subType === Constants.VMWARE_TANZU_KUBERNETES
          ? {
              'url.protocol': discoveryInfo.urlType,
            }
          : {}),
        ...(discoveryInfo.subType === Constants.CISCO_VMANAGE ||
        Constants.CISCO_ACI ||
        discoveryInfo.subType === Constants.NSXT
          ? {
              'url.protocol': discoveryInfo.urlType,
            }
          : {}),

        ...(type === Constants.STORAGE
          ? {
              'url.protocol': discoveryInfo.urlType,
            }
          : {}),
        ...(type === Constants.NETWORK
          ? {
              'snmp.check.retries': discoveryInfo.retryCount,
              ...(discoveryInfo.manageNCM
                ? {
                    'config.port': discoveryInfo.ncmPort,
                  }
                : {}),

              'interface.discovery': discoveryInfo.interfaceDiscover
                ? 'yes'
                : 'no',
              'topology.plugin.discovery': discoveryInfo.runTopology
                ? 'yes'
                : 'no',
              ...(discoveryInfo.topologyEntryMonitor
                ? {
                    'topology.plugin.entry.points': [
                      discoveryInfo.topologyEntryMonitor,
                    ],
                  }
                : {}),
              ...(discoveryInfo.runTopology
                ? {
                    'topology.link.layer': discoveryInfo.layerProtocol,
                    'topology.protocols': discoveryInfo.protocols,
                    // 'topology.discovery.unknown.link': discoveryInfo.showUnknownDevices
                    //   ? 'yes'
                    //   : 'no',
                  }
                : {}),
            }
          : {}),
        ...(discoveryInfo.subType === Constants.CISCO_MERAKI
          ? {
              'ping.check.retries': discoveryInfo.retryCount,
            }
          : {}),
      }
    case Constants.CLOUD:
      if (
        [Constants.AWS_CLOUD, Constants.AZURE_CLOUD].includes(
          discoveryInfo.subType
        )
      ) {
        return {
          'cloud.service.down.instance.discovery':
            discoveryInfo.cloudInstanceCheck ? 'yes' : 'no',

          ...(discoveryInfo.monitoredResources === 'resourcesTags'
            ? {
                'cloud.service.discovery.tags': discoveryInfo.resourcesTags
                  .filter((data) => data.keyName)
                  .map((val) => {
                    return `${val.keyName}_|@#|_${val.value}`
                  }),
              }
            : {}),
        }
      }
      return {}
    case Constants.OTHER:
      return {
        // timeout: parseInt(discoveryInfo.timeout),
        ...(discoveryInfo.subType === Constants.PING
          ? {
              'ping.check.retries': discoveryInfo.retryCount,
            }
          : {
              'ping.check.status': discoveryInfo.pingCheck ? 'yes' : 'no',
              port: discoveryInfo.port,
              ...(discoveryInfo.subType === Constants.CISCO_UCS
                ? {
                    'url.protocol': discoveryInfo.urlType,
                  }
                : {}),
            }),
      }
    case Constants.SERVICE_CHECK:
      return {
        // timeout: parseInt(discoveryInfo.timeout),
        ...([
          Constants.PORT,
          Constants.RADIUS,
          Constants.NTP,
          Constants.DNS,
          Constants.FTP,
          Constants.EMAIL,
          Constants.SSL_CERTIFICATE,
        ].indexOf(discoveryInfo.serviceType) >= 0
          ? {
              port: discoveryInfo.port,
            }
          : {}),
        ...(discoveryInfo.serviceType === Constants.PING
          ? {
              'ping.check.retries': discoveryInfo.retryCount,
            }
          : {}),
        ...(discoveryInfo.serviceType === Constants.PORT
          ? {
              'port.command.status': discoveryInfo.enableCommandExecution
                ? 'yes'
                : 'no',
              'port.type': discoveryInfo.portType,
              'port.command.output.search.keyword': discoveryInfo.content,
              'port.check.status':
                discoveryInfo.portType === 'udp' ? 'no' : 'yes',
              'port.command.output.max.lines': discoveryInfo.maxLines,
              'port.command': discoveryInfo.command,
            }
          : {}),
        ...(discoveryInfo.serviceType === Constants.URL
          ? {
              'url.protocol': discoveryInfo.urlType,
              'url.json': discoveryInfo.jsonUrl,
              'url.endpoint': discoveryInfo.urlEndPoint,
              'url.method': discoveryInfo.urlMethod,
              'url.content.search.keyword': discoveryInfo.urlContent,
              'url.headers': transformMapForServer(discoveryInfo.headers),
              'url.parameters': transformMapForServer(discoveryInfo.parameters),
            }
          : {}),
        ...(discoveryInfo.serviceType === Constants.RADIUS
          ? {
              username: discoveryInfo.username,
              password: discoveryInfo.password,
              'radius.secret': discoveryInfo.radiusSecret,
            }
          : {}),
        ...(discoveryInfo.serviceType === Constants.DNS
          ? {
              'dns.lookup.address': discoveryInfo.lookupAddress,
              'dns.record.type': discoveryInfo.dnsRecordType,
            }
          : {}),
        ...(discoveryInfo.serviceType === Constants.FTP
          ? {
              username: discoveryInfo.username,
              password: discoveryInfo.password,
            }
          : {}),
        ...(discoveryInfo.serviceType === Constants.EMAIL
          ? {
              'email.security.type': discoveryInfo.emailSecurityType,
            }
          : {}),
      }
    default:
      return {}
  }
}

function getDiscoveryAdditionalParamsForServer(discovery) {
  if (discovery.type === Constants.STORAGE) {
    return { 'discovery.vendor': discovery.discoveryInfo.vendor }
  }
  if (discovery.type !== Constants.SERVICE_CHECK) {
    return {}
  }
  const discoveryInfo = discovery.discoveryInfo
  return {
    'discovery.method': discoveryInfo.sourceAgent ? 'AGENT' : 'REMOTE',
    ...(discoveryInfo.sourceAgent
      ? {
          'discovery.agents': discoveryInfo.sourceAgents,
        }
      : {}),
    ...(discoveryInfo.serviceType === Constants.PING
      ? { 'ping.check.status': 'yes' }
      : {}),
    ...([Constants.PING, Constants.PORT].indexOf(discoveryInfo.serviceType) ===
      -1 || discoveryInfo.targetType === 'OBJECT'
      ? {
          'discovery.target.type': discoveryInfo.targetType,
        }
      : {
          'discovery.target.type': 'OTHER',
        }),
    'discovery.target':
      discoveryInfo.targetType === 'CSV'
        ? discoveryInfo.target[0].result
        : String(discoveryInfo.target),
  }
}

function getTargetFromIpTypeForServer(ipType, formData) {
  if (ipType === 'IPRange') {
    return formData.ipRange
  }
  if (ipType === 'IP') {
    return formData.ipAddress
  }
  if (ipType === 'CSV') {
    return formData.csv ? formData.csv[0].result : formData.target[0].result
  }
  if (ipType === 'CIDR') {
    return formData.cidr
  }
  if (ipType === 'OBJECT') {
    return formData.target
  }
}

function transformIPTypeOptionForServer(option, discoveryType) {
  if (!DISCOVERY_TYPE_MAP[option.targetType || option.ipType]) {
    return {}
  }
  return {
    'discovery.type': DISCOVERY_TYPE_MAP[option.targetType || option.ipType],
    'discovery.target': getTargetFromIpTypeForServer(
      option.targetType || option.ipType,
      option
    ),
    ...((option.targetType || option.ipType) === 'CSV'
      ? {
          'discovery.target.name': (
            (option.csv || option.target || [])[0] || {}
          ).name,
        }
      : {}),
  }
}

function transformExcludeIPTypeOptionForServer(option) {
  if (!DISCOVERY_TYPE_MAP[option.excludeIpType]) {
    return {}
  }
  return {
    'discovery.exclude.target.type': DISCOVERY_TYPE_MAP[option.excludeIpType],
    'discovery.exclude.targets': option.excludedIpsOrRange,
  }
}

/** Client side conversion starts */

export function transformNetworkDiscoveryForClient(discovery) {
  return {
    id: discovery[Constants.ID_PROPERTY],
    type: getDiscoveryCategoryForClient(discovery),
    isInProgress: discovery.state === 'Running',
    discoveryInfo: {
      subType: getObjectTypeForClient(discovery),
      name: discovery['discovery.name'],
      ...transformIPTypeOptionForClient(discovery),
      ...transformExcludeIPTypeOptionForClient(discovery),
      rpe: discovery['discovery.event.processors'],
      group: discovery['discovery.groups'],
      region: discovery['aws.discovery.regions'],
      credentials:
        discovery['discovery.object.type'] === Constants.CLOUD
          ? discovery['discovery.credential.profiles'][0]
          : discovery['discovery.credential.profiles'],
      notification: {
        email: discovery['discovery.email.recipients'],
        sms: discovery['discovery.sms.recipients'],
      },
      manageNCM: discovery['discovery.config.management.status'] === 'yes',
      tags: discovery['discovery.user.tags'],
      ...getDiscoveryAdditionalParamsForClient(discovery),
      ...getTypeSpecificParamsForClient(discovery),
      // controllerModel: discovery['discovery.object.model'],
    },
  }
}

function getDiscoveryCategoryForClient(discovery) {
  const subType = getObjectTypeForClient(discovery)
  if (
    [
      Constants.CISCO_WIRELESS,
      Constants.RUCKUS_WIRELESS,
      Constants.ARUBA_WIRELESS,
    ].indexOf(subType) >= 0
  ) {
    return 'Wireless'
  }
  return discovery['discovery.category']
}

function getVirtualizationSubType(type) {
  if ([Constants.VCENTER, Constants.VMWARE_ESXI].indexOf(type) >= 0) {
    return 'VMWare'
  }
  if ([Constants.HYPER_V, Constants.HYPER_V_CLUSTER].indexOf(type) >= 0) {
    return 'Hyper-V'
  }
  if ([Constants.CITRIX_XEN, Constants.CITRIX_XEN_CLUSTER].indexOf(type) >= 0) {
    return 'Citrix Xen'
  }
}

function getDiscoveryAdditionalParamsForClient(discovery) {
  if (discovery['discovery.category'] !== Constants.SERVICE_CHECK) {
    return {}
  }
  const keys = transformIPTypeOptionForClient(discovery)
  return {
    serviceType: discovery['discovery.object.type'],
    sourceAgent: discovery['discovery.method'] === 'AGENT',
    ...(discovery['discovery.method'] === 'AGENT'
      ? {
          sourceAgents: discovery['discovery.agents'],
        }
      : {}),
    targetType: keys.targetType || discovery['discovery.target.type'],
    target:
      (keys.targetType || discovery['discovery.target.type']) === 'CSV'
        ? [
            {
              result: discovery['discovery.target'],
              name: discovery['discovery.target.name'],
            },
          ]
        : discovery['discovery.target'],
  }
}

function getObjectTypeForClient(discovery) {
  if (discovery['discovery.object.type'] === Constants.WINDOWS_CLUSTER) {
    return 'Windows'
  }
  if (discovery['discovery.category'] === Constants.VIRTUALIZATION) {
    return getVirtualizationSubType(discovery['discovery.object.type'])
  }
  return discovery['discovery.object.type']
}

function transformIPTypeOptionForClient(discovery) {
  const map = Invert(DISCOVERY_TYPE_MAP)
  return {
    ...(discovery['discovery.category'] === Constants.SERVICE_CHECK
      ? { targetType: map[discovery['discovery.type']] }
      : { ipType: map[discovery['discovery.type']] }),
    ...getTargetFromIpTypeForClient(discovery),
  }
}

function transformExcludeIPTypeOptionForClient(discovery) {
  const map = Invert(DISCOVERY_TYPE_MAP)
  return {
    excludeIpType: map[discovery['discovery.exclude.target.type']],
    excludedIpsOrRange: discovery['discovery.exclude.targets'],
  }
}

function getTargetFromIpTypeForClient(discovery) {
  if (discovery['discovery.type'] === 'ip.address.range') {
    return {
      ipRange: discovery['discovery.target'],
    }
  } else if (discovery['discovery.type'] === 'ip.address') {
    return {
      ipAddress: discovery['discovery.target'],
    }
  } else if (discovery['discovery.type'] === 'csv') {
    return {
      csv: [
        {
          result: discovery['discovery.target'],
          name: discovery['discovery.target.name'],
        },
      ],
    }
  } else if (discovery['discovery.type'] === 'cidr') {
    return {
      cidr: discovery['discovery.target'],
    }
  } else if (discovery['discovery.type'] === 'object') {
    return {
      target: discovery['discovery.target'],
    }
  }
  return {}
}

export function transformMapForClient(data) {
  let result = Object.keys(data || {}).map((key) => {
    return {
      key: generateId(),
      name: key,
      value: data[key],
    }
  })
  return result
}

function getTypeSpecificParamsForClient(discovery) {
  const discoveryContext = discovery['discovery.context']
  const category = discovery['discovery.category']
  switch (category) {
    case Constants.SERVER:
    case Constants.NETWORK:
    case Constants.VIRTUALIZATION:
    case Constants.HYPERCONVERGED_INFRASTRUCTURE:
    case Constants.WIRELESS:
    case Constants.SDN:
    case Constants.STORAGE:
    case Constants.CONTAINER_ORCHESTRATION:
      return {
        port: discoveryContext.port,
        pingCheck: discoveryContext['ping.check.status'] === 'yes',
        ...(discovery['discovery.object.type'] === Constants.RUCKUS_WIRELESS
          ? {
              urlType: discoveryContext['url.protocol'],
            }
          : {}),
        ...(discovery['discovery.object.type'] === Constants.RUCKUS_WIRELESS
          ? {
              controllerModel: discoveryContext['controller.model'],
            }
          : {}),
        ...(discovery['discovery.object.type'] === Constants.PRISM
          ? {
              urlType: discoveryContext['url.protocol'],
            }
          : {}),
        ...(discovery['discovery.object.type'] === Constants.CISCO_VMANAGE ||
        Constants.CISCO_ACI ||
        discovery['discovery.object.type'] === Constants.NSXT
          ? {
              urlType: discoveryContext['url.protocol'],
            }
          : {}),
        ...(discovery['discovery.object.type'] === Constants.CITRIX_XEN ||
        Constants.CITRIX_XEN_CLUSTER
          ? {
              urlType: discoveryContext['url.protocol'],
            }
          : {}),
        ...(category === Constants.STORAGE
          ? {
              urlType: discoveryContext['url.protocol'],
              deviceModel: discovery['discovery.object.type'],
              vendor: discovery['discovery.vendor'],
            }
          : {}),
        ...(discovery['discovery.object.type'] ===
        Constants.VMWARE_TANZU_KUBERNETES
          ? {
              urlType: discoveryContext['url.protocol'],
            }
          : {}),
        ...(category === Constants.NETWORK
          ? {
              retryCount: discoveryContext['retry.count'],
              // ...(discovery['discovery.config.management.status'] === 'yes'
              //   ? {
              ncmPort: discoveryContext['config.port'] || 22,
              // }
              // : {}),

              interfaceDiscover:
                discoveryContext['interface.discovery'] === 'yes',
              runTopology:
                discoveryContext['topology.plugin.discovery'] === 'yes',
              ...(discoveryContext['topology.plugin.entry.points']
                ? {
                    topologyEntryMonitor:
                      discoveryContext['topology.plugin.entry.points'][0],
                  }
                : {}),
              ...(discoveryContext['topology.plugin.discovery'] === 'yes'
                ? {
                    // showUnknownDevices:
                    //   discoveryContext['topology.discovery.unknown.link'] ===
                    //   'yes',
                    layerProtocol: discoveryContext['topology.link.layer'],
                    protocols: discoveryContext['topology.protocols'],
                  }
                : {}),
            }
          : {}),
        ...(discovery['discovery.object.type'].indexOf('Windows') >= 0
          ? {
              windowsType: discovery['discovery.object.type'] || 'Windows',
            }
          : {}),

        ...(category === Constants.SERVER
          ? {
              windowsServiceStartMode:
                discoveryContext['discovery.windows.service.mode'],
            }
          : {}),
        ...(category === Constants.NETWORK
          ? {
              retryCount: discoveryContext['snmp.check.retries'],
              interfaceDiscover:
                discoveryContext['interface.discovery'] === 'yes',
            }
          : {}),
        ...(category === Constants.VIRTUALIZATION
          ? { virtualizationType: discovery['discovery.object.type'] }
          : {}),

        ...(discovery['discovery.object.type'] === Constants.CISCO_MERAKI
          ? {
              retryCount: discoveryContext['ping.check.retries'],
            }
          : {}),
      }
    case Constants.OTHER:
      return {
        // timeout: discoveryContext.timeout,
        port: discoveryContext.port,
        pingCheck: discoveryContext['ping.check.status'] === 'yes',
        ...(discovery['discovery.object.type'] === Constants.CISCO_UCS
          ? {
              urlType: discoveryContext['url.protocol'],
            }
          : {}),
        ...(discovery['discovery.object.type'] === Constants.PING
          ? {
              retryCount: discoveryContext['ping.check.retries'],
            }
          : {}),
      }
    case Constants.CLOUD:
      return {
        // timeout: discoveryContext.timeout,
        secretKey: discoveryContext['secret.key'],
        ...(discovery['discovery.object.type'] === Constants.AWS_CLOUD
          ? {
              accessKey: discoveryContext['access.id'],
            }
          : {
              tenantId: discoveryContext['tenant.id'],
              clientId: discoveryContext['client.id'],
            }),
        ...([Constants.AWS_CLOUD, Constants.AZURE_CLOUD].includes(
          discovery['discovery.object.type']
        )
          ? {
              cloudInstanceCheck:
                discoveryContext['cloud.service.down.instance.discovery'] ===
                'yes',

              ...(discoveryContext['cloud.service.discovery.tags']
                ? {
                    resourcesTags: discoveryContext[
                      'cloud.service.discovery.tags'
                    ].map((key) => {
                      let finalValue = key.split('_|@#|_')
                      return {
                        key: generateId(),
                        keyName: finalValue[0],
                        value: finalValue[1],
                      }
                    }),
                  }
                : {
                    monitoredResources: 'resources',
                  }),

              ...((discoveryContext['cloud.service.discovery.tags'] || [])
                .length
                ? { monitoredResources: 'resourcesTags' }
                : {}),
            }
          : {}),
      }
    case Constants.SERVICE_CHECK:
      return {
        // timeout: discoveryContext.timeout,
        port: discoveryContext.port,
        ...(discovery['discovery.object.type'] === Constants.PING
          ? {
              retryCount: discoveryContext['ping.check.retries'],
            }
          : {}),
        ...(discovery['discovery.object.type'] === Constants.PORT
          ? {
              content: discoveryContext['port.command.output.search.keyword'],
              maxLines: discoveryContext['port.command.output.max.lines'],
              command: discoveryContext['port.command'],
              enableCommandExecution:
                discoveryContext['port.command.status'] === 'yes',
              portType: discoveryContext['port.type'],
            }
          : {}),
        ...(discovery['discovery.object.type'] === Constants.URL
          ? {
              urlType: discoveryContext['url.protocol'],
              jsonUrl: discoveryContext['url.json'],
              urlMethod: discoveryContext['url.method'],
              urlContent: discoveryContext['url.content.search.keyword'],
              urlEndPoint: discoveryContext['url.endpoint'],
              headers: transformMapForClient(discoveryContext['url.headers']),
              parameters: transformMapForClient(
                discoveryContext['url.parameters']
              ),
            }
          : {}),
        ...(discovery['discovery.object.type'] === Constants.RADIUS
          ? {
              username: discoveryContext['username'],
              password: discoveryContext['password'],
              radiusSecret: discoveryContext['radius.secret'],
            }
          : {}),
        ...(discovery['discovery.object.type'] === Constants.DNS
          ? {
              lookupAddress: discoveryContext['dns.lookup.address'],
              dnsRecordType: discoveryContext['dns.record.type'],
            }
          : {}),
        ...(discovery['discovery.object.type'] === Constants.FTP
          ? {
              username: discoveryContext['username'],
              password: discoveryContext['password'],
            }
          : {}),
        ...(discovery['discovery.object.type'] === Constants.EMAIL
          ? {
              emailSecurityType: discoveryContext['email.security.type'],
            }
          : {}),
      }
    default:
      return {}
  }
}

export function transformNetworkDiscoveryProgressData(result, discoveryData) {
  if (
    [
      Constants.SERVER,
      Constants.NETWORK,
      Constants.VIRTUALIZATION,
      Constants.WIRELESS,
      Constants.SERVICE_CHECK,
      Constants.OTHER,
      Constants.HYPERCONVERGED_INFRASTRUCTURE,
      Constants.SDN,
      Constants.STORAGE,
      Constants.CONTAINER_ORCHESTRATION,
    ].indexOf(result.type) >= 0
  ) {
    return transformServerDiscoveryProgress(result, discoveryData)
  } else if (result.type === Constants.CLOUD) {
    return transformCloudDiscoveryProgress(result, discoveryData)
  }
}

export function transformMonitorProgress(monitor) {
  if (!(monitor['object.ip'] || monitor['object.target'])) {
    return null
  }
  return {
    agentId: monitor['object.agent'],
    ip: monitor['object.ip'] || monitor['object.target'],
    hasError:
      monitor['discovery.progress'] > 0 &&
      monitor.status === Constants.EVENT_FAIL_STATUS,
    progress: monitor['discovery.progress'] || 0,
    isCompleted:
      (monitor['discovery.progress'] || 0) >= 100 &&
      monitor.status === Constants.EVENT_SUCCESS_STATUS,
    commands: Uniq(monitor.commands || []),
    message: monitor.message,
    error: monitor.error,
  }
}

export function transformCloudMonitorProgress(monitor) {
  return {
    hasError: monitor.status !== Constants.EVENT_SUCCESS_STATUS,
    progress: monitor['discovery.progress'],
    isRunning: monitor['discovery.progress'] < 100,
    isCompleted: monitor['discovery.progress'] >= 100,
    commands: monitor.commands || [],
    message: monitor.message,
    error: monitor.error,
  }
}

function transformServerDiscoveryProgress(result, discoveryData) {
  const statistics = discoveryData['discovery.statistics']
  const monitorsWithoutEvent = (discoveryData.result || []).filter(
    (m) => !m['event.id']
  )
  const monitorsWithEvent = (discoveryData.result || []).filter(
    (m) => m['event.id']
  )
  const monitors = GroupBy(monitorsWithEvent, 'event.id')
  return {
    id: result.id,
    name: result.name,
    type: result.type,
    isInProgress: result.isInProgress,
    isCompleted: !result.isInProgress,
    isAgentBased: result.sourceAgent,
    monitorsSearched: statistics['discovery.total.objects'] || 0,
    monitorsDiscovered: statistics['discovery.discovered.objects'] || 0,
    monitorsError: statistics['discovery.failed.objects'] || 0,
    progress: statistics['discovery.progress'] || 0,
    subType: result.subType,
    monitors: Object.keys(monitors)
      .map((key) => {
        const n = SortBy(monitors[key], 'discovery.progress')
        return transformMonitorProgress({
          ...n[n.length - 1],
          commands: n.map((monitor) => monitor.message),
        })
      })
      .filter((n) => n)
      .concat(monitorsWithoutEvent.map(transformMonitorProgress)),
  }
}

function transformCloudDiscoveryProgress(result, discoveryData) {
  const monitors = discoveryData.result || []
  const n = SortBy(monitors, 'discovery.progress')
  const finalMonitor = n[n.length - 1]
  const statistics = discoveryData['discovery.statistics']
  const monitor = finalMonitor
    ? transformCloudMonitorProgress({
        ...n[n.length - 1],
        commands: n.map((monitor) => monitor.message),
      })
    : undefined
  return {
    id: result.id,
    name: result.name,
    type: result.type,
    subType: result.subType,
    hasError: monitor
      ? finalMonitor.status !== Constants.EVENT_SUCCESS_STATUS
      : false,
    isInProgress: statistics['discovery.progress'] < 100,
    isCompleted:
      statistics['discovery.progress'] >= 100 && finalMonitor
        ? finalMonitor.status === Constants.EVENT_SUCCESS_STATUS
        : false,
    progress: monitor ? monitor.progress : statistics['discovery.progress'],
    monitor,
  }
}

export function transformNetworkDiscoveryResultData(result, data) {
  if (
    [
      Constants.SERVER,
      Constants.NETWORK,
      Constants.WIRELESS,
      Constants.OTHER,
      Constants.SDN,
      Constants.STORAGE,
    ].indexOf(result.type) >= 0
  ) {
    return transformServerDiscoveryResult(result, data)
  } else if (result.type === Constants.CLOUD) {
    return transformCloudDiscoveryResult(result, data)
  } else if (result.type === Constants.VIRTUALIZATION) {
    return transformVirtualDiscoveryResult(result, data)
  } else if (result.type === Constants.SERVICE_CHECK) {
    return transformServiceCheckDiscoveryResult(result, data)
  } else if (result.type === Constants.HYPERCONVERGED_INFRASTRUCTURE) {
    return transformVirtualDiscoveryResult(result, data)
  } else if (result.type === Constants.CONTAINER_ORCHESTRATION) {
    return transformVirtualDiscoveryResult(result, data)
  }
}

function transformNetworkInterface(instance, defaultSelectedWithUp = true) {
  return {
    id: generateId(),
    context: Object.freeze({ ...instance }),
    name: instance['interface.name'] || instance['object.name'],
    alias: instance['interface.alias'],
    description: instance['interface.description'],
    monitorType: instance['object.type'],
    ip: instance['object.ip'] || instance['interface.ip.address'],
    status: instance.status.toLowerCase(),
    selected: defaultSelectedWithUp
      ? instance.status.toLowerCase() === 'up'
      : false,
  }
}

function transformWireless(wireless, defaultSelectedWithUp = true) {
  return {
    id: generateId(),
    context: Object.freeze({ ...wireless }),
    name: wireless['wireless.access.point'],
    mac: wireless['wireless.access.point.mac.address'],
    ip: wireless['object.ip'] || wireless['wireless.access.point.ip.address'],
    status: wireless.status.toLowerCase(),
    selected: defaultSelectedWithUp
      ? wireless.status.toLowerCase() === 'up'
      : false,
  }
}

function transfromServerMonitorResult(
  monitor,
  defaultSelectedWithUp = true,
  type
) {
  const instances = (monitor.objects || []).map((i) =>
    type === Constants.WIRELESS
      ? transformWireless(i, defaultSelectedWithUp)
      : transformNetworkInterface(i, defaultSelectedWithUp)
  )
  return {
    id: monitor[Constants.ID_PROPERTY],
    name: monitor['object.name'],
    ip: monitor['object.ip'],
    oid: monitor['object.system.oid'],
    status: Lowercase(monitor['object.state']),
    host: monitor['object.host'],
    monitorType: monitor['object.type'],
    credentialProfile: monitor['object.credential.profile'],
    rpe: monitor['object.event.processors'],
    instancesCount: instances.length || 0,
    selectedInstancesCount: instances.filter((i) => i.selected).length,
    instances: Reverse(SortBy(instances, ['status'])),
    context: Object.freeze({ ...monitor }),
    nutanixCluster: monitor['prism.cluster'],
    ...(type === Constants.STORAGE ? { vendor: monitor['object.vendor'] } : {}),

    // message: monitor.message,
  }
}

function transformCloudMonitorResult(monitor) {
  return {
    id: monitor[Constants.ID_PROPERTY],
    name: monitor['object.name'],
    status: Lowercase(monitor['object.state']),
    monitorType: monitor['object.type'],
    region: monitor['object.region'] || monitor['object.resource.group'],
    target: monitor['object.target'],
    credentialProfile: monitor['object.credential.profile'],
    rpe: monitor['object.event.processors'],
    context: Object.freeze({ ...monitor }),
  }
}

function transformServerDiscoveryResult(result, monitors) {
  return {
    id: result.id,
    name: result.name,
    type: result.type,
    subType: result.objectType,
    discoveryFailedObjects: result.monitorsError,
    discoveredObjects: result.discoveredObjects,
    monitors: monitors.map((i) =>
      transfromServerMonitorResult(i, true, result.type)
    ),
  }
}

function transformCloudDiscoveryResult(result, monitors) {
  return {
    id: result.id,
    name: result.name,
    type: result.type,
    subType: result.objectType,
    discoveryFailedObjects: result.monitorsError,
    discoveredObjects: result.discoveredObjects,
    monitors: monitors.map(transformCloudMonitorResult),
  }
}

function transformVirtualDiscoveryResult(result, monitors) {
  const isVCenterDiscovery =
    [
      Constants.VCENTER,
      Constants.HYPER_V_CLUSTER,
      Constants.CITRIX_XEN_CLUSTER,
      Constants.VMWARE_TANZU_KUBERNETES,
      Constants.KUBERNETES,
    ].indexOf(result.objectType) >= 0
  return {
    id: result.id,
    name: result.name,
    type: result.type,
    subType: result.objectType,
    isVCenterDiscovery,
    discoveryFailedObjects: result.monitorsError,
    discoveredObjects: result.discoveredObjects,
    monitors: monitors.map((i) =>
      transfromServerMonitorResult(i, true, result.type)
    ),
  }
}

function transformServiceCheckDiscoveryResult(result, monitors) {
  return {
    id: result.id,
    name: result.name,
    type: result.type,
    isAgentBased: result.sourceAgent,
    discoveryFailedObjects: result.monitorsError,
    discoveredObjects: result.discoveredObjects,
    monitors: monitors.map((n) => {
      const serviceCheckName =
        n['object.type'] === Constants.PORT
          ? `${n['object.name']}:${n['object.context']['port']}`
          : n['object.name']
      return {
        id: n[Constants.ID_PROPERTY],
        status: Lowercase(n['object.state']),
        type: n['object.type'],
        agent: n['object.agent'],
        target: n['object.target'],
        rpe: n['object.event.processors'],
        host: n['object.host'],
        name: serviceCheckName,
        context: Object.freeze({
          ...n,
          'object.name': serviceCheckName,
        }),
      }
    }),
  }
}

export function transformSelectedProvisionMonitors(monitors, discoveryType) {
  return monitors.map((monitor) => ({
    ...monitor.context,
    ...((discoveryType === Constants.NETWORK ||
      discoveryType === Constants.SDN) &&
    (monitor.instances || []).length > 0
      ? {
          'object.context': {
            ...((monitor.context || {})['object.context'] || {}),
            'discovered.objects': monitor.instances.map(
              transformNetworkInterfaceForServer
            ),
          },
        }
      : {}),
    ...(monitor.host ? { 'object.host': monitor.host } : {}),
    ...(monitor.monitorType ? { 'object.type': monitor.monitorType } : {}),
    ...(monitor.name ? { 'object.name': monitor.name } : {}),
    ...((monitor.instances || []).length
      ? {
          objects: monitor.instances
            .filter((instance) => instance.selected)
            .map(transformNetworkInterfaceForServer),
        }
      : {}),
  }))
}

function transformNetworkInterfaceForServer(instance) {
  return {
    ...instance.context,
  }
}
