/**
 * transform credential profile list received from server
 */
import Constants from '@constants'
import { generateId } from '@utils/id'

export const CUSTOM_FILTER = {
  NCM_SSH: 'NCM_SSH',
}

export function transformCredentialProfileForList(profile) {
  const protocol = profile['credential.profile.protocol']
  return {
    id: profile[Constants.ID_PROPERTY],
    credentialProfileName: profile['credential.profile.name'],
    credentialProfileProtocol: protocol,
    canEdit: profile._type !== '0',
    monitorCount: profile['count'] || 0,
    ...(protocol.indexOf('SNMP') >= 0
      ? {
          community: profile['credential.profile.context']['snmp.community'],
          // writeCommunity:
          //   profile['credential.profile.context']['snmp.write.community'],
          version:
            profile['credential.profile.context']['snmp.version'] || 'v3',
          securityUserName:
            profile['credential.profile.context']['snmp.security.user.name'],
          securityLevel:
            profile['credential.profile.context']['snmp.security.level'],
          authenticationProtocol:
            profile['credential.profile.context'][
              'snmp.authentication.protocol'
            ],
          privacyProtocol:
            profile['credential.profile.context']['snmp.privacy.protocol'],
        }
      : protocol.indexOf('Cloud') >= 0
      ? {
          subType: profile['credential.profile.context']['cloud.type'],
          tenantId: profile['credential.profile.context']['cloud.tenant.id'],
        }
      : {
          username: profile['credential.profile.context']['username'],
          sshKey: profile['credential.profile.context']['ssh.key'],
          passPhrase: profile['credential.profile.context']['passphrase'],
          authenticationapikey: profile['credential.profile.context']['key'],
          authenticationProvider:
            profile['credential.profile.context']['authentication.provider'],
          grantType: profile['credential.profile.context']['grant.type'],
          clientID: profile['credential.profile.context']['client.id'],
          clientSecret: profile['credential.profile.context']['client.secret'],
          authenticationURL:
            profile['credential.profile.context']['authentication.url'],
          tokenURL: profile['credential.profile.context']['token.url'],
          redirectURL: profile['credential.profile.context']['redirect.url'],
          scopes: profile['credential.profile.context']?.['scopes']?.length
            ? profile['credential.profile.context']['scopes'].map((s) => ({
                key: generateId(),
                scope: s,
              }))
            : [{ key: generateId() }],
          timeout: profile['credential.profile.context']['timeout'],
          authenticationType:
            profile['credential.profile.context']['authentication.type'],

          ...([Constants.SSH].includes(protocol)
            ? {
                isCliEnable:
                  profile['credential.profile.context']['cli.enabled'] ===
                  'yes',

                ...(profile['credential.profile.context']['cli.enabled'] ===
                'yes'
                  ? {
                      prompt: profile['credential.profile.context']['prompt'],
                      enablePrompt:
                        profile['credential.profile.context']['enable.prompt'],
                      enableUsername:
                        profile['credential.profile.context'][
                          'enable.username'
                        ],
                      backupTransferProtocols:
                        profile['credential.profile.context'][
                          'file.transfer.protocol'
                        ],
                      enablePassword:
                        profile['credential.profile.context'][
                          'enable.password'
                        ],
                      configCommand:
                        profile['credential.profile.context']['config.command'],
                      configPassword:
                        profile['credential.profile.context'][
                          'config.password'
                        ],
                      vrfName:
                        profile['credential.profile.context']['vrf.name'],
                      ...(profile['credential.profile.context'][
                        'file.transfer.protocol'
                      ] === 'SCP/SFTP'
                        ? {
                            scpSftpUsername:
                              profile['credential.profile.context'][
                                'server.username'
                              ],
                            scpSftpPassword:
                              profile['credential.profile.context'][
                                'server.password'
                              ],
                          }
                        : {}),
                    }
                  : {}),
              }
            : {}),

          ...(protocol.indexOf('HTTP') >= 0
            ? {
                clientCertificateConfig:
                  profile['credential.profile.context'][
                    'client.certificate.configuration'
                  ],
                clientCertificate:
                  profile['credential.profile.context']['client.certificate'],
                clientKey: profile['credential.profile.context']['client.key'],
                certificateAuthority:
                  profile['credential.profile.context'][
                    'certificate.authority'
                  ],

                clientCertificateFile: profile['credential.profile.context'][
                  'client.certificate.file'
                ]
                  ? [
                      profile['credential.profile.context'][
                        'client.certificate.file'
                      ],
                    ].filter(Boolean)
                  : [],
                clientKeyFile: profile['credential.profile.context'][
                  'client.key.file'
                ]
                  ? [
                      profile['credential.profile.context']['client.key.file'],
                    ].filter(Boolean)
                  : [],
                certificateAuthorityFile: profile['credential.profile.context'][
                  'certificate.authority.file'
                ]
                  ? [
                      profile['credential.profile.context'][
                        'certificate.authority.file'
                      ],
                    ].filter(Boolean)
                  : [],
              }
            : {}),
        }),
  }
}
export function transformCredentialProfileForClient(profile) {
  const protocol = profile['credential.profile.protocol']
  return {
    id: profile[Constants.ID_PROPERTY],
    credentialProfileName: profile['credential.profile.name'],
    credentialProfileProtocol: protocol,
    canEdit: profile._type !== '0',
    ...(protocol.indexOf('SNMP') >= 0
      ? {
          community: profile['credential.profile.context']['snmp.community'],
          writeCommunity:
            profile['credential.profile.context']['snmp.write.community'],
          version:
            profile['credential.profile.context']['snmp.version'] || 'v3',
          securityUserName:
            profile['credential.profile.context']['snmp.security.user.name'],
          securityLevel:
            profile['credential.profile.context']['snmp.security.level'],
          authenticationProtocol:
            profile['credential.profile.context'][
              'snmp.authentication.protocol'
            ],
          privacyProtocol:
            profile['credential.profile.context']['snmp.privacy.protocol'],
        }
      : protocol.indexOf('Cloud') >= 0
      ? {
          secretKey: profile['credential.profile.context']['cloud.secret.key'],
          subType: profile['credential.profile.context']['cloud.type'],
          accessKey: profile['credential.profile.context']['cloud.access.id'],
          clientId: profile['credential.profile.context']['cloud.client.id'],
          tenantId: profile['credential.profile.context']['cloud.tenant.id'],
        }
      : {
          username: profile['credential.profile.context']['username'],
          sshKey: profile['credential.profile.context']['ssh.key'],
          passPhrase: profile['credential.profile.context']['passphrase'],
          authenticationType:
            profile['credential.profile.context']['authentication.type'],

          ...([Constants.SSH].includes(protocol)
            ? {
                isCliEnable:
                  profile['credential.profile.context']['cli.enabled'] ===
                  'yes',

                ...(profile['credential.profile.context']['cli.enabled'] ===
                'yes'
                  ? {
                      prompt: profile['credential.profile.context']['prompt'],
                      enablePrompt:
                        profile['credential.profile.context']['enable.prompt'],
                      enableUsername:
                        profile['credential.profile.context'][
                          'enable.username'
                        ],
                      backupTransferProtocols:
                        profile['credential.profile.context'][
                          'file.transfer.protocol'
                        ],
                      enablePassword:
                        profile['credential.profile.context'][
                          'enable.password'
                        ],
                      configCommand:
                        profile['credential.profile.context']['config.command'],
                      configPassword:
                        profile['credential.profile.context'][
                          'config.password'
                        ],
                      vrfName:
                        profile['credential.profile.context']['vrf.name'],
                      ...(profile['credential.profile.context'][
                        'file.transfer.protocol'
                      ] === 'SCP/SFTP'
                        ? {
                            scpSftpUsername:
                              profile['credential.profile.context'][
                                'server.username'
                              ],
                            scpSftpPassword:
                              profile['credential.profile.context'][
                                'server.password'
                              ],
                          }
                        : {}),
                    }
                  : {}),
              }
            : {}),
          authenticationapikey: profile['credential.profile.context']['key'],
          authenticationProvider:
            profile['credential.profile.context']['authentication.provider'],
          grantType: profile['credential.profile.context']['grant.type'],
          clientID: profile['credential.profile.context']['client.id'],
          clientSecret: profile['credential.profile.context']['client.secret'],
          authenticationURL:
            profile['credential.profile.context']['authentication.url'],
          tokenURL: profile['credential.profile.context']['token.url'],
          redirectURL: profile['credential.profile.context']['redirect.url'],
          scopes: profile['credential.profile.context']?.['scopes']?.length
            ? profile['credential.profile.context']['scopes'].map((s) => ({
                key: generateId(),
                scope: s,
              }))
            : [{ key: generateId() }],
          timeout: profile['credential.profile.context']['timeout'],
          ...(protocol.indexOf('HTTP') >= 0
            ? {
                clientCertificateConfig:
                  profile['credential.profile.context'][
                    'client.certificate.configuration'
                  ],
                clientCertificate:
                  profile['credential.profile.context']['client.certificate'],
                clientKey: profile['credential.profile.context']['client.key'],
                certificateAuthority:
                  profile['credential.profile.context'][
                    'certificate.authority'
                  ],
                clientCertificateFile: profile['credential.profile.context'][
                  'client.certificate.file'
                ]
                  ? [
                      profile['credential.profile.context'][
                        'client.certificate.file'
                      ],
                    ].filter(Boolean)
                  : [],
                clientKeyFile: profile['credential.profile.context'][
                  'client.key.file'
                ]
                  ? [
                      profile['credential.profile.context']['client.key.file'],
                    ].filter(Boolean)
                  : [],
                certificateAuthorityFile: profile['credential.profile.context'][
                  'certificate.authority.file'
                ]
                  ? [
                      profile['credential.profile.context'][
                        'certificate.authority.file'
                      ],
                    ].filter(Boolean)
                  : [],
              }
            : {}),
        }),
  }
}
export function transformAssignCredentialProfile(profile, protocol) {
  return {
    params: profile,
    'credential.profile.protocol': protocol,
  }
}

export function transformCredentialProfileForServer(profile, sessionId) {
  const protocol = profile.credentialProfileProtocol

  // Extract repeated conditions for better readability
  const authType = profile.authenticationType

  return {
    'credential.profile.name': profile.credentialProfileName,
    'credential.profile.protocol': protocol,
    'credential.profile.context': {
      ...(protocol.indexOf('SNMP') >= 0
        ? {
            'snmp.community':
              (profile.community || '').length > 0
                ? profile.community
                : undefined,
            'snmp.write.community':
              (profile.writeCommunity || '').length > 0
                ? profile.writeCommunity
                : undefined,
            'snmp.version': profile.version || 'v3',
            'snmp.security.user.name': profile.securityUserName,
            'snmp.security.level': profile.securityLevel,
            'snmp.authentication.protocol': profile.authenticationProtocol,
            'snmp.privacy.protocol': profile.privacyProtocol,
            ...((profile.authenticationPassword || '').length > 0
              ? {
                  'snmp.authentication.password':
                    profile.authenticationPassword,
                }
              : {}),
            ...((profile.privatePassword || '').length > 0
              ? { 'snmp.private.password': profile.privatePassword }
              : {}),
          }
        : protocol.indexOf('Cloud') >= 0
        ? {
            'cloud.secret.key': profile.secretKey,
            'cloud.type': profile.subType,
            'cloud.access.id': profile.accessKey,
            'cloud.client.id': profile.clientId,
            'cloud.tenant.id': profile.tenantId,
          }
        : {
            ...(profile.authenticationapikey
              ? {
                  key: profile.authenticationapikey,
                }
              : {}),
            ...(authType === 'oauth'
              ? {
                  'session-id': sessionId,
                  'client.id': profile.clientID,
                  'client.secret': profile.clientSecret,
                  'grant.type': profile.grantType,

                  ...(profile.grantType === 'Authorization Code'
                    ? {
                        'authentication.url': profile.authenticationURL,
                        'redirect.url': profile.redirectURL,
                        'token.url': profile.tokenURL,
                        scopes: profile.scopes.map((s) => s.scope),
                        'authentication.provider':
                          profile.authenticationProvider,
                        timeout: parseInt(profile.timeout),
                      }
                    : {}),
                  // ...(profile.grantType === 'Password' ? {} : {}),
                  ...(profile.uniqueId
                    ? { 'unique.id': profile.uniqueId }
                    : {}),
                }
              : {}),

            ...(profile.authenticationType !== 'no_auth'
              ? { username: profile.username }
              : {}),
            'ssh.key': profile.sshKey,
            passphrase: profile.passPhrase,
            'authentication.type': profile.authenticationType,
            ...((profile.password || '').length > 0 &&
            profile.authenticationType !== 'no_auth'
              ? { password: profile.password }
              : {}),

            ...([Constants.SSH].includes(protocol)
              ? {
                  'cli.enabled': profile.isCliEnable ? 'yes' : 'no',

                  ...(profile.isCliEnable
                    ? {
                        prompt: profile.prompt,
                        'file.transfer.protocol':
                          profile.backupTransferProtocols,
                        'enable.prompt': profile.enablePrompt,
                        'enable.username': profile.enableUsername,
                        'enable.password': profile.enablePassword,
                        'config.command': profile.configCommand,
                        'config.password': profile.configPassword,
                        'vrf.name': profile.vrfName,
                        ...(profile.backupTransferProtocols === 'SCP/SFTP'
                          ? {
                              'server.username': profile.scpSftpUsername,
                              'server.password': profile.scpSftpPassword,
                            }
                          : {}),
                      }
                    : {}),
                }
              : {}),

            ...(protocol.indexOf('HTTP') >= 0
              ? {
                  ...(authType === 'certificate'
                    ? {
                        'client.certificate.configuration':
                          profile.clientCertificateConfig,

                        ...(profile.clientCertificateConfig === 'manual'
                          ? {
                              'client.certificate': profile.clientCertificate,
                              'client.key': profile.clientKey,
                              'certificate.authority':
                                profile.certificateAuthority,
                            }
                          : {}),
                        ...(profile.clientCertificateConfig === 'upload'
                          ? {
                              'client.certificate.file':
                                profile.clientCertificateFile[0],
                              'client.key.file': profile.clientKeyFile[0],
                              'certificate.authority.file':
                                profile.certificateAuthorityFile[0],
                            }
                          : {}),
                      }
                    : {}),
                }
              : {}),
          }),
    },
  }
}
export function getProtocolDropDownItems() {
  return [
    Constants.POWERSHELL,
    Constants.SNMP_V12,
    Constants.SNMP_V3,
    Constants.SSH,
    Constants.JDBC,
    Constants.HTTP_HTTPS,
    // Constants.HTTP,
    // Constants.HTTPS,
    Constants.CLOUD,
    Constants.JMX,
    Constants.JMS,
    // Constants.NCM_TELNET,
  ].map((k) => ({
    key: k,
    text: k,
    id: k.replace(/\s/g, '').replace(/\//g, ''),
  }))
}
export function getVersionDropDownItems() {
  const items = []
  items.push({
    key: 'v1',
    name: 'V1',
  })
  items.push({
    key: 'v2c',
    name: 'V2c',
  })
  return items
}
export function getSecurityLevelDropDownItems() {
  const items = []
  items.push({
    key: 'No Authentication No Privacy',
    name: 'No Authentication No Privacy',
    id: 'no-authentication-no-privacy',
  })
  items.push({
    key: 'Authentication No Privacy',
    name: 'Authentication No Privacy',
    id: 'authentication-no-privacy',
  })
  items.push({
    key: 'Authentication Privacy',
    name: 'Authentication Privacy',
    id: 'authentication-privacy',
  })
  return items
}
export function getAuthenticationProtocolDropDownItems() {
  const items = []
  items.push({
    key: 'MD5',
    name: 'MD5',
  })
  items.push({
    key: 'SHA',
    name: 'SHA',
  })
  items.push({
    key: 'SHA224',
    name: 'SHA224',
  })
  items.push({
    key: 'SHA256',
    name: 'SHA256',
  })
  items.push({
    key: 'SHA384',
    name: 'SHA384',
  })
  items.push({
    key: 'SHA512',
    name: 'SHA512',
  })
  return items
}
export function getPrivacyProtocolDropDownItems() {
  const items = []
  items.push({
    key: 'DES',
    name: 'DES',
  })
  // items.push({
  //   key: '3DES',
  //   name: '3DES',
  // })
  items.push({
    key: 'AES',
    name: 'AES',
  })
  // items.push({
  //   key: 'AES128',
  //   name: 'AES128',
  // })
  items.push({
    key: 'AES192C',
    name: 'AES192C',
  })
  items.push({
    key: 'AES192',
    name: 'AES192',
  })
  items.push({
    key: 'AES256C',
    name: 'AES256C',
  })
  items.push({
    key: 'AES256',
    name: 'AES256',
  })
  return items
}
export function transformCredentialProfileForDropdown(credential) {
  const protocol = credential['credential.profile.protocol']

  return {
    key: credential[Constants.ID_PROPERTY],
    name: credential['credential.profile.name'],
    protocol,
    authenticationType:
      credential['credential.profile.context']['authentication.type'],
    type: credential['credential.profile.context']['cloud.type'],
    ...(protocol === Constants.SSH
      ? {
          isCliEnable:
            credential['credential.profile.context']['cli.enabled'] === 'yes',
        }
      : {}),
    authenticationProvider:
      credential['credential.profile.context']?.['authentication.provider'],
    grantType: credential['credential.profile.context']?.['grant.type'],
  }
}

export function customCredentialFilter(protocols, customFilterType) {
  if (customFilterType === CUSTOM_FILTER.NCM_SSH) {
    return protocols.filter((profile) => {
      if (profile.protocol === Constants.SSH) {
        return profile.isCliEnable
      }

      return true
    })
  }
}
