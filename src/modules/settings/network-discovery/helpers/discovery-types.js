import Constants from '@constants'

export const A<PERSON><PERSON><PERSON><PERSON>_DISCOVERY_TYPE = [
  { text: Constants.SERVER, key: Constants.SERVER },
  { text: Constants.CLOUD, key: Constants.CLOUD },
  { text: Constants.NETWORK, key: Constants.NETWORK },
  { text: Constants.SDN, key: Constants.SDN },
  { text: Constants.VIRTUALIZATION, key: Constants.VIRTUALIZATION },
  {
    text: Constants.HYPERCONVERGED_INFRASTRUCTURE,
    key: Constants.HYPERCONVERGED_INFRASTRUCTURE,
  },
  { text: Constants.STORAGE, key: Constants.STORAGE },
  { text: Constants.SERVICE_CHECK, key: Constants.SERVICE_CHECK },
  { text: Constants.WIRELESS, key: Constants.WIRELESS },
  {
    text: Constants.CONTAINER_ORCHESTRATION,
    key: Constants.CONTAINER_ORCHESTRATION,
  },
  { text: Constants.<PERSON>THER, key: Constants.<PERSON><PERSON>ER },
]
