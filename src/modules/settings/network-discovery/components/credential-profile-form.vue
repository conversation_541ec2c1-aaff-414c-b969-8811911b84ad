<template>
  <div class="mt-4">
    <MRow>
      <!-- Basic Information -->
      <MCol :size="6">
        <FlotoFormItem
          id="credential-profile-name-id"
          v-model="value.credentialProfileName"
          placeholder="Must be unique"
          rules="required"
          label="Credential Profile Name"
          :disabled="isView"
          auto-focus
          name="credential-profile-name"
        />
      </MCol>
      <MCol :size="6">
        <FlotoFormItem rules="required" label="Protocol">
          <FlotoDropdownPicker
            id="protocol"
            v-model="value.credentialProfileProtocol"
            :options="protocolOptions"
            :disabled="isView || Boolean(value.id)"
          />
        </FlotoFormItem>
      </MCol>

      <!-- HTTP/HTTPS Authentication -->
      <template v-if="isHttpHttpsProtocol">
        <MCol :size="6">
          <FlotoFormItem label="Authentication Type" rules="required">
            <FlotoDropdownPicker
              id="authentication-type"
              v-model="value.authenticationType"
              :disabled="isView || value.disabledAuthenticationType"
              :options="httpAuthTypeOptions"
            />
          </FlotoFormItem>
        </MCol>
        <MCol v-if="value.authenticationType !== 'oauth'" :size="6"></MCol>
      </template>

      <!-- Standard Authentication Fields -->
      <template v-if="showStandardAuthFields">
        <MCol v-if="value.authenticationType === 'certificate'">
          <FlotoFormItem
            label="Client Certificate Configuration"
            :disabled="isView"
          >
            <MRadioGroup
              v-model="value.clientCertificateConfig"
              :options="clientCertificateConfigOptions"
              as-button
              :disabled="isView"
            />
          </FlotoFormItem>
        </MCol>

        <MCol v-if="showUsernamePasswordField" :size="6">
          <FlotoFormItem
            id="username-id"
            v-model="value.username"
            name="username"
            :rules="usernameRules"
            label="User Name"
            :disabled="isView"
          />
        </MCol>

        <MCol v-if="showUsernamePasswordField && !isView" :size="6">
          <PasswordInput
            id="password-id"
            v-model="value.password"
            validation-label="Password"
            class="mb-0"
            name="password"
            label="Password"
            :rules="passwordRules"
          >
            <template v-slot:prefix>
              <MIcon name="unlock-alt" />
            </template>
          </PasswordInput>
        </MCol>

        <MCol v-if="value.authenticationType === 'apikey'" :size="6">
          <PasswordInput
            id="api-key-id"
            v-model="value.authenticationapikey"
            placeholder="Enter your API Key"
            rules="required"
            label="API Key"
            name="api-key"
          />
        </MCol>

        <!-- OAuth Configuration -->
        <template v-if="isHttpHttpsProtocol">
          <MCol v-if="value.authenticationType === 'oauth'" :size="6">
            <FlotoFormItem label="Grant Type" rules="required">
              <FlotoDropdownPicker
                v-model="value.grantType"
                :options="grantTypeOptions"
                :searchable="false"
                placeholder="Select"
                :disabled="isView || value.disableGrantType"
              />
            </FlotoFormItem>
          </MCol>

          <MCol v-if="showOAuthPasswordFields" :size="6">
            <FlotoFormItem
              id="username-id"
              v-model="value.username"
              name="username"
              :rules="{ required: true }"
              label="User Name"
              :disabled="isView"
            />
          </MCol>

          <MCol v-if="showOAuthPasswordFields && !isView" :size="6">
            <PasswordInput
              id="password-id"
              v-model="value.password"
              validation-label="Password"
              class="mb-0"
              name="password"
              label="Password"
              :rules="{ required: true }"
            >
              <template v-slot:prefix>
                <MIcon name="unlock-alt" />
              </template>
            </PasswordInput>
          </MCol>

          <MCol v-if="showOAuthAuthorizationCodeFields" :size="6">
            <FlotoFormItem label="Authentication Provider" rules="required">
              <FlotoDropdownPicker
                v-model="value.authenticationProvider"
                :options="authProviderOptions"
                :searchable="false"
                placeholder="Select"
                :disabled="isView || value.disabledAuthenticationProvider"
              />
            </FlotoFormItem>
          </MCol>

          <MCol
            v-if="
              value.authenticationType === 'oauth' &&
              value.grantType !== 'Password'
            "
            :size="6"
          ></MCol>

          <MCol v-if="value.authenticationType === 'oauth'" :size="6">
            <FlotoFormItem
              v-model="value.clientID"
              rules="required"
              label="Client ID"
              :disabled="isView"
            />
          </MCol>

          <MCol v-if="value.authenticationType === 'oauth'" :size="6">
            <FlotoFormItem
              v-model="value.clientSecret"
              rules="required"
              label="Client Secret"
              :disabled="isView"
            />
          </MCol>

          <MCol v-if="showOAuthAuthorizationCodeFields" :size="6">
            <FlotoFormItem
              v-model="value.authenticationURL"
              rules="required|url"
              label="Authentication URL"
              :disabled="isView"
            />
          </MCol>

          <MCol v-if="showOAuthAuthorizationCodeFields" :size="6">
            <FlotoFormItem
              v-model="value.tokenURL"
              rules="required|url"
              label="Token URL"
              :disabled="isView"
            />
          </MCol>

          <MCol v-if="showOAuthAuthorizationCodeFields" :size="6">
            <span class="text-neutral-light">Redirect URL</span>
            <MRow :gutter="0" class="code-container w-full" :size="4">
              <MCol class="" :size="10">
                <div class="px-2 py-2 whitespace-nowrap text-ellipsis">
                  {{ value.redirectURL }}
                </div>
              </MCol>

              <!-- <FlotoFormItem v-model="value.redirectURL" disabled label=" "> -->
              <MCol
                :size="2"
                class="grow-0 bg-neutral-lightest cursor-pointer p-1 flex justify-center items-center"
                @click.stop="handleCopyRedirectURL"
              >
                <MIcon name="copy" size="lg" />
              </MCol>
              <!-- </FlotoFormItem> -->
            </MRow>
          </MCol>

          <MCol v-if="showOAuthAuthorizationCodeFields" :size="6">
            <FlotoFormItem
              v-model="value.timeout"
              rules="required|numeric"
              label="Timeout (sec)"
              :disabled="isView"
            />
          </MCol>

          <MCol v-if="showOAuthAuthorizationCodeFields" :size="12">
            <MultipleFormItems
              id="add-headers-btn"
              v-model="value.scopes"
              :show-icon="false"
              :max-items="10"
            >
              <template
                v-slot="{ item, remove, add, isLastItem, total, canAdd, index }"
              >
                <MRow class="items-center">
                  <MCol :size="10">
                    <FlotoFormItem
                      v-model="item.scope"
                      rules="required"
                      :label="index === 0 ? 'Scope' : undefined"
                      :disabled="isView"
                    />
                  </MCol>

                  <MCol v-if="!isView" :size="2" class="mt-2">
                    <span v-if="total > 1" id="remove-counter" @click="remove">
                      <MIcon
                        name="times-circle"
                        class="cursor-pointer text-secondary-red"
                        size="lg"
                      />
                    </span>
                    <a v-if="isLastItem && canAdd" class="ml-2" @click="add">
                      <MIcon
                        name="plus-circle"
                        class="text-primary"
                        size="lg"
                      />
                    </a>
                  </MCol>
                </MRow>
              </template>
            </MultipleFormItems>
          </MCol>

          <!-- Client Certificate Configuration -->
          <MCol v-if="value.authenticationType === 'certificate'" :size="12">
            <FlotoFormItem
              v-for="field in clientCertificateFields"
              :key="`${value.clientCertificateConfig}-${field.key}`"
              :label="field.label"
              :info-tooltip="
                $message(
                  value.clientCertificateConfig === 'upload'
                    ? field.uploadMessageKey
                    : field.messageKey
                )
              "
              class="full-border-text-area"
              rules="required"
              :disabled="isView"
              :vid="`${value.clientCertificateConfig}-${field.key}-upload`"
            >
              <MInput
                v-if="value.clientCertificateConfig === 'manual'"
                v-model="value[field.modelKey]"
                type="textarea"
                as-input
                :rows="4"
                class="mt-1"
                :disabled="isView"
              />
              <div
                v-if="value.clientCertificateConfig === 'upload'"
                class="mt-1"
              >
                <FileDropper
                  v-if="!isView"
                  :id="`${field.key}-upload`"
                  v-model="value[field.uploadKey]"
                  button-text="Browse File"
                  as-link
                  :multiple="false"
                  :max-files="1"
                  :max-allowed-file-size="100"
                  :allowed-file-types="field.allowedFileTypes"
                  @remove="fileRemove(field.uploadKey)"
                >
                  <span class="text-center drop-zone-content">
                    <MIcon name="paper-clip" />
                    Drag & Drop file operation
                    <a class="browse-file">Browse File</a>
                  </span>
                </FileDropper>

                <div v-else class="disabled-file-list">
                  <div
                    :key="
                      value[field.uploadKey][0].uid ||
                      value[field.uploadKey][0].result
                    "
                    class="disabled-file-item"
                  >
                    <span class="file-name">{{
                      value[field.uploadKey][0].name
                    }}</span>
                  </div>
                </div>
              </div>
            </FlotoFormItem>
          </MCol>
        </template>
      </template>

      <!-- SSH Configuration -->
      <template v-if="isSshProtocol">
        <MCol :size="12" />
        <MCol :size="6">
          <FlotoFormItem
            id="ssh-id"
            v-model="value.sshKey"
            type="textarea"
            :rows="4"
            name="ssh-key"
            label="SSH Key"
            :disabled="isView"
          />
        </MCol>
        <MCol :size="6">
          <FlotoFormItem
            v-model="value.passPhrase"
            name="passphrase"
            label="Passphrase"
            :disabled="isView"
          />
        </MCol>
        <MCol :size="12" class="my-4">
          <MCheckbox
            v-model="value.isCliEnable"
            :disabled="isView || value.disabledCliEnable"
          >
            Cli options
          </MCheckbox>
        </MCol>

        <template v-if="value.isCliEnable">
          <MCol :size="6">
            <FlotoFormItem rules="required" label="Config Transfer Protocol">
              <FlotoDropdownPicker
                id="protocol"
                v-model="value.backupTransferProtocols"
                :options="transferProtocols"
                :disabled="isView"
              />
            </FlotoFormItem>
          </MCol>
          <MCol :size="6"></MCol>

          <MCol :size="6">
            <FlotoFormItem
              id="username-id"
              v-model="value.enableUsername"
              name="username"
              label="Enable User Name"
              :disabled="isView"
            />
          </MCol>
          <MCol v-if="!isView" :size="6">
            <PasswordInput
              id="password-id"
              v-model="value.enablePassword"
              validation-label="Password"
              class="mb-0"
              name="password"
              label="Enable Password"
            >
              <template v-slot:prefix>
                <MIcon name="unlock-alt" />
              </template>
            </PasswordInput>
          </MCol>
          <MCol :size="6">
            <FlotoFormItem
              v-model="value.enablePrompt"
              label="Enable Command"
              :disabled="isView"
            />
          </MCol>
          <MCol :size="6">
            <FlotoFormItem
              v-model="value.prompt"
              label="Enable Prompt"
              :disabled="isView"
              rules="required"
            />
          </MCol>
          <MCol :size="6">
            <FlotoFormItem
              v-model="value.configCommand"
              label="Config Mode Command"
              :disabled="isView"
              placeholder="Write here"
            />
          </MCol>
          <MCol v-if="!isView" :size="6">
            <FlotoFormItem
              v-model="value.configPassword"
              label="Config Password"
              :disabled="isView"
              placeholder="Write here"
            />
          </MCol>
          <MCol :size="6">
            <FlotoFormItem
              v-model="value.vrfName"
              label="VRF Name"
              :disabled="isView"
              placeholder="Write here"
            />
          </MCol>

          <MCol v-if="value.backupTransferProtocols === 'SCP/SFTP'" :size="6">
            <FlotoFormItem
              id="scp-sftp-username-id"
              v-model="value.scpSftpUsername"
              name="scp-sftp-username"
              label="User Name"
              info-tooltip="Username for SCP/SFTP"
              :disabled="isView"
            />
          </MCol>

          <MCol
            v-if="!isView && value.backupTransferProtocols === 'SCP/SFTP'"
            :size="6"
          >
            <PasswordInput
              id="scp-sftp-password-id"
              v-model="value.scpSftpPassword"
              validation-label="Password"
              class="mb-0"
              name="scp-sftp-password"
              label="Password"
              info-tooltip="Password for SCP/SFTP"
            >
              <template v-slot:prefix>
                <MIcon name="unlock-alt" />
              </template>
            </PasswordInput>
          </MCol>
        </template>
      </template>

      <!-- SNMP V1/V2c Configuration -->
      <template v-if="isSnmpV12Protocol">
        <MCol :size="6">
          <FlotoFormItem
            id="credential-version-id"
            rules="required"
            vid="version"
            label="Version"
          >
            <FlotoDropdownPicker
              id="version-id"
              v-model="value.version"
              :options="versionOptions"
              :disabled="isView"
            />
          </FlotoFormItem>
        </MCol>
        <MCol v-if="!isView" :size="6">
          <PasswordInput
            id="community-id"
            v-model="value.community"
            validation-label="Community"
            class="mb-0"
            name="community"
            label="Community"
            :rules="value.id ? {} : { required: true }"
          >
            <template v-slot:prefix>
              <MIcon name="unlock-alt" />
            </template>
          </PasswordInput>
        </MCol>
        <MCol v-if="!isView" :size="6">
          <PasswordInput
            id="write-community-id"
            v-model="value.writeCommunity"
            validation-label="Write Community"
            class="mb-0"
            name="writeCommunity"
            label="Write Community"
            vid="writeCommunity"
          >
            <template v-slot:prefix>
              <MIcon name="unlock-alt" />
            </template>
          </PasswordInput>
        </MCol>
      </template>

      <!-- SNMP V3 Configuration -->
      <template v-if="isSnmpV3Protocol">
        <MCol v-if="!isView" :size="6">
          <FlotoFormItem
            v-model="value.securityUserName"
            label="Security User Name"
            :disabled="isView"
            name="security-user-name"
            :rules="value.id ? {} : { required: true }"
          />
        </MCol>
        <MCol :size="6">
          <FlotoFormItem
            rules="required"
            vid="securityLevel"
            label="Security Level"
            name="security-level"
          >
            <FlotoDropdownPicker
              id="security-level-id"
              v-model="value.securityLevel"
              :options="securityLevelOptions"
              :disabled="isView"
            />
          </FlotoFormItem>
        </MCol>

        <template v-if="showSnmpV3AuthFields">
          <MCol :size="6">
            <FlotoFormItem rules="required" label="Authentication Protocol">
              <FlotoDropdownPicker
                id="authentication-protocol-id"
                v-model="value.authenticationProtocol"
                :options="authenticationProtocolOptions"
                :disabled="isView"
              />
            </FlotoFormItem>
          </MCol>
          <MCol v-if="!isView" :size="6">
            <PasswordInput
              v-model="value.authenticationPassword"
              validation-label="Authentication Password"
              class="mb-0"
              name="authentication-password"
              label="Authentication Password"
              :rules="value.id ? {} : { required: true }"
            >
              <template v-slot:prefix>
                <MIcon name="unlock-alt" />
              </template>
            </PasswordInput>
          </MCol>
        </template>

        <template v-if="showSnmpV3PrivacyFields">
          <MCol :size="6">
            <FlotoFormItem rules="required" label="Privacy Protocol">
              <FlotoDropdownPicker
                id="privacy-protocol"
                v-model="value.privacyProtocol"
                as-input
                :options="privacyProtocolOptions"
                :disabled="isView"
              />
            </FlotoFormItem>
          </MCol>
          <MCol v-if="!isView" :size="6">
            <PasswordInput
              v-model="value.privatePassword"
              validation-label="Private Password"
              class="mb-0"
              name="private-password"
              label="Private Password"
              :rules="value.id ? {} : { required: true }"
            >
              <template v-slot:prefix>
                <MIcon name="unlock-alt" />
              </template>
            </PasswordInput>
          </MCol>
        </template>
      </template>

      <!-- Cloud Configuration -->
      <template v-if="isCloudProtocol">
        <MCol :size="6">
          <FlotoFormItem label="Cloud Type" rules="required">
            <MRadioGroup
              v-model="value.subType"
              :options="cloudTypeOptions"
              :disabled="canChangeSubType || Boolean(value.id)"
              as-button
            />
          </FlotoFormItem>
        </MCol>
        <MCol :size="6"></MCol>

        <template v-if="isAwsCloud">
          <MCol :size="6">
            <FlotoFormItem
              v-model="value.accessKey"
              label="Access Key"
              name="access-key"
              :rules="value.id ? {} : { required: true }"
            />
          </MCol>
          <MCol :size="6">
            <PasswordInput
              v-model="value.secretKey"
              label="Secret Key"
              name="aws-secret-key"
              :rules="value.id ? {} : { required: true }"
            />
          </MCol>
        </template>

        <template v-else-if="isAzureOrOffice365">
          <MCol :size="6">
            <FlotoFormItem
              v-model="value.clientId"
              label="Client ID"
              name="client-id"
              :rules="value.id ? {} : { required: true }"
            />
          </MCol>
          <MCol :size="6">
            <FlotoFormItem
              v-model="value.tenantId"
              label="Tenant ID"
              name="tenant-id"
              :rules="value.id ? {} : { required: true }"
            />
          </MCol>
          <MCol :size="6">
            <PasswordInput
              v-model="value.secretKey"
              name="secret-key"
              label="Secret Key"
              :rules="value.id ? {} : { required: true }"
            />
          </MCol>
        </template>
      </template>

      <!-- Documentation Link -->
      <MCol :size="12">
        <span class="text-neutral"> For more information: </span>
        <a
          href="https://docs.motadata.com/motadata-aiops-docs/Adding%20and%20Managing%20Devices/Credential%20Profile"
          target="_blank"
        >
          Credential Policy
        </a>
        <MIcon name="external-link" class="ml-1 text-primary" />
      </MCol>
    </MRow>
  </div>
</template>

<script>
import PasswordInput from '@components/password-input.vue'
import {
  getProtocolDropDownItems,
  getVersionDropDownItems,
  getSecurityLevelDropDownItems,
  getAuthenticationProtocolDropDownItems,
  getPrivacyProtocolDropDownItems,
} from '../helpers/credential-profile'
import MultipleFormItems from '@components/multiple-form-items.vue'
import FileDropper from '@/src/components/file-dropper.vue'

export default {
  name: 'CredentialProfileForm',
  components: { PasswordInput, MultipleFormItems, FileDropper },
  props: {
    value: { type: Object, required: true },
    isView: { type: Boolean, required: false },
    canChangeSubType: { type: Boolean, default: false },
    availableProtocols: {
      type: Array,
      default() {
        return []
      },
    },
    resetForm: { type: Function, required: false, default: () => ({}) },
  },
  data() {
    return {
      // Static options moved to data for better performance
      cloudTypeOptions: [
        {
          text: 'Aws',
          key: this.$constants.AWS_CLOUD,
          value: this.$constants.AWS_CLOUD,
        },
        {
          text: 'Azure',
          key: this.$constants.AZURE_CLOUD,
          value: this.$constants.AZURE_CLOUD,
        },
        {
          text: 'Office365',
          key: this.$constants.OFFICE_365,
          value: this.$constants.OFFICE_365,
        },
      ],
      httpAuthTypeOptions: [
        { text: 'Basic', key: 'basic' },
        { text: 'NTLM', key: 'ntlm' },
        { text: 'Digest', key: 'digest' },
        { text: 'API Key', key: 'apikey' },
        { text: 'OAuth 2.0', key: 'oauth' },
        {
          text: 'Client Certificate Authentication',
          key: 'certificate',
        },
      ],
      transferProtocols: [
        { text: 'TFTP', key: 'TFTP' },
        // { text: 'FTP', key: 'FTP' },
        { text: 'SCP/SFTP', key: 'SCP/SFTP' },
        { text: 'No Protocol', key: 'NONE' },
      ],
      clientCertificateConfigOptions: [
        {
          text: 'Configure Manually',
          key: 'manual',
          value: 'manual',
        },
        {
          text: 'Upload Certificate',
          key: 'upload',
          value: 'upload',
        },
      ],
      grantTypeOptions: [
        { key: 'Password', name: 'Password' },
        { key: 'Authorization Code', name: 'Authorization Code' },
      ],
      authProviderOptions: [
        { key: 'google', name: 'Google' },
        { key: 'microsoft', name: 'Microsoft' },
      ],
      // Dynamic options
      protocolOptions: this.availableProtocols.length
        ? this.availableProtocols.map((p) => ({
            name: p,
            key: p,
            id: p.replace(/\s/g, '').replace(/\//g, ''),
          }))
        : getProtocolDropDownItems(),
      versionOptions: getVersionDropDownItems(),
      securityLevelOptions: getSecurityLevelDropDownItems(),
      authenticationProtocolOptions: getAuthenticationProtocolDropDownItems(),
      privacyProtocolOptions: getPrivacyProtocolDropDownItems(),
      subType: this.$constants.AWS_CLOUD,
      clientCertificateConfig: 'manual',
      clientCertificateFields: [
        {
          key: 'clientCertificate',
          label: 'Client Certificate',
          messageKey: 'client_certificate',
          uploadMessageKey: 'client_certificate_file',
          modelKey: 'clientCertificate',
          uploadKey: 'clientCertificateFile',
          allowedFileTypes: ['crt'],
        },
        {
          key: 'clientKey',
          label: 'Client Key',
          messageKey: 'client_key',
          uploadMessageKey: 'client_key_file',
          modelKey: 'clientKey',
          uploadKey: 'clientKeyFile',
          allowedFileTypes: ['key'],
        },
        {
          key: 'certificateAuthority',
          label: 'Certificate Authority',
          messageKey: 'certificate_authority',
          uploadMessageKey: 'certificate_authority_file',
          modelKey: 'certificateAuthority',
          uploadKey: 'certificateAuthorityFile',
          allowedFileTypes: ['crt'],
        },
      ],
    }
  },
  computed: {
    // Protocol checks
    isHttpHttpsProtocol() {
      return this.value.credentialProfileProtocol === this.$constants.HTTP_HTTPS
    },
    isSshProtocol() {
      return this.value.credentialProfileProtocol === this.$constants.SSH
    },
    isSnmpV12Protocol() {
      return this.value.credentialProfileProtocol === this.$constants.SNMP_V12
    },
    isSnmpV3Protocol() {
      return this.value.credentialProfileProtocol === this.$constants.SNMP_V3
    },
    isCloudProtocol() {
      return this.value.credentialProfileProtocol === this.$constants.CLOUD
    },

    // Authentication type checks
    showStandardAuthFields() {
      const supportedProtocols = [
        this.$constants.POWERSHELL,
        this.$constants.JDBC,
        this.$constants.HTTP_HTTPS,
        this.$constants.SSH,
        this.$constants.JMX,
        this.$constants.JMS,
      ]
      return supportedProtocols.includes(this.value.credentialProfileProtocol)
    },

    showUsernamePasswordField() {
      const excludedAuthTypes = ['apikey', 'oauth', 'certificate']
      return !excludedAuthTypes.includes(this.value.authenticationType)
    },

    usernameRules() {
      return this.value.credentialProfileProtocol === 'JMX' ? '' : 'required'
    },

    passwordRules() {
      const excludedProtocols = [this.$constants.SSH, this.$constants.JMX]
      return !excludedProtocols.includes(
        this.value.credentialProfileProtocol
      ) && !this.value.id
        ? 'required'
        : ''
    },

    // OAuth specific checks
    showOAuthPasswordFields() {
      return (
        this.value.authenticationType === 'oauth' &&
        this.value.grantType === 'Password'
      )
    },

    showOAuthAuthorizationCodeFields() {
      return (
        this.value.authenticationType === 'oauth' &&
        this.value.grantType === 'Authorization Code'
      )
    },

    // SNMP V3 specific checks
    showSnmpV3AuthFields() {
      return ['Authentication No Privacy', 'Authentication Privacy'].includes(
        this.value.securityLevel
      )
    },

    showSnmpV3PrivacyFields() {
      return this.value.securityLevel === 'Authentication Privacy'
    },

    // Cloud specific checks
    isAwsCloud() {
      return this.value.subType === this.$constants.AWS_CLOUD
    },

    isAzureOrOffice365() {
      return [this.$constants.AZURE_CLOUD, this.$constants.OFFICE_365].includes(
        this.value.subType
      )
    },
  },
  watch: {
    'value.credentialProfileProtocol': function (newValue) {
      this.handleProtocolChange(newValue)
    },
    'value.authenticationType': function (newValue) {
      this.handleAuthenticationTypeChange(newValue)
    },
    'value.authenticationProvider': function (newValue) {
      this.handleAuthProviderChange(newValue)
    },
  },
  methods: {
    handleProtocolChange(newValue) {
      const updates = {}

      if (newValue !== 'SNMP V3') {
        updates.securityLevel = undefined
      }
      if (newValue !== 'SNMP V1/V2c') {
        updates.version = undefined
      }
      if (newValue !== this.$constants.HTTP_HTTPS) {
        updates.authenticationType = undefined
        updates.clientCertificateConfig = undefined
      }
      if (newValue === this.$constants.HTTP_HTTPS) {
        updates.authenticationType = 'basic'
        updates.clientCertificateConfig = 'manual'
      }

      if (Object.keys(updates).length > 0) {
        this.resetForm({
          ...this.value,
          ...updates,
        })
      }
    },

    handleAuthenticationTypeChange(newValue) {
      const updates = {}

      // Reset clientCertificateConfig when authentication type changes
      if (newValue !== 'certificate') {
        updates.clientCertificateConfig = undefined
        if (this.value.clientCertificateConfig) {
          if (this.value.clientCertificateConfig === 'manual') {
            updates.clientCertificate = undefined
            updates.clientKey = undefined
            updates.certificateAuthority = undefined
          } else {
            updates.clientCertificateFile = undefined
            updates.clientKeyFile = undefined
            updates.certificateAuthorityFile = undefined
          }
        }
      } else {
        updates.clientCertificateConfig = 'manual'
      }

      if (Object.keys(updates).length > 0) {
        this.resetForm({
          ...this.value,
          ...updates,
        })
      }
    },

    handleAuthProviderChange(newValue) {
      const providerUrls = {
        google: {
          authenticationURL: 'https://accounts.google.com/o/oauth2/v2/auth',
          tokenURL: 'https://oauth2.googleapis.com/token',
        },
        microsoft: {
          authenticationURL:
            'https://login.microsoftonline.com/common/oauth2/authorize',
          tokenURL:
            'https://login.microsoftonline.com/common/oauth2/v2.0/token',
        },
      }

      if (providerUrls[newValue]) {
        this.resetForm({
          ...this.value,
          ...providerUrls[newValue],
        })
      }
    },

    handleCopyRedirectURL() {
      const url = this.value.redirectURL
      if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
          this.$successToast('Redirect URL copied to clipboard')
        })
      }
    },

    fileRemove(key) {
      this.resetForm({
        ...this.value,
        [key]: undefined,
      })
    },
  },
}
</script>

<style scoped>
.browse-file {
  text-decoration: underline;
  cursor: pointer;
}

.drop-zone-content {
  pointer-events: none;
}

.disabled-file-list {
  margin-top: 4px;
  border: 1px dashed var(--border-color);
  border-radius: 4px;
}

.disabled-file-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 0;
  color: var(--text-color);
  cursor: default;

  .file-name {
    font-size: 0.8rem;
    word-break: break-all;
  }
}
</style>
