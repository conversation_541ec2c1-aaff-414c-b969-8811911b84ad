<template>
  <FlotoFixedView :gutter="0" class="discovery-progress-page">
    <MRow :gutter="0" class="mb-4">
      <MCol>
        <FlotoPageHeader :title="discoveryInfo.name">
          <template v-slot:after-title>
            <MButton
              v-if="discoveryInfo.isRunning && !discoveryInfo.isCompleted"
              id="abort-btn-id"
              outline
              variant="error"
              :disabled="isAbortDisabled"
              @click="$emit('abort')"
            >
              Abort
            </MButton>
          </template>
        </FlotoPageHeader>
      </MCol>
    </MRow>
    <MRow class="flex-1 justify-center mt-4">
      <MCol auto-size class="flex flex-col items-center">
        <div style="height: 40vh">
          <!-- #3627-Added black theme icon -->

          <Prism
            v-if="discoveryInfo.subType === $constants.PRISM"
            id="prism-img-id"
            class="h-full"
          />
          <CiscoVManage
            v-if="discoveryInfo.subType === $constants.CISCO_VMANAGE"
            id="cisco-vamange-img-id"
            class="h-full"
          />
          <CiscoMeraki
            v-if="discoveryInfo.subType === $constants.CISCO_MERAKI"
            id="meraki-img-id"
            class="h-full"
          />
          <CiscoACI
            v-if="discoveryInfo.subType === $constants.CISCO_ACI"
            id="aci-img-id"
            class="h-full"
          />
          <Hpe
            v-if="discoveryInfo.subType === $constants.HPE"
            id="hpe-img-id"
            class="h-full"
          />
          <NetApp
            v-if="discoveryInfo.subType === $constants.NETAPP"
            id="netapp-img-id"
            class="h-full"
          />
          <TanzuKubernetes
            v-if="discoveryInfo.subType === $constants.VMWARE_TANZU_KUBERNETES"
            id="tanzu-kubernetes-img-id"
            class="h-full"
          />
          <Kubernetes
            v-if="discoveryInfo.subType === $constants.KUBERNETES"
            id="kubernetes-img-id"
            class="h-full"
          />
        </div>
        <div
          v-if="discoveryInfo.monitor"
          class="flex flex-col my-2 items-start"
        >
          <div
            v-for="(command, index) in discoveryInfo.monitor.commands"
            :key="`${command}-${index}`"
            class="my-1 flex justify-center"
          >
            <div class="w-6">
              <MIcon
                :name="getIconForState(discoveryInfo.monitor, index)"
                :class="getClassForState(discoveryInfo.monitor, index)"
              />
            </div>
            <h5 class="m-0">
              {{ command }}
              <TestError
                v-if="
                  discoveryInfo.monitor.error &&
                  discoveryInfo.monitor.commands.length - 1 === index
                "
                :error="discoveryInfo.monitor.error"
              />
            </h5>
          </div>
          <div class="mb-2 w-full">
            <Progress
              :width="discoveryInfo.isCompleted ? 100 : discoveryInfo.progress"
              :type="`${
                discoveryInfo.hasError
                  ? 'error'
                  : discoveryInfo.isCompleted
                  ? 'success'
                  : 'active'
              }`"
            />
          </div>
          <div v-if="discoveryInfo.hasError" class="flex justify-center w-full">
            <FlotoLink
              as-button
              :to="
                $currentModule.getRoute('edit-discovery-profile', {
                  params: { id: discoveryInfo.id },
                })
              "
            >
              Edit Network Discovery Profile
            </FlotoLink>
          </div>
        </div>
      </MCol>
    </MRow>
  </FlotoFixedView>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import Bus from '@utils/emitter'
import TestError from '@components/error-info.vue'
import Progress from '@components/progress.vue'
import Prism from '@assets/images/prism.svg'
import CiscoVManage from '@assets/images/cisco-vmanage.svg'
import { transformCloudMonitorProgress } from '../../helpers/network-discovery'
import { authComputed } from '@state/modules/auth'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import CiscoMeraki from '@assets/images/cisco-meraki.svg'
import Hpe from '@assets/images/hpe.svg'
import NetApp from '@assets/images/netapp.svg'
import CiscoACI from '@assets/images/cisco-aci.svg'
import TanzuKubernetes from '@assets/images/tanzu-kubernetes.svg'
import Kubernetes from '@assets/images/kubernetes.svg'
export default {
  name: 'GroupingDiscoveryProgress',
  components: {
    Progress,
    Prism,
    TestError,
    CiscoVManage,
    CiscoMeraki,
    Hpe,
    NetApp,
    CiscoACI,
    TanzuKubernetes,
    Kubernetes,
  },
  props: {
    discovery: { type: Object, required: true },
    discoveryOwner: { type: String, default: undefined },
  },
  data() {
    return {
      isAbortDisabled: false,
      discoveryInfo: CloneDeep(this.discovery),
    }
  },
  computed: {
    ...authComputed,
    ...UserPreferenceComputed,
  },
  created() {
    const progressHandler = (payload) => {
      if (this.discovery.id !== payload.id) {
        return
      }
      if (!this.user.isSuperAdmin) {
        this.isAbortDisabled = this.discoveryOwner !== this.user.userName
      }
      this.updateDiscovery({
        isRunning: payload['discovery.progress'] < 100,
        progress: payload['discovery.progress'],
      })
    }
    const monitorHandler = (payload) => {
      if (this.discovery.id !== payload.id) {
        return
      }
      const currentMonitor = this.discoveryInfo.monitor || {}
      const commands = currentMonitor.commands || []
      commands.push(payload.message)
      const monitor = transformCloudMonitorProgress({
        ...payload,
        commands,
      })
      this.updateDiscovery({
        hasError: monitor.hasError,
        isCompleted: monitor.isCompleted,
        isRunning: monitor.isRunning,
        progress: monitor.progress,
        monitor,
      })
    }
    Bus.$on(
      this.$currentModule.getConfig().DISCOVERY_STATISTICS_PROGRESS_EVENT,
      progressHandler
    )
    Bus.$on(
      this.$currentModule.getConfig().DISCOVERY_OBJECT_PROGRESS_EVENT,
      monitorHandler
    )
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(
        this.$currentModule.getConfig().DISCOVERY_STATISTICS_PROGRESS_EVENT,
        progressHandler
      )
      Bus.$off(
        this.$currentModule.getConfig().DISCOVERY_OBJECT_PROGRESS_EVENT,
        monitorHandler
      )
    })
  },
  methods: {
    getIconForState(monitor, index) {
      const totalCommands = monitor.commands.length - 1
      if (index === totalCommands && monitor.hasError) {
        return 'times-circle'
      }
      return 'check-circle'
    },
    getClassForState(monitor, index) {
      const totalCommands = monitor.commands.length - 1
      if (index === totalCommands && monitor.hasError) {
        return 'text-secondary-red'
      }
      return 'text-secondary-green'
    },
    updateDiscovery(discovery) {
      this.discoveryInfo = {
        ...this.discoveryInfo,
        ...discovery,
      }
    },
  },
}
</script>
