<template>
  <MRow>
    <MCol :size="3">
      <FlotoFormItem id="port-type-id" label="Port Type" rules="required">
        <MRadioGroup
          v-model="data.portType"
          as-button
          :options="portTypeOptions"
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="3" class="material-input">
      <FlotoFormItem id="port-id" label="Port" rules="required|port">
        <MInputNumber
          v-model="data.port"
          :min="1"
          :max="65535"
          :precision="0"
        />
      </FlotoFormItem>
    </MCol>
    <MCol v-if="data.portType === 'tcp'">
      <FlotoFormItem label="Send Command">
        <MSwitch
          id="btn-enable-port-command"
          v-model="data.enableCommandExecution"
          checked-children="ON"
          un-checked-children="OFF"
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="12" />
    <template
      v-if="
        (data.portType === 'tcp' && data.enableCommandExecution) ||
        data.portType === 'udp'
      "
    >
      <MCol :size="3">
        <FlotoFormItem
          id="command-id"
          v-model="data.command"
          name="command"
          placeholder="ex. GET/index.html HTTP/1.0"
          label="Command"
          rules="required"
        />
      </MCol>
      <MCol :size="3" class="material-input">
        <FlotoFormItem
          id="max-lines-id"
          rules="required"
          label="Max Command Output Lines"
        >
          <MInputNumber v-model="data.maxLines" :min="1" :precision="0" />
        </FlotoFormItem>
      </MCol>
      <MCol :size="3">
        <FlotoFormItem
          id="content-id"
          v-model="data.content"
          label="Search Keyword"
          placeholder="ex. HTTP/1.0 200 OK"
          name="content"
        />
      </MCol>
    </template>
  </MRow>
</template>

<script>
import { PORT_TYPES } from '@data/monitor'

export default {
  name: 'PortService',
  props: { data: { type: Object, required: true } },
  data() {
    this.portTypeOptions = PORT_TYPES
    return {}
  },
  computed: {
    enableCommandExecution: {
      get() {
        return (this.value || {}).enableCommandExecution
      },
      set(enableCommandExecution) {
        this.$emit('change', { ...(this.value || {}), enableCommandExecution })
      },
    },
  },
}
</script>
