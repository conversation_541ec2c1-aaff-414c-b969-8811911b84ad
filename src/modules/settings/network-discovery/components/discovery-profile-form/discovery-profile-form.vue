<template>
  <FlotoFixedView :gutter="0">
    <MRow :gutter="0">
      <MCol>
        <FlotoPageHeader
          use-divider
          :back-link="$currentModule.getRoute('discovery-profiles')"
          :title="`${formData.id ? 'Edit' : 'Create'} Discovery Profile`"
        />
      </MCol>
    </MRow>
    <MRow
      :gutter="0"
      class="flex-1 min-h-0"
      :class="{ 'form-edit-mode': isEditMode }"
    >
      <MCol :size="2" :gutter="0">
        <MMenu
          id="network-tabs"
          class="selection-menu pt-0 pr-0 h-100"
          :selected-keys="[formData.type]"
          @select="setType"
        >
          <MMenuItem v-for="type in availableTypes" :key="type.key">
            {{ type.text }}
          </MMenuItem>
        </MMenu>
      </MCol>
      <GroupProvider>
        <RpeProvider :remote-event-type-filter="['APP', 'COLLECTOR']">
          <MCol class="h-100 pl-4 flex flex-col" :size="10">
            <FlotoScrollView>
              <ServerForm
                v-if="formData.type === $constants.SERVER"
                id="server-id"
                :is-edit-mode="isEditMode"
                :processing="processing"
                :value="formData.discoveryInfo"
                @submit="handleSubmit($event.data, $event.command)"
              />
              <AwsRegionProvider v-else-if="formData.type === $constants.CLOUD">
                <CloudForm
                  id="cloud-id"
                  :processing="processing"
                  :is-edit-mode="isEditMode"
                  :value="formData.discoveryInfo"
                  @submit="handleSubmit($event.data, $event.command)"
                />
              </AwsRegionProvider>
              <NetworkForm
                v-else-if="formData.type === $constants.NETWORK"
                id="network-id"
                :processing="processing"
                :is-edit-mode="isEditMode"
                :value="formData.discoveryInfo"
                @submit="handleSubmit($event.data, $event.command)"
              />
              <VirtualizationForm
                v-else-if="formData.type === $constants.VIRTUALIZATION"
                id="vertualization-id"
                :value="formData.discoveryInfo"
                :is-edit-mode="isEditMode"
                :processing="processing"
                @submit="handleSubmit($event.data, $event.command)"
              />
              <HyperconvergedInfrastructureForm
                v-else-if="
                  formData.type === $constants.HYPERCONVERGED_INFRASTRUCTURE
                "
                id="hyper-converged-infrastructure-id"
                :value="formData.discoveryInfo"
                :is-edit-mode="isEditMode"
                :processing="processing"
                @submit="handleSubmit($event.data, $event.command)"
              />
              <SDNForm
                v-else-if="formData.type === $constants.SDN"
                id="sdn-id"
                :value="formData.discoveryInfo"
                :is-edit-mode="isEditMode"
                :processing="processing"
                @submit="handleSubmit($event.data, $event.command)"
              />
              <StorageForm
                v-else-if="formData.type === $constants.STORAGE"
                id="storage-id"
                :value="formData.discoveryInfo"
                :is-edit-mode="isEditMode"
                :processing="processing"
                @submit="handleSubmit($event.data, $event.command)"
              />
              <ServiceForm
                v-else-if="formData.type === $constants.SERVICE_CHECK"
                id="services-id"
                :value="formData.discoveryInfo"
                :is-edit-mode="isEditMode"
                :processing="processing"
                @submit="handleSubmit($event.data, $event.command)"
              />
              <WirelessForm
                v-else-if="formData.type === $constants.WIRELESS"
                id="wireless-id"
                :value="formData.discoveryInfo"
                :is-edit-mode="isEditMode"
                :processing="processing"
                @submit="handleSubmit($event.data, $event.command)"
              />
              <ContainerOrchestrationForm
                v-else-if="formData.type === $constants.CONTAINER_ORCHESTRATION"
                :value="formData.discoveryInfo"
                :is-edit-mode="isEditMode"
                :processing="processing"
                @submit="handleSubmit($event.data, $event.command)"
              />
              <!-- <IpslaForm
                v-else-if="formData.type === 'IPSLA'"
                id="ip-sla-id"
                :value="formData.discoveryInfo"
                :is-edit-mode="isEditMode"
                :processing="processing"
                @submit="handleSubmit($event.data, $event.command)"
              /> -->
              <OtherForm
                v-else-if="formData.type === $constants.OTHER"
                id="other-id"
                :value="formData.discoveryInfo"
                :is-edit-mode="isEditMode"
                :processing="processing"
                @submit="handleSubmit($event.data, $event.command)"
              />
            </FlotoScrollView>
          </MCol>
        </RpeProvider>
      </GroupProvider>
    </MRow>
  </FlotoFixedView>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import GroupProvider from '@components/data-provider/group-provider.vue'
import AwsRegionProvider from '@components/data-provider/aws-region-provider.vue'
import RpeProvider from '@components/data-provider/rpe-provider.vue'
import ServerForm from './server-form.vue'
import CloudForm from './cloud-form.vue'
import NetworkForm from './network-form.vue'
import VirtualizationForm from './virtualization-form.vue'
import HyperconvergedInfrastructureForm from './hyperconverged-infrastructure-form.vue'
import SDNForm from './sdn-form.vue'
import ServiceForm from './service-form.vue'
import WirelessForm from './wireless-form.vue'
import OtherForm from './other-form.vue'
import StorageForm from './storage-form.vue'
import { AVAILABLE_DISCOVERY_TYPE } from '../../helpers/discovery-types'
import GroupMap from '@src/statics/default-group-map'
import { authComputed } from '@/src/state/modules/auth'
import { generateId } from '@utils/id'
import ContainerOrchestrationForm from './container-orchestration.vue'

export default {
  name: 'DiscoveryProfileForm',
  components: {
    ServerForm,
    CloudForm,
    NetworkForm,
    VirtualizationForm,
    HyperconvergedInfrastructureForm,
    SDNForm,
    ServiceForm,
    WirelessForm,
    OtherForm,
    GroupProvider,
    RpeProvider,
    AwsRegionProvider,
    StorageForm,
    ContainerOrchestrationForm,
  },
  props: {
    processing: { type: String, default: undefined },
    defaultValue: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    // this.availableTypes = AVAILABLE_DISCOVERY_TYPE
    return {
      formData: this.getDefaultValue(),
      isEditMode: Boolean(this.defaultValue.id),
    }
  },
  computed: {
    ...authComputed,
    availableTypes() {
      if (
        !this.hasLicensePermission(
          this.$constants.APPLICATION_DATABASE_CLOUD_LICENSE_PERMISSION
        )
      ) {
        return AVAILABLE_DISCOVERY_TYPE.filter(
          (type) =>
            ![this.$constants.CLOUD].includes(type.key) &&
            ![this.$constants.SDN].includes(type.key) &&
            ![this.$constants.HYPERCONVERGED_INFRASTRUCTURE].includes(
              type.key
            ) &&
            ![this.$constants.STORAGE].includes(type.key)
        )
      }

      return AVAILABLE_DISCOVERY_TYPE
    },
  },
  created() {
    const setup = this.$route.query._setup
    if (setup) {
      try {
        const decodedSetup = JSON.parse(atob(decodeURIComponent(setup)))
        if (decodedSetup.defaultData) {
          this.formData = {
            ...this.formData,
            type: decodedSetup.defaultData.type,
            discoveryInfo: this.getDefaultInfo(decodedSetup.defaultData.type),
          }
        }
      } catch (e) {}
    }
  },
  methods: {
    getDefaultInfo(type) {
      if (type === this.$constants.SERVER) {
        return {
          subType: this.$constants.LINUX,
          ipType: 'IP',
          excludeIpType: 'IP',
          excludedIpsOrRange: [],
          windowsType: this.$constants.WINDOWS,
          port: 22,
          group:
            (this.user.userGroup || []).includes(
              GroupMap[this.$constants.SERVER][0]
            ) || this.user.isSuperAdmin
              ? GroupMap[this.$constants.SERVER]
              : undefined,
          // timeout: 60,
          pingCheck: true,
        }
      }
      if (type === this.$constants.CLOUD) {
        return {
          subType: this.$constants.AWS_CLOUD,
          group:
            (this.user.userGroup || []).includes(
              GroupMap[this.$constants.CLOUD][0]
            ) || this.user.isSuperAdmin
              ? GroupMap[this.$constants.CLOUD]
              : undefined,
          // timeout: 60,
          resourcesTags: [{ key: generateId() }],
          monitoredResources: 'resources',
        }
      }
      if (type === this.$constants.NETWORK) {
        return {
          subType: this.$constants.SNMP_DEVICE,
          ipType: 'IP',
          excludeIpType: 'IP',
          excludedIpsOrRange: [],
          monitorUnusedPort: false,
          interfaceDiscover: true,
          port: 161,
          ncmPort: 22,
          // timeout: 60,
          retryCount: 2,
          pingCheck: true,
          manageNCM: false,
          runTopology: false,
          layerProtocol: ['L2'],
          protocols: ['CDP', 'LLDP'],
          group:
            (this.user.userGroup || []).includes(
              GroupMap[this.$constants.NETWORK][0]
            ) || this.user.isSuperAdmin
              ? GroupMap[this.$constants.NETWORK]
              : undefined,
        }
      }
      if (type === this.$constants.VIRTUALIZATION) {
        return {
          subType: 'VMWare',
          virtualizationType: this.$constants.VCENTER,
          // timeout: 60,
          pingCheck: true,
          ipType: 'IP',
          urlType: 'http',
          excludeIpType: 'IP',
          excludedIpsOrRange: [],
          port: 443,
          group:
            (this.user.userGroup || []).includes(
              GroupMap[this.$constants.VIRTUALIZATION][0]
            ) || this.user.isSuperAdmin
              ? GroupMap[this.$constants.VIRTUALIZATION]
              : undefined,
        }
      }
      if (type === this.$constants.HYPERCONVERGED_INFRASTRUCTURE) {
        return {
          subType: this.$constants.PRISM,
          port: 9440,
          protocol: 'https',
          ipType: 'IP',
          pingCheck: true,
          urlType: 'https',
          group:
            (this.user.userGroup || []).includes(
              GroupMap[this.$constants.HYPERCONVERGED_INFRASTRUCTURE][0]
            ) || this.user.isSuperAdmin
              ? GroupMap[this.$constants.HYPERCONVERGED_INFRASTRUCTURE]
              : undefined,
        }
      }
      if (type === this.$constants.SDN) {
        return {
          subType: this.$constants.CISCO_VMANAGE,
          port: 443,
          protocol: 'https',
          ipType: 'IP',
          pingCheck: true,
          urlType: 'https',
          group:
            (this.user.userGroup || []).includes(
              GroupMap[this.$constants.SDN][0]
            ) || this.user.isSuperAdmin
              ? GroupMap[this.$constants.SDN]
              : undefined,
          retryCount: 2,
        }
      }
      if (type === this.$constants.STORAGE) {
        return {
          port: 443,
          protocol: 'https',
          ipType: 'IP',
          pingCheck: true,
          urlType: 'https',
          group:
            (this.user.userGroup || []).includes(
              GroupMap[this.$constants.STORAGE][0]
            ) || this.user.isSuperAdmin
              ? GroupMap[this.$constants.STORAGE]
              : undefined,
        }
      }
      if (type === this.$constants.SERVICE_CHECK) {
        return {
          targetType: 'OBJECT',
          sourceAgent: false,
          serviceType: this.$constants.PING,
          urlType: 'http',
          // timeout: 60,
          portType: 'tcp',
          retryCount: 1,
          excludeIpType: 'IP',
          excludedIpsOrRange: [],
          group:
            (this.user.userGroup || []).includes(
              GroupMap[this.$constants.SERVICE_CHECK][0]
            ) || this.user.isSuperAdmin
              ? GroupMap[this.$constants.SERVICE_CHECK]
              : undefined,
        }
      }
      if (type === this.$constants.WIRELESS) {
        return {
          subType: this.$constants.CISCO_WIRELESS,
          // timeout: 60,
          pingCheck: true,
          ipType: 'IP',
          port: 161,
          urlType: 'http',
          group:
            (this.user.userGroup || []).includes(
              GroupMap[this.$constants.WIRELESS][0]
            ) || this.user.isSuperAdmin
              ? GroupMap[this.$constants.WIRELESS]
              : undefined,
        }
      }

      if (type === this.$constants.CONTAINER_ORCHESTRATION) {
        return {
          subType: this.$constants.KUBERNETES,
          port: 22,
          protocol: 'https',
          ipType: 'IP',
          pingCheck: true,
          urlType: 'https',
          group:
            (this.user.userGroup || []).includes(
              GroupMap[this.$constants.CONTAINER_ORCHESTRATION][0]
            ) || this.user.isSuperAdmin
              ? GroupMap[this.$constants.CONTAINER_ORCHESTRATION]
              : undefined,
        }
      }

      if (type === this.$constants.OTHER) {
        return {
          subType: this.$constants.IBM_TAPE_LIBRARY,
          // timeout: 60,
          port: 161,
          protocol: 'https',
          ipType: 'IP',
          pingCheck: true,
          urlType: 'http',
          retryCount: 1,
          group:
            (this.user.userGroup || []).includes(
              GroupMap[this.$constants.OTHER][0]
            ) || this.user.isSuperAdmin
              ? GroupMap[this.$constants.OTHER]
              : undefined,
        }
      }
      return {}
    },
    getDefaultValue() {
      return {
        id: this.defaultValue.id,
        type: this.defaultValue.type,
        discoveryInfo: CloneDeep(this.defaultValue.discoveryInfo),
      }
    },
    resetForm() {
      this.formData = this.getDefaultValue()
    },
    setType(type) {
      // do not allow to change if its edit mode
      if (this.isEditMode) {
        return
      }
      this.formData = {
        type: type.key,
        discoveryInfo: this.getDefaultInfo(type.key),
      }
    },
    handleSubmit(formData, command = 'run') {
      this.$emit('submit', {
        id: this.formData.id,
        type: this.formData.type,
        discoveryInfo: formData,
        command,
      })
    },
  },
}
</script>
