<template>
  <CredentialProvider :search-params="credentialSearchParams">
    <FlotoForm ref="formRef" @submit="handleSubmit" @reset="resetForm">
      <div class="flex flex-col">
        <FlotoFormItem class="sticky-tab">
          <MTab v-if="isEditMode" :value="data.subType" class="sticky-tab">
            <MTabPane
              v-for="tab in tabTypes"
              :key="tab.key"
              disabled
              :tab="tab.text"
            />
          </MTab>
          <MTab v-else v-model="data.subType">
            <MTabPane v-for="tab in tabTypes" :key="tab.key" :tab="tab.text" />
          </MTab>
        </FlotoFormItem>
        <div class="mx-4 ml-0">
          <MRow>
            <MCol :size="3">
              <FlotoFormItem
                id="profile-id"
                v-model="data.name"
                rules="required"
                placeholder="Must be unique"
                label="Discovery Profile Name"
                name="profile-name"
              />
            </MCol>
          </MRow>
          <MRow>
            <MCol v-if="data.subType !== $constants.CISCO_MERAKI" :size="3">
              <FlotoFormItem
                id="ip-address-id"
                v-model="data.ipAddress"
                rules="required|ip"
                placeholder="e.g. *********** or fd00::1"
                label="IP/Host"
                name="ip-host"
                :info-tooltip="$message('sdn_ip_host')"
              />
            </MCol>
            <MCol v-if="data.subType === $constants.CISCO_MERAKI" :size="3">
              <FlotoFormItem
                id="profile-url-endpoint"
                v-model="data.target"
                rules="required|url"
                placeholder="Enter text"
                label="URL Endpoint"
                name="profile-url-endpoint"
              />
            </MCol>
            <MCol :size="3">
              <FlotoFormItem
                id="rpe-picker-id"
                label="Collectors"
                :info-tooltip="$message('collector_tooltip')"
              >
                <RpePicker v-model="data.rpe" multiple />
              </FlotoFormItem>
            </MCol>
          </MRow>
          <MRow>
            <MCol :size="3">
              <FlotoFormItem id="group-picker" label="Groups" rules="required">
                <GroupPicker v-model="data.group" multiple />
              </FlotoFormItem>
            </MCol>
            <MCol :size="5">
              <FlotoFormItem
                id="credential-id"
                label="Credential Profiles"
                rules="required"
              >
                <CredentialPicker
                  v-model="data.credentials"
                  multiple
                  allow-create
                  :available-protocols="[$constants.HTTP_HTTPS]"
                  :default-form-data="{
                    credentialProfileProtocol: $constants.HTTP_HTTPS,
                    authenticationType: 'basic',
                  }"
                />
              </FlotoFormItem>
            </MCol>
          </MRow>
          <MRow>
            <MCol :size="6">
              <FlotoFormItem label="Tags">
                <LooseTags
                  v-model="data.tags"
                  variant="default"
                  placeholder="Tags"
                  rounded
                  :full-width="true"
                  always-text-mode
                  user-tag-only
                />
              </FlotoFormItem>
            </MCol>
          </MRow>
          <!-- <MDivider /> -->
          <MRow>
            <MCol>
              <h5 id="discovery-params-header-id" class="text-primary">
                Discovery Parameters of
                {{ data.subType }}
              </h5>
            </MCol>
          </MRow>
          <MRow>
            <MCol :size="3" class="material-input">
              <FlotoFormItem
                v-if="data.subType !== $constants.CISCO_MERAKI"
                label="URL Type"
                rules="required"
              >
                <MRadioGroup
                  v-model="data.urlType"
                  as-button
                  :options="urlTypeOptions"
                />
              </FlotoFormItem>
              <FlotoFormItem
                v-if="data.subType !== $constants.CISCO_MERAKI"
                rules="required|port"
                label="Port"
              >
                <MInputNumber
                  id="port-id"
                  v-model="data.port"
                  :min="1"
                  :max="65535"
                  :precision="0"
                />
              </FlotoFormItem>
              <FlotoFormItem
                v-if="data.subType === $constants.CISCO_MERAKI"
                id="retry-count-id"
                label="Retry Count"
                rules="required|min_value:1|max_value:5"
              >
                <MInputNumber
                  v-model="data.retryCount"
                  :precision="0"
                  :min="1"
                  :max="5"
                />
              </FlotoFormItem>
            </MCol>
            <MCol :size="3">
              <FlotoFormItem label="Ping Check">
                <MSwitch
                  id="ping-check-id"
                  v-model="data.pingCheck"
                  checked-children="ON"
                  un-checked-children="OFF"
                />
              </FlotoFormItem>
            </MCol>
          </MRow>
          <NotificationElements v-model="data.notification" />
        </div>
      </div>
      <template v-slot:submit="{ submit, reset }">
        <MRow :gutter="0">
          <MCol :size="6">
            <MButton
              id="save-exit-btn-id"
              outline
              :loading="processing === 'exit'"
              :disabled="processing && processing !== 'exit'"
              @click="submit('exit')"
            >
              Save and Exit
            </MButton>
          </MCol>
          <!-- <MCol :size="12" class="mt-4">
            <span class="text-neutral"> For more information: </span>
            <a
              href="https://docs.motadata.com/motadata-aiops-docs/Adding%20and%20Managing%20Devices/Adding%20Network%20Devices%20for%20Monitoring"
              target="_blank"
              >Discovery Profile</a
            >
            <MIcon name="external-link" class="ml-1 text-primary" />
          </MCol> -->
          <MCol class="text-right mb-4">
            <MButton
              id="reset-btn-id"
              class="mr-2"
              variant="default"
              :disabled="processing ? true : false"
              @click="reset"
            >
              Reset
            </MButton>
            <MButton
              id="save-run-btn-id"
              :loading="processing === 'run'"
              :disabled="processing && processing !== 'run'"
              @click="submit('run')"
            >
              Save and Run
            </MButton>
          </MCol>
        </MRow>
        <MRow>
          <MCol :size="12">
            <span class="text-neutral"> For more information: </span>
            <a
              href="https://docs.motadata.com/motadata-aiops-docs/Adding%20and%20Managing%20Devices/Adding-SDN-devices-for-monitoring"
              target="_blank"
              >Discovery Profile</a
            >
            <MIcon name="external-link" class="ml-1 text-primary" />
          </MCol>
        </MRow>
      </template>
    </FlotoForm>
  </CredentialProvider>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import CredentialPicker from '@components/data-picker/credential-picker.vue'
import CredentialProvider from '@components/data-provider/credential-provider.vue'
import LooseTags from '@components/loose-tags.vue'
import NotificationElements from './notification-elements.vue'
import { URL_TYPES } from '@data/monitor'

export default {
  name: 'SDNForm',
  components: {
    CredentialPicker,
    CredentialProvider,
    NotificationElements,
    LooseTags,
  },
  props: {
    value: { type: Object, required: true },
    processing: { type: String, default: undefined },
    isEditMode: { type: Boolean, default: false },
  },
  data() {
    this.tabTypes = [
      {
        key: this.$constants.CISCO_VMANAGE,
        text: 'Cisco Catalyst SD-WAN',
      },
      {
        key: this.$constants.CISCO_MERAKI,
        text: 'Cisco Meraki',
      },
      {
        key: this.$constants.CISCO_ACI,
        text: 'Cisco ACI',
      },
      {
        key: this.$constants.NSXT,
        text: this.$constants.NSXT,
      },
    ]
    this.urlTypeOptions = URL_TYPES
    return {
      data: CloneDeep(this.value),
    }
  },
  computed: {
    credentialSearchParams() {
      return {
        key: 'credential.profile.protocol',
        value: [this.$constants.HTTP_HTTPS],
      }
    },
  },
  watch: {
    'data.subType': function (newValue) {
      this.data = {
        ...this.data,
        credentials: undefined,
        ipType: newValue === this.$constants.CISCO_MERAKI ? 'OBJECT' : 'IP',
      }
      this.$refs.formRef.resetValidation()
    },
  },
  methods: {
    handleSubmit(command) {
      this.$emit('submit', {
        data: this.data,
        command,
      })
    },
    resetForm() {
      this.data = CloneDeep(this.value)
    },
  },
}
</script>
