<template>
  <CredentialProvider :search-params="credentialSearchParams">
    <FlotoForm ref="formRef" @submit="handleSubmit" @reset="resetForm">
      <div class="flex flex-col">
        <FlotoFormItem class="sticky-tab">
          <MTab v-if="isEditMode" :value="data.subType" class="sticky-tab">
            <MTabPane v-for="tab in tabTypes" :key="tab" disabled :tab="tab" />
          </MTab>
          <MTab v-else v-model="data.subType">
            <MTabPane v-for="tab in tabTypes" :key="tab" :tab="tab" />
          </MTab>
        </FlotoFormItem>
        <div class="mx-4 ml-0">
          <MRow>
            <MCol :size="3">
              <FlotoFormItem
                v-model="data.name"
                rules="required"
                placeholder="Must be unique"
                label="Discovery Profile Name"
                name="profile-name"
              />
            </MCol>
            <MCol :size="3">
              <FlotoFormItem
                v-model="data.ipAddress"
                label="IP/Host"
                placeholder="e.g. *********** or fd00::1"
                rules="required|ip"
                name="wireless-ip-address"
              />
            </MCol>
            <MCol :size="3">
              <FlotoFormItem
                label="Collectors"
                :info-tooltip="$message('collector_tooltip')"
              >
                <RpePicker id="rpe-picker" v-model="data.rpe" multiple />
              </FlotoFormItem>
            </MCol>
          </MRow>

          <MRow>
            <MCol :size="3">
              <FlotoFormItem id="group-picker" label="Groups" rules="required">
                <GroupPicker v-model="data.group" multiple />
              </FlotoFormItem>
            </MCol>
            <MCol v-if="data.subType === $constants.RUCKUS_WIRELESS" :size="3">
              <FlotoFormItem label="Controller Model" rules="required">
                <FlotoDropdownPicker
                  id="device-model"
                  v-model="data.controllerModel"
                  :options="controllerModelOptions"
                  class="w-full"
                  :searchable="false"
                  @change="handleControllerModelChange"
                />
              </FlotoFormItem>
            </MCol>
            <MCol :size="5">
              <FlotoFormItem
                id="credential-id"
                label="Credential Profiles"
                rules="required"
              >
                <CredentialPicker
                  v-model="data.credentials"
                  multiple
                  allow-create
                  :available-protocols="
                    ciscoAndAruba.includes(data.subType)
                      ? [$constants.SNMP_V12, $constants.SNMP_V3]
                      : [$constants.HTTP_HTTPS]
                  "
                  :default-form-data="
                    ciscoAndAruba.includes(data.subType)
                      ? { credentialProfileProtocol: $constants.SNMP_V12 }
                      : {
                          credentialProfileProtocol: $constants.HTTP_HTTPS,
                          authenticationType: 'basic',
                        }
                  "
                />
              </FlotoFormItem>
            </MCol>
          </MRow>
          <MRow>
            <MCol :size="6">
              <FlotoFormItem label="Tags">
                <LooseTags
                  v-model="data.tags"
                  variant="default"
                  placeholder="Tags"
                  rounded
                  :full-width="true"
                  always-text-mode
                  user-tag-only
                />
              </FlotoFormItem>
            </MCol>
          </MRow>
          <!-- <MDivider /> -->
          <MRow>
            <MCol>
              <h5 id="discovery-parameter-id" class="text-primary">
                Discovery Parameters of
                {{ data.subType }}
              </h5>
            </MCol>
          </MRow>
          <MRow>
            <MCol
              v-if="ciscoAndAruba.includes(data.subType) === false"
              :size="2"
            >
              <FlotoFormItem label="URL Type" rules="required">
                <MRadioGroup
                  v-model="data.urlType"
                  as-button
                  :options="urlTypeOptions"
                />
              </FlotoFormItem>
            </MCol>
            <MCol :size="3" class="material-input">
              <FlotoFormItem rules="required|port" label="Port">
                <MInputNumber
                  v-model="data.port"
                  :min="1"
                  :max="65535"
                  :precision="0"
                  name="port"
                />
              </FlotoFormItem>
            </MCol>
            <!-- <MCol :size="3" class="days-input-box">
              <FlotoFormItem
                v-model="data.timeout"
                rules="required|numeric|min_value:1"
                name="timeout"
                label="Timeout"
              >
                <template v-slot:suffix>
                  <span class="days">
                    Second
                  </span>
                </template>
              </FlotoFormItem>
            </MCol> -->
            <MCol :size="3">
              <FlotoFormItem label="Ping Check">
                <MSwitch
                  id="ping-check-id"
                  v-model="data.pingCheck"
                  checked-children="ON"
                  un-checked-children="OFF"
                />
              </FlotoFormItem>
            </MCol>
          </MRow>
          <NotificationElements v-model="data.notification" />
        </div>
      </div>
      <template v-slot:submit="{ submit, reset }">
        <MRow :gutter="0">
          <MCol :size="6">
            <MButton
              id="save-exit-btn-id"
              outline
              :loading="processing === 'exit'"
              :disabled="processing && processing !== 'exit'"
              @click="submit('exit')"
            >
              Save and Exit
            </MButton>
          </MCol>

          <MCol class="text-right mb-4">
            <MButton
              id="reset-btn-id"
              class="mr-2"
              variant="default"
              :disabled="processing ? true : false"
              @click="reset"
            >
              Reset
            </MButton>
            <MButton
              id="save-run-btn-id"
              :loading="processing === 'run'"
              :disabled="processing && processing !== 'run'"
              @click="submit('run')"
            >
              Save and Run
            </MButton>
          </MCol>
        </MRow>
        <MRow>
          <MCol :size="12">
            <span class="text-neutral"> For more information: </span>
            <a
              href="https://docs.motadata.com/motadata-aiops-docs/Adding%20and%20Managing%20Devices/Adding%20Servers%20for%20Monitoring"
              target="_blank"
              >Discovery Profile</a
            >
            <MIcon name="external-link" class="ml-1 text-primary" />
          </MCol>
        </MRow>
      </template>
    </FlotoForm>
  </CredentialProvider>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import CredentialPicker from '@components/data-picker/credential-picker.vue'
import CredentialProvider from '@components/data-provider/credential-provider.vue'
import LooseTags from '@components/loose-tags.vue'
import NotificationElements from './notification-elements.vue'
import { URL_TYPES } from '@data/monitor'

export default {
  name: 'WirelessForm',
  components: {
    CredentialPicker,
    CredentialProvider,
    NotificationElements,
    LooseTags,
  },
  props: {
    value: { type: Object, required: true },
    processing: { type: String, default: undefined },
    isEditMode: { type: Boolean, default: false },
  },
  data() {
    this.tabTypes = [
      this.$constants.CISCO_WIRELESS,
      this.$constants.ARUBA_WIRELESS,
      this.$constants.RUCKUS_WIRELESS,
    ]
    this.urlTypeOptions = URL_TYPES
    this.controllerModelOptions = [
      { key: 'SmartZone', value: 'SmartZone', text: 'SmartZone' },
      { key: 'ZoneDirector', value: 'ZoneDirector', text: 'ZoneDirector' },
    ]
    return {
      data: CloneDeep(this.value),
    }
  },
  computed: {
    ciscoAndAruba() {
      return [this.$constants.ARUBA_WIRELESS, this.$constants.CISCO_WIRELESS]
    },
    credentialSearchParams() {
      const subType = this.data.subType
      if (subType.indexOf('Cisco') >= 0 || subType.indexOf('Aruba') >= 0) {
        return {
          key: 'credential.profile.protocol',
          value: [this.$constants.SNMP_V12, this.$constants.SNMP_V3],
        }
      } else {
        return {
          key: 'credential.profile.protocol',
          value: [this.$constants.HTTP_HTTPS],
        }
      }
    },
  },
  watch: {
    'data.subType': function (newValue) {
      this.data = {
        ...this.data,
        credentials: undefined,
        port:
          newValue.indexOf('Cisco') >= 0 || newValue.indexOf('Aruba') >= 0
            ? 161
            : 443,
      }
      this.$refs.formRef.resetValidation()
    },
  },
  methods: {
    handleSubmit(command) {
      this.$emit('submit', {
        data: this.data,
        command,
      })
    },
    resetForm() {
      this.data = CloneDeep(this.value)
    },
  },
}
</script>
