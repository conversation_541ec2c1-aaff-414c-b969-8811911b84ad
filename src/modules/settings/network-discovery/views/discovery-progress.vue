<template>
  <AgentProvider>
    <FlotoContentLoader :loading="loading" :message="progressMessage">
      <template v-if="discovery.id">
        <ServerDiscoveryProgress
          v-if="
            [
              $constants.SERVER,
              $constants.NETWORK,
              $constants.WIRELESS,
              $constants.OTHER,
              $constants.SERVICE_CHECK,
              $constants.VIRTUALIZATION,
            ].indexOf(discovery.type) >= 0
          "
          :discovery="discovery"
          :discovery-owner="discoveryOwner"
          @abort="handleAbort"
          @next="handleNext"
        />
        <CloudDiscoveryPrgoress
          v-else-if="discovery.type === $constants.CLOUD"
          :discovery="discovery"
          :discovery-owner="discoveryOwner"
          @abort="handleAbort"
          @next="handleNext"
        />
        <GroupingDiscoveryProgress
          v-else-if="isCustomGroupingProgress"
          :discovery="discovery"
          :discovery-owner="discoveryOwner"
          @abort="handleAbort"
          @next="handleNext"
        />
      </template>
    </FlotoContentLoader>
  </AgentProvider>
</template>

<script>
import Omit from 'lodash/omit'
import Bus from '@utils/emitter'
import AgentProvider from '@components/data-provider/agent-provider.vue'
import {
  getDiscoveryProgressApi,
  abortDiscoveryApi,
} from '../network-discovery-profile-api'
import ServerDiscoveryProgress from '../components/discovery-progress/server.vue'
import CloudDiscoveryPrgoress from '../components/discovery-progress/cloud.vue'
import GroupingDiscoveryProgress from '../components/discovery-progress/grouping.vue'

export default {
  name: 'DiscoveryProgress',
  components: {
    ServerDiscoveryProgress,
    CloudDiscoveryPrgoress,
    GroupingDiscoveryProgress,
    AgentProvider,
  },
  data() {
    this.isDiscoveryCompleted = false
    return {
      loading: true,
      discoveryOwner: '',
      discovery: {
        id: +this.$route.params.id,
      },
      progressMessage: null,
    }
  },
  computed: {
    isCustomGroupingProgress() {
      return (
        this.discovery.type === this.$constants.HYPERCONVERGED_INFRASTRUCTURE ||
        this.discovery.type === this.$constants.SDN ||
        this.discovery.type === this.$constants.STORAGE ||
        this.discovery.type === this.$constants.CONTAINER_ORCHESTRATION
      )
    },
  },
  created() {
    const handler = (payload) => {
      if (payload.id !== this.discovery.id) {
        return
      }
      this.discoveryOwner = payload['user.name']
      this.progressMessage = payload.message
      // if got last message then mark discovery as completed.
      if (
        payload.message ===
        this.$currentModule.getConfig().DISCOVERY_SAVING_MESSAGE
      ) {
        this.progressMessage = payload.message
        this.isDiscoveryCompleted = true
        return
      }
      if (payload.status === this.$constants.EVENT_SUCCESS_STATUS) {
        // if discovery is completed.
        if (this.isDiscoveryCompleted) {
          this.handleNext()
          return
        }
        this.fetchDiscoveryProgress(this.$route.params.id)
      } else {
        if (payload.status === this.$constants.EVENT_FAIL_STATUS) {
          this.$errorNotification({
            message: 'Error',
            description: payload.message,
          })
          this.$router.replace(
            this.$currentModule.getRoute('discovery-profiles', undefined, {
              query: this.$route.query,
            })
          )
        }
      }
    }
    const progressHandler = (payload) => {
      if (this.loading && payload.id === this.discovery.id) {
        this.loading = false
        Bus.$off(
          this.$currentModule.getConfig().DISCOVERY_OBJECT_PROGRESS_EVENT,
          progressHandler
        )
        this.$nextTick(() => {
          Bus.$emit(
            this.$currentModule.getConfig().DISCOVERY_OBJECT_PROGRESS_EVENT,
            payload
          )
        })
      }
    }
    const probeHandler = (payload) => {
      if (!this.loading) {
        return
      }
      if (payload.id === this.discovery.id) {
        this.loading = false
        Bus.$off(
          this.$currentModule.getConfig().DISCOVERY_PROBE_EVENT,
          probeHandler
        )
        this.$nextTick(() => {
          Bus.$emit(
            this.$currentModule.getConfig().DISCOVERY_PROBE_EVENT,
            payload
          )
        })
      }
    }
    Bus.$on(
      this.$currentModule.getConfig().DISCOVERY_OBJECT_PROGRESS_EVENT,
      progressHandler
    )
    Bus.$on(
      this.$currentModule.getConfig().DISCOVERY_PREPARATION_EVENT,
      handler
    )
    Bus.$on(this.$currentModule.getConfig().DISCOVERY_PROBE_EVENT, probeHandler)
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(
        this.$currentModule.getConfig().DISCOVERY_PREPARATION_EVENT,
        handler
      )
      Bus.$off(
        this.$currentModule.getConfig().DISCOVERY_OBJECT_PROGRESS_EVENT,
        progressHandler
      )
      Bus.$off(
        this.$currentModule.getConfig().DISCOVERY_PROBE_EVENT,
        probeHandler
      )
    })
    this.fetchDiscoveryProgress(this.$route.params.id)
  },
  methods: {
    fetchDiscoveryProgress(id) {
      let promise
      promise = getDiscoveryProgressApi(id)
      promise.then((data) => {
        this.discovery = Omit(data, ['monitors'])
        if (data.isInProgress) {
          if (data.monitor || (data.monitors || []).length) {
            this.loading = false
            if ((data.monitors || []).length) {
              this.$nextTick(() => {
                Bus.$emit(
                  this.$currentModule.getConfig().DISCOVERY_PROBE_EVENT,
                  {
                    result: data.monitors,
                    translated: true,
                    id: this.discovery.id,
                  }
                )
              })
            }
          }
        } else {
          if (!this.$route.params.triggeredRun) {
            this.$router.push(
              this.$currentModule.getRoute('discovery-profiles', undefined, {
                query: this.$route.query,
              })
            )
          }
        }
      })
    },
    handleAbort() {
      abortDiscoveryApi(this.discovery.id).then((data) => {
        this.$router.replace(
          this.$currentModule.getRoute('discovery-profiles', undefined, {
            query: this.$route.query,
          })
        )
      })
    },
    handleNext() {
      this.$router.replace(
        this.$currentModule.getRoute('discovery-result', {
          params: { id: this.discovery.id },
          query: this.$route.query,
        })
      )
    },
  },
}
</script>
