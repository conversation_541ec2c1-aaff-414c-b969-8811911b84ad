<template>
  <RpeProvider>
    <CredentialProvider>
      <FlotoContentLoader :loading="loading">
        <template v-if="!loading">
          <ServerDiscoveryResult
            v-if="
              [
                $constants.SERVER,
                $constants.NETWORK,
                $constants.SDN,
                $constants.WIRELESS,
                $constants.VIRTUALIZATION,
                $constants.OTHER,
                $constants.HYPERCONVERGED_INFRASTRUCTURE,
                $constants.STORAGE,
                $constants.CONTAINER_ORCHESTRATION,
              ].indexOf(discovery.type) >= 0
            "
            :discovery="discovery"
            :guid="guid"
            :is-schedule-log="$route.params.scheduleId ? true : false"
            :update-monitor="updateMonitor"
            @show-provision-result="triggerShowProvisioningResult"
            @update-monitor="replaceMonitor"
          />
          <CloudDiscoveryResult
            v-else-if="discovery.type === $constants.CLOUD"
            :guid="guid"
            :is-schedule-log="$route.params.scheduleId ? true : false"
            :discovery="discovery"
            @show-provision-result="triggerShowProvisioningResult"
          />
          <ServiceDiscoveryResult
            v-else-if="discovery.type === $constants.SERVICE_CHECK"
            :guid="guid"
            :is-schedule-log="$route.params.scheduleId ? true : false"
            :discovery="discovery"
            :update-monitor="updateMonitor"
            @show-provision-result="triggerShowProvisioningResult"
            @update-monitor="replaceMonitor"
          />
        </template>
        <DiscoveryProvisioningResult
          v-if="showProvisioningResult"
          :discovery-id="discovery.id"
          :guid="guid"
          :event="$constants.DISCOVERY_OBJECT_PROVISION_EVENT"
          v-bind="discoverySpecificProvisionProps"
          @close="handleCloseProvisioningResult"
        />
      </FlotoContentLoader>
    </CredentialProvider>
  </RpeProvider>
</template>

<script>
import FindIndex from 'lodash/findIndex'
import { generateId } from '@utils/id'
import Bus from '@utils/emitter'
import RpeProvider from '@components/data-provider/rpe-provider.vue'
import CredentialProvider from '@components/data-provider/credential-provider.vue'
import {
  getDiscoveryResultApi,
  getDiscoveryScheduleResultApi,
  updateMonitorApi,
} from '../network-discovery-profile-api'
import ServerDiscoveryResult from '../components/discovery-result/server.vue'
import CloudDiscoveryResult from '../components/discovery-result/cloud.vue'
import ServiceDiscoveryResult from '../components/discovery-result/service.vue'
import DiscoveryProvisioningResult from '../components/discovery-provisioning-result.vue'

export default {
  name: 'DiscoveryResult',
  components: {
    ServerDiscoveryResult,
    CloudDiscoveryResult,
    ServiceDiscoveryResult,
    DiscoveryProvisioningResult,
    RpeProvider,
    CredentialProvider,
  },
  data() {
    return {
      loading: true,
      discovery: {},
      guid: generateId(),
      showProvisioningResult: false,
    }
  },
  computed: {
    discoverySpecificProvisionProps() {
      const type = this.discovery.type
      if (type === this.$constants.CLOUD) {
        return {
          event: 'cloud.instance.provision',
          title: 'Cloud Instances',
        }
      }
      return {}
    },
  },
  created() {
    if (this.$route.params.scheduleId) {
      this.fetchDiscoveryScheduleResult(
        this.$route.params.id,
        this.$route.params.scheduleId
      )
    } else {
      this.fetchDiscoveryResult(this.$route.params.id)
    }
  },
  methods: {
    replaceMonitor(data) {
      const index = FindIndex(this.discovery.monitors, { id: data.id })
      if (index === -1) {
        return
      }
      this.discovery = {
        ...this.discovery,
        monitors: [
          ...this.discovery.monitors.slice(0, index),
          { ...this.discovery.monitors[index], ...data },
          ...this.discovery.monitors.slice(index + 1),
        ],
      }
    },
    updateMonitor(data) {
      return updateMonitorApi(this.discovery.id, data)
    },
    fetchDiscoveryScheduleResult(id, scheduleId) {
      getDiscoveryScheduleResultApi(id, scheduleId).then((data) => {
        this.discovery = Object.freeze(data)
        this.loading = false
      })
    },
    fetchDiscoveryResult(id) {
      getDiscoveryResultApi(id)
        .then((data) => {
          this.discovery = Object.freeze(data)
          this.$nextTick(() => {
            this.loading = false
          })
        })
        .catch((e) => {
          if (e.response && e.response.status === 400) {
            this.$router.push(
              this.$currentModule.getRoute('discovery-progress', {
                params: this.$route.params,
              })
            )
          }
        })
    },
    triggerShowProvisioningResult() {
      this.showProvisioningResult = true
    },
    handleCloseProvisioningResult() {
      this.showProvisioningResult = false
      // this.loading = true
      // this.fetchDiscoveryResult(this.discovery.id)
      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_EVENT_HEART_BEAT,
        'event.context': {},
      })
      this.$router.push(
        this.$currentModule.getRoute('discovery-profiles', undefined, {
          query: this.$route.query,
        })
      )
    },
  },
}
</script>
