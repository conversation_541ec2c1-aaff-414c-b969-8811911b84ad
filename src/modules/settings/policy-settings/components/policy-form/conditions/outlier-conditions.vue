<template>
  <MonitorProvider :search-params="searchParams">
    <CounterPovider
      :search-params="counterSearchParams"
      :data-type="allowedCounterDataTypes"
    >
      <div>
        <MRow>
          <MCol :size="6">
            <MonitorSelection v-model="monitorSelectionInfo" />
          </MCol>
          <MCol :size="6">
            <FlotoFormItem id="instance" rules="required">
              <CounterPicker
                v-model="instance"
                name="group"
                placeholder="Select Metric"
              />
            </FlotoFormItem>
          </MCol>
          <MCol :size="4">
            <FlotoFormItem label="Severity">
              <MRadioGroup
                id="severity"
                v-model="severity"
                as-button
                :options="severityOptions"
              />
            </FlotoFormItem>
          </MCol>
        </MRow>
        <MRow>
          <MCol :size="2">
            <FlotoFormItem
              label="Tolerance"
              rules="required"
              :info-tooltip="toleranceTooltip"
            >
              <FlotoDropdownPicker
                id="tolerance"
                v-model="tolerance"
                :options="toleranceOptions"
              />
            </FlotoFormItem>
          </MCol>
          <MCol :size="2">
            <FlotoFormItem label="Algorithm" rules="required">
              <FlotoDropdownPicker
                id="algorithm"
                v-model="algorithm"
                :options="algorithmOptions"
              />
            </FlotoFormItem>
          </MCol>
          <MCol :size="2">
            <FlotoFormItem rules="required" label="For the last">
              <FlotoDropdownPicker
                id="breachTime"
                v-model="breachTime"
                :allow-clear="false"
                :options="breachTimeOptions"
              />
            </FlotoFormItem>
          </MCol>
          <MCol :size="2">
            <FlotoFormItem label="Auto Clear">
              <AutoClearPolicyPicker
                id="auto-clear-policy"
                v-model="autoClearPolicy"
              />
            </FlotoFormItem>
          </MCol>
        </MRow>
      </div>
    </CounterPovider>
  </MonitorProvider>
</template>

<script>
import Range from 'lodash/range'
import Pick from 'lodash/pick'
import MonitorSelection from '@components/widgets/monitor-or-group-selection.vue'
import CounterPicker from '@components/data-picker/counter-picker.vue'
import CounterPovider from '@components/data-provider/counter-provider.vue'
import AutoClearPolicyPicker from '@components/data-picker/auto-clear-policy-picker.vue'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import getMessage from '@constants/messages'

export default {
  name: 'OutlierConditions',
  components: {
    MonitorSelection,
    AutoClearPolicyPicker,
    CounterPovider,
    CounterPicker,
    MonitorProvider,
  },
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default() {
        return undefined
      },
    },
    isEditMode: { type: Boolean, default: false },
  },
  data() {
    this.allowedCounterDataTypes = ['numeric']
    this.severityOptions = [
      { value: this.$constants.WARNING, label: 'Warning' },
      { value: this.$constants.CRITICAL, label: 'Critical' },
      { value: this.$constants.MAJOR, label: 'Major' },
    ]

    this.breachTimeOptions = [
      { key: 5 * 60, text: '5 min' },
      { key: 10 * 60, text: '10 min' },
      { key: 15 * 60, text: '15 min' },
      { key: 30 * 60, text: '30 min' },
      { key: 60 * 60, text: '1 hour' },
      { key: 2 * 60 * 60, text: '2 hour' },
      { key: 4 * 60 * 60, text: '4 hour' },
      { key: 8 * 60 * 60, text: '8 hour' },
      { key: 12 * 60 * 60, text: '12 hour' },
      { key: 24 * 60 * 60, text: '24 hour' },
    ]
    this.toleranceOptions = Range(0, 6, 0.5).map((i) => ({
      key: `${i.toFixed(1)}`,
      text: `${i.toFixed(1)}`,
    }))
    this.algorithmOptions = ['DBSCAN', 'MAD', 'SCALEDDBSCAN', 'SCALEDMAD'].map(
      (i) => ({ key: i, text: i })
    )
    return {
      toleranceTooltip: getMessage('outlier_tolerance'),
    }
  },
  computed: {
    searchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.OTHER,
          this.$constants.CLOUD,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.SDN,
          this.$constants.STORAGE,
          this.$constants.CONTAINER_ORCHESTRATION,
        ],
      }
    },
    counterSearchParams() {
      const monitorInfo = this.monitorSelectionInfo
      if ((monitorInfo.entities || []).length > 0) {
        return {
          'string.counter.required': 'no',
          'group.type': 'metric',
          'entity.type': this.monitorSelectionInfo.entityType,
          entities: this.monitorSelectionInfo.entities,
        }
      }
      return undefined
    },
    monitorSelectionInfo: {
      get() {
        return Pick(this.value || {}, ['entities', 'entityType'])
      },
      set(v) {
        this.$emit('change', { ...(this.value || {}), ...v })
      },
    },
    instance: {
      get() {
        if (this.value.instance) {
          return (this.value || {}).instance['key']
        }
        return undefined
      },
      set(instance) {
        this.$emit('change', { ...(this.value || {}), instance })
      },
    },
    severity: {
      get() {
        return (this.value || {}).severity
      },
      set(severity) {
        this.$emit('change', { ...(this.value || {}), severity })
      },
    },
    autoClearPolicy: {
      get() {
        return (this.value || {}).autoClearPolicy
      },
      set(autoClearPolicy) {
        this.$emit('change', { ...(this.value || {}), autoClearPolicy })
      },
    },
    tolerance: {
      get() {
        return (this.value || {}).tolerance
      },
      set(tolerance) {
        this.$emit('change', { ...(this.value || {}), tolerance })
      },
    },
    algorithm: {
      get() {
        return (this.value || {}).algorithm
      },
      set(algorithm) {
        this.$emit('change', { ...(this.value || {}), algorithm })
      },
    },
    breachTime: {
      get() {
        return (this.value || {}).breachTime
      },
      set(breachTime) {
        this.$emit('change', { ...(this.value || {}), breachTime })
      },
    },
  },
}
</script>
