<template>
  <MonitorProvider :search-params="searchParams">
    <CounterPovider
      :data-type="allowedCounterDataTypes"
      :search-params="counterSearchParams"
    >
      <div>
        <MRow>
          <MCol v-if="policy.subType === 'Metric'" :size="6">
            <FlotoFormItem id="instance" rules="required" label="Counter">
              <CounterPicker
                :value="(counter || {}).key"
                name="group"
                placeholder="Select Metric"
                @change="counter = $event"
              />
            </FlotoFormItem>
          </MCol>
          <MCol :size="5">
            <MonitorSelection
              :key="policy.subType"
              v-model="monitorSelectionInfo"
              :counter="{ key: (counter || {}).key }"
              :entity-options="monitorSelectionTypes[policy.subType]"
              :excluded-entity-type-options="excludedEntityTypes"
              show-label
            />
          </MCol>
          <MCol v-if="policy.subType !== 'Metric'" :size="12">
            <LogFlowCounterSelection
              v-model="condition"
              :counter-data-types="counterDataTypes"
            />
          </MCol>
          <MCol v-if="counter" :size="10" class="mb-3">
            <InstanceMetricProvider
              v-if="counter.instanceType"
              :instance="counter.instanceType"
              :group-type="
                policy.subType.toLowerCase() === 'monitor'
                  ? 'metric'
                  : policy.subType.toLowerCase()
              "
              :target="counter.instanceType && monitorSelectionInfo.target"
            >
              <FiltersContainer
                v-model="filters"
                :max-pre-groups="1"
                :excluded-tabs="filtersExcludedTabs"
                :use-instance-grid="showFilterGrid"
                placeholder="Filters"
              />
            </InstanceMetricProvider>

            <!-- :search-params="filterSearchParams" -->
          </MCol>
          <!-- <MCol :size="6">
              <MonitorSelection
                :key="policy.subType"
                v-model="monitorSelectionInfo"
                :entity-options="monitorSelectionTypes[policy.subType]"
                :source-type="policy.subType.toLowerCase()"
              />
            </MCol>
            <MCol v-if="policy.subType !== 'Metric'" :size="12">
              <LogFlowCounterSelection
                v-model="condition"
                :counter-data-types="counterDataTypes"
              />
            </MCol>
            <MCol v-if="policy.subType === 'Metric'" :size="5">
              <FlotoFormItem id="instance" rules="required">
                <CounterPicker
                  v-model="instance"
                  name="group"
                  placeholder="Select Metric"
                  :data-type="allowedCounterDataTypes"
                />
              </FlotoFormItem>
            </MCol>
            <MCol v-if="showFilterBtn" :size="10" class="mb-3">
              <FiltersContainer
                v-model="filters"
                :max-pre-groups="1"
                :excluded-tabs="filtersExcludedTabs"
                :use-instance-grid="showFilterGrid"
                :use-any-field-type="policy.subType !== 'Metric'"
              />
            </MCol> -->
        </MRow>
        <MRow v-if="policy.subType === 'Metric'">
          <MCol :size="6" class="mb-3">
            <MetricConditionsSeverity
              id="critical-severity"
              v-model="critical"
              :percent-value="true"
              :operators="annomalyConditions"
            />
          </MCol>
          <MCol :size="6" class="mb-3">
            <MetricConditionsSeverity
              id="major-severity"
              v-model="major"
              :percent-value="true"
              :operators="annomalyConditions"
            />
          </MCol>
          <MCol :size="6" class="mb-3">
            <MetricConditionsSeverity
              id="warning-severity"
              v-model="warning"
              :percent-value="true"
              :operators="annomalyConditions"
            />
          </MCol>
        </MRow>

        <MRow v-if="policy.subType !== 'Metric'" class="my-2">
          <MCol :size="3">
            <FlotoFormItem
              label="Deviation"
              rules="required"
              :info-tooltip="deviationTooltip"
            >
              <FlotoDropdownPicker
                id="deviation"
                v-model="deviation"
                as-input
                :options="deviationOptions"
              />
            </FlotoFormItem>
          </MCol>
          <MCol auto-size>
            <FlotoFormItem label="Algorithm" :info-tooltip="algorithmTooltip">
              <MRadioGroup
                id="algorithm"
                v-model="algorithm"
                as-button
                :options="algorithmOptions"
              />
            </FlotoFormItem>
          </MCol>
          <MCol v-if="algorithm === 'Agile'">
            <FlotoFormItem label="Seasonality" rules="required">
              <MRadioGroup
                id="seasonality"
                v-model="seasonality"
                as-button
                :options="seasonalityOptions"
              />
            </FlotoFormItem>
          </MCol>
        </MRow>

        <MRow class="my-2 flex items-center material-input">
          <template v-if="policy.subType !== 'Metric'">
            <MCol auto-size>
              <span class="opacity-50"> For the last </span>
            </MCol>
            <MCol :size="2">
              <FlotoDropdownPicker
                id="breachTime"
                v-model="breachTime"
                as-input
                :allow-clear="false"
                :options="breachTimeOptions"
              />
            </MCol>
            <MCol
              v-if="policy.subType !== 'Metric'"
              :size="3"
              class="flex items-center"
            >
              <span class="opacity-50 mr-2"> Severity </span>
              <MRadioGroup
                v-model="severity"
                as-button
                :options="severityOptions"
              />
            </MCol>
            <span class="mx-2 label-strip opacity-50">Auto Clear</span>
            <MCol :size="4">
              <AutoClearPolicyPicker
                id="auto-clear-policy"
                v-model="autoClearPolicy"
              />
            </MCol>
            <!-- <MCol
              v-if="autoClearPolicy === 0 && policy.subType === 'Metric'"
              class="ml-auto"
              :size="4"
            >
              <MetricConditionsSeverity
                id="clear-severity"
                v-model="clear"
                :is-percentage-show="true"
                :required="true"
                :operators="annomalyConditions"

              />
            </MCol> -->
          </template>
          <template v-else>
            <MCol :size="3">
              <FlotoFormItem label="Sample Lookup">
                <FlotoDropdownPicker
                  id="sample-lookup"
                  v-model="sampleLookup"
                  :options="sampleLookupOptions"
                  as-input
                />
              </FlotoFormItem>
            </MCol>
            <MCol :size="3">
              <FlotoFormItem label="Auto Clear">
                <AutoClearPolicyPicker
                  id="auto-clear-policy"
                  v-model="autoClearPolicy"
                  as-input
                />
              </FlotoFormItem>
            </MCol>
          </template>
        </MRow>

        <!-- <MRow v-if="policy.subType === 'Metric'" class="my-2">
          <MCol id="info-message" class="text-neutral-light">
            100% of the values in the last 5 mins are above the bounds
          </MCol>

          <MRow />

        -->
      </div>
    </CounterPovider>
  </MonitorProvider>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import Pick from 'lodash/pick'
import Range from 'lodash/range'
import CounterPicker from '@components/data-picker/counter-picker.vue'
import CounterPovider from '@components/data-provider/counter-provider.vue'
import MonitorSelection from '@components/widgets/monitor-or-group-selection.vue'
import MetricConditionsSeverity from './metric-conditions-severity.vue'
import AutoClearPolicyPicker from '@components/data-picker/auto-clear-policy-picker.vue'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import getMessage from '@constants/messages'
import InstanceMetricProvider from '@components/data-provider/instance-metric-provider.vue'
import FiltersContainer from '@components/filters/filters-container.vue'
import { FILTER_CONDITION_DEFAULT_DATA } from '@components/widgets/constants'
import LogFlowCounterSelection from './log-flow-counter-selection.vue'
import Config from '../../../config'

export default {
  name: 'AnomalyConditions',
  components: {
    MonitorSelection,
    MetricConditionsSeverity,
    AutoClearPolicyPicker,
    CounterPovider,
    CounterPicker,
    MonitorProvider,
    InstanceMetricProvider,
    FiltersContainer,
    LogFlowCounterSelection,
  },
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default() {
        return undefined
      },
    },
    policy: {
      type: Object,
      default() {
        return undefined
      },
    },
    isEditMode: { type: Boolean, default: false },
  },
  data() {
    this.filtersExcludedTabs = ['post']
    this.counterDataTypes = ['numeric']
    this.monitorSelectionTypes = {
      Log: [
        { key: 'event.source', text: 'Source Host', inputType: 'source' },
        // {
        //   key: 'source.plugin',
        //   text: 'Source Plugin',
        //   inputType: 'source.plugin',
        // },
        {
          key: 'event.source.type',
          text: 'Source Type',
          inputType: 'source.type',
        },
        { key: 'Group', text: 'Group', inputType: 'group' },
      ],
      Flow: [
        { key: 'event.source', text: 'Source Host', inputType: 'source' },
        { key: 'Group', text: 'Group', inputType: 'group' },
      ],
    }
    this.severityOptions = [
      { value: 'Warning', label: 'Warning' },
      { value: 'Critical', label: 'Critical' },
      { value: 'Major', label: 'Major' },
    ]

    this.annomalyConditions = [
      { key: 'above', text: 'Above' },
      { key: 'below', text: 'Below' },
      // { key: 'above or below', text: 'Above or Below' },
    ]
    this.seasonalityOptions = [
      { value: 'Hourly', text: 'Hourly' },
      { value: 'Daily', text: 'Daily' },
      { value: 'Weekly', text: 'Weekly' },
      { value: 'Monthly', text: 'Monthly' },
    ]
    this.algorithmOptions = [
      { value: 'Basic', text: 'Basic' },
      { value: 'Agile', text: 'Agile' },
    ]
    this.deviationOptions = Range(1, 6).map((i) => ({ key: i, text: i }))

    this.excludedEntityTypes = []

    this.sampleLookupOptions = [
      { key: -1, text: 'Default' },
      ...Range(0, 35, 5)
        .map((i) => ({ key: i, text: i }))
        .filter((o) => o.key),
    ]
    return {
      algorithmTooltip: getMessage('anomaly_algorithm'),
      deviationTooltip: getMessage('anomaly_deviation'),
      showFilterBtn: false,
      counterOptions: undefined,
      floatValue: false,
    }
  },

  computed: {
    condition: {
      get() {
        return (this.value || {}).condition
      },
      set(condition) {
        this.$emit('change', { ...(this.value || {}), condition })
      },
    },
    filterSearchParams() {
      if (this.value.instance && this.value.instance.instanceType) {
        return {
          'group.type': 'metric',
          'instance.type': this.value.instance['instanceType'],
          'entity.type': this.monitorSelectionInfo.entityType,
          entities: this.monitorSelectionInfo.entities,
        }
      }
      return undefined
    },
    allowedCounterDataTypes() {
      return ['numeric']
    },
    breachTimeOptions() {
      if (this.seasonality === 'Hourly' && this.algorithm === 'Agile') {
        return [
          { key: 1 * 60, text: '1 min' },
          { key: 5 * 60, text: '5 min' },
          { key: 10 * 60, text: '10 min' },
          { key: 15 * 60, text: '15 min' },
          { key: 30 * 60, text: '30 min' },
          { key: 45 * 60, text: '45 min' },
          { key: 60 * 60, text: '1 hour' },
        ]
      } else if (this.algorithm === 'Agile') {
        return [
          { key: 5 * 60, text: '5 min' },
          { key: 10 * 60, text: '10 min' },
          { key: 15 * 60, text: '15 min' },
          { key: 30 * 60, text: '30 min' },
          { key: 45 * 60, text: '45 min' },
          { key: 60 * 60, text: '1 hour' },
          { key: 2 * 60 * 60, text: '2 hour' },
          { key: 6 * 60 * 60, text: '6 hour' },
          { key: 12 * 60 * 60, text: '12 hour' },
          { key: 24 * 60 * 60, text: '24 hour' },
        ]
      }
      return [
        { key: 5 * 60, text: '5 min' },
        { key: 10 * 60, text: '10 min' },
        { key: 15 * 60, text: '15 min' },
        { key: 30 * 60, text: '30 min' },
        { key: 60 * 60, text: '1 hour' },
        { key: 2 * 60 * 60, text: '2 hour' },
        { key: 4 * 60 * 60, text: '4 hour' },
        { key: 8 * 60 * 60, text: '8 hour' },
        { key: 12 * 60 * 60, text: '12 hour' },
        { key: 24 * 60 * 60, text: '24 hour' },
      ]
    },
    // showLogAndFlowFilter() {
    //   if (
    //     this.anomalyInnerConditions['groups'][0]['conditions'][0].operand !==
    //     undefined
    //   ) {
    //     return true
    //   }
    //   return false
    // },
    searchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.OTHER,
          this.$constants.CLOUD,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.SDN,
          this.$constants.STORAGE,
          this.$constants.CONTAINER_ORCHESTRATION,
        ],
      }
    },
    showFilterGrid() {
      if (this.policy.subType === Config.METRIC) {
        return ((this.monitorSelectionInfo || {}).entities || []).length <= 1
      }
      return false
    },
    counterSearchParams() {
      if (this.policy.subType === Config.METRIC) {
        // const monitorInfo = this.monitorSelectionInfo || {}
        // if ((monitorInfo.entities || []).length > 0) {
        return {
          'string.counter.required': 'no',
          'visualization.group.type': Config.METRIC,
          // 'entity.type': this.monitorSelectionInfo.entityType,
          // entities: this.monitorSelectionInfo.entities,
          // }
        }
      } else {
        // const target = this.monitorSelectionInfo
        // if (target && (target.entities || []).length > 0) {
        return {
          'string.counter.required': 'no',
          'visualization.group.type': this.policy.subType.toLowerCase(),
          // 'entity.type': target.entityType,
          // entities: target.entities,
        }
        // }
      }
    },
    anomalyInnerConditions: {
      get() {
        return (this.value || {}).anomalyInnerConditions
      },
      set(anomalyInnerConditions) {
        this.$emit('change', { ...(this.value || {}), anomalyInnerConditions })
      },
    },
    groups: {
      get() {
        return (this.value || {}).groups
      },
      set(groups) {
        this.$emit('change', { ...(this.value || {}), groups })
      },
    },
    filters: {
      get() {
        return (this.value || {}).filters
      },
      set(filters) {
        this.$emit('change', { ...(this.value || {}), filters })
      },
    },
    monitorSelectionInfo: {
      get() {
        return Pick(this.value || {}, ['entityType', 'entities'])
      },
      set(v) {
        this.$emit('change', { ...(this.value || {}), ...v })
      },
    },
    instance: {
      get() {
        if (this.value.instance) {
          return (this.value || {}).instance['key']
        }
        return undefined
      },
      set(instance) {
        this.$emit('change', { ...(this.value || {}), instance })
      },
    },
    critical: {
      get() {
        return (this.value || {}).critical
      },
      set(critical) {
        this.$emit('change', { ...(this.value || {}), critical })
      },
    },
    major: {
      get() {
        return (this.value || {}).major
      },
      set(major) {
        this.$emit('change', { ...(this.value || {}), major })
      },
    },
    warning: {
      get() {
        return (this.value || {}).warning
      },
      set(warning) {
        this.$emit('change', { ...(this.value || {}), warning })
      },
    },
    // clear: {
    //   get() {
    //     return (this.value || {}).clear
    //   },
    //   set(clear) {
    //     this.$emit('change', { ...(this.value || {}), clear })
    //   },
    // },
    autoClearPolicy: {
      get() {
        return (this.value || {}).autoClearPolicy
      },
      set(autoClearPolicy) {
        this.$emit('change', { ...(this.value || {}), autoClearPolicy })
      },
    },
    deviation: {
      get() {
        return (this.value || {}).deviation
      },
      set(deviation) {
        this.$emit('change', {
          ...(this.value || {}),
          deviation,
        })
      },
    },
    algorithm: {
      get() {
        return (this.value || {}).algorithm
      },
      set(algorithm) {
        this.$emit('change', {
          ...(this.value || {}),
          algorithm,
        })
      },
    },
    seasonality: {
      get() {
        return (this.value || {}).seasonality
      },
      set(seasonality) {
        this.$emit('change', {
          ...(this.value || {}),
          seasonality,
        })
      },
    },
    severity: {
      get() {
        return (this.value || {}).severity
      },
      set(severity) {
        this.$emit('change', { ...(this.value || {}), severity })
      },
    },
    breachTime: {
      get() {
        return (this.value || {}).breachTime
      },
      set(breachTime) {
        this.$emit('change', {
          ...(this.value || {}),
          breachTime,
        })
      },
    },
    counter: {
      get() {
        if (this.value.counter) {
          return (this.value || {}).counter
        }
        return undefined
      },
      set(counter) {
        if (
          counter !== undefined &&
          (counter.dataType || []).includes('string')
        ) {
          this.floatValue = false
        } else {
          this.isStringInstance = true
        }

        this.$emit('change', { ...(this.value || {}), counter })
      },
    },
    sampleLookup: {
      get() {
        return (this.value || {}).sampleLookup
      },
      set(sampleLookup) {
        this.$emit('change', {
          ...(this.value || {}),
          sampleLookup,
        })
      },
    },
  },
  watch: {
    'policy.subType'(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.resetFiltersForm()
      }
    },
    instance: {
      immediate: true,
      handler(newVal, oldVal) {
        if (oldVal) {
          this.resetFiltersForm()
        }
        if (
          this.value.instance !== undefined &&
          this.value.instance['instanceType'] !== undefined
        ) {
          this.showFilterBtn = true
        } else {
          this.showFilterBtn = false
        }
      },
    },
  },
  methods: {
    resetFiltersForm() {
      this.filters = {
        pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
      }
    },
  },
}
</script>
