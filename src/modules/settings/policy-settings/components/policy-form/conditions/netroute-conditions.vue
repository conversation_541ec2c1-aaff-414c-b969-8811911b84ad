<template>
  <NetrouteProvider>
    <MonitorProvider :search-params="searchParams">
      <div>
        <MRow>
          <MCol :size="3">
            <FlotoFormItem rules="required" label="Route Evaluation Type">
              <MRadioGroup
                v-model="routeEvaluationType"
                as-button
                :options="[
                  {
                    value: $constants.SOURCE_TO_DESTINATION,
                    label: 'Source to destination',
                  },
                  { value: $constants.HOP_TO_HOP, label: 'Hop to Hop' },
                ]"
                :disabled="isEditMode"
              />
              <slot />
            </FlotoFormItem>
          </MCol>
        </MRow>
        <MRow>
          <MCol :size="6">
            <FlotoFormItem
              rules="required"
              label="Counter"
              :class="{ 'disabled-bordered-dropdown': isEditMode }"
            >
              <FlotoDropdownPicker
                v-model="metric"
                placeholder="Select Metric"
                allow-clear
                :options="filteredCounterOptions"
                :disabled="isEditMode"
              />
            </FlotoFormItem>
          </MCol>
          <MCol :size="3">
            <FlotoFormItem label="Source Filter">
              <FlotoDropdownPicker
                v-model="entityType"
                allow-clear
                :options="filterOptions"
              />
            </FlotoFormItem>
          </MCol>
          <MCol :size="3">
            <FlotoFormItem
              :label="
                entityType === 'NetRoute'
                  ? 'Source NetRoute'
                  : entityType === 'Tag'
                  ? 'Source Tag'
                  : 'Source'
              "
              :class="{
                'disabled-bordered-dropdown': shouldDisabledEntityOptions,
              }"
            >
              <NetroutePicker
                v-if="entityType === 'NetRoute'"
                v-model="entities"
                multiple
              />
              <template v-if="entityType === 'Tag'">
                <LooseTags
                  v-if="!shouldDisabledEntityOptions"
                  v-model="entities"
                  title="Tag"
                  :tag-type="$currentModule.getConfig().NETROUTE"
                  as-dropdown
                  :disabled="shouldDisabledEntityOptions"
                />
                <FlotoDropdownPicker
                  v-else
                  placeholder=" "
                  :options="[]"
                  :disabled="shouldDisabledEntityOptions"
                />
              </template>
            </FlotoFormItem>
          </MCol>
        </MRow>

        <MRow v-if="showMetricConditionsSeverity">
          <MCol :size="6" class="mb-3">
            <MetricConditionsSeverity
              id="critical-severity"
              v-model="critical"
              :counter="counter"
              :float-value="floatValue"
              :operators="netrouteConditions"
            />
          </MCol>
          <MCol :size="6" class="mb-3">
            <MetricConditionsSeverity
              id="major-severity"
              v-model="major"
              :counter="counter"
              :float-value="floatValue"
              :operators="netrouteConditions"
            />
          </MCol>
          <MCol :size="6" class="mb-3">
            <MetricConditionsSeverity
              id="warning-severity"
              v-model="warning"
              :counter="counter"
              :float-value="floatValue"
              :operators="netrouteConditions"
            />
          </MCol>
        </MRow>
        <MRow v-if="routeEvaluationType === $constants.HOP_TO_HOP">
          <MCol :size="6">
            <FlotoFormItem
              validation-label="Operator"
              rules="required"
              label="Operator"
            >
              <FlotoDropdownPicker
                v-model="operator"
                :options="operatorOptions"
                placeholder="Select Operator"
              />
            </FlotoFormItem>
          </MCol>
          <MCol class="flex" :size="4">
            <div class="flex-1 flex self-end" style="flex-shrink: 0">
              <FlotoFormItem
                v-model="conditionValue"
                validation-label="Value"
                placeholder="Value"
                class="ml-1"
                rules="required"
              />
            </div>
          </MCol>
          <MCol class="flex" :size="4">
            <FlotoFormItem label="Severity" rules="required">
              <MRadioGroup
                v-model="severity"
                as-button
                :options="severityOptions"
              />
            </FlotoFormItem>
          </MCol>
        </MRow>
      </div>
    </MonitorProvider>
  </NetrouteProvider>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import MetricConditionsSeverity from './metric-conditions-severity.vue'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import { getOperatorOptions } from '@/src/components/widgets/helper'
import { netrouteConditions } from '../../../helpers/dropdown-options'
import NetrouteProvider from '@components/data-provider/netroute-provider.vue'
import NetroutePicker from '@components/data-picker/netroute-picker.vue'
import LooseTags from '@components/loose-tags.vue'

export default {
  name: 'NetRouteConditions',
  components: {
    MetricConditionsSeverity,
    MonitorProvider,
    NetrouteProvider,
    NetroutePicker,
    LooseTags,
  },
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default() {
        return undefined
      },
    },
    isEditMode: { type: Boolean, default: false },
  },
  data() {
    this.netrouteConditions = CloneDeep(netrouteConditions)
    this.counterOptions = [
      { key: 'netroute.min.latency.ms', text: 'netroute.min.latency.ms' },
      { key: 'netroute.max.latency.ms', text: 'netroute.max.latency.ms' },
      {
        key: 'netroute.packet.lost.percent',
        text: 'netroute.packet.lost.percent',
      },
      { key: 'netroute.latency.ms', text: 'netroute.latency.ms' },
      { key: 'status', text: 'status' },
    ]
    this.filterOptions = [
      { key: 'NetRoute', text: 'NetRoute' },
      { key: 'Tag', text: 'Tag' },
    ]
    this.operatorOptions = getOperatorOptions([
      '>',
      '>=',
      '=',
      '!=',
      '>',
      '>=',
      '<',
      '<=',
    ])
    this.severityOptions = [
      { value: 'CRITICAL', label: 'Critical' },
      { value: 'MAJOR', label: 'Major' },
      { value: 'WARNING', label: 'Warning' },
    ]
    return {
      floatValue: false,
    }
  },
  computed: {
    filteredCounterOptions() {
      if (this.routeEvaluationType === this.$constants.HOP_TO_HOP) {
        return this.counterOptions.filter((option) => option.key !== 'status')
      }
      return this.counterOptions
    },
    showMetricConditionsSeverity() {
      return (
        this.routeEvaluationType !== this.$constants.HOP_TO_HOP &&
        this.metric !== 'status'
      )
    },
    shouldDisabledEntityOptions() {
      return !this.entityType
    },
    searchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.SDN,
          this.$constants.OTHER,
          this.$constants.CLOUD,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.STORAGE,
          this.$constants.CONTAINER_ORCHESTRATION,
        ],
      }
    },
    entityType: {
      get() {
        return (this.value || {}).entityType
      },
      set(entityType) {
        this.$emit('change', {
          ...(this.value || {}),
          entityType,
          entities: [],
        })
      },
    },
    entities: {
      get() {
        return (this.value || {}).entities
      },
      set(entities) {
        this.$emit('change', { ...(this.value || {}), entities })
      },
    },
    critical: {
      get() {
        return (this.value || {}).critical
      },
      set(critical) {
        this.$emit('change', { ...(this.value || {}), critical })
      },
    },
    major: {
      get() {
        return (this.value || {}).major
      },
      set(major) {
        this.$emit('change', { ...(this.value || {}), major })
      },
    },
    warning: {
      get() {
        return (this.value || {}).warning
      },
      set(warning) {
        this.$emit('change', { ...(this.value || {}), warning })
      },
    },
    routeEvaluationType: {
      get() {
        return (this.value || {}).routeEvaluationType
      },
      set(routeEvaluationType) {
        this.$emit('change', { ...(this.value || {}), routeEvaluationType })
      },
    },
    metric: {
      get() {
        return (this.value || {}).metric
      },
      set(metric) {
        this.$emit('change', { ...(this.value || {}), metric })
      },
    },
    conditionValue: {
      get() {
        return (this.value || {}).conditionValue
      },
      set(conditionValue) {
        this.$emit('change', {
          ...(this.value || {}),
          conditionValue,
        })
      },
    },
    operator: {
      get() {
        return (this.value || {}).operator
      },
      set(operator) {
        this.$emit('change', { ...(this.value || {}), operator })
      },
    },
    severity: {
      get() {
        return (this.value || {}).severity
      },
      set(severity) {
        this.$emit('change', { ...(this.value || {}), severity })
      },
    },
  },
}
</script>
