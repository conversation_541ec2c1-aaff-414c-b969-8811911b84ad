<template>
  <!-- <MRow :gutter="32" class="w-full"> -->
  <!-- <MCol :size="1">
      <FlotoFormItem label="Criterias">
        <FlotoDropdownPicker v-model="grouping" :options="criteriasOptions" />
      </FlotoFormItem>
    </MCol> -->
  <MultipleFormItems
    v-model="conditions"
    :show-icon="false"
    :max-items="3"
    class="w-full"
  >
    <template
      v-slot="{ item, update, remove, add, isLastItem, canAdd, total, index }"
    >
      <MRow class="w-full" :gutter="16">
        <MCol :size="1">
          <FlotoFormItem v-if="index === 0" label="Criteria">
            <FlotoDropdownPicker
              v-model="grouping"
              :options="criteriasOptions"
            />
          </FlotoFormItem>
        </MCol>
        <!-- <MRow :gutter="16"> -->
        <!-- <MCol :size="2">
                <FlotoFormItem label="Criterias">
                  <FlotoDropdownPicker
                    v-model="item.criterias"
                    :options="criteriasOptions"
                  />
                </FlotoFormItem>
              </MCol> -->
        <MCol :size="3">
          <FlotoFormItem :label="index === 0 ? 'Varbind' : ''">
            <FlotoDropdownPicker
              v-model="item.operand"
              :options="varbindOptions"
              @change="update({ ...(item || {}), operand: $event })"
            />
          </FlotoFormItem>
        </MCol>
        <MCol :size="2">
          <FlotoFormItem
            validation-label="Operator"
            :label="index === 0 ? 'Operator' : ''"
            :vid="`${index}-operator`"
          >
            <FlotoDropdownPicker
              v-model="item.operator"
              :options="operatorOptions"
              placeholder="Select Operator"
            />
          </FlotoFormItem>
        </MCol>
        <MCol class="flex" :size="2">
          <!-- <div class="flex-1 flex self-end" style="flex-shrink: 0"> -->
          <FlotoFormItem
            v-model="item.value"
            validation-label="Value"
            placeholder="Value"
            :label="index === 0 ? ' ' : ''"
          />
          <!-- </div> -->
        </MCol>
        <MCol
          :size="1"
          style="flex-shrink: 0; width: 80px"
          class="fixed-size flex items-center"
        >
          <span
            v-if="total > 1"
            id="remove-counter"
            class="mr-2 flex items-center"
            outline
            @click="remove"
          >
            <MIcon
              name="times-circle"
              class="cursor-pointer text-secondary-red"
              size="lg"
            />
          </span>
          <span
            v-if="isLastItem && canAdd"
            id="add-counter"
            outline
            class="flex items-center"
            @click="add"
          >
            <MIcon
              name="plus-circle"
              class="text-primary cursor-pointer"
              size="lg"
            />
          </span>
        </MCol>
      </MRow>
    </template>
  </MultipleFormItems>
  <!-- </MRow> -->
</template>

<script>
import MultipleFormItems from '@components/multiple-form-items.vue'
import { getOperatorOptions } from '@/src/components/widgets/helper'

export default {
  name: 'TrapFilterCondition',
  components: { MultipleFormItems },
  model: {
    event: 'change',
  },
  props: {
    value: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    this.varbindOptions = Array.from({ length: 100 }, (v, i) => ({
      key: i + 1,
      text: (i + 1).toString(),
    }))
    this.operatorOptions = getOperatorOptions([
      '>',
      '>=',
      '=',
      'contain',
      'in',
      'start with',
      'end with',
      '!=',
      'contain',
      'not contain',
      '>',
      '>=',
      '<',
      '<=',
    ])
    this.criteriasOptions = [
      { key: 'and', text: 'All' },
      { key: 'or', text: 'Any' },
    ]
    return {}
  },
  computed: {
    conditions: {
      get() {
        return (this.value || {}).conditions
      },
      set(conditions) {
        return this.$emit('change', { ...(this.value || {}), conditions })
      },
    },
    grouping: {
      get() {
        return (this.value || {}).grouping
      },
      set(grouping) {
        return this.$emit('change', { ...(this.value || {}), grouping })
      },
    },
  },
}
</script>
