<template>
  <MonitorProvider :search-params="searchParams">
    <CounterPovider
      :data-type="allowedCounterDataTypes"
      :search-params="counterSearchParams"
      show-loader
    >
      <div>
        <MRow>
          <MCol :size="6">
            <FlotoFormItem id="instance" rules="required" label="Counter">
              <CounterPicker
                :value="(counter || {}).key"
                name="group"
                placeholder="Select Metric"
                :filter-fn="counterFilter"
                @change="counter = $event"
              />
            </FlotoFormItem>
            <!-- <MonitorSelection v-model="monitorSelectionInfo" /> -->
          </MCol>
          <MCol :size="5">
            <MonitorSelection
              v-model="monitorSelectionInfo"
              :counter="{ key: (counter || {}).key }"
              :excluded-entity-type-options="excludedEntityTypes"
              show-label
            />
          </MCol>

          <MCol v-if="counter" :size="10" class="mb-3">
            <InstanceMetricProvider
              v-if="counter.instanceType"
              :instance="counter.instanceType"
              group-type="metric"
              :target="counter.instanceType && monitorSelectionInfo.target"
            >
              <FiltersContainer
                v-model="filters"
                :max-pre-groups="1"
                :excluded-tabs="filtersExcludedTabs"
                placeholder="Filters"
              />
            </InstanceMetricProvider>
          </MCol>
          <!-- <MCol v-if="showFilterInput" :size="10" class="mb-3">
            <FiltersContainer
              v-model="filters"
              :max-pre-groups="1"
              :use-instance-grid="showFilterGrid"
              :excluded-tabs="filtersExcludedTabs"
              placeholder="Filters"
            />
          </MCol> -->
        </MRow>
        <!-- <MRow>
          <MCol :size="6" class="mb-3">
            <MRadioGroup
              id="trend-selection-id"
              v-model="baselineAlertValueType"
              as-button
              :options="baselineAlertValueOptions"
            />
          </MCol>
        </MRow> -->
        <!-- <MRow>
          <MCol :size="6" class="mb-3">
            <MetricConditionsSeverity
              id="critical-severity"
              v-model="critical"
              :counter="counter"
              :percent-value="isrelativeValue"
              :float-value="floatValue"
              :operators="forecastConditions"
            />
          </MCol>
          <MCol :size="6" class="mb-3">
            <MetricConditionsSeverity
              id="major-severity"
              v-model="major"
              :counter="counter"
              :percent-value="isrelativeValue"
              :float-value="floatValue"
              :operators="forecastConditions"
            />
          </MCol>
          <MCol :size="6" class="mb-3">
            <MetricConditionsSeverity
              id="warning-severity"
              v-model="warning"
              :counter="counter"
              :percent-value="isrelativeValue"
              :float-value="floatValue"
              :operators="forecastConditions"
            />
          </MCol>
        </MRow> -->
        <!-- <MRow class="mt-5">
          <MCol :size="3">
            <FlotoFormItem
              label="Deviation"
              rules="required"
              :info-tooltip="deviationTooltip"
            >
              <FlotoDropdownPicker
                id="deviation"
                v-model="deviation"
                as-input
                :options="deviationOptions"
              />
            </FlotoFormItem>
          </MCol>
          <MCol auto-size>
            <FlotoFormItem
              label="Algorithm"
              rules="required"
              :info-tooltip="algorithmTooltip"
            >
              <MRadioGroup
                id="algorithm"
                v-model="algorithm"
                as-button
                :options="algorithmOptions"
              />
            </FlotoFormItem>
          </MCol>
          <MCol v-if="algorithm === 'Seasonal'">
            <FlotoFormItem label="Seasonality" rules="required">
              <MRadioGroup
                id="seasonality"
                v-model="seasonality"
                as-button
                :options="seasonalityOptions"
              />
            </FlotoFormItem>
          </MCol>
        </MRow> -->
        <MRow class="my-2 flex items-center">
          <MCol :size="3">
            <FlotoFormItem label="Condition" rules="required">
              <FlotoDropdownPicker
                id="select-month"
                v-model="operator"
                class="w-full multi-select-group"
                searchable
                :options="forecastConditions"
              />
            </FlotoFormItem>
          </MCol>
          <MCol :size="3">
            <FlotoFormItem
              v-model="threshold"
              label=" "
              rules="required"
              placeholder="Threshold Value (%)"
              type="number"
              :precision="0"
              class="no-label-form-item"
            >
            </FlotoFormItem>
          </MCol>
          <MCol :size="3">
            <FlotoFormItem
              label="Analyze the forecast range within the next "
              rules="required"
            >
              <FlotoDropdownPicker
                id="select-month"
                v-model="evaluationWindow"
                class="w-full multi-select-group"
                searchable
                :options="forecastRangeOptions"
              />
            </FlotoFormItem>
          </MCol>
        </MRow>
        <MRow class="my-2 flex items-center">
          <MCol :size="3">
            <FlotoFormItem label="Severity" rules="required">
              <MRadioGroup
                v-model="severity"
                as-button
                :options="severityOptions"
              />
            </FlotoFormItem>
          </MCol>
        </MRow>
        <MRow class="my-2 flex items-center material-input">
          <!-- <MCol auto-size>
            <span class="label-strip opacity-50">
              next forecasting points
            </span>
          </MCol> -->
          <!-- <MCol :size="2">
            <FlotoFormItem rules="required">
            <FlotoDropdownPicker
              id="next-forecasting-point"
              v-model="forecastingPoint"
              as-input
              :allow-clear="false"
              :options="forecastingOptions"
            />
            </FlotoFormItem>
          </MCol> -->
          <!-- <MCol auto-size>
            <span class="mt-3 label-strip opacity-50">Auto Clear</span>
          </MCol> -->
          <MCol :size="3">
            <FlotoFormItem label="Auto Clear">
              <AutoClearPolicyPicker
                id="auto-clear-policy"
                v-model="autoClearPolicy"
                as-input
              />
            </FlotoFormItem>
          </MCol>
          <!-- <MCol v-if="autoClearPolicy === 0" :size="4" class="ml-auto">
              <MetricConditionsSeverity
                id="clear-severity"
                v-model="clear"
                :is-percentage-show="true"
                :required="true"
                :conditions="forecastConditions"
              />
            </MCol> -->
        </MRow>
      </div>
    </CounterPovider>
  </MonitorProvider>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import Range from 'lodash/range'
import Pick from 'lodash/pick'
import CounterPicker from '@components/data-picker/counter-picker.vue'
import CounterPovider from '@components/data-provider/counter-provider.vue'
import MonitorSelection from '@components/widgets/monitor-or-group-selection.vue'
// import MetricConditionsSeverity from './metric-conditions-severity.vue'
import AutoClearPolicyPicker from '@components/data-picker/auto-clear-policy-picker.vue'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import InstanceMetricProvider from '@components/data-provider/instance-metric-provider.vue'
import FiltersContainer from '@components/filters/filters-container.vue'
import getMessage from '@constants/messages'
import { FILTER_CONDITION_DEFAULT_DATA } from '@components/widgets/constants'
// import { baselineAlertValueOptions } from '../../../helpers/dropdown-options'

export default {
  name: 'ForecastConditions',
  components: {
    MonitorSelection,
    // MetricConditionsSeverity,
    CounterPovider,
    AutoClearPolicyPicker,
    CounterPicker,
    MonitorProvider,
    InstanceMetricProvider,
    FiltersContainer,
  },
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default() {
        return undefined
      },
    },
    isEditMode: { type: Boolean, default: false },
  },
  data() {
    this.forecastRangeOptions = [
      { key: '-12h', text: '12 Hours' },
      { key: '-24h', text: '1 Day ' },
      { key: '-48h', text: '2 Days' },
      { key: '-7d', text: '1 Week' },
      { key: '-14d', text: '2 Week' },
      { key: '-30d', text: '1 Month' },
      { key: '-60d', text: '2 Month' },
      { key: '-90d', text: '3 Month' },
    ]
    this.severityOptions = [
      { value: 'CRITICAL', label: 'Critical' },
      { value: 'MAJOR', label: 'Major' },
      { value: 'WARNING', label: 'Warning' },
    ]
    this.filtersExcludedTabs = ['post']
    this.forecastConditions = [
      { key: 'above', text: 'Above' },
      { key: 'below', text: 'Below' },
      // { key: 'above or below', text: 'Above or Below' },
    ]
    this.deviationOptions = Range(1, 6).map((i) => ({ key: i, text: i }))
    this.seasonalityOptions = [
      { value: 'Hourly', text: 'Hourly' },
      { value: 'Daily', text: 'Daily' },
      { value: 'Weekly', text: 'Weekly' },
      { value: 'Monthly', text: 'Monthly' },
    ]
    this.algorithmOptions = [
      { value: 'Linear', text: 'Linear' },
      { value: 'Seasonal', text: 'Seasonal' },
    ]
    // this.baselineAlertValueOptions = CloneDeep(baselineAlertValueOptions)
    this.excludedEntityTypes = []

    return {
      algorithmTooltip: getMessage('forecast_algorithm'),
      deviationTooltip: getMessage('anomaly_deviation'),
      showFilterInput: false,
      floatValue: false,
      isStringInstance: false,
    }
  },
  computed: {
    filterSearchParams() {
      if (this.value.instance && this.value.instance.instanceType) {
        return {
          'group.type': 'metric',
          'instance.type': this.value.instance['instanceType'],
          'entity.type': this.monitorSelectionInfo.entityType,
          entities: this.monitorSelectionInfo.entities,
        }
      }
      return undefined
    },
    allowedCounterDataTypes() {
      return ['numeric']
    },
    isrelativeValue() {
      return this.baselineAlertValueType === 'relative'
    },

    forecastingOptions() {
      if (this.seasonality === 'Hourly' && this.algorithm === 'Seasonal') {
        return [
          { key: '1 hour', text: '1 hour' },
          { key: '2 hour', text: '2 hour' },
          { key: '4 hour', text: '4 hour' },
          { key: '6 hour', text: '6 hour' },
          { key: '12 hour', text: '12 hour' },
        ]
      } else if (
        this.seasonality === 'Daily' &&
        this.algorithm === 'Seasonal'
      ) {
        return [
          { key: '1 hour', text: '1 hour' },
          { key: '2 hour', text: '2 hour' },
          { key: '4 hour', text: '4 hour' },
          { key: '6 hour', text: '6 hour' },
          { key: '12 hour', text: '12 hour' },
          { key: '1 day', text: '1 day' },
          { key: '7 day', text: '7 day' },
          { key: '15 day', text: '15 day' },
          { key: '1 month', text: '1 month' },
        ]
      } else if (this.algorithm === 'Seasonal') {
        return [
          { key: '1 hour', text: '1 hour' },
          { key: '2 hour', text: '2 hour' },
          { key: '4 hour', text: '4 hour' },
          { key: '6 hour', text: '6 hour' },
          { key: '12 hour', text: '12 hour' },
          { key: '1 day', text: '1 day' },
          { key: '7 day', text: '7 day' },
          { key: '15 day', text: '15 day' },
          { key: '1 month', text: '1 month' },
          { key: '2 month', text: '2 month' },
          { key: '3 month', text: '3 month' },
        ]
      }
      return [
        { key: '1 hour', text: '1 hour' },
        { key: '2 hour', text: '2 hour' },
        { key: '5 hour', text: '5 hour' },
        { key: '12 hour', text: '12 hour' },
        { key: '1 day', text: '1 day' },
        { key: '7 day', text: '7 day' },
        { key: '15 day', text: '15 day' },
        { key: '1 month', text: '1 month' },
        { key: '2 month', text: '2 month' },
        { key: '3 month', text: '3 month' },
      ]
    },
    searchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.SDN,
          this.$constants.OTHER,
          this.$constants.CLOUD,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.STORAGE,
          this.$constants.CONTAINER_ORCHESTRATION,
        ],
      }
    },
    counterSearchParams() {
      return {
        'string.counter.required': 'no',
        'visualization.group.type': 'metric',
      }
    },
    showFilterGrid() {
      return ((this.monitorSelectionInfo || {}).entities || []).length <= 1
    },
    filters: {
      get() {
        return (this.value || {}).filters
      },
      set(filters) {
        this.$emit('change', { ...(this.value || {}), filters })
      },
    },
    monitorSelectionInfo: {
      get() {
        return Pick(this.value || {}, ['entities', 'entityType'])
      },
      set(v) {
        this.$emit('change', { ...(this.value || {}), ...v })
      },
    },

    instance: {
      get() {
        if (this.value.instance) {
          return (this.value || {}).instance['key']
        }
        return undefined
      },
      set(instance) {
        this.$emit('change', { ...(this.value || {}), instance })
      },
    },
    operator: {
      get() {
        return (this.value || {}).operator
      },
      set(operator) {
        this.$emit('change', { ...(this.value || {}), operator })
      },
    },
    threshold: {
      get() {
        return (this.value || {}).threshold
      },
      set(threshold) {
        this.$emit('change', { ...(this.value || {}), threshold })
      },
    },
    evaluationWindow: {
      get() {
        return (this.value || {}).evaluationWindow
      },
      set(evaluationWindow) {
        this.$emit('change', { ...(this.value || {}), evaluationWindow })
      },
    },

    // critical: {
    //   get() {
    //     return (this.value || {}).critical
    //   },
    //   set(critical) {
    //     this.$emit('change', { ...(this.value || {}), critical })
    //   },
    // },
    // major: {
    //   get() {
    //     return (this.value || {}).major
    //   },
    //   set(major) {
    //     this.$emit('change', { ...(this.value || {}), major })
    //   },
    // },
    // warning: {
    //   get() {
    //     return (this.value || {}).warning
    //   },
    //   set(warning) {
    //     this.$emit('change', { ...(this.value || {}), warning })
    //   },
    // },
    severity: {
      get() {
        return (this.value || {}).severity
      },
      set(severity) {
        this.$emit('change', { ...(this.value || {}), severity })
      },
    },
    // clear: {
    //   get() {
    //     return (this.value || {}).clear
    //   },
    //   set(clear) {
    //     this.$emit('change', { ...(this.value || {}), clear })
    //   },
    // },
    deviation: {
      get() {
        return (this.value || {}).deviation
      },
      set(deviation) {
        this.$emit('change', {
          ...(this.value || {}),
          deviation,
        })
      },
    },
    algorithm: {
      get() {
        return (this.value || {}).algorithm
      },
      set(algorithm) {
        this.$emit('change', {
          ...(this.value || {}),
          algorithm,
        })
      },
    },
    seasonality: {
      get() {
        return (this.value || {}).seasonality
      },
      set(seasonality) {
        this.$emit('change', {
          ...(this.value || {}),
          seasonality,
        })
      },
    },
    forecastingPoint: {
      get() {
        return (this.value || {}).forecastingPoint
      },
      set(forecastingPoint) {
        this.$emit('change', {
          ...(this.value || {}),
          forecastingPoint,
        })
      },
    },
    autoClearPolicy: {
      get() {
        return (this.value || {}).autoClearPolicy
      },
      set(autoClearPolicy) {
        this.$emit('change', { ...(this.value || {}), autoClearPolicy })
      },
    },
    baselineAlertValueType: {
      get() {
        return (this.value || {}).baselineAlertValueType
      },
      set(baselineAlertValueType) {
        this.$emit('change', {
          ...(this.value || {}),
          baselineAlertValueType,
        })
      },
    },
    counter: {
      get() {
        if (this.value.counter) {
          return (this.value || {}).counter
        }
        return undefined
      },
      set(counter) {
        if (
          counter !== undefined &&
          (counter.dataType || []).includes('string')
        ) {
          this.floatValue = false
        } else {
          this.isStringInstance = true
        }

        this.$emit('change', { ...(this.value || {}), counter })
      },
    },
  },
  watch: {
    instance: {
      immediate: true,
      handler(newVal, oldVal) {
        if (oldVal) {
          this.resetFiltersForm()
        }
        if (this.value.instance) {
          if (this.value.instance['instanceType']) {
            this.showFilterInput = true
          } else {
            this.showFilterInput = false
          }
        }
      },
    },
  },
  methods: {
    resetFiltersForm() {
      this.filters = {
        pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
      }
    },
    counterFilter(counterMap) {
      const computedMap = new Map()
      Array.from(counterMap.keys()).map((c) => {
        if (c.includes('percent')) {
          const counter = counterMap.get(c)

          computedMap.set(c, counter)
        }
      })
      return Object.freeze(
        Array.from(computedMap.values()).map((c) => ({
          ...c,
        }))
      )
    },
  },
}
</script>
