<template>
  <MonitorProvider :search-params="searchParams">
    <CounterPovider
      :data-type="allowedCounterDataTypes"
      :search-params="counterSearchParams"
      show-loader
      ignore-shadow-counters
    >
      <div>
        <MRow>
          <MCol :size="6">
            <FlotoFormItem
              id="instance"
              rules="required"
              label="Counter"
              :class="{ 'disabled-bordered-dropdown': isEditMode }"
            >
              <CounterPicker
                :value="(counter || {}).key"
                name="group"
                placeholder="Select Metric"
                :disabled="isEditMode"
                @change="counter = $event"
              />
            </FlotoFormItem>
          </MCol>
          <MCol :size="5">
            <MonitorSelection
              v-model="monitorSelectionInfo"
              :counter="{ key: (counter || {}).key }"
              :excluded-entity-type-options="excludedEntityTypes"
              show-label
            />
          </MCol>
          <MCol v-if="counter" :size="10" class="mb-3">
            <InstanceMetricProvider
              v-if="counter.instanceType"
              :instance="counter.instanceType"
              group-type="metric"
              :target="counter.instanceType && monitorSelectionInfo.target"
            >
              <FiltersContainer
                v-model="filters"
                :max-pre-groups="1"
                group-type="metric"
                :excluded-tabs="filtersExcludedTabs"
                placeholder="Filters"
                :target="counter.instanceType && monitorSelectionInfo"
              />
            </InstanceMetricProvider>
          </MCol>
        </MRow>
        <MRow v-if="isBaseLine">
          <MCol :size="6" class="mb-3">
            <MRadioGroup
              id="trend-selection-id"
              v-model="baselineAlertValueType"
              as-button
              :options="baselineAlertValueOptions"
            />
          </MCol>
        </MRow>
        <MRow>
          <MCol :size="6" class="mb-3">
            <MetricConditionsSeverity
              id="critical-severity"
              v-model="critical"
              :counter="counter"
              :percent-value="isBaseLine && isrelativeValue"
              :float-value="floatValue"
              :operators="
                isThreshold ? thresholdConditions : baselineConditions
              "
              :value-type="valueType"
            />
          </MCol>
          <MCol :size="6" class="mb-3">
            <MetricConditionsSeverity
              id="major-severity"
              v-model="major"
              :counter="counter"
              :percent-value="isBaseLine && isrelativeValue"
              :float-value="floatValue"
              :operators="
                isThreshold ? thresholdConditions : baselineConditions
              "
              :value-type="valueType"
            />
          </MCol>
          <MCol :size="6" class="mb-3">
            <MetricConditionsSeverity
              id="warning-severity"
              v-model="warning"
              :counter="counter"
              :percent-value="isBaseLine && isrelativeValue"
              :float-value="floatValue"
              :operators="
                isThreshold ? thresholdConditions : baselineConditions
              "
              :value-type="valueType"
            />
          </MCol>
          <!-- <MCol v-if="autoClearPolicy === 0" :size="6" class="mb-3">
            <MetricConditionsSeverity
              id="clear-severity"
              v-model="clear"
              :counter="counter"
              :percent-value="isBaseLine"
              :float-value="floatValue"
              :required="true"
              :operators="
                isThreshold ? thresholdConditions : baselineConditions
              "
            />
          </MCol> -->
        </MRow>
        <MRow class="items-center material-input my-2">
          <!-- <MCol v-if="subType === 'Baseline'" auto-size>
            <FlotoFormItem label="Baseline Trend Selection">
              <MRadioGroup
                id="trend-selection-id"
                v-model="baseLineTrend"
                as-button
                :options="baseLineTrendOptions"
              />
            </FlotoFormItem>
          </MCol> -->
          <MCol v-if="isThreshold" auto-size :size="3">
            <FlotoFormItem label="Notify if Threshold value breach within">
              <FlotoDropdownPicker
                id="notify-id"
                v-model="breachTime"
                as-input
                :allow-clear="false"
                :options="breachTimeOptions"
              />
            </FlotoFormItem>
          </MCol>
          <MCol v-if="isThreshold" :size="3">
            <FlotoFormItem label="Abnormality Occurrence">
              <FlotoDropdownPicker
                id="abnormality-occurence-id"
                v-model="abnormalityOccurence"
                :options="occurenceOptions"
                as-input
              />
            </FlotoFormItem>
          </MCol>
          <MCol :size="3">
            <FlotoFormItem label="Auto Clear">
              <AutoClearPolicyPicker
                id="auto-clear-policy"
                v-model="autoClearPolicy"
                as-input
              />
            </FlotoFormItem>
          </MCol>
          <MCol />
        </MRow>
      </div>
    </CounterPovider>
  </MonitorProvider>
</template>

<script>
import Pick from 'lodash/pick'
import CloneDeep from 'lodash/cloneDeep'
import CounterPicker from '@components/data-picker/counter-picker.vue'
import CounterPovider from '@components/data-provider/counter-provider.vue'
import MonitorSelection from '@components/widgets/monitor-or-group-selection.vue'
import MetricConditionsSeverity from './metric-conditions-severity.vue'
import AutoClearPolicyPicker from '@components/data-picker/auto-clear-policy-picker.vue'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import InstanceMetricProvider from '@components/data-provider/instance-metric-provider.vue'
import FiltersContainer from '@components/filters/filters-container.vue'
import { FILTER_CONDITION_DEFAULT_DATA } from '@components/widgets/constants'
import {
  baselineConditionOptions,
  baselineTrendOptions,
  breachTimeOptions,
  defaultFilterExcludedTabs,
  occurenceOptions,
  thresholdConditionOptions,
  baselineAlertValueOptions,
} from '../../../helpers/dropdown-options'

export default {
  name: 'MetricConditions',
  components: {
    MonitorSelection,
    MetricConditionsSeverity,
    AutoClearPolicyPicker,
    CounterPovider,
    CounterPicker,
    MonitorProvider,
    InstanceMetricProvider,
    FiltersContainer,
  },
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default() {
        return undefined
      },
    },
    subType: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      required: true,
    },
    isEditMode: { type: Boolean, default: false },
  },
  data() {
    this.excludedEntityTypes = []
    this.filtersExcludedTabs = CloneDeep(defaultFilterExcludedTabs)
    this.thresholdConditions = CloneDeep(thresholdConditionOptions)
    this.baselineConditions = CloneDeep(baselineConditionOptions)
    this.breachTimeOptions = CloneDeep(breachTimeOptions)
    this.occurenceOptions = CloneDeep(occurenceOptions)
    this.baseLineTrendOptions = CloneDeep(baselineTrendOptions)
    this.baselineAlertValueOptions = CloneDeep(baselineAlertValueOptions)
    return {
      floatValue: false,
    }
  },
  computed: {
    isBaseLine() {
      return this.subType === this.$currentModule.getConfig().BASELINE
    },
    isThreshold() {
      return this.subType === this.$currentModule.getConfig().THRESHOLD
    },
    isrelativeValue() {
      return this.baselineAlertValueType === 'relative'
    },
    allowedCounterDataTypes() {
      if (this.isBaseLine) {
        return ['numeric']
      }
      return undefined
    },
    searchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.OTHER,
          this.$constants.CLOUD,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.SDN,
          this.$constants.STORAGE,
          this.$constants.CONTAINER_ORCHESTRATION,
        ],
      }
    },
    counterSearchParams() {
      return {
        'string.counter.required': this.isThreshold ? 'yes' : 'no',
        'visualization.group.type': 'metric',
      }
    },
    filters: {
      get() {
        return (this.value || {}).filters
      },
      set(filters) {
        this.$emit('change', { ...(this.value || {}), filters })
      },
    },
    critical: {
      get() {
        return (this.value || {}).critical
      },
      set(critical) {
        this.$emit('change', { ...(this.value || {}), critical })
      },
    },
    major: {
      get() {
        return (this.value || {}).major
      },
      set(major) {
        this.$emit('change', { ...(this.value || {}), major })
      },
    },
    warning: {
      get() {
        return (this.value || {}).warning
      },
      set(warning) {
        this.$emit('change', { ...(this.value || {}), warning })
      },
    },
    clear: {
      get() {
        return (this.value || {}).clear
      },
      set(clear) {
        this.$emit('change', { ...(this.value || {}), clear })
      },
    },
    monitorSelectionInfo: {
      get() {
        return Pick(this.value || {}, ['entities', 'entityType'])
      },
      set(v) {
        this.$emit('change', {
          ...(this.value || {}),
          ...v,
          instance: undefined,
          filters: {
            pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
          },
        })
      },
    },
    counter: {
      get() {
        if (this.value.counter) {
          return (this.value || {}).counter
        }
        return undefined
      },
      set(counter) {
        if (
          counter !== undefined &&
          (counter.dataType || []).includes('string')
        ) {
          this.floatValue = false
        } else {
          this.isStringInstance = true
        }

        this.$emit('change', { ...(this.value || {}), counter })
      },
    },

    autoClearPolicy: {
      get() {
        return (this.value || {}).autoClearPolicy
      },
      set(autoClearPolicy) {
        this.$emit('change', { ...(this.value || {}), autoClearPolicy })
      },
    },
    breachTime: {
      get() {
        return (this.value || {}).breachTime
      },
      set(breachTime) {
        this.$emit('change', { ...(this.value || {}), breachTime })
      },
    },
    baseLineTrend: {
      get() {
        return (this.value || {}).baseLineTrend
      },
      set(baseLineTrend) {
        this.$emit('change', {
          ...(this.value || {}),
          baseLineTrend,
        })
      },
    },
    abnormalityOccurence: {
      get() {
        return (this.value || {}).abnormalityOccurence
      },
      set(abnormalityOccurence) {
        this.$emit('change', {
          ...(this.value || {}),
          abnormalityOccurence,
        })
      },
    },
    baselineAlertValueType: {
      get() {
        return (this.value || {}).baselineAlertValueType
      },
      set(baselineAlertValueType) {
        this.$emit('change', {
          ...(this.value || {}),
          baselineAlertValueType,
        })
      },
    },
    valueType() {
      if (this.subType === 'Baseline') {
        return this.baselineAlertValueType
      }

      return undefined
    },
  },
}
</script>
