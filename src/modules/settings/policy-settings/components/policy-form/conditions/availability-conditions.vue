<template>
  <MonitorProvider :search-params="searchParams">
    <CounterPovider
      ref="counterProviderRef"
      :search-params="counterSearchParams"
      show-loader
      @counters="handleCounterReceived"
    >
      <span>
        <MRow>
          <MCol :size="6">
            <FlotoFormItem
              rules="required"
              label="Counter"
              :class="{ 'disabled-bordered-dropdown': isEditMode }"
            >
              <FlotoDropdownPicker
                :value="counter"
                placeholder="Select Counter"
                allow-clear
                :options="counterOptions"
                :disabled="isEditMode"
                @change="counter = $event"
              />
            </FlotoFormItem>
          </MCol>
          <MCol :size="6">
            <MonitorSelection
              v-model="monitorSelectionInfo"
              :counter="{ key: counter }"
              :excluded-entity-type-options="excludedEntityTypes"
              show-label
            />
          </MCol>
          <MCol :size="12" class="mb-3">
            <InstanceMetricProvider
              v-if="value && value.counter && value.counter.instanceType"
              :instance="value.counter.instanceType"
              group-type="availability"
              :target="
                value.counter.instanceType && monitorSelectionInfo.target
              "
              should-fetch-all-counters
              :fetch-counter-fn="fetchCounters"
            >
              <FiltersContainer
                v-model="filters"
                :max-pre-groups="1"
                :excluded-tabs="filtersExcludedTab"
                placeholder="Filters"
                group-type="metric"
              />
            </InstanceMetricProvider>
          </MCol>
        </MRow>
        <MRow>
          <MCol :size="3">
            <FlotoFormItem label="Notify if monitor/instance is down for">
              <NofityTimePicker id="notify-id" v-model="notifyAfter" />
            </FlotoFormItem>
          </MCol>
          <MCol :size="3">
            <FlotoFormItem label="Abnormality Occurrence">
              <FlotoDropdownPicker
                id="occurence-id"
                v-model="abnormalityOccurence"
                :allow-clear="false"
                as-input
                :options="occurenceOptions"
              />
            </FlotoFormItem>
          </MCol>
        </MRow>
      </span>
    </CounterPovider>
  </MonitorProvider>
</template>

<script>
import Pick from 'lodash/pick'
import CloneDeep from 'lodash/cloneDeep'
import CounterPovider from '@components/data-provider/counter-provider.vue'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import MonitorSelection from '@components/widgets/monitor-or-group-selection.vue'
import NofityTimePicker from '@components/data-picker/notify-time-picker.vue'
import FiltersContainer from '@components/filters/filters-container.vue'
import InstanceMetricProvider from '@components/data-provider/instance-metric-provider.vue'
import {
  FILTER_CONDITION_DEFAULT_DATA,
  WidgetTypeConstants,
} from '@components/widgets/constants'
import {
  defaultFilterExcludedTabs,
  occurenceOptions,
} from '../../../helpers/dropdown-options'

export default {
  name: 'AvailabilityConditions',
  components: {
    MonitorSelection,
    NofityTimePicker,
    CounterPovider,
    MonitorProvider,
    FiltersContainer,
    InstanceMetricProvider,
  },
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default() {
        return undefined
      },
    },
    isEditMode: { type: Boolean, default: false },
    type: {
      type: String,
      required: true,
    },
  },
  data() {
    this.excludedEntityTypes = []
    this.occurenceOptions = CloneDeep(occurenceOptions)
    this.filtersExcludedTab = CloneDeep(defaultFilterExcludedTabs)

    return {
      counterOptions: [],
    }
  },

  computed: {
    searchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.OTHER,
          this.$constants.CLOUD,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.SDN,
          this.$constants.STORAGE,
          this.$constants.CONTAINER_ORCHESTRATION,
        ],
      }
    },
    counterSearchParams() {
      return {
        'string.counter.required': 'yes',
        'visualization.group.type': 'availability',
      }
    },
    filters: {
      get() {
        return (this.value || {}).filters
      },
      set(filters) {
        this.$emit('change', {
          ...(this.value || {}),
          filters,
        })
      },
    },
    monitorSelectionInfo: {
      get() {
        return Pick(this.value || {}, ['entities', 'entityType'])
      },
      set(v) {
        this.$emit('change', {
          ...(this.value || {}),
          ...v,
          filters: {
            pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
          },
        })
      },
    },
    notifyAfter: {
      get() {
        return (this.value || {}).notifyAfter
      },
      set(notifyAfter) {
        this.$emit('change', { ...(this.value || {}), notifyAfter })
      },
    },
    abnormalityOccurence: {
      get() {
        return (this.value || {}).abnormalityOccurence
      },
      set(abnormalityOccurence) {
        this.$emit('change', { ...(this.value || {}), abnormalityOccurence })
      },
    },
    counter: {
      get() {
        if ((this.value || {}).counter) {
          const counter = this.value.counter
          return counter.key
        }
        return undefined
      },
      set(counter) {
        let value
        if (counter) {
          const counterObject = this.counterOptions.find(
            (c) => c.key === counter
          )
          if (counterObject) {
            value = CloneDeep(counterObject)
          }
        }
        this.$emit('change', {
          ...(this.value || {}),
          counter: value,
        })
      },
    },
  },
  methods: {
    handleCounterReceived(counterMap) {
      const computedMap = new Map()
      Array.from(counterMap.keys()).map((c) => {
        const counterKey =
          c.indexOf('~') >= 0 ? `${c.split('~')[0]}~status` : 'status'
        const counter = counterMap.get(c)
        counter.key = counterKey
        computedMap.set(counterKey, counter)
      })
      this.counterOptions = Object.freeze(
        Array.from(computedMap.values()).map((c) => ({
          ...c,
          key: c.key,
          text: c.key.replace(/~/g, '.'),
        }))
      )
    },
    fetchCounters() {
      return this.$refs?.counterProviderRef?.fetchCountersWithParams({
        'visualization.group.type': 'metric',
        'visualization.category': WidgetTypeConstants.GRID,
      })
    },
  },
}
</script>
