<template>
  <MRow :gutter="16">
    <MCol :size="4">
      <FlotoFormItem
        rules="required"
        label="Counter"
        :class="{ 'disabled-bordered-dropdown': isEditMode }"
      >
        <CounterPicker
          v-model="counter"
          placeholder="Select Counter"
          :data-type="counterDataTypes"
          :disabled-options="disabledOptions"
          :disabled="isEditMode"
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="2">
      <FlotoFormItem
        :rules="{ required: <PERSON><PERSON><PERSON>(counter) }"
        label="Aggregation"
        validation-label="Aggr."
      >
        <FlotoDropdownPicker
          v-model="aggrigateFn"
          placeholder="Select Aggr."
          :options="
            counter
              ? value.counter.dataType.length === 1
                ? aggrigateOptions[value.counter.dataType[0]]
                : aggrigateOptions.all
              : []
          "
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="2">
      <FlotoFormItem
        :rules="{ required: <PERSON><PERSON><PERSON>(counter) }"
        label="Operator"
        validation-label="Operator"
      >
        <FlotoDropdownPicker
          v-model="operator"
          placeholder="Select Operator."
          :options="counter ? allOoperatorOptions : []"
        />
      </FlotoFormItem>
    </MCol>

    <template v-if="operator === 'range'">
      <MCol :size="2">
        <FlotoFormItem
          v-model="startValue"
          label="Start Value"
          :rules="{
            required: true,
            numeric: true,
          }"
          validation-label="Start Value"
          placeholder="Start Value"
          class="ml-1"
        />
      </MCol>
      <MCol :size="2">
        <FlotoFormItem
          v-model="endValue"
          label="End Value"
          :rules="{
            required: true,
            numeric: true,
          }"
          validation-label="End Value"
          placeholder="End Value"
          class="ml-1"
        />
      </MCol>
    </template>
    <MCol v-else :size="4" class="flex">
      <div class="flex-1 flex self-end" style="flex-shrink: 0">
        <FlotoFormItem
          v-model="conditionValue"
          :rules="{
            required: counter
              ? (value.counter.dataType || []).includes('any') === false
              : Boolean(counter),
            numeric:
              counter &&
              value.counter.dataType.includes('numeric') &&
              value.counter.dataType.length === 1,
          }"
          validation-label="Value"
          placeholder="Value"
          class="ml-1"
        />
      </div>
      <div class="flex-1" style="flex-shrink: 0">
        <div
          v-if="formattedValue"
          class="mx-2 font-semibold"
          style="flex-shrink: 0"
        >
          {{ formattedValue }}
        </div>
      </div>
    </MCol>
  </MRow>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import CounterPicker from '@components/data-picker/counter-picker.vue'
import {
  AGGRIGATION_OPTIONS,
  DATA_TYPE_OPERATORS,
} from '@components/widgets/constants'
import { isUnitConvertible } from '@/src/utils/unit-checker'
import applyUnit from '@/src/utils/unit-applier'

export default {
  name: 'LogFlowCounterSelection',
  components: {
    CounterPicker,
  },
  model: { event: 'change' },
  props: {
    isEditMode: { type: Boolean, default: false },
    value: {
      type: Object,
      default: undefined,
    },
    counterDataTypes: {
      type: Array,
      default: undefined,
    },
  },
  data() {
    this.disabledOptions = [
      'source.host',
      'source.plugin',
      'log.volume.bytes',
      'log.volume.bytes.per.sec',
      'logs.per.sec',
    ]
    this.aggrigateOptions = CloneDeep({
      ...AGGRIGATION_OPTIONS,
      numeric: AGGRIGATION_OPTIONS.numeric
        .filter(({ key }) => ['avg', 'sum'].includes(key))
        .concat([{ key: 'count', text: 'Count' }]),
      string: AGGRIGATION_OPTIONS.string.filter(({ key }) => key === 'count'),
      all: AGGRIGATION_OPTIONS.all
        .filter((i) => ['sum', 'avg'].includes(i.key))
        .concat([{ key: 'count', text: 'Count' }]),
    })
    this.operatorOptions = CloneDeep(DATA_TYPE_OPERATORS)
    return {}
  },
  computed: {
    formattedValue() {
      if (
        this.counter &&
        !/\.status(?:\.(?:avg|sum|last|min|max|sparkline))?$/.test(this.counter)
      ) {
        if (isUnitConvertible(this.counter) && this.conditionValue) {
          return applyUnit(this.counter, this.conditionValue)
        }
      }
      return undefined
    },

    allOoperatorOptions() {
      return this.operatorOptions.all.concat([{ key: 'range', name: 'Range' }])
    },
    counter: {
      get() {
        if ((this.value || {}).counter) {
          return this.value.counter.key
        }
        return undefined
      },
      set(counter) {
        if (counter && counter.key !== ((this.value || {}).counter || {}).key) {
          this.$emit('change', {
            counter,
          })
        } else {
          this.$emit('change', {
            ...(this.value || {}),
            counter,
          })
        }
      },
    },
    aggrigateFn: {
      get() {
        return (this.value || {}).aggrigateFn
      },
      set(aggrigateFn) {
        this.$emit('change', {
          ...(this.value || {}),
          aggrigateFn,
        })
      },
    },
    operator: {
      get() {
        return (this.value || {}).operator
      },
      set(operator) {
        this.$emit('change', {
          ...(this.value || {}),
          operator,
        })
      },
    },
    conditionValue: {
      get() {
        return (this.value || {}).conditionValue
      },
      set(conditionValue) {
        this.$emit('change', {
          ...(this.value || {}),
          conditionValue,
        })
      },
    },
    startValue: {
      get() {
        return (this.value || {}).startValue
      },
      set(startValue) {
        this.$emit('change', {
          ...(this.value || {}),
          startValue,
        })
      },
    },
    endValue: {
      get() {
        return (this.value || {}).endValue
      },
      set(endValue) {
        this.$emit('change', {
          ...(this.value || {}),
          endValue,
        })
      },
    },
  },
}
</script>
