<template>
  <div>
    <MRow>
      <MCol :size="4">
        <FlotoFormItem label="Service Selection">
          <FlotoDropdownPicker
            id="service"
            v-model="service"
            :options="serviceOptions"
            placeholder="Service"
            multiple
          />
        </FlotoFormItem>
      </MCol>
      <MCol :size="4">
        <MonitorProvider :search-params="searchParams">
          <FlotoFormItem label="Monitor">
            <MonitorPicker v-model="monitors" placeholder="Monitor" multiple />
          </FlotoFormItem>
        </MonitorProvider>
      </MCol>
      <MCol :size="4">
        <FlotoFormItem label="Transaction">
          <FlotoDropdownPicker
            id="transaction"
            v-model="transaction"
            :options="transactionOptions"
            placeholder="Transaction"
            multiple
          />
        </FlotoFormItem>
      </MCol>
    </MRow>
    <MRow class="items-center">
      <MCol :size="3">
        <FlotoFormItem label="Service Metric" rules="required">
          <FlotoDropdownPicker
            id="service"
            v-model="serviceMetric"
            :options="serviceMetricOptions"
            placeholder="Service Metric"
          />
        </FlotoFormItem>
      </MCol>
      <MCol :size="2">
        <FlotoFormItem label="Condition" rules="required">
          <FlotoDropdownPicker
            id="condition"
            v-model="condition"
            :options="conditionOptions"
            placeholder="Condition"
          />
        </FlotoFormItem>
      </MCol>
      <MCol :size="3">
        <FlotoFormItem
          v-model="thresholdValue"
          label="Threshold Value"
          rules="required"
        />
      </MCol>
      <MCol :size="4" class="flex items-center material-input mt-2">
        <span class="label-strip opacity-50">within</span>
        <NofityTimePicker v-model="notify" class="ml-5" />
      </MCol>
    </MRow>
  </div>
</template>

<script>
import Range from 'lodash/range'
import Pick from 'lodash/pick'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import MonitorPicker from '@components/data-picker/monitor-picker.vue'
import NofityTimePicker from '@components/data-picker/notify-time-picker.vue'
export default {
  name: 'AvailabilityConditions',
  components: { MonitorProvider, MonitorPicker, NofityTimePicker },
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default() {
        return undefined
      },
    },
  },
  data() {
    this.occurenceOptions = Range(1, 6).map((i) => ({ key: i, text: i }))
    this.serviceOptions = [
      { key: 'Booking Service', text: 'Booking Service' },
      { key: 'Log Service', text: 'Log Service' },
      { key: 'Flow Service', text: 'Flow Service' },
    ]
    this.transactionOptions = [
      { key: 'hotel', text: '/hotel' },
      { key: 'hotelcontainer.index', text: 'hotelcontainer.index' },
    ]
    this.serviceMetricOptions = [
      { key: 'Request Per Sec', text: 'Request Per Sec' },
      { key: 'Request Latency', text: 'Request Latency' },
      { key: 'Error Count', text: 'Error Count' },
    ]
    this.conditionOptions = [
      { key: 'above', text: 'Above' },
      { key: 'below', text: 'Below' },
      { key: 'stop reporting', text: 'Stop Reporting' },
    ]
    return {}
  },
  computed: {
    searchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.OTHER,
          this.$constants.CLOUD,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.SDN,
          this.$constants.STORAGE,
          this.$constants.CONTAINER_ORCHESTRATION,
        ],
      }
    },
    monitorSelectionInfo: {
      get() {
        return Pick(this.value || {}, ['monitors', 'groups'])
      },
      set(v) {
        this.$emit('change', { ...(this.value || {}), ...v })
      },
    },
    notify: {
      get() {
        return (this.value || {}).notify
      },
      set(notify) {
        this.$emit('change', { ...(this.value || {}), notify })
      },
    },
    occurence: {
      get() {
        return (this.value || {}).occurence
      },
      set(occurence) {
        this.$emit('change', { ...(this.value || {}), occurence })
      },
    },
    service: {
      get() {
        return (this.value || {}).service
      },
      set(service) {
        this.$emit('change', { ...(this.value || {}), service })
      },
    },
    monitors: {
      get() {
        return (this.value || {}).monitors
      },
      set(monitors) {
        this.$emit('change', { ...(this.value || {}), monitors })
      },
    },
    transaction: {
      get() {
        return (this.value || {}).transaction
      },
      set(transaction) {
        this.$emit('change', { ...(this.value || {}), transaction })
      },
    },
    serviceMetric: {
      get() {
        return (this.value || {}).serviceMetric
      },
      set(serviceMetric) {
        this.$emit('change', { ...(this.value || {}), serviceMetric })
      },
    },
    condition: {
      get() {
        return (this.value || {}).condition
      },
      set(condition) {
        this.$emit('change', { ...(this.value || {}), condition })
      },
    },
    thresholdValue: {
      get() {
        return (this.value || {}).thresholdValue
      },
      set(thresholdValue) {
        this.$emit('change', { ...(this.value || {}), thresholdValue })
      },
    },
  },
}
</script>
