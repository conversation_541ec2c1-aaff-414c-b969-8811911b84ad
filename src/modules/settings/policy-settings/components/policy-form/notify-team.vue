<template>
  <UserProvider>
    <div class="form-box-container w-full flex">
      <div class="form-box w-full flex">
        <MRow :gutter="0" class="w-full">
          <MCol :size="12">
            <h5 id="notify-team-message" class="mb-0">Notify Team</h5>
            <div
              v-if="!isExpand"
              id="expand"
              class="toggleArrow"
              @click="isExpand = true"
            >
              <MIcon name="angle-down" size="lg" />
            </div>
            <div
              v-else
              id="collapse"
              class="toggleArrow"
              @click="isExpand = false"
            >
              <MIcon name="angle-up" size="lg" />
            </div>
            <div v-if="isExpand">
              <!-- <MCol
              v-if="
                [
                  $currentModule.getConfig().LOG,
                  $currentModule.getConfig().FLOW,
                  $currentModule.getConfig().TRAP,
                ].includes(type) === true
              "
              class="mt-3"
              :size="12"
            >
              <FlotoFormItem
                id="select-email"
                label="Select team member to notify via Email"
              >
                <FlotoTagsPicker
                  v-model="notification.emails"
                  always-text-mode
                  class="notify-full-w"
                  type="email"
                  title="Email"
                />
              </FlotoFormItem>
            </MCol>
            <MCol
              v-if="
                [
                  $currentModule.getConfig().LOG,
                  $currentModule.getConfig().FLOW,
                  $currentModule.getConfig().TRAP,
                ].includes(type) === true
              "
              :size="12"
            >
              <FlotoFormItem
                id="select-sms"
                label="Select team member to notify via SMS"
              >
                <FlotoTagsPicker
                  v-model="notification.sms"
                  always-text-mode
                  class="notify-full-w"
                  title="Phone"
                />
              </FlotoFormItem>
            </MCol> -->
              <MCol :size="12" class="flex my-2 items-center material-input">
                <!-- <template
                v-if="
                  [
                    $currentModule.getConfig().LOG,
                    $currentModule.getConfig().FLOW,
                    $currentModule.getConfig().TRAP,
                  ].includes(type) === false
                "
              >
                <span class="mr-8"> Always Notify if Flap Changes</span>
                <MRadioGroup
                  id="group-btn"
                  v-model="notification.shouldRenotify"
                  class="mr-10"
                  as-button
                  :options="yesNoOptions"
                />
              </template> -->

                <template v-if="notification.shouldRenotify === 'no'">
                  <span class="mr-1">
                    Renotify Team if policy has not been resolved
                  </span>

                  <FlotoDropdownPicker
                    id="renotify"
                    v-model="notification.shouldRenotify"
                    :allow-clear="false"
                    as-input
                    :options="renotifyOptions"
                  />
                </template>
              </MCol>

              <MCol :size="12">
                <MultipleFormItems
                  v-model="notification.notifySeverity"
                  :show-icon="false"
                  :max-items="
                    type === $currentModule.getConfig().LOG ||
                    type === $currentModule.getConfig().FLOW ||
                    type === $currentModule.getConfig().TRAP ||
                    isNetRouteHopToHop
                      ? 1
                      : 4
                  "
                  :item-template="defaultNotificationItemTemplate"
                >
                  <template
                    v-slot="{
                      item,
                      remove,
                      add,
                      update,
                      isLastItem,
                      canAdd,
                      total,
                    }"
                  >
                    <MRow class="flex-no-wrap items-center">
                      <MCol :size="6">
                        <div class="flex">
                          <label class="mb-1 mr-2">Notify</label>
                          <div class="flex items-center flex-1 flex-wrap">
                            <UserOrEmailPicker
                              v-model="item.name"
                              name="parameter-name"
                              :title="`@User or Email${
                                type !== 'NetRoute' ? ' or /Handle' : ''
                              }`"
                              always-text-mode
                              rules="required"
                              disable-justify-around
                              type="email"
                              :disabled-team-integration="type === 'NetRoute'"
                            />
                          </div>
                        </div>
                      </MCol>
                      <MCol
                        v-if="
                          [
                            $currentModule.getConfig().LOG,
                            $currentModule.getConfig().FLOW,
                            $currentModule.getConfig().TRAP,
                          ].includes(type) === false && !isNetRouteHopToHop
                        "
                        :size="4"
                      >
                        <span class="flex items-center">
                          <label class="mb-1 mr-2">if severity is</label>
                          <FlotoFormItem
                            :rules="
                              item.name && item.name.length
                                ? { required: true }
                                : {}
                            "
                            class="flex-1 no-label-form-item"
                          >
                            <FlotoDropdownPicker
                              v-model="item.severity"
                              :value="item.severity"
                              :searchable="false"
                              multiple
                              :options="dynamicSeverityOptions"
                              @change="
                                update({ ...(item || {}), severity: $event })
                              "
                            />
                          </FlotoFormItem>
                        </span>
                      </MCol>
                      <MCol :size="4">
                        <span
                          v-if="total > 1"
                          id="remove-counter"
                          class="mr-2"
                          @click="remove"
                        >
                          <MIcon
                            name="times-circle"
                            class="cursor-pointer text-secondary-red"
                            size="lg"
                          />
                        </span>
                        <a
                          v-if="isLastItem && canAdd"
                          class="ml-2"
                          @click="add"
                        >
                          <MIcon
                            name="plus-circle"
                            class="text-primary"
                            size="lg"
                          />
                        </a>
                      </MCol>
                    </MRow>
                  </template>
                </MultipleFormItems>
              </MCol>

              <MCol
                v-if="
                  [
                    $currentModule.getConfig().AVAILABILITY,
                    $currentModule.getConfig().METRIC,
                    $currentModule.getConfig().LOG,
                    $currentModule.getConfig().FLOW,
                    $currentModule.getConfig().TRAP,
                    $currentModule.getConfig().NETROUTE,
                  ].includes(type) === true
                "
                :size="12"
                class="my-4"
              >
                <MRow :gutter="0" class="flex items-center">
                  <label class="mr-2">Play Sound</label>

                  <MSwitch
                    id="play-sound-switch"
                    v-model="notification.shouldPlaySound"
                    :checked="notification.shouldPlaySound"
                    checked-children="ON"
                    un-checked-children="OFF"
                  />

                  <span
                    v-if="
                      notification.shouldPlaySound &&
                      [
                        $currentModule.getConfig().METRIC,
                        $currentModule.getConfig().NETROUTE,
                        $currentModule.getConfig().AVAILABILITY,
                      ].includes(type) === true &&
                      !isNetRouteHopToHop
                    "
                    class="flex items-center ml-2"
                  >
                    <label class="mb-1 mr-2">if severity is</label>

                    <FlotoFormItem
                      label=" "
                      rules="required"
                      class="no-label-form-item"
                    >
                      <FlotoDropdownPicker
                        v-model="notification.playsoundSeverities"
                        :value="notification.playsoundSeverities"
                        :searchable="false"
                        multiple
                        :options="
                          [
                            $currentModule.getConfig().ANOMALY,
                            $currentModule.getConfig().FORECAST,
                          ].includes(type)
                            ? anomalyForecastSeverityOptions
                            : type === $currentModule.getConfig().AVAILABILITY
                            ? availabilitySeverityOptions
                            : severityOptions
                        "
                      />
                    </FlotoFormItem>
                  </span>
                </MRow>
              </MCol>

              <MCol
                v-if="
                  [
                    $currentModule.getConfig().LOG,
                    $currentModule.getConfig().FLOW,
                    $currentModule.getConfig().TRAP,
                  ].includes(type) === false && !isNetRouteHopToHop
                "
                :size="12"
                class="my-2"
              >
                <span>
                  <label class="mb-1">Renotification</label>
                  <MTooltip
                    id="error-message"
                    overlay-class-name="readable-content-overlay"
                  >
                    <template v-slot:trigger>
                      <MIcon
                        name="info-circle"
                        class="text-primary cursor-pointer mx-2"
                      />
                    </template>
                    <div style="white-space: preline">
                      {{ $message('notify_team_renotification') }}
                    </div>
                  </MTooltip>
                </span>
                <MSwitch
                  id="change-password-switch"
                  v-model="notification.renotification"
                  :checked="notification.renotification"
                  checked-children="ON"
                  un-checked-children="OFF"
                />
              </MCol>

              <MCol v-if="notification.renotification" :size="12" class="my-2">
                <MultipleFormItems
                  v-model="notification.renotifySeverity"
                  :show-icon="false"
                  :max-items="
                    type === $currentModule.getConfig().AVAILABILITY
                      ? 1
                      : type === $currentModule.getConfig().METRIC
                      ? 4
                      : 3
                  "
                  :item-template="defaultRenotificationItemTemplate"
                >
                  <template
                    v-slot="{
                      item,
                      remove,
                      add,
                      update,
                      isLastItem,
                      canAdd,
                      total,
                    }"
                  >
                    <MRow class="flex-no-wrap items-center">
                      <MCol :size="5">
                        <span class="flex items-center">
                          <label class="mb-1 mr-2">Renotify</label>
                          <UserOrEmailPicker
                            v-model="item.renotify"
                            name="parameter-name"
                            :title="`@User or Email${
                              type !== 'NetRoute' ? ' or /Handle' : ''
                            }`"
                            always-text-mode
                            rules="required"
                            disable-justify-around
                            :disabled-team-integration="type === 'NetRoute'"
                          />
                        </span>
                      </MCol>
                      <MCol :size="2" class="flex items-center">
                        <span class="flex items-center">
                          <label class="mb-1 mr-2">every</label>
                          <FlotoFormItem
                            :rules="
                              item.renotify && item.renotify.length
                                ? { required: true }
                                : {}
                            "
                          >
                            <FlotoDropdownPicker
                              id="notify-id"
                              v-model="item.every"
                              as-input
                              :allow-clear="false"
                              :options="renotifyTimeOptions"
                            />
                          </FlotoFormItem>
                        </span>
                      </MCol>
                      <MCol :size="3" class="flex items-center">
                        <span class="flex items-center">
                          <label class="mb-1 mr-2">if alert stays in</label>
                          <FlotoFormItem
                            :rules="
                              item.renotify && item.renotify.length
                                ? { required: true }
                                : {}
                            "
                          >
                            <FlotoDropdownPicker
                              v-model="item.severity"
                              :value="item.severity"
                              :searchable="false"
                              :disabled-options="
                                allActions.map((i) => i.severity)
                              "
                              :options="dynamicRenotifySeverityOptions"
                              @change="
                                update({ ...(item || {}), severity: $event })
                              "
                            />
                          </FlotoFormItem>
                        </span>
                      </MCol>
                      <MCol :size="3">
                        <span
                          v-if="total > 1"
                          id="remove-counter"
                          class="mr-2"
                          @click="remove"
                        >
                          <MIcon
                            name="times-circle"
                            class="cursor-pointer text-secondary-red"
                            size="lg"
                          />
                        </span>
                        <a
                          v-if="isLastItem && canAdd"
                          class="ml-2"
                          @click="add"
                        >
                          <MIcon
                            name="plus-circle"
                            class="text-primary"
                            size="lg"
                          />
                        </a>
                      </MCol>
                    </MRow>
                  </template>
                </MultipleFormItems>
              </MCol>
              <MCol
                v-if="
                  policy.subType === $currentModule.getConfig().THRESHOLD ||
                  type === $currentModule.getConfig().AVAILABILITY
                "
                :size="12"
                class="flex my-2 items-center material-input"
              >
                <!-- <span class="mr-8"> Monitor Polling Failed Notification</span> -->
                <!-- <MRadioGroup
                id="group-btn"
                v-model="notification.hasPollingFailed"
                class="mr-10"
                as-button
                :options="yesNoOptions"
              /> -->
                <!-- <template v-if="notification.hasPollingFailed === 'yes'">
                <span class="mr-1"
                  >Renotify Team if monitor polling failed
                </span>

                <FlotoDropdownPicker
                  v-model="notification.pollingFailedNotify"
                  :allow-clear="false"
                  as-input
                  :options="pollingFailednotifyOptions"
                />
              </template> -->
              </MCol>

              <MCol v-if="notification.renotification">
                <span>
                  <label class="mb-1">Do not renotify if acknowledged</label>
                  <MTooltip
                    id="error-message"
                    overlay-class-name="readable-content-overlay"
                  >
                    <template v-slot:trigger>
                      <MIcon
                        name="info-circle"
                        class="text-primary cursor-pointer mx-2"
                      />
                    </template>
                    <div style="white-space: preline">
                      {{ $message('renotify_acknowledged') }}
                    </div>
                  </MTooltip>
                </span>
                <MSwitch
                  v-if="notification.renotification"
                  id="change-password-switch"
                  v-model="notification.renotifyAcknowledged"
                  :checked="notification.renotifyAcknowledged"
                  checked-children="ON"
                  un-checked-children="OFF"
                />
              </MCol>

              <MCol
                v-if="
                  type === $currentModule.getConfig().LOG ||
                  type === $currentModule.getConfig().FLOW ||
                  type === $currentModule.getConfig().APM
                "
                :size="12"
                class="flex my-2 items-center material-input"
              >
                <!-- <div id="suppress-action-message" class="label-strip">
                Suppress Action
              </div>

              <FlotoFormItem class="ml-3 mt-3">
                <MSwitch
                  id="suppress-action"
                  v-model="notification.suppressAction"
                  :checked="notification.suppressAction"
                  class=""
                  checked-children="ON"
                  un-checked-children="OFF"
                />
              </FlotoFormItem> -->
                <!-- <template v-if="notification.suppressAction">
                <div class="inline-flex items-start">
                  <FlotoFormItem
                    label="Suppress Window"
                    rules="required|numeric"
                    class="ml-4"
                  >
                    <FlotoFormItem
                      v-model="notification.suppressWindow"
                      type="number"
                      :precision="0"
                    />
                  </FlotoFormItem>
                  <FlotoFormItem
                    class="ml-4"
                    label=" "
                    vid="rollingWindowTime"
                    rules="required"
                  >
                    <FlotoDropdownPicker
                      id="rolling-window-time"
                      v-model="notification.rollingWindowTime"
                      as-input
                      :options="rollingWindowTimeOptions"
                    />
                  </FlotoFormItem>
                </div>
              </template> -->
              </MCol>
            </div>
          </MCol>
        </MRow>
      </div>
    </div>
  </UserProvider>
</template>

<script>
import MultipleFormItems from '@components/multiple-form-items.vue'
// import SeverityPicker from '@components/severity-picker.vue'
import { renotifyTimeOptions } from '@/src/modules/settings/policy-settings/helpers/dropdown-options'
import CloneDeep from 'lodash/cloneDeep'
import UserOrEmailPicker from '@components/data-picker/user-or-email-picker.vue'
import UserProvider from '@/src/components/data-provider/user-provider.vue'

export default {
  name: 'NotifyTeam',
  components: {
    MultipleFormItems,
    UserOrEmailPicker,
    UserProvider,
  },
  model: { event: 'change', prop: 'actions' },
  props: {
    notification: { type: Object, required: true },
    type: { type: String, required: true },
    policy: { type: Object, required: true },
  },
  data() {
    this.renotifyTimeOptions = CloneDeep(renotifyTimeOptions)
    this.defaultNotificationItemTemplate = {
      // severity: 'CRITICAL',
    }

    this.yesNoOptions = [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
    ]
    this.renotifyOptions = [
      { key: 0, text: 'Never' },
      { key: 60 * 60, text: 'Every 1 hour' },
      { key: 2 * 60 * 60, text: 'Every 2 hour' },
      { key: 5 * 60 * 60, text: 'Every 5 hour' },
      { key: 12 * 60 * 60, text: 'Every 12 hour' },
      // { key: 24 * 60 * 60, text: 'Every 24 hour' },
    ]
    // this.pollingFailednotifyOptions = [
    //   { key: 0, text: 'Always' },
    //   { key: 60 * 60, text: 'Every 1 hour' },
    //   { key: 2 * 60 * 60, text: 'Every 2 hour' },
    //   { key: 5 * 60 * 60, text: 'Every 5 hour' },
    //   { key: 12 * 60 * 60, text: 'Every 12 hour' },
    //   { key: 24 * 60 * 60, text: 'Every 24 hour' },
    // ]
    this.rollingWindowTimeOptions = [
      { key: 'second', text: 'Second' },
      { key: 'minute', text: 'Minute' },
      { key: 'hour', text: 'Hour' },
      { key: 'day', text: 'Day' },
      // { key: 'month', text: 'Month' },
      // { key: 'year', text: 'Year' },
    ]
    this.severityOptions = ['CRITICAL', 'WARNING', 'MAJOR', 'CLEAR'].map(
      (i) => ({
        key: i,
        text: i,
      })
    )

    this.availabilitySeverityOptions = ['DOWN', 'CLEAR'].map((i) => ({
      key: i,
      text: i,
    }))

    this.availabilityRenotifySeverityOptions = ['DOWN'].map((i) => ({
      key: i,
      text: i,
    }))

    this.metricRenotifySeverityOptions = ['CRITICAL', 'WARNING', 'MAJOR'].map(
      (i) => ({
        key: i,
        text: i,
      })
    )

    this.anomalyForecastSeverityOptions = ['CRITICAL', 'WARNING', 'MAJOR'].map(
      (i) => ({
        key: i,
        text: i,
      })
    )
    return {
      isExpand: false,
      defaultNotificationItemTemplate: {
        // ...(this.type === 'Metric'
        //   ? { severity: ['CRITICAL', 'MAJOR', 'WARNING', 'CLEAR'] }
        //   : this.type === 'Availability'
        //   ? {
        //       severity: ['DOWN', 'UNREACHABLE', 'UP'],
        //     }
        //   : this.type === 'Forecast' || this.type === 'Anomaly'
        //   ? {
        //       severity: ['CRITICAL', 'MAJOR', 'WARNING'],
        //     }
        //   : this.type === 'Log' || this.type === 'Flow' || this.type === 'Trap'
        //   ? {
        //       severity: ['CRITICAL', 'MAJOR', 'WARNING', 'CLEAR'],
        //     }
        //   : {}),
      },
      defaultRenotificationItemTemplate: {
        every: 1800,
        // ...(this.type === 'Metric' ||
        // this.type === 'Anomaly' ||
        // this.type === 'Forecast'
        //   ? { severity: 'CRITICAL' }
        //   : this.type === 'Availability'
        //   ? { severity: 'DOWN' }
        //   : {}),
      },
    }
  },
  computed: {
    isNetRouteHopToHop() {
      return (
        this.policy.type === this.$currentModule.getConfig().NETROUTE &&
        (this.policy.conditions.routeEvaluationType || '') ===
          this.$constants.HOP_TO_HOP
      )
    },
    allActions: {
      get() {
        return (this.notification || {}).renotifySeverity || []
      },
      set(renotifySeverity) {
        this.$emit('change', { ...this.notification, renotifySeverity })
      },
    },
    dynamicSeverityOptions() {
      if (
        this.policy.conditions?.metric === 'status' &&
        this.type === this.$currentModule.getConfig().NETROUTE
      ) {
        return ['DOWN', 'CLEAR'].map((i) => ({
          key: i,
          text: i,
        }))
      }

      if (
        [
          this.$currentModule.getConfig().ANOMALY,
          this.$currentModule.getConfig().FORECAST,
        ].includes(this.type)
      ) {
        return this.anomalyForecastSeverityOptions
      }

      if (this.type === this.$currentModule.getConfig().AVAILABILITY) {
        return this.availabilitySeverityOptions
      }

      return this.severityOptions
    },
    dynamicRenotifySeverityOptions() {
      if (
        this.policy.conditions?.metric === 'status' &&
        this.type === this.$currentModule.getConfig().NETROUTE
      ) {
        return ['DOWN'].map((i) => ({
          key: i,
          text: i,
        }))
      }
      if (
        [
          this.$currentModule.getConfig().ANOMALY,
          this.$currentModule.getConfig().FORECAST,
        ].includes(this.type)
      ) {
        return this.anomalyForecastSeverityOptions
      }

      if (this.type === this.$currentModule.getConfig().AVAILABILITY) {
        return this.availabilityRenotifySeverityOptions
      }

      return this.metricRenotifySeverityOptions
    },
  },
}
</script>
