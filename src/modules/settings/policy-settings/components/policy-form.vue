<template>
  <HandlerProvider ref="handlerProviderRef">
    <FlotoFixedView :gutter="0">
      <MRow :gutter="0">
        <MCol>
          <FlotoPageHeader
            use-divider
            :back-link="$currentModule.getRoute(`${policyType}`)"
            :title="`${formData.id ? 'Edit' : 'Create'} Policy`"
          />
        </MCol>
      </MRow>
      <MRow
        :gutter="0"
        class="flex-1 min-h-0"
        :class="{ 'form-edit-mode': isEditMode }"
      >
        <MCol :size="2" :gutter="0">
          <MMenu
            id="policy-type-tab"
            class="selection-menu pt-0 pr-0 h-100"
            :selected-keys="[formData.type]"
            @select="setType"
          >
            <MMenuItem v-for="type in availableTypes" :key="type">
              {{ type }}
            </MMenuItem>
          </MMenu>
        </MCol>
        <GroupProvider>
          <MCol
            class="h-100 policy-form pt-0 bg-neutral-lightest dashboard-container"
            :size="10"
          >
            <FlotoScrollView class="overflow-x-hidden">
              <div class="px-3 mb-3 mt-2">
                <MRow :gutter="0">
                  <MCol class="w-full">
                    <FlotoForm
                      :loading="processing"
                      :submit-text="
                        isEditMode ? 'Update Policy' : 'Create Policy'
                      "
                      allow-reset
                      class="flip-buttons"
                      reset-text="Reset"
                      @reset="handleReset"
                      @submit="handleSubmit"
                    >
                      <h4 class="text-primary flex items-center">
                        {{ formData.type }} Policy
                        <!-- <MTooltip>
                        <template v-slot:trigger>
                          <MIcon name="info-circle" class="text-primary ml-2" />
                        </template>
                        Policy comes into effect after installation of agent
                        (300ms)
                      </MTooltip> -->
                      </h4>
                      <MRow v-if="formData.type === 'Availability'">
                        <MCol :size="12">
                          <span class="text-neutral">
                            Configure Availability Policy to receive alerts on
                            monitor status changes and take corrective actions
                            as needed. For more information:
                          </span>
                          <a
                            href="https://docs.motadata.com/motadata-aiops-docs/alerts-and-policies/basic-policies/availability-policy"
                            target="_blank"
                            >Availability Policy</a
                          >
                          <MIcon
                            name="external-link"
                            class="ml-1 text-primary"
                          />
                        </MCol>
                      </MRow>

                      <MRow v-if="formData.type === 'Metric'">
                        <MCol :size="12">
                          <span class="text-neutral">
                            Configure Metric Policy to receive alerts on metrics
                            surpassing defined thresholds and take corrective
                            actions as needed. For more information:
                          </span>
                          <a
                            href="https://docs.motadata.com/motadata-aiops-docs/alerts-and-policies/basic-policies/metric-policy"
                            target="_blank"
                            >Metric Policy</a
                          >
                          <MIcon
                            name="external-link"
                            class="ml-1 text-primary"
                          />
                        </MCol>
                      </MRow>

                      <MRow v-if="formData.type === 'Log'">
                        <MCol :size="12">
                          <span class="text-neutral">
                            Configure Log Policy to receive alerts on critical
                            log events and take corrective actions as needed.
                            For more information:
                          </span>
                          <a
                            href="https://docs.motadata.com/motadata-aiops-docs/alerts-and-policies/basic-policies/log-policy"
                            target="_blank"
                            >Log Policy</a
                          >
                          <MIcon
                            name="external-link"
                            class="ml-1 text-primary"
                          />
                        </MCol>
                      </MRow>

                      <MRow v-if="formData.type === 'Flow'">
                        <MCol :size="12">
                          <span class="text-neutral">
                            Configure Flow Policy to receive alerts on network
                            flow data and take corrective actions as needed. For
                            more information:
                          </span>
                          <a
                            href="https://docs.motadata.com/motadata-aiops-docs/alerts-and-policies/basic-policies/flow-policy"
                            target="_blank"
                            >Flow Policy</a
                          >
                          <MIcon
                            name="external-link"
                            class="ml-1 text-primary"
                          />
                        </MCol>
                      </MRow>

                      <MRow v-if="formData.type === 'Trap'">
                        <MCol :size="12">
                          <span class="text-neutral">
                            Configure Trap Policy to receive alerts on SNMP
                            traps and take corrective actions as needed. For
                            more information:
                          </span>
                          <a
                            href="https://docs.motadata.com/motadata-aiops-docs/alerts-and-policies/basic-policies/trap-policy"
                            target="_blank"
                            >Trap Policy</a
                          >
                          <MIcon
                            name="external-link"
                            class="ml-1 text-primary"
                          />
                        </MCol>
                      </MRow>

                      <MRow v-if="formData.type === 'Anomaly'">
                        <MCol :size="12">
                          <span class="text-neutral">
                            Configure Anomaly Policy to receive alerts on
                            anomalous/abnormal behavior in system metrics and
                            take corrective actions as needed. For more
                            information:
                          </span>
                          <a
                            href="https://docs.motadata.com/motadata-aiops-docs/alerts-and-policies/aiops-policies/anomaly-policy"
                            target="_blank"
                            >Anomaly Policy</a
                          >
                          <MIcon
                            name="external-link"
                            class="ml-1 text-primary"
                          />
                        </MCol>
                      </MRow>

                      <MRow v-if="formData.type === 'Forecast'">
                        <MCol :size="12">
                          <span class="text-neutral">
                            Configure Forecast Policy to receive alerts on
                            deviations from predicted metric values for
                            proactive issue identification and take corrective
                            actions as needed. For more information:
                          </span>
                          <a
                            href="https://docs.motadata.com/motadata-aiops-docs/alerts-and-policies/aiops-policies/forecast-policy"
                            target="_blank"
                          >
                            Forecast Policy</a
                          >
                          <MIcon
                            name="external-link"
                            class="ml-1 text-primary"
                          />
                        </MCol>
                      </MRow>

                      <MRow v-if="formData.type === 'NetRoute'">
                        <MCol :size="12">
                          <span class="text-neutral">
                            Configure netroute Policy to receive alerts on
                            network paths to identify network battlenecks. For
                            more information:
                          </span>
                          <a
                            href="https://docs.motadata.com/motadata-aiops-docs/alerts-and-policies/basic-policies/netroute-policy/"
                            target="_blank"
                          >
                            NetRoute Policy</a
                          >
                          <MIcon
                            name="external-link"
                            class="ml-1 text-primary"
                          />
                        </MCol>
                      </MRow>

                      <div class="box-line policy-form-panel">
                        <div class="form-box-container form-box-border">
                          <BasicInfo
                            :policy.sync="formData"
                            :type="formData.type"
                            :is-edit-mode="isEditMode"
                          />
                        </div>
                      </div>
                      <div class="box-line policy-form-panel">
                        <div class="line" />
                        <Conditions
                          v-model="formData.conditions"
                          class="form-box-border"
                          :type="formData.type"
                          :policy="formData"
                          :is-edit-mode="isEditMode"
                        />

                        <AlertMessage
                          :alert="formData.alert"
                          :type="formData.type"
                        />
                        <NotifyTeam
                          class="form-box-border"
                          :notification="formData.notification"
                          :type="formData.type"
                          :policy="formData"
                        />
                        <Actions
                          v-if="formData.type !== 'NetRoute'"
                          v-model="formData.actions"
                          class="form-box-border"
                          :type="formData.type"
                        />
                        <DeclareIncident
                          v-if="
                            ['Metric', 'Availability'].includes(formData.type)
                          "
                          v-model="formData.incidents"
                          class="form-box-border"
                          :type="formData.type"
                        />
                      </div>
                    </FlotoForm>
                  </MCol>
                </MRow>
              </div>
            </FlotoScrollView>
          </MCol>
        </GroupProvider>
      </MRow>
    </FlotoFixedView>
  </HandlerProvider>
</template>

<script>
import Invert from 'lodash/invert'
import CloneDeep from 'lodash/cloneDeep'
import { authComputed } from '@state/modules/auth'

import GroupProvider from '@components/data-provider/group-provider.vue'
import BasicInfo from './policy-form/basic-info.vue'
import Conditions from './policy-form/conditions.vue'
import AlertMessage from './policy-form/alert-message.vue'
import NotifyTeam from './policy-form/notify-team.vue'
import Actions from './policy-form/actions.vue'
import DeclareIncident from './policy-form/declare-incident.vue'
import { AVAILABLE_POLICY_TYPES } from '../helpers/policy'
import Config from '../config'
import HandlerProvider from '@/src/components/data-provider/handler-provider.vue'
import { generateId } from '@/src/utils/id'

export default {
  name: 'PolicyForm',
  components: {
    GroupProvider,
    BasicInfo,
    Conditions,
    AlertMessage,
    NotifyTeam,
    Actions,
    DeclareIncident,
    HandlerProvider,
  },
  props: {
    processing: { type: Boolean, default: false },
    defaultValue: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    //  this.availableTypes = Object.values(AVAILABLE_POLICY_TYPES)

    return {
      formData: CloneDeep(this.defaultValue),
      isEditMode: Boolean(this.defaultValue.id),
    }
  },
  computed: {
    ...authComputed,
    policyType() {
      if (/log/i.test(this.$route.params.policyType)) {
        return 'log'
      } else if (/flow/i.test(this.$route.params.policyType)) {
        return 'flow'
      } else if (/trap/i.test(this.$route.params.policyType)) {
        return 'trap'
      } else if (/netroute/i.test(this.$route.params.policyType)) {
        return 'netroute'
      }
      return 'metric'
    },
    availableTypes() {
      let availableTypes = Object.values(AVAILABLE_POLICY_TYPES)

      if (
        !this.hasLicensePermission(this.$constants.ANOMALY_OUTLINER_FORECAST)
      ) {
        availableTypes = availableTypes.filter(
          (type) => ![Config.ANOMALY, Config.FORECAST].includes(type)
        )
      }

      if (
        !this.hasLicensePermission(this.$constants.NETROUTE_LICENSE_PERMISSION)
      ) {
        availableTypes = availableTypes.filter(
          (type) => ![Config.NETROUTE].includes(type)
        )
      }

      if (
        !this.hasLicensePermission(this.$constants.ANOMALY_LICENSE_PERMISSION)
      ) {
        availableTypes = availableTypes.filter(
          (type) => ![Config.ANOMALY].includes(type)
        )
      }
      if (
        !this.hasLicensePermission(this.$constants.FORECAST_LICENSE_PERMISSION)
      ) {
        availableTypes = availableTypes.filter(
          (type) => ![Config.FORECAST].includes(type)
        )
      }
      if (
        !this.hasLicensePermission(this.$constants.LOG_FLOW_LICENSE_PERMISSION)
      ) {
        availableTypes = availableTypes.filter(
          (type) => ![Config.LOG, Config.FLOW].includes(type)
        )
      }
      return availableTypes
    },
  },
  watch: {
    'formData.conditions.routeEvaluationType': function (newValue, oldValue) {
      if (newValue !== oldValue && newValue) {
        this.formData = {
          ...this.formData,
          notification: {
            ...this.formData.notification,
            renotification: false,
            renotifySeverity: [{ key: generateId(), every: 1800 }],
            notifySeverity: [{ key: generateId() }],
          },
        }
      }
    },
  },
  mounted() {
    const setup = this.$route.query._setup
    if (setup) {
      try {
        const decodedSetup = JSON.parse(atob(decodeURIComponent(setup)))
        if (decodedSetup.defaultData) {
          this.formData = {
            ...this.formData,
            ...this.setType({ key: decodedSetup.defaultData.type }, setup),
            ...decodedSetup.defaultData,
          }
        }
      } catch (e) {}
    }
  },

  methods: {
    setType(type, decodedSetup) {
      if (this.isEditMode) {
        return
      }
      this.$router.replace(
        this.$currentModule.getRoute('create-policy', {
          params: {
            policyType:
              Invert(AVAILABLE_POLICY_TYPES)[type.key] ||
              this.$route.params.policyType,
          },
          ...(decodedSetup ? { query: { _setup: decodedSetup } } : {}),
        })
      )
    },
    handleReset() {
      this.formData = CloneDeep(this.defaultValue)
    },
    handleSubmit() {
      let handlerMap

      if (this.$refs.handlerProviderRef) {
        handlerMap = this.$refs.handlerProviderRef.gethandlerMaps()
      }

      if (
        this.formData.type === this.$currentModule.getConfig().METRIC ||
        this.formData.type === this.$currentModule.getConfig().SUDDEN_SHIFT ||
        this.formData.subType === this.$currentModule.getConfig().METRIC
        // ||        this.formData.type === this.$currentModule.getConfig().FORECAST
      ) {
        let isOperatorNotSelected = ['critical', 'major', 'warning'].find(
          (s) => {
            const value = this.formData.conditions[s].value
            const operator = this.formData.conditions[s].operator
            return (
              value !== undefined &&
              value !== null &&
              value !== '' &&
              operator === undefined
            )
          }
        )
        let checkRules =
          (this.formData.conditions.critical.value === undefined ||
            this.formData.conditions.critical.value.trim() === '') &&
          (this.formData.conditions.major.value === undefined ||
            this.formData.conditions.major.value.trim() === '') &&
          (this.formData.conditions.warning.value === undefined ||
            this.formData.conditions.warning.value.trim() === '')
        if (checkRules) {
          this.$errorNotification({
            message: 'Error',
            description: 'One of the severity is required',
          })
        } else if (isOperatorNotSelected) {
          this.$errorNotification({
            message: 'Error',
            description: 'Operator is required',
          })
        } else {
          this.$emit('submit', this.formData, handlerMap)
        }
      } else {
        this.$emit('submit', this.formData, handlerMap)
      }
    },
  },
}
</script>
<style lang="less" scoped>
.form-box-border {
  border: 1px solid var(--policy-border-color) !important;
}
</style>
