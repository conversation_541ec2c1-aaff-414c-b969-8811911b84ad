<template>
  <div>
    <MRow>
      <MCol :size="6">
        <FlotoFormItem
          id="role-name"
          v-model="value.roleName"
          placeholder="Must be unique"
          auto-focus
          rules="required"
          label="Role Name"
          name="role-name"
          :disabled="isView"
        />
      </MCol>
      <MCol :size="6">
        <FlotoFormItem
          v-model="value.roleDescription"
          label="Role Description"
          name="role-description"
          :disabled="isView"
        />
      </MCol>
    </MRow>
    <MRow :gutter="0">
      <MCol>
        <div
          class="bordered rounded mb-2 bg-neutral-lightest"
          style="line-height: 0"
        >
          <PermissionSectionBody
            :feature="{ text: 'All Module' }"
            :selected-permissions="value.permissions"
            :disabled-permissions="mergedDisabledPermissions"
            :available-permissions="permissions"
            display-checkbox-help
            :sections="sections"
            :disabled="isView"
            parent-header
            @change="checkboxChangeAllModule($event)"
          />
        </div>
        <MCollapse
          class="role-form-collapse"
          :destroy-inactive-panel="true"
          :default-active-key="[sections[0].key, 'visualization']"
          :accordion="false"
        >
          <MCollapsePanel
            v-for="section in sections"
            :key="section.key"
            :disabled="section.features.length === 1"
          >
            <template v-slot:header>
              <PermissionSectionHeader
                :section="section"
                :disabled="isView"
                :selected-permissions="value.permissions"
                :disabled-permissions="mergedDisabledPermissions"
                :available-permissions="permissions"
                @change="headerCheckboxChange($event, section)"
              />
            </template>
            <PermissionSectionBody
              v-for="feature in section.features"
              :key="feature.key"
              :feature="feature"
              :disabled="isView"
              :selected-permissions="value.permissions"
              :disabled-permissions="mergedDisabledPermissions"
              :available-permissions="permissions"
              @change="bodyCheckboxChange($event, feature)"
            />
          </MCollapsePanel>
        </MCollapse>
      </MCol>
    </MRow>
  </div>
</template>

<script>
import Differnce from 'lodash/difference'
import PermissionSectionHeader from './permission-section-header.vue'
import PermissionSectionBody from './permission-section-body.vue'
import { getAutoAppliedPermissions } from '../helpers/permission-dependency'

export default {
  name: 'RoleForm',
  components: { PermissionSectionHeader, PermissionSectionBody },
  props: {
    updateFn: { type: Function, default: (e) => e },
    value: { type: Object, required: true },
    preAppliedPermissions: {
      type: Array,
      default() {
        return []
      },
    },
    isView: { type: Boolean, required: false },
  },
  data() {
    return {
      autoAppliedPermissions: [],
      sections: [
        {
          text: 'General Settings',
          key: 'general-settings',
          features: [
            { text: 'User Settings', key: 'user-settings' },
            { text: 'System Settings', key: 'system-settings' },
            { text: 'Group Settings', key: 'group-settings' },
            { text: 'My Account', key: 'my-account-settings' },
          ],
        },
        {
          text: 'Monitoring',
          key: 'monitoring',
          features: [
            { text: 'Discovery Settings', key: 'discovery-settings' },
            { text: 'Monitor Settings', key: 'monitor-settings' },
            { text: 'Plugin Library Settings', key: 'plugin-library-settings' },
            { text: 'Agent Settings', key: 'agent-settings' },
            { text: 'NetRoute Settings', key: 'netroute-settings' },
          ],
        },
        {
          text: 'Policy Settings',
          key: 'policy-settings',
          features: [{ text: 'Policy Settings', key: 'policy-settings' }],
        },
        {
          text: 'Visualization',
          key: 'visualization',
          features: [
            { text: 'Dashboard', key: 'dashboards' },
            { text: 'Template', key: 'templates' },
            { text: 'Widget', key: 'widgets' },
            { text: 'Inventory', key: 'inventory' },
            { text: 'Metric Explorer', key: 'metric-explorers' },
            { text: 'Log Explorer', key: 'log-explorer' },
            { text: 'Flow Explorer', key: 'flow-explorer' },
            { text: 'NetRoute', key: 'netroute-explorer' },
            { text: 'Trap Explorer', key: 'trap-explorer' },
            { text: 'Topology', key: 'topology' },
            { text: 'Reports', key: 'reports' },
          ],
        },
        {
          text: 'Data Collection',
          key: 'data-collection',
          features: [
            { text: 'Log Settings', key: 'log-settings' },
            { text: 'Flow Settings', key: 'flow-settings' },
          ],
        },
        {
          text: 'SNMP Trap Settings',
          key: 'snmp-trap-settings',
          features: [{ text: 'SNMP Trap Settings', key: 'snmp-trap-settings' }],
        },
        {
          text: 'AIOps',
          key: 'aiops',
          features: [{ text: 'AiOps Settings', key: 'aiops-settings' }],
        },
        {
          text: 'Alert',
          key: 'alert',
          features: [{ text: 'Alert', key: 'alert-explorer' }],
        },
        {
          text: 'Audit',
          key: 'audit',
          features: [{ text: 'Audit', key: 'audit-settings' }],
        },
        {
          text: 'Network Config Management',
          key: 'config',
          features: [{ text: 'NCM', key: 'config' }],
        },
        {
          text: 'Compliance Management',
          key: 'compliance-settings',
          features: [{ text: 'Compliance', key: 'compliance-settings' }],
        },
        {
          text: 'Health Monitoring',
          key: 'health-monitoring',
          features: [{ text: 'Health Monitoring', key: 'health-monitoring' }],
        },
        {
          text: 'Service Level Objective',
          key: 'slo',
          features: [
            {
              text: 'Service Level Objective',
              key: 'slo',
            },
          ],
        },
      ],
      permissions: [
        { text: 'Read', key: 'read' },
        { text: 'Read & Write', key: 'read-write' },
        { text: 'Delete', key: 'delete' },
      ],
    }
  },
  computed: {
    mergedDisabledPermissions() {
      const appliedPermissions = this.value.permissions
      const deletePermissions = appliedPermissions.filter((p) =>
        /:delete$/.test(p)
      )
      const readWritePermissions = appliedPermissions.filter((p) =>
        /:read-write$/.test(p)
      )
      return this.disabledPermissions
        .concat(this.autoAppliedPermissions)
        .concat([
          ...deletePermissions.reduce((prev, permission) => {
            const moduleName = permission.split(':')[0]
            return [...prev, `${moduleName}:read`, `${moduleName}:read-write`]
          }, []),
          ...readWritePermissions.reduce((prev, permission) => {
            const moduleName = permission.split(':')[0]
            return [...prev, `${moduleName}:read`]
          }, []),
        ])
    },
    disabledPermissions() {
      return [
        'audit-settings:read-write',
        'audit-settings:read-write',
        'audit-settings:delete',
        'my-account-settings:read',
        'my-account-settings:read-write',
        'my-account-settings:delete',
        'dashboards:read',
        // 'widgets:read',
        // 'monitor-settings:read',
        // 'group-settings:read',
        'notification-settings:read-write',
        'notification-settings:read-write',
        'notification-settings:delete',
        'inventory:read-write',
        'inventory:delete',
        // 'metric-explorer:read-write',
        // 'metric-explorer:delete',
        'flow-explorer:read-write',
        'flow-explorer:delete',
        'netroute-explorer:read-write',
        'netroute-explorer:delete',
        'log-explorer:read-write',
        'log-explorer:delete',
        'trap-explorer:read-write',
        'trap-explorer:delete',
        'alert-explorer:delete',
        // 'topology:read-write',
        // 'topology:delete',
        'user-settings:read',
        'health-monitoring:delete',
      ]
    },
  },
  watch: {
    'value.permissions': {
      immediate: true,
      handler(newValue, oldValue) {
        if (Differnce(newValue, oldValue).length > 0) {
          const { autoAppliedPermissions } = getAutoAppliedPermissions(newValue)
          this.autoAppliedPermissions = Object.freeze(autoAppliedPermissions)
        }
      },
    },
  },
  methods: {
    checkboxChangeAllModule(event) {
      let permissions
      let implicitePermissions = this.getImplicitePermissions(event.type)
      if (event.type === 'all') {
        const availablePermissionKeys = this.permissions.map((p) => p.key)
        permissions = this.sections.reduce(
          (prev, section) => [
            ...prev,
            ...section.features.reduce(
              (previous, feature) => [
                ...previous,
                ...availablePermissionKeys.map(
                  (key) => `${feature.key}:${key}`
                ),
                ...implicitePermissions.map((key) => `${feature.key}:${key}`),
              ],
              []
            ),
          ],
          []
        )
      } else {
        permissions = this.sections.reduce(
          (prev, section) => [
            ...prev,
            ...section.features.reduce(
              (previous, feature) => [
                ...previous,
                `${feature.key}:${event.type}`,
                ...(event.value
                  ? implicitePermissions.map((p) => `${feature.key}:${p}`)
                  : []),
              ],
              []
            ),
          ],
          []
        )
      }
      this.updatePermissions(event.value, permissions)
    },
    getImplicitePermissions(permission) {
      let implicitePermissions = []
      if (permission === 'delete') {
        implicitePermissions = ['read', 'read-write']
      } else if (permission === 'read-write') {
        implicitePermissions = ['read']
      }
      return implicitePermissions
    },
    headerCheckboxChange(event, section) {
      let permissions
      let implicitePermissions = this.getImplicitePermissions(event.type)
      if (event.type === 'all') {
        const availablePermissionKeys = this.permissions.map((p) => p.key)
        permissions = section.features.reduce(
          (previous, feature) => [
            ...previous,
            ...availablePermissionKeys.map((key) => `${feature.key}:${key}`),
            ...implicitePermissions.map((key) => `${feature.key}:${key}`),
          ],
          []
        )
      } else {
        permissions = section.features.map(
          (feature) => `${feature.key}:${event.type}`
        )
        if (event.value) {
          permissions = [
            ...permissions,
            ...section.features.reduce(
              (prev, feature) => [
                ...prev,
                ...implicitePermissions.map(
                  (permission) => `${feature.key}:${permission}`
                ),
              ],
              []
            ),
          ]
        }
      }
      this.updatePermissions(event.value, permissions)
    },
    bodyCheckboxChange(event, feature) {
      let permissions
      let implicitePermissions = this.getImplicitePermissions(event.type)
      if (event.type === 'all') {
        const availablePermissionKeys = this.permissions.map((p) => p.key)
        permissions = availablePermissionKeys
          .map((key) => `${feature.key}:${key}`)
          .concat(implicitePermissions.map((key) => `${feature.key}:${key}`))
      } else {
        permissions = [
          `${feature.key}:${event.type}`,
          ...(event.value ? implicitePermissions : []).map(
            (key) => `${feature.key}:${key}`
          ),
        ]
      }
      this.updatePermissions(event.value, permissions)
    },

    updatePermissions(inclusive, permissions) {
      // if checked then add
      let mergedPermissions
      if (inclusive) {
        mergedPermissions = [...this.value.permissions, ...permissions]
      } else {
        mergedPermissions = this.value.permissions.filter(
          (permission) =>
            permissions.includes(permission) === false ||
            this.disabledPermissions.includes(permission) === true
        )
      }
      const { all, autoAppliedPermissions } =
        getAutoAppliedPermissions(mergedPermissions)
      this.autoAppliedPermissions = Object.freeze(autoAppliedPermissions)
      this.updateFn({
        ...this.value,
        permissions: Object.freeze(
          all.filter(
            (p) =>
              this.disabledPermissions.includes(p) === false ||
              this.preAppliedPermissions.includes(p) === true
          )
        ),
      })
    },
  },
}
</script>
