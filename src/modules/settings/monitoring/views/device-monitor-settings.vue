<template>
  <div class="w-full flex flex-col flex-1 -mt-4">
    <MRow class="min-h-0 flex-no-wrap flex-1">
      <MCol class="flex flex-col overflow-hidden">
        <FlotoFixedView>
          <MonitoringFieldsProvider>
            <MonitorList
              :columns="columns"
              :device-types="deviceTypes"
              :actions="actions"
              column-persist-module="device-monitor-settings"
              for-device-monitor
            >
              <template v-slot:additional-fields="{ value }">
                <MCol :size="6">
                  <FlotoFormItem
                    v-model="value.name"
                    rules="required"
                    label="Monitor name"
                    name="monitor"
                  />
                </MCol>
                <MCol :size="6">
                  <FlotoFormItem
                    v-model="value.host"
                    rules="required"
                    label="Host"
                    name="host"
                    :disabled="Boolean(value.id)"
                  />
                </MCol>
              </template>
            </MonitorList>
          </MonitoringFieldsProvider>
        </FlotoFixedView>
      </MCol>
    </MRow>
  </div>
</template>

<script>
import Constants from '@constants'
import MonitoringFieldsProvider from '@components/data-provider/monitoring-fields-provider.vue'
import MonitorList from '../components/monitor-list.vue'

export default {
  name: 'DeviceMonitorSettings',
  components: { MonitorList, MonitoringFieldsProvider },
  data() {
    this.columns = [
      {
        key: 'name',
        name: 'Monitor',
        searchable: true,
        sortable: true,
        disable: true,
      },
      {
        key: 'ip',
        name: 'IP',
        searchable: true,
        sortable: true,
        width: '120px',
        disable: true,
      },
      {
        key: 'host',
        name: 'Host',
        searchable: true,
        sortable: true,
        disable: true,
      },
      {
        key: 'instanceCount',
        name: 'Instances Count',
        searchable: true,
        sortable: true,
        minWidth: '60px',
      },
      {
        key: 'groups',
        name: 'Groups',
        contextKey: 'groupContext',
        searchable: true,
        sortable: true,
        searchKey: 'groupsDisplay',
        sortKey: 'groupsDisplay',
      },
      {
        key: 'tags',
        name: 'Tags',
        searchable: true,
        sortable: true,
        hidden: true,
        searchKey: 'tagsStr',
        sortKey: 'tagsStr',
      },
      {
        key: 'type',
        name: 'Type',
        align: 'center',
        width: '75px',
        searchable: true,
        sortable: true,
        disable: true,
      },
      {
        key: 'apps',
        name: 'Apps',
        minWidth: '160px',
        searchable: true,
        searchKey: 'appsSearchKey',
        sortable: true,
        sortKey: 'appsSearchKey',
      },
      {
        key: 'status',
        name: 'Status',
        sortable: true,
        searchable: true,
        width: '150px',
      },
      {
        key: 'schedule',
        name: 'Scheduler',
        width: '120px',
        hidden: true,
        export: false,
      },
      {
        key: 'objectId',
        name: 'Object Id',
        width: '120px',
        hidden: true,
        sortable: true,
        searchable: true,
      },
      {
        key: 'id',
        name: 'ID',
        width: '120px',
        hidden: true,
        sortable: true,
        searchable: true,
      },
      {
        key: 'action',
        name: 'Actions',
        align: 'right',
        width: '120px',
        export: false,
      },
    ]
    this.actions = [
      {
        key: 'enable',
        name: 'Enable',
        icon: 'monitor-enable',
        message: this.$message('monitor_action', {
          action: 'enable monitor',
        }),
      },
      {
        key: 'disable',
        name: 'Disable',
        icon: 'monitor-disable',
        message: this.$message('monitor_action', {
          action: 'disable monitor',
        }),
        note: {
          category: [Constants.NETWORK],
          message: this.$message('config_device_disable_note'),
        },
      },
      {
        key: 'off-maintainance',
        name: 'Off Maintenance',
        icon: 'circle',
        message: this.$message('monitor_action', {
          action: 'remove monitor from maintenance',
        }),
      },
      {
        key: 'on-maintainance',
        name: 'On Maintenance',
        icon: 'cog',
        message: this.$message('monitor_action', {
          action: 'put monitor on maintenance',
        }),
        note: {
          category: [Constants.NETWORK],
          message: this.$message('config_device_on_maintainance_note'),
        },
      },
      { key: 'd1', type: 'divider' },
      {
        key: 'schedule-maintainance',
        name: 'Schedule Maintenance',
        icon: 'schedule',
      },
      {
        key: 'metric-collection-time',
        name: 'Metric Settings',
        icon: 'metric-collection-time',
      },
      { key: 'd2', type: 'divider' },
      { key: 'edit', name: 'Edit', icon: 'pencil' },
      { key: 'delete', name: 'Delete', icon: 'trash-alt', isDanger: true },
    ]
    this.deviceTypes = [
      Constants.SERVER,
      Constants.NETWORK,
      Constants.WIRELESS,
      Constants.VIRTUALIZATION,
      Constants.OTHER,
      Constants.HYPERCONVERGED_INFRASTRUCTURE,
      Constants.SDN,
      Constants.STORAGE,
      Constants.CONTAINER_ORCHESTRATION,
    ]
    return {}
  },
}
</script>
