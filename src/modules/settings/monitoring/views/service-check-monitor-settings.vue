<template>
  <div class="w-full flex flex-col flex-1 -mt-4">
    <MRow class="min-h-0 flex-no-wrap flex-1">
      <MCol class="flex flex-col overflow-hidden">
        <FlotoFixedView>
          <AgentProvider>
            <MonitoringFieldsProvider>
              <MonitorList
                :columns="columns"
                :device-types="deviceTypes"
                column-persist-module="service-check-monitor-settings"
                :actions="actions"
              >
                <template v-slot:additional-fields="{ value }">
                  <MCol :size="6">
                    <FlotoFormItem
                      v-model="value.serviceCheckName"
                      rules="required"
                      label="Monitor name"
                      name="monitor"
                    />
                  </MCol>
                  <MCol :size="6">
                    <FlotoFormItem
                      v-model="value.host"
                      rules="required"
                      label="Host"
                      name="host"
                      :disabled="Boolean(value.id)"
                    />
                  </MCol>
                </template>
              </MonitorList>
            </MonitoringFieldsProvider>
          </AgentProvider>
        </FlotoFixedView>
      </MCol>
    </MRow>
  </div>
</template>

<script>
import Constants from '@constants'
import AgentProvider from '@components/data-provider/agent-provider.vue'
import MonitoringFieldsProvider from '@components/data-provider/monitoring-fields-provider.vue'
import MonitorList from '../components/monitor-list.vue'

export default {
  name: 'ServiceCheckMonitorSettings',
  components: { MonitorList, MonitoringFieldsProvider, AgentProvider },
  data() {
    this.columns = [
      {
        key: 'serviceCheckName',
        name: 'Monitor',
        searchable: true,
        sortable: true,
        disable: true,
      },
      {
        key: 'agent',
        name: 'Agent',
        searchable: true,
        sortable: true,
        contextKey: 'agentContext',
        searchKey: 'agentDisplay',
        sortKey: 'agentDisplay',
        cellRender: 'agent',
        width: '150px',
        disable: true,
      },
      {
        key: 'type',
        name: 'Type',
        align: 'center',
        searchable: true,
        sortable: true,
        width: '75px',
        disable: true,
      },
      {
        key: 'host',
        name: 'Host',
        searchable: true,
        sortable: true,
      },
      {
        key: 'groups',
        name: 'Groups',
        contextKey: 'groupContext',
        searchable: true,
        sortable: true,
        searchKey: 'groupsDisplay',
        sortKey: 'groupsDisplay',
      },
      {
        key: 'tags',
        name: 'Tags',
        searchable: true,
        sortable: true,
        hidden: true,
        searchKey: 'tagsStr',
        sortKey: 'tagsStr',
      },
      {
        key: 'serviceCheckTarget',
        name: 'Target',
        searchable: true,
        sortable: true,
        disable: true,
      },
      {
        key: 'status',
        name: 'Status',
        width: '150px',
        searchable: true,
        sortable: true,
      },
      {
        key: 'schedule',
        name: 'Scheduler',
        width: '120px',
        hidden: true,
        export: false,
      },
      {
        key: 'objectId',
        name: 'Object Id',
        width: '120px',
        hidden: true,
        sortable: true,
        searchable: true,
      },
      {
        key: 'id',
        name: 'ID',
        width: '120px',
        hidden: true,
        sortable: true,
        searchable: true,
      },
      {
        key: 'action',
        name: 'Actions',
        align: 'right',
        width: '120px',
        export: false,
      },
    ]
    this.actions = [
      {
        key: 'enable',
        name: 'Enable',
        icon: 'monitor-enable',
        message: this.$message('monitor_action', {
          action: 'enable monitor',
        }),
      },
      {
        key: 'disable',
        name: 'Disable',
        icon: 'monitor-disable',
        message: this.$message('monitor_action', {
          action: 'disable monitor',
        }),
      },
      {
        key: 'off-maintainance',
        name: 'Off Maintenance',
        icon: 'circle',
        message: this.$message('monitor_action', {
          action: 'remove monitor from maintenance',
        }),
      },
      {
        key: 'on-maintainance',
        name: 'On Maintenance',
        icon: 'cog',
        message: this.$message('monitor_action', {
          action: 'put monitor on maintenance',
        }),
      },
      { key: 'd1', type: 'divider' },
      {
        key: 'schedule-maintainance',
        name: 'Schedule Maintenance',
        icon: 'schedule',
      },
      {
        key: 'metric-collection-time',
        name: 'Metric Settings',
        icon: 'metric-collection-time',
      },
      { key: 'd2', type: 'divider' },
      { key: 'edit', name: 'Edit', icon: 'pencil' },
      { key: 'delete', name: 'Delete', icon: 'trash-alt', isDanger: true },
    ]
    this.deviceTypes = [Constants.SERVICE_CHECK]
    return {}
  },
}
</script>
