import api from '@api'
import {
  transformScheduleForClient,
  transformScheduleForServer,
} from '@data/scheduler.js'

export const TOPOLOGY_SCANNER_TYPE_NAME_MAPPING = {
  network: 'Network',
  cloud: 'Cloud',
  virtualization: 'Virtualization',
  others: 'Other',
}

export function getTopologyScannerSchedulesApi(config) {
  return api.get('/settings/schedulers', config).then((data) => {
    return (data.result || [])
      .map(transformSchedulersForList)
      .filter((t) => !t.isPluginDiscovery)
  })
}

export function getSchedulerById(id) {
  return api
    .get('/settings/schedulers/' + id, {
      params: {
        filter: {
          'scheduler.job.type': 'Topology',
        },
      },
    })
    .then((data) => transformSchedulersForList(data.result))
}

export function createTopologyScannerScheduleApi(payload) {
  return api
    .post('/settings/schedulers', transformSchedulersForServer(payload))
    .then((response) => {
      return getSchedulerById(response.id)
    })
}

export function updateTopologyScannerScheduleApi(payload) {
  if (payload.stateChange) {
    return api
      .put(`/settings/schedulers/${payload.id}/state`, {
        'scheduler.state': payload.enabled ? 'yes' : 'no',
        'scheduler.job.type': 'Topology',
      })
      .then(() => ({ ...payload, stateChange: false }))
  }
  return api
    .put(
      '/settings/schedulers/' + payload.id,
      transformSchedulersForServer(payload)
    )
    .then((response) => {
      return getSchedulerById(payload.id, false)
    })
}

export function deleteScheduleApi(scheduleId, config) {
  return api.delete('/settings/schedulers/' + scheduleId, config)
}

export function transformSchedulersForList(schedule) {
  const context = schedule['scheduler.context']
  return {
    id: schedule['id'],
    isDefault: schedule['_type'] === '0',
    isInProgress: schedule.state === 'Running',
    scheduleType: schedule['scheduler.timeline'],
    startDate: schedule['scheduler.start.date'],
    dateTime: schedule['scheduler.date.time'],
    timestamp: schedule['event.timestamp'] || 0,
    email: schedule['scheduler.email.recipients'],
    sms: schedule['scheduler.sms.recipients'],
    monitors: context['topology.plugin.entry.points']?.[0],
    ...transformScheduleForClient(schedule),
    ...transformExcludeIPTypeOptionForClient(context),
    // showUnknownDevices: context['topology.plugin.unknown.link'] === 'yes',
    layerProtocol: context['topology.link.layer'],
    protocols: context['topology.protocols'],
    isPluginDiscovery: context['topology.plugin.discovery'],
  }
}
export function transformExcludeIPTypeOptionForClient(schedule) {
  return {
    includeOrExclude:
      (schedule['topology.plugin.filter.target.type'] || '').indexOf(
        'exclude'
      ) >= 0
        ? 'exclude'
        : 'include',
    ...(schedule['topology.plugin.filter.target.type']
      ? {
          includeExcludeTargetType:
            schedule['topology.plugin.filter.target.type'],
          includeExcludeTargets: schedule['topology.plugin.filter.targets'],
        }
      : {}),
  }
  // const map = Invert(DISCOVERY_TYPE_MAP)
  // return {
  //   excludeIpType: map[schedule['topology.plugin.exclude.target.type']],
  //   excludedIpsOrRange: schedule['topology.plugin.exclude.targets'],
  // }
}

export function transformSchedulersForServer(schedule) {
  let result = {}
  result = transformScheduleForServer({
    ...schedule.schedule,
    type: 'Topology',
    context: {
      'topology.plugin.entry.points': Array.isArray(schedule.monitors)
        ? schedule.monitors
        : [schedule.monitors],
      'topology.link.layer': schedule.layerProtocol,
      'topology.protocols': schedule.protocols,
      // 'topology.plugin.unknown.link': schedule.showUnknownDevices
      //   ? 'yes'
      //   : 'no',
      ...transformExcludeIPTypeOptionForServer(schedule),
    },
  })
  result['scheduler.email.recipients'] = schedule.email
  result['scheduler.sms.recipients'] = schedule.sms

  return result
}

export function transformExcludeIPTypeOptionForServer(option) {
  return {
    ...(option.includeExcludeTargetType
      ? {
          'topology.plugin.filter.target.type': option.includeExcludeTargetType,
          'topology.plugin.filter.targets': option.includeExcludeTargets,
        }
      : {}),
  }
}
