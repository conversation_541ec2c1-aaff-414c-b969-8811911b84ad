<template>
  <MRow>
    <MCol :size="12">
      <FlotoFormItem
        label="Entry Point"
        :rules="!value.id ? { required: true } : {}"
      >
        <MonitorPicker
          id="monitor-picker-id"
          v-model="value.monitors"
          @change="changeMonitor"
        />
      </FlotoFormItem>
    </MCol>
    <MCol v-if="!value.isDefault" :size="12">
      <FlotoFormItem>
        <ScheduleInput
          v-model="value.schedule"
          :time-options="externalOptions"
        />
      </FlotoFormItem>
    </MCol>
    <template v-if="!value.isDefault">
      <!-- <MCol :size="6">
        <FlotoFormItem
          :label-col="{ xxl: 4, xl: 6, lg: 6 }"
          rules="required"
          class="mt-2"
        >
          <MRadioGroup
            v-model="value.includeOrExclude"
            as-button
            :options="topologyOptions"
            @change="handleIncludeOrExcludeTypeChange"
          />
        </FlotoFormItem>
      </MCol> -->
      <!-- <MCol :size="6" /> -->
      <!-- <MCol :size="6">
        <FlotoFormItem label="Monitor Filter">
          <FlotoDropdownPicker
            v-model="value.includeExcludeTargetType"
            as-button
            :options="currentValidTargetTypes"
            :searchable="false"
            @change="handleTargetTypeChange"
          />
        </FlotoFormItem>
      </MCol> -->
      <!--  <MCol :size="6">
        <FlotoFormItem
          v-if="value.includeExcludeTargetType"
          :label="
            value.includeExcludeTargetType === 'exclude-ip.address'
              ? 'Enter one or more IPs'
              : value.includeExcludeTargetType === 'exclude-ip.address.range'
              ? 'Enter one or more IP Ranges'
              : (value.includeExcludeTargetType || '').indexOf('tag') >= 0
              ? 'Select Tags'
              : 'Select Groups'
          "
          rules="required"
        >
          <ObjectTagPicker
            v-if="(value.includeExcludeTargetType || '').indexOf('tag') >= 0"
            v-model="value.includeExcludeTargets"
            :full-width="true"
            as-dropdown
            placeholder="Select Tags"
            title="Tag"
            variant="default"
            rounded
            class="w-full"
          />
          <GroupPicker
            v-else-if="
              (value.includeExcludeTargetType || '').indexOf('group') >= 0
            "
            id="group-parent"
            v-model="value.includeExcludeTargets"
            multiple
            placeholder="Select Groups"
            :included-groups-by-name="[$constants.NETWORK]"
          />
          <FlotoTagsPicker
            v-else-if="
              value.includeExcludeTargetType === 'exclude-ip.address' ||
              value.includeExcludeTargetType === 'exclude-ip.address.range'
            "
            id="exclude-ip"
            v-model="value.includeExcludeTargets"
            class="mt-3"
            variant="default"
            :title="
              value.includeExcludeTargetType === 'exclude-ip.address'
                ? 'e.g. ***********'
                : 'e.g. **********-10'
            "
            rounded
            disable-justify-around
            :full-width="true"
            always-text-mode
            :type="
              value.includeExcludeTargetType === 'exclude-ip.address'
                ? 'ip'
                : 'ip_range'
            "
          />
        </FlotoFormItem>
      </MCol> -->
    </template>
    <MCol :size="6">
      <FlotoFormItem
        id="layer-protocol"
        label="Link Layer"
        :rules="
          (value.monitors || []).length > 0 || !value.id
            ? { required: true }
            : {}
        "
      >
        <FlotoDropdownPicker
          v-model="value.layerProtocol"
          class="w-full multi-select-group"
          multiple
          searchable
          :options="layerProtocolOptions"
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="6">
      <FlotoFormItem
        id="protocol-options"
        label="Protocol"
        rules="required"
        :vid="`layerWiseProtocol`"
      >
        <FlotoDropdownPicker
          v-model="value.protocols"
          class="w-full multi-select-group"
          multiple
          searchable
          :options="protocolOptions"
        />
      </FlotoFormItem>
    </MCol>
    <!-- <MCol v-if="!value.isDefault" :size="6" class="mt-5">
      <MCheckbox
        id="showUnknownDevices"
        v-model="value.showUnknownDevices"
        :checked="value.showUnknownDevices"
      >
        Show Unknown devices in Topology
      </MCheckbox>
    </MCol> -->
    <MCol :size="12">
      <FlotoFormItem id="add-email-btn" label="Notify via Email">
        <FlotoTagsPicker
          v-model="value.email"
          :full-width="true"
          always-text-mode
          type="email"
          placeholder="Email Recipients"
          title="Email Recipients"
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="12">
      <FlotoFormItem id="add-number-btn" label="Notify via SMS">
        <FlotoTagsPicker
          v-model="value.sms"
          :full-width="true"
          type="mobile_number"
          always-text-mode
          placeholder="SMS Recipients"
          title="SMS Recipients"
        />
      </FlotoFormItem>
    </MCol>
  </MRow>
</template>

<script>
import MonitorPicker from '@components/data-picker/monitor-picker.vue'
import ScheduleInput from '@components/schedule-input/index.vue'
// import ObjectTagPicker from '@components/data-picker/object-tag-picker.vue'
import Flatten from 'lodash/flatten'

export default {
  name: 'ScheduleForm',
  components: {
    ScheduleInput,
    MonitorPicker,
    // ObjectTagPicker,
  },
  props: {
    value: { type: Object, required: true },
    resetForm: { type: Function, required: true },
  },
  data() {
    this.externalOptions = ['00:00', '06:00', '12:00'].map((i) => ({
      key: i,
      text: i,
      id: i.replace(':', ''),
    }))

    this.layerProtocolOptions = [
      { key: 'L2', text: 'L2' },
      { key: 'L3', text: 'L3' },
    ]
    this.allTargetTypeOptions = [
      { key: 'include-tag', text: 'Tag' },
      { key: 'include-group', text: 'Group' },
      { key: 'exclude-tag', text: 'Tag' },
      { key: 'exclude-group', text: 'Group' },
      { key: 'exclude-ip.address', text: 'IP' },
      { key: 'exclude-ip.address.range', text: 'IP Range' },
    ]
    this.excludeIpTypeOptions = [
      { value: 'IP', text: 'IP' },
      { value: 'IPRange', text: 'IP Range' },
    ]
    this.layerWiseProtocolOptions = {
      L2: [
        { key: 'CDP', text: 'CDP' },
        { key: 'LLDP', text: 'LLDP' },
        { key: 'SPM', text: 'SPM' },
      ],
      L3: [
        { key: 'IS-IS', text: 'ISIS' },
        { key: 'OSPF', text: 'OSPF' },
        { key: 'BGP', text: 'BGP' },
      ],
    }
    this.topologyOptions = [
      { value: 'include', text: 'Include' },
      { value: 'exclude', text: 'Exclude' },
    ]
    return {}
  },
  computed: {
    currentValidTargetTypes() {
      return this.allTargetTypeOptions.filter(
        (i) => i.key.indexOf(this.value.includeOrExclude) >= 0
      )
    },
    protocolOptions() {
      const selectedLayerProtocol = this.value.layerProtocol || []

      return Flatten(
        selectedLayerProtocol.map((p) => this.layerWiseProtocolOptions[p])
      )
    },
  },
  watch: {
    'value.layerProtocol': function (newValue, oldValue) {
      if (newValue !== oldValue) {
        let selectedLayerProtocol = this.protocolOptions.map(
          (protocol) => protocol.key
        )
        let matchedProtocols = this.value.protocols?.filter((protocol) =>
          selectedLayerProtocol.includes(protocol)
        )

        this.resetForm({
          ...this.value,
          protocols: matchedProtocols,
        })
      }
    },
  },
  methods: {
    handleTargetTypeChange(value) {
      this.resetForm({
        ...this.value,
        includeExcludeTargetType: value,
        includeExcludeTargets: undefined,
      })
    },
    handleIncludeOrExcludeTypeChange(value) {
      this.resetForm({
        ...this.value,
        includeOrExclude: value,
        includeExcludeTargetType: undefined,
        includeExcludeTargets: undefined,
      })
    },
    changeMonitor(selectedMonitors) {
      if (selectedMonitors?.length === 0 || !selectedMonitors) {
        this.resetForm({ ...this.value, layerProtocol: [] })
      }
    },
  },
}
</script>
