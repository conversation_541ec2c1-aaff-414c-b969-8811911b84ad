<template>
  <MModal
    :width="width"
    centered
    :open="open"
    overlay-class-name="scrollable-modal smaller-modal"
    @cancel="$emit('cancle')"
  >
    <template v-slot:title>
      <h5 class="text-primary my-2">OID Group Test Result</h5>
    </template>
    <VirtualTable :columns="columns" :data="result.row" />
    <template v-slot:footer>
      <MRow>
        <MCol :size="12" class="text-right">
          <MButton @click="selectOID"> Select Parent OID </MButton>
        </MCol>
      </MRow>
    </template>
  </MModal>
</template>

<script>
import VirtualTable from '@components/crud/virtual-table.vue'

export default {
  name: 'TabularMetricTestResult',
  components: {
    VirtualTable,
  },
  props: {
    result: { type: Object, required: true },
  },
  data() {
    this.columns = this.result.headers.map((h) => ({
      name: h.key,
      key: h.gridKey,
      headerCell: this.headerCellFunction,
      reorderable: false,
    }))
    this.width = window.innerWidth / 2
    return {
      open: true,
      selectedParentOID: this.result.headers[0].key,
    }
  },
  methods: {
    headerCellFunction(h, tdElement, props, listeners) {
      const _this = this
      return h(
        'MRadio',
        {
          props: {
            checked: this.selectedParentOID === props.title,
          },
          on: {
            click(e) {
              _this.selectedParentOID = props.title
            },
          },
        },
        [props.title]
      )
    },
    fetchRows() {
      return Promise.resolve(this.result.row)
    },
    selectOID() {
      this.open = false
      setTimeout(() => {
        this.$emit('select', this.selectedParentOID)
      }, 400)
    },
  },
}
</script>
