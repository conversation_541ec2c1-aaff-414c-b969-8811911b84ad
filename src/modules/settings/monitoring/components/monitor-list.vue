<template>
  <MonitorTypeProvider :device-types="deviceTypes">
    <RpeProvider :remote-event-type-filter="['APP', 'COLLECTOR']">
      <MonitoringHourProvider>
        <GroupProvider>
          <!-- <CredentialProvider> -->
          <div class="flex flex-col flex-1 min-h-0 mt-4">
            <div
              v-if="!availableColumns.length"
              class="flex flex-col flex-1 items-center justify-center"
            >
              <MLoader />
            </div>
            <MPersistedColumns
              v-else
              v-model="gridColumns"
              :default-value="gridColumns"
              :module-key="columnPersistModule"
              :available-columns="availableColumns"
            >
              <template
                v-slot="{
                  columns: persistedColumns,
                  setColumns: updatePersistedColumns,
                }"
              >
                <FlotoPaginatedCrud
                  ref="paginatedCrudRef"
                  form-width="50%"
                  as-table
                  selectable
                  resource-name="Monitor"
                  default-sort="-name"
                  :get-edit-item="getMontorForEdit"
                  :columns="persistedColumns"
                  :filters="filters"
                  :fetch-fn="fetchMonitors"
                  :delete-fn="deleteMonitor"
                  :update-fn="updateMonitor"
                  @column-change="updatePersistedColumns"
                  @selection-change="selectedItems = $event"
                >
                  <template v-slot:confirm-delete-message="{ item }">
                    {{
                      $message('confirm', {
                        message: $message('delete_resource', {
                          resource: 'Monitor',
                        }),
                      })
                    }}?

                    <div
                      v-if="[$constants.NETWORK].includes(item.category)"
                      class="rounded-lg p-2 bg-neutral-lightest mt-2"
                    >
                      <span>
                        <MIcon name="info-circle" size="sm" />
                        {{ $message('config_device_delete_note') }}</span
                      >
                    </div>
                  </template>

                  <template
                    v-slot:add-controls="{
                      filter,
                      resetFilter,
                      searchTerm,
                      items,
                    }"
                  >
                    <MRow>
                      <MCol :size="5">
                        <MInput
                          :value="searchTerm"
                          class="search-box"
                          placeholder="Search"
                          name="search"
                          @update="filter"
                        >
                          <template v-slot:prefix>
                            <MIcon name="search" />
                          </template>
                          <template v-if="searchTerm" v-slot:suffix>
                            <MIcon
                              name="times-circle"
                              class="text-neutral-light cursor-pointer"
                              @click="resetFilter"
                            />
                          </template>
                        </MInput>
                      </MCol>
                      <MCol
                        :size="7"
                        class="text-right flex items-center justify-end"
                      >
                        <div class="flex items-center">
                          <MTooltip v-if="items.length">
                            <template v-slot:trigger>
                              <MButton
                                id="bulk-metric-collection-type"
                                :shadow="false"
                                :rounded="false"
                                variant="neutral-lightest"
                                class="squared-button mr-2"
                                @click="showBulkMetricCollectionTime = true"
                              >
                                <MIcon name="metric-collection-time" />
                              </MButton>
                            </template>
                            Metric Collection Time (Bulk)
                          </MTooltip>

                          <MonitorBulkMetricCollectionTime
                            v-if="items.length"
                            :open="showBulkMetricCollectionTime"
                            :device-types="deviceTypes"
                            :for-device-monitor="forDeviceMonitor"
                            @cancel="showBulkMetricCollectionTime = false"
                          />

                          <MPermissionChecker
                            :permission="
                              $constants.MY_ACCOUNT_UPDATE_PERMISSION
                            "
                          >
                            <ColumnSelector
                              v-model="gridColumns"
                              :columns="availableColumns"
                              @change="updatePersistedColumns"
                            />
                          </MPermissionChecker>
                          <MButton
                            :shadow="false"
                            class="squared-button mr-2"
                            :rounded="false"
                            title="Export As PDF"
                            variant="neutral-lightest"
                            @click="handleExportMonitors('pdf')"
                          >
                            <MIcon name="export-pdf" />
                          </MButton>
                          <MButton
                            :shadow="false"
                            class="squared-button mr-2"
                            :rounded="false"
                            variant="neutral-lightest"
                            title="Export As CSV"
                            @click="handleExportMonitors('csv')"
                          >
                            <MIcon name="export-csv" />
                          </MButton>
                        </div>
                        <MButton
                          id="filter-btn"
                          :shadow="false"
                          :rounded="false"
                          :variant="
                            showFilters ? 'neutral-lighter' : 'neutral-lightest'
                          "
                          class="squared-button"
                          @click="showFilters = !showFilters"
                        >
                          <MIcon name="filter" />
                        </MButton>
                        <MButton
                          v-if="selectedItems.length > 1"
                          id="filter-btn"
                          :shadow="false"
                          :rounded="false"
                          :variant="
                            showTagForm ? 'neutral-lighter' : 'neutral-lightest'
                          "
                          class="squared-button ml-2"
                          @click="showTagForm = !showTagForm"
                        >
                          <MIcon name="tag" />
                        </MButton>
                        <FlotoGridActions
                          v-if="selectedItems.length > 1"
                          id="bulk-action"
                          class="mr-4"
                          :resource="{}"
                          :actions="bulkActions"
                          :delete-permission-name="
                            $constants.MONITOR_SETTINGS_DELETE_PERMISSION
                          "
                          :create-permission-name="
                            $constants.MONITOR_SETTINGS_CREATE_PERMISSION
                          "
                          :delete-permission-keys="['bulk_delete']"
                          :create-permission-keys="[
                            'bulk_enable',
                            'bulk_disable',
                            'bulk-off-maintainance',
                            'bulk-on-maintainance',
                            'schedule',
                            'bulk_manage-rpe',
                            'bulk_monitoringHours',
                          ]"
                          @bulk_enable="
                            handleShowBulkDrawerForAction(
                              'bulk_enable',
                              selectedItems
                            )
                          "
                          @bulk_disable="
                            handleShowBulkDrawerForAction(
                              'bulk_disable',
                              selectedItems
                            )
                          "
                          @bulk-off-maintainance="
                            handleShowBulkDrawerForAction(
                              'bulk-off-maintainance',
                              selectedItems
                            )
                          "
                          @bulk-on-maintainance="
                            handleShowBulkDrawerForAction(
                              'bulk-on-maintainance',
                              selectedItems
                            )
                          "
                          @metric-collection-time="
                            handleShowBulkDrawerForAction(
                              'metric-collection-time',
                              selectedItems
                            )
                          "
                          @schedule="
                            handleShowBulkDrawerForAction(
                              'schedule',
                              selectedItems
                            )
                          "
                          @bulk_manage-rpe="
                            handleShowBulkDrawerForAction(
                              'bulk_manage-rpe',
                              selectedItems
                            )
                          "
                          @bulk_monitoringHours="
                            handleShowBulkDrawerForAction(
                              'bulk_monitoringHours',
                              selectedItems
                            )
                          "
                          @bulk_delete="
                            showConfirmDeleteModalForSelectedItems =
                              selectedItems
                          "
                          @bulk_group_assign="
                            handleShowBulkDrawerForAction(
                              'bulk_group_assign',
                              selectedItems
                            )
                          "
                        >
                          <template v-slot:trigger>
                            <MButton
                              id="btn-show-hide-columns"
                              :shadow="false"
                              :rounded="false"
                              variant="neutral-lightest"
                              class="squared-button mx-2"
                            >
                              <MIcon
                                name="ellipsis-v"
                                class="excluded-header-icon"
                              />
                            </MButton>
                          </template>
                        </FlotoGridActions>
                      </MCol>

                      <MCol :size="12" :class="{ 'mt-4': showFilters }">
                        <MonitorFilters
                          v-if="showFilters"
                          v-model="appliedFilters"
                          :for-device-monitor="forDeviceMonitor"
                          @change="applyFilter"
                          @hide="showFilters = !showFilters"
                        />
                      </MCol>
                      <MCol :size="12" :class="{ 'mt-4': showTagForm }">
                        <BulkAddTagFrom
                          v-if="showTagForm"
                          :processing="tagProcessing"
                          @save="bulkUpdateTag"
                          @hide="showTagForm = !showTagForm"
                        />
                      </MCol>
                    </MRow>
                  </template>

                  <template v-slot:form-header="{ item }"
                    >Edit
                    {{
                      item.category === $constants.SERVICE_CHECK
                        ? item.serviceCheckName
                        : item.name
                    }}</template
                  >

                  <template v-slot:form-items="{ item }">
                    <MonitorEditForm :value="item">
                      <template v-slot:additional-fields="{ value }">
                        <slot name="additional-fields" :value="value"></slot>
                      </template>
                    </MonitorEditForm>
                  </template>

                  <template
                    v-slot:form-actions="{ submit, processing, resetForm }"
                  >
                    <MButton
                      id="reset-btn"
                      class="mr-2"
                      variant="default"
                      @click="resetForm(null)"
                      >Reset</MButton
                    >
                    <MButton
                      id="update-monitor-btn"
                      :loading="processing"
                      @click="submit"
                      >Update Monitor</MButton
                    >
                  </template>

                  <template v-slot:groups="{ item }">
                    <GroupPicker :value="item.groups" multiple disabled />
                  </template>
                  <template v-slot:tags="{ item }">
                    <LooseTags :value="item.tags" disabled />
                  </template>

                  <template v-slot:type="{ item }">
                    <MonitorType :type="item.type" />
                  </template>

                  <template v-slot:apps="{ item }">
                    <MonitorApplicationList :value="item.apps" />
                  </template>

                  <template v-slot:status="{ item }">
                    <ObjectState :status="item.status" />
                  </template>

                  <template v-slot:instanceCount="{ item }">
                    <UsedCounts
                      v-if="
                        ![
                          $constants.OTHER,
                          $constants.SYMANTEC_MESSAGING_GATEWAY,
                        ].includes(item.category)
                      "
                      :title="`Instance Count for ${item.name}`"
                      :display-count="item.instanceCount"
                      :parent-resource-id="item.id"
                      parent-resource-type="instance-count"
                      use-custom-resource-key
                      :count-types="[
                        ...(item.category === $constants.SERVER
                          ? [
                              {
                                countType: 'Processes',
                                title: 'Processes',
                                resourceKey: 'system.process',
                                url: `settings/tags/instances/${item.id}`,
                                putUrl: `settings/tags/instance-tags/${item.id}`,
                              },
                            ]
                          : []),

                        ...(item.category === $constants.WIRELESS
                          ? [
                              {
                                countType: 'Access points',
                                title: 'Access points',
                                resourceKey: 'access.points',
                                url: `settings/tags/instances/${item.id}`,
                                putUrl: `settings/tags/instance-tags/${item.id}`,
                              },
                            ]
                          : []),

                        ...(item.category === $constants.NETWORK
                          ? [
                              ...([
                                $constants.RUCKUS_WIRELESS,
                                $constants.CISCO_WIRELESS,
                                $constants.ARUBA_WIRELESS,
                              ].includes(item.type || item.category)
                                ? [
                                    {
                                      countType: 'Access points',
                                      title: 'Access points',
                                      resourceKey: 'Access Point',
                                      url: `settings/tags/instances/${item.id}`,
                                      putUrl: `settings/tags/instance-tags/${item.id}`,
                                    },
                                  ]
                                : [
                                    {
                                      countType: 'Interface',
                                      title: 'Interface',
                                      resourceKey: 'interface',
                                      url: `settings/tags/instances/${item.id}`,
                                      putUrl: `settings/tags/instance-tags/${item.id}`,
                                    },
                                  ]),
                            ]
                          : []),

                        ...(item.category === $constants.VIRTUALIZATION
                          ? [
                              {
                                countType: 'VM',
                                title: 'Vm',
                                resourceKey: 'vm',
                                url: `settings/tags/instances/${item.id}`,
                                putUrl: `settings/tags/instance-tags/${item.id}`,
                              },
                            ]
                          : []),
                        ...(item.category ===
                        $constants.HYPERCONVERGED_INFRASTRUCTURE
                          ? [
                              {
                                countType: 'VM',
                                title: 'Vm',
                                resourceKey: 'vm',
                                url: `settings/tags/instances/${item.id}`,
                                putUrl: `settings/tags/instance-tags/${item.id}`,
                              },
                            ]
                          : []),
                      ]"
                    />
                    <span v-else></span>
                  </template>

                  <template v-slot:schedule="{ item }">
                    <a
                      v-if="item.scheduled === 'yes'"
                      :class="{
                        'ml-3': item.scheduled,
                        'ml-2': !item.scheduled,
                      }"
                      @click="showScheduleLogForItem = item"
                    >
                      <MIcon
                        name="schedule"
                        size="lg"
                        class="text-neutral-light"
                      />
                    </a>
                  </template>

                  <template v-slot:agent="{ item }">
                    <AgentPicker :value="item.agent" disabled />
                  </template>

                  <template v-slot:action="{ item, edit }">
                    <FlotoGridActions
                      name="grid-action"
                      :resource="item"
                      :actions="getActionsForMonitor(item)"
                      :edit-permission-name="
                        $constants.MONITOR_SETTINGS_UPDATE_PERMISSION
                      "
                      :delete-permission-name="
                        $constants.MONITOR_SETTINGS_DELETE_PERMISSION
                      "
                      :create-permission-name="
                        $constants.MONITOR_SETTINGS_CREATE_PERMISSION
                      "
                      :create-permission-keys="['schedule-maintainance']"
                      :edit-permission-keys="[
                        'enable',
                        'disable',
                        'on-maintainance',
                        'off-maintainance',
                        'edit',
                      ]"
                      @enable="handleShowConfirmForAction('enable', item)"
                      @disable="handleShowConfirmForAction('disable', item)"
                      @off-maintainance="
                        handleShowConfirmForAction('off-maintainance', item)
                      "
                      @on-maintainance="
                        handleShowConfirmForAction('on-maintainance', item)
                      "
                      @schedule-maintainance="handleShowNewScheduleForm(item)"
                      @metric-collection-time="
                        showMetricTimeCollectionForItem = item
                      "
                      @edit="edit"
                    />
                  </template>
                </FlotoPaginatedCrud>
              </template>
            </MPersistedColumns>

            <FlotoDrawerForm
              :open="newScheduleFormForItem !== null"
              @submit="createNewScheduleForMonitor"
              @reset="resetNewSchedule"
              @cancel="hideNewScheduleForm"
            >
              <template v-if="newScheduleFormForItem" v-slot:header
                >{{ newScheduleFormForItem.name }} Schedule
                Maintenance</template
              >

              <!-- scheduleForm -->
              <MonitorScheduleForm
                v-if="newScheduleFormForItem"
                v-model="newMonitorSchedule"
                :error="scheduleCreateError"
              />

              <template v-slot:actions="{ submit, reset }">
                <MButton
                  id="create-schedule-btn"
                  :loading="creatingNewSchedule"
                  @click="submit"
                  >Schedule</MButton
                >
                <MButton
                  id="reset-schedule-btn"
                  variant="default"
                  class="ml-2"
                  @click="reset"
                  >Reset</MButton
                >
              </template>
            </FlotoDrawerForm>

            <MonitorSchedules
              v-if="showScheduleLogForItem !== null"
              :monitor="showScheduleLogForItem"
              @hide="showScheduleLogForItem = null"
              @refresh="handleRefreshPaginationCrud"
            />

            <MonitorMetricCollectionTime
              :monitor="showMetricTimeCollectionForItem"
              :show="showMetricTimeCollectionForItem !== null"
              @cancel="showMetricTimeCollectionForItem = null"
            />

            <!-- bulk action drawer -->
            <FlotoDrawerForm
              :open="
                bulkDrawerConfig.event !== null &&
                bulkDrawerConfig.event !== 'metric-collection-time'
              "
              @submit="performBulkAction"
              @reset="resetFormForBulk"
              @cancel="hideBulkDrawer"
            >
              <template v-slot:header>{{
                bulkDrawerConfig.drawerTitle
              }}</template>
              <FlotoFormItem rules="required" label="Monitors">
                <MonitorPicker
                  id="monitors-dropdown"
                  v-model="bulkDrawerConfig.drawerSelectedItems"
                  class="w-full"
                  multiple
                  searchable
                  :options="monitorOptions"
                  use-body-container
                />
              </FlotoFormItem>
              <template v-if="bulkDrawerConfig.event === 'schedule'">
                <FlotoFormItem>
                  <MonitorScheduleForm
                    v-model="bulkDrawerConfig.scheduleForm"
                    :error="scheduleCreateError"
                  />
                </FlotoFormItem>
              </template>
              <template
                v-else-if="bulkDrawerConfig.event === 'bulk_manage-rpe'"
              >
                <MRow>
                  <MCol :size="12">
                    <FlotoFormItem label="Collectors">
                      <RpePicker
                        id="rpe-picker"
                        v-model="bulkDrawerConfig.rpe"
                        multiple
                      />
                    </FlotoFormItem>
                  </MCol>
                </MRow>
              </template>
              <template
                v-else-if="bulkDrawerConfig.event === 'bulk_monitoringHours'"
              >
                <MRow>
                  <MCol :size="6">
                    <FlotoFormItem rules="required" label="Monitoring Hour">
                      <MonitoringHourPicker
                        id="monitoring-hour-picker"
                        v-model="bulkDrawerConfig.monitorHourProfile"
                      />
                    </FlotoFormItem>
                  </MCol>
                </MRow>
              </template>
              <template
                v-else-if="bulkDrawerConfig.event === 'bulk_group_assign'"
              >
                <MRow>
                  <MCol :size="12">
                    <FlotoFormItem
                      id="group-id"
                      label="Groups"
                      rules="required"
                    >
                      <GroupPicker
                        v-model="bulkDrawerConfig.selectedItemGroups"
                        multiple
                        show-custom-only
                      />
                    </FlotoFormItem>
                  </MCol>
                </MRow>
              </template>
              <template v-slot:actions="{ reset, submit }">
                <MButton
                  id="reset-btn"
                  class="mr-2"
                  variant="default"
                  @click="reset"
                  >Reset</MButton
                >
                <MButton
                  id="submit-btn"
                  :loading="bulkDrawerConfig.processing"
                  @click="submit"
                >
                  {{
                    bulkDrawerConfig.event === 'schedule'
                      ? 'Schedule'
                      : bulkDrawerConfig.buttonText || 'Update Monitors'
                  }}
                </MButton>
              </template>
            </FlotoDrawerForm>

            <!-- if bulk delete -->
            <FlotoConfirmModal
              v-if="showConfirmDeleteModalForSelectedItems.length"
              open
              no-icon-shadow
              @confirm="handleBulkDeleteMonitors"
              @hide="showConfirmDeleteModalForSelectedItems = []"
            >
              <template v-slot:icon>
                <slot name="confirm-delete-icon">
                  <MIcon
                    name="trash-alt"
                    size="2x"
                    class="text-secondary-red"
                  />
                </slot>
              </template>
              <template v-slot:message>
                {{
                  $message('monitor_action', {
                    action: 'delete selected monitors',
                  })
                }}
              </template>
            </FlotoConfirmModal>
            <!-- single node confirmation modal -->
            <FlotoConfirmModal
              v-if="showConfirmForAction"
              open
              variant="primary-alt"
              @hide="hideConfirmation"
              @confirm="handleConfirmAction"
            >
              <template v-slot:icon>
                <MIcon
                  :name="confirmIcon"
                  class="w-full text-primary-alt"
                  size="2x"
                />
              </template>
              <template v-slot:message>
                {{ confirmMessage }}
                <br />
                <h3>
                  {{ showConfirmForResource.name || showConfirmForResource.ip }}
                </h3>

                <div
                  v-if="
                    [$constants.NETWORK].includes(
                      showConfirmForResource.category
                    ) && confirmMessageNote
                  "
                  class="rounded-lg p-2 bg-neutral-lightest mt-2"
                >
                  <span>
                    <MIcon name="info-circle" size="sm" />
                    {{ confirmMessageNote }}</span
                  >
                </div>
              </template>
            </FlotoConfirmModal>
            <!-- Delete Failed Modal -->
            <LinkedRecordsDetailModal
              v-if="isLinkedRecordsDetailModalVisible"
              :data="linkedRecordsDetailData"
              :fetch-fn="true"
              :bulk-delete-columns="bulkDeleteColumns"
              @hide="isLinkedRecordsDetailModalVisible = false"
            >
              <template v-slot:title>
                <div class="flex items-center">
                  <div class="flex-1">
                    <slot name="title">
                      <h4 id="header-id" class="mb-0 text-secondary-red">
                        Usage Details
                      </h4>
                    </slot>
                  </div>
                  <MButton
                    id="close-used-count"
                    variant="transparent"
                    :shadow="false"
                    shape="circle"
                    @click="isLinkedRecordsDetailModalVisible = false"
                  >
                    <MIcon name="times" class="text-neutral-light" />
                  </MButton>
                </div>
              </template>
            </LinkedRecordsDetailModal>
          </div>
          <!-- </CredentialProvider> -->
        </GroupProvider>
      </MonitoringHourProvider>
    </RpeProvider>
  </MonitorTypeProvider>
</template>

<script>
import Moment from 'moment'
import CloneDeep from 'lodash/cloneDeep'
import MonitorPicker from '@components/data-picker/monitor-picker.vue'
import GroupProvider from '@components/data-provider/group-provider.vue'
import RpeProvider from '@components/data-provider/rpe-provider.vue'
import MonitoringHourProvider from '@components/data-provider/monitoring-hour-provider.vue'
import MonitorType from '@components/monitor-type.vue'
import MonitorTypeProvider from '@components/data-provider/monitor-type-provider.vue'
import ColumnSelector from '@components/column-selector.vue'
// import CredentialProvider from '@components/data-provider/credential-provider.vue'
import { downloadFile } from '@utils/download'
import Bus from '@utils/emitter'
import {
  getMonitorsApi,
  updateMonitorApi,
  deleteMonitorApi,
  createNewMonitorScheduleApi,
  changeStateMonitorBulkApi,
  createMonitorScheduleBulkApi,
  updateMonitorBulkApi,
  deleteMonitorBulkApi,
  enableMonitorApi,
  disableMonitorApi,
  onMaintainanceMonitorApi,
  offMaintainanceMonitorApi,
  getMetricCollectionTimeForMonitorApi,
} from '../monitors-api'
import { APPLICATIONS_METRIC_TYPE } from '../helpers/metric-collection-time'
import MonitorFilters from './monitor-filters.vue'
import exportData from '../helpers/export-pdf-csv'
import MonitorApplicationList from './monitor-application-list.vue'
import MonitorSchedules from './monitor-schedules.vue'
import MonitorScheduleForm from './monitor-schedule-form.vue'
import MonitorEditForm from './monitor-edit-form.vue'
import MonitorMetricCollectionTime from './monitor-metric-collection-time.vue'
import MonitorBulkMetricCollectionTime from './monitor-bulk-metric-collection-time.vue'
import LinkedRecordsDetailModal from '@components/crud/linked-records-detail-modal.vue'
import LooseTags from '@components/loose-tags.vue'
import ObjectState from './object-state.vue'
import UsedCounts from '@components/used-counts/used-counts.vue'
import BulkAddTagFrom from './bulk-add-tag-form.vue'
import AgentPicker from '@components/data-picker/agent-picker.vue'

const scheduleFormData = {
  scheduleDetails: {
    scheduleType: 'Once',
  },
  schedule: {
    startDate: Moment().startOf('day').unix() * 1000,
  },
  offSchedule: {
    startDate: Moment().add(1, 'days').startOf('day').unix() * 1000,
  },
}
export default {
  name: 'MonitorList',
  components: {
    GroupProvider,
    MonitorType,
    MonitorApplicationList,
    MonitorSchedules,
    MonitorScheduleForm,
    MonitorEditForm,
    RpeProvider,
    MonitoringHourProvider,
    MonitorMetricCollectionTime,
    MonitorFilters,
    MonitorTypeProvider,
    ColumnSelector,
    MonitorBulkMetricCollectionTime,
    // CredentialProvider,
    MonitorPicker,
    LinkedRecordsDetailModal,
    ObjectState,
    LooseTags,
    UsedCounts,
    BulkAddTagFrom,
    AgentPicker,
  },
  inject: { monitoringFieldsContext: { default: { options: [] } } },
  model: { event: 'change' },
  props: {
    columnPersistModule: {
      type: String,
      required: true,
    },
    columns: {
      type: Array,
      default() {
        return []
      },
    },
    deviceTypes: {
      type: Array,
      default() {
        return []
      },
    },
    actions: {
      type: Array,
      default() {
        return []
      },
    },
    forDeviceMonitor: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.bulkActions = [
      {
        key: 'bulk_enable',
        name: 'Enable',
        icon: 'monitor-enable',
        buttonText: 'Enable Monitors',
        message: this.$message('monitor_action', { action: 'enable monitors' }),
      },
      {
        key: 'bulk_disable',
        name: 'Disable',
        icon: 'monitor-disable',
        buttonText: 'Disable Monitors',
        message: this.$message('monitor_action', {
          action: 'disable monitors',
        }),
      },
      { key: 'd1', type: 'divider' },
      {
        key: 'bulk-off-maintainance',
        name: 'Off Maintenance',
        buttonText: 'Remove Monitors from Maintenance',
        icon: 'circle',
        message: this.$message('monitor_action', {
          action: 'remove monitors from maintenance',
        }),
      },
      {
        key: 'bulk-on-maintainance',
        name: 'On Maintenance',
        icon: 'cog',
        buttonText: 'On Maintenance Monitors',
        message: this.$message('monitor_action', {
          action: 'put monitors on maintenance',
        }),
      },
      { key: 'schedule', name: 'Schedule Maintenance', icon: 'schedule' },
      { key: 'bulk_manage-rpe', name: 'Manage Collectors', icon: 'manage-rpe' },
      {
        key: 'bulk_monitoringHours',
        name: 'Monitoring Hour',
        icon: 'metric-collection-time',
      },
      {
        key: 'bulk_group_assign',
        name: 'Manage Groups',
        icon: 'group-settings',
        buttonText: 'Assign Group',
      },
      {
        key: 'bulk_delete',
        name: 'Delete',
        icon: 'trash-alt',
        redAction: true,
      },
    ]
    this.monitorOptions = []
    this.bulkDeleteColumns = [
      { key: 'entity', name: 'Monitor', searchable: true, sortable: true },
      { key: 'name', name: 'Entity Name', searchable: true, sortable: true },
      { key: 'type', name: 'Entity Type', searchable: true, sortable: true },
    ]
    return {
      selectedItems: [],
      isLinkedRecordsDetailModalVisible: false,
      linkedRecordsDetailData: undefined,
      gridColumns: this.columns,
      showConfirmForAction: null,
      showConfirmForResource: null,
      showFilters: false,
      showScheduleLogForItem: null,
      appliedFilters: {
        groups: [],
        types: [],
        apps: [],
      },
      // schedule related data
      newMonitorSchedule: CloneDeep(scheduleFormData),
      creatingNewSchedule: false,
      newScheduleFormForItem: null,
      showMetricTimeCollectionForItem: null,
      showConfirmDeleteModalForSelectedItems: [],
      scheduleCreateError: null,
      showBulkMetricCollectionTime: false,
      // bulk drawer config
      bulkDrawerConfig: {
        drawerTitle: null,
        event: null,
        drawerSelectedItems: [],
        untouchedSelectedItems: [],
        processing: false,
        scheduleForm: CloneDeep(scheduleFormData),
        rpe: [],
      },
      showTagForm: false,
      tagProcessing: false,
    }
  },
  computed: {
    availableColumns() {
      const monitoringFields = Array.from(
        this.monitoringFieldsContext.options.values()
      )
      if (!monitoringFields.length) {
        return []
      }
      const customFields = monitoringFields.map((o) => ({
        key: String(o.key),
        name: o.name,
        searchable: true,
        sortable: true,
        hidden: true,
      }))
      return [...this.columns, ...customFields]
    },
    filters() {
      let filters
      const value = this.appliedFilters

      if (value.groups && value.groups.length) {
        filters = [
          ...(filters || []),
          {
            field: 'groups',
            operator: 'array_contains',
            value: value.groups,
          },
        ]
      }
      if (value.types && value.types.length) {
        filters = [
          ...(filters || []),
          {
            field: 'type',
            operator: 'array_contains',
            value: value.types,
          },
        ]
      }
      if (value.apps && value.apps.length) {
        filters = [
          ...(filters || []),
          {
            field: 'apps',
            operator: 'array_contains',
            value: value.apps,
          },
        ]
      }
      return filters
    },
    confirmMessageNote() {
      const action = this.actions.find(
        (a) => a.key === this.showConfirmForAction
      )

      if (action?.note) {
        if (
          action?.note?.category?.includes(
            this.showConfirmForResource?.category
          )
        ) {
          return action?.note?.message
        }
      }

      return undefined
    },
  },
  created() {
    const monitorLoadedHandler = () => {
      if (this.$refs.paginatedCrudRef) {
        this.$refs.paginatedCrudRef.refresh()
      }
    }
    Bus.$on(this.$constants.EVENT_MONITOR_DB_CHANGED, monitorLoadedHandler)
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(this.$constants.EVENT_MONITOR_DB_CHANGED, monitorLoadedHandler)
    })
  },
  methods: {
    handleConfirmAction() {
      const monitor = this.showConfirmForResource
      const event = this.showConfirmForAction
      this.hideConfirmation()
      switch (event) {
        case 'enable':
          return this.handleEnableMonitor(monitor)
        case 'disable':
          return this.handleDisableMonitor(monitor)
        case 'off-maintainance':
          return this.handleOffMaintainance(monitor)
        case 'on-maintainance':
          return this.handleOnMaintainance(monitor)
      }
    },
    hideConfirmation() {
      this.showConfirmForAction = null
      this.showConfirmForResource = null
    },
    handleMonitorUpdated(monitor) {
      this.$refs.paginatedCrudRef.handleUpdateItem(monitor)
    },
    handleRefreshPaginationCrud() {
      if (this.$refs.paginatedCrudRef) {
        this.$refs.paginatedCrudRef.resetList()
      }
    },
    getActionsForMonitor(monitor) {
      let actions = this.actions
      if (monitor.status === this.$constants.STATE_ENABLE) {
        actions = actions.filter(
          (a) => ['off-maintainance', 'enable'].indexOf(a.key) === -1
        )
      }
      // #3621-Improvement:Case 1
      if (monitor.status === this.$constants.STATE_DISABLE) {
        actions = actions.filter(
          (a) =>
            ['off-maintainance', 'disable', 'schedule-maintainance'].indexOf(
              a.key
            ) === -1
        )
      }
      if (monitor.status === 'MAINTENANCE') {
        actions = actions.filter((a) => a.key !== 'on-maintainance')
      }
      return actions
    },
    applyFilter() {
      this.showFilters = false
    },
    // bulk drawer action related function
    resetFormForBulk() {
      this.scheduleCreateError = null
      this.bulkDrawerConfig = {
        ...this.bulkDrawerConfig,
        drawerSelectedItems: CloneDeep(
          this.bulkDrawerConfig.untouchedSelectedItems
        ),
        scheduleForm: CloneDeep(scheduleFormData),
        rpe: [],
      }
    },
    handleShowBulkDrawerForAction(event, selectedMonitors) {
      this.scheduleCreateError = null
      const action = this.bulkActions.find((a) => a.key === event)
      this.bulkDrawerConfig = {
        drawerTitle: action.name,
        buttonText: action.buttonText,
        event,
        drawerSelectedItems: selectedMonitors,
        untouchedSelectedItems: CloneDeep(selectedMonitors),
        processing: false,
        scheduleForm: CloneDeep(scheduleFormData),
        rpe: [],
      }
    },
    hideBulkDrawer() {
      this.bulkDrawerConfig = {
        drawerTitle: null,
        event: null,
        drawerSelectedItems: [],
        untouchedSelectedItems: [],
        processing: false,
        scheduleForm: CloneDeep(scheduleFormData),
        rpe: [],
      }
    },
    performBulkAction() {
      this.scheduleCreateError = null
      const event = this.bulkDrawerConfig.event
      const selectedIds = this.bulkDrawerConfig.drawerSelectedItems
      const scheduleForm = this.bulkDrawerConfig.scheduleForm
      const rpe = this.bulkDrawerConfig.rpe
      const monitoringHour = this.bulkDrawerConfig.monitorHourProfile
      const groups = this.bulkDrawerConfig.selectedItemGroups
      this.bulkDrawerConfig = {
        ...this.bulkDrawerConfig,
        processing: true,
      }
      let fn = Promise.resolve()
      switch (event) {
        case 'schedule':
          fn = createMonitorScheduleBulkApi(selectedIds, scheduleForm)
          break
        case 'bulk_enable':
          fn = changeStateMonitorBulkApi({
            ids: selectedIds,
            'object.state': this.$constants.STATE_ENABLE,
          })
          break
        case 'bulk_disable':
          fn = changeStateMonitorBulkApi({
            ids: selectedIds,
            'object.state': this.$constants.STATE_DISABLE,
          })
          break
        case 'bulk-off-maintainance':
          fn = changeStateMonitorBulkApi({
            ids: selectedIds,
            'object.state': this.$constants.STATE_ENABLE,
          })
          break
        case 'bulk-on-maintainance':
          fn = changeStateMonitorBulkApi({
            ids: selectedIds,
            'object.state': 'MAINTENANCE',
          })
          break
        case 'bulk_manage-rpe':
          fn = updateMonitorBulkApi({
            ids: selectedIds,
            'object.event.processors': rpe,
          })
          break
        case 'bulk_monitoringHours':
          fn = updateMonitorBulkApi({
            ids: selectedIds,
            'object.business.hour.profile': monitoringHour,
          })
          break
        case 'bulk_group_assign':
          fn = updateMonitorBulkApi({
            ids: selectedIds,
            'object.groups': groups,
          })
      }
      fn.then(() => {
        this.hideBulkDrawer()
        this.$nextTick(() => {
          setTimeout(() => {
            this.$refs.paginatedCrudRef.resetList()
          }, 400)
        })
      }).catch((e) => {
        this.bulkDrawerConfig = {
          ...this.bulkDrawerConfig,
          processing: false,
        }
        if (event === 'schedule') {
          this.scheduleCreateError = e.message
        }
      })
    },
    // monitor operations api
    async handleExportMonitors(type) {
      const items = await this.$refs?.paginatedCrudRef?.getFilteredData()
      this.$successNotification({
        message: 'Success',
        description: `The file will be downloaded once ready`,
      })
      exportData(
        this.gridColumns.filter((obj) => obj.key && !obj.hidden),
        items,
        type,
        this.$refs.paginatedCrudRef.getContextData()
      ).then((blob) => {
        downloadFile(blob, undefined, `monitors.${type}`)
      })
    },
    async handleBulkDeleteMonitors() {
      await deleteMonitorBulkApi(this.showConfirmDeleteModalForSelectedItems)
        .catch((e) => {
          this.linkedRecordsDetailData = e.response?.data
          this.isLinkedRecordsDetailModalVisible = true
          throw e
        })
        .finally(() => {
          this.showConfirmDeleteModalForSelectedItems = []
          this.$refs.paginatedCrudRef.resetList()
        })
    },
    handleShowConfirmForAction(event, agent) {
      this.showConfirmForAction = event
      this.showConfirmForResource = agent
      const action = this.actions.find((a) => a.key === event)
      this.confirmIcon = action.icon
      this.confirmMessage = action.message
    },
    // add new schedule Related functions
    handleShowNewScheduleForm(item) {
      this.newScheduleFormForItem = item
      this.scheduleCreateError = null
      this.newMonitorSchedule = CloneDeep(scheduleFormData)
    },
    hideNewScheduleForm() {
      this.newScheduleFormForItem = null
      this.newDiscoverySchedule = {
        scheduleType: 'Once',
        scheduleInfo: {},
      }
    },
    resetNewSchedule() {
      this.scheduleCreateError = null
      this.newMonitorSchedule = CloneDeep(scheduleFormData)
    },
    createNewScheduleForMonitor() {
      this.creatingNewSchedule = true
      this.scheduleCreateError = null
      createNewMonitorScheduleApi(
        this.newScheduleFormForItem,
        this.newMonitorSchedule
      )
        .then((data) => {
          // schedule created
          this.hideNewScheduleForm()
          this.newScheduleFormForItem = null
          this.newMonitorSchedule = {
            scheduleDetails: {
              scheduleType: 'Once',
            },
            schedule: {},
            offSchedule: {},
          }
          this.handleMonitorUpdated(data)
        })
        .catch((e) => {
          this.scheduleCreateError = e.message
        })
        .finally(() => (this.creatingNewSchedule = false))
    },
    deleteMonitor(monitor) {
      return deleteMonitorApi(monitor.id).then(() => {
        this.monitorOptions = this.monitorOptions.filter(
          (n) => n.key !== monitor.id
        )
      })
    },
    async fetchMonitors() {
      return getMonitorsApi({
        params: {
          'object.category': JSON.stringify(this.deviceTypes),
          ...(this.forDeviceMonitor
            ? {
                'object.discovery.method': JSON.stringify([
                  this.$constants.REMOTE_METHOD,
                ]),
              }
            : {}),
        },
      }).then((data) => {
        this.monitorOptions = data.map((d) => ({
          ...d,
          key: d.id,
          text: d.name,
        }))
        return data
      })
    },
    updateMonitor(monitor) {
      return updateMonitorApi(monitor)
    },
    handleEnableMonitor(monitor) {
      return enableMonitorApi(monitor).then((response) => {
        this.handleMonitorUpdated(response)
      })
    },
    handleDisableMonitor(monitor) {
      return disableMonitorApi(monitor).then((response) => {
        this.handleMonitorUpdated(response)
      })
    },
    handleOffMaintainance(item) {
      return offMaintainanceMonitorApi(item).then((response) =>
        this.handleMonitorUpdated(response)
      )
    },
    handleOnMaintainance(item) {
      return onMaintainanceMonitorApi(item).then((response) =>
        this.handleMonitorUpdated(response)
      )
    },
    getMontorForEdit(item) {
      if (item.category === this.$constants.SERVER && item.apps.length) {
        return getMetricCollectionTimeForMonitorApi(item.id, true).then(
          (data) => {
            const applicationKeys = Object.keys(data).filter(
              (key) =>
                APPLICATIONS_METRIC_TYPE.indexOf(key) >= 0 &&
                Object.keys(data[key].params || {}).length
            )
            const applicationParams = {}
            applicationKeys.forEach((key) => {
              applicationParams[key] = data[key]
            })
            return applicationKeys.length
              ? {
                  applicationParams,
                }
              : {}
          }
        )
      }
      return Promise.resolve(item)
    },
    bulkUpdateTag(tags) {
      if (tags.length) {
        this.tagProcessing = true
        updateMonitorBulkApi({
          ids: this.selectedItems,
          'object.tags': tags,
        }).then(() => {
          this.$nextTick(() => {
            setTimeout(() => {
              this.showTagForm = false
              this.tagProcessing = false
              this.$refs.paginatedCrudRef.resetList()
            }, 400)
          })
        })
      }
    },
  },
}
</script>
