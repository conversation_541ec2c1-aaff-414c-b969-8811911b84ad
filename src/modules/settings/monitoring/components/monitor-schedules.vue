<template>
  <FlotoDrawer
    ref="drawer"
    open
    width="60%"
    :scrolled-content="false"
    @hide="$emit('hide')"
  >
    <template v-slot:title>
      {{ monitor.name }} - Maintenance Scheduler Details
    </template>
    <FlotoPaginatedCrud
      class="monitor-schedule-list"
      resource-name="Schedule"
      :fetch-fn="fetchMonitorSchedules"
      :update-fn="updateSchedule"
      :delete-fn="deleteSchedule"
      as-table
      :columns="columns"
      :get-edit-item="getDataToEditItem"
      @empty-list="closeDrawer"
    >
      <template v-slot:form-header> Edit Scheduler </template>
      <template v-slot:form-items="{ item }">
        <MonitorScheduleForm v-model="item.scheduleFormData" />
      </template>
      <template v-slot:form-actions="{ submit, resetForm, processing }">
        <MButton id="update-schedule-btn" :loading="processing" @click="submit">
          Update Scheduler
        </MButton>
        <MButton
          id="reset-schedule-btn"
          variant="default"
          class="ml-2"
          @click="resetForm(editingItem)"
        >
          Reset
        </MButton>
      </template>

      <template v-slot:onSchedules="{ item }">
        <SelectedItemPills :value="item.onSchedules" :max-items="1" />
      </template>

      <template v-slot:offSchedules="{ item }">
        <SelectedItemPills :value="item.offSchedules" :max-items="1" />
      </template>

      <template v-slot:action="{ item, edit, update }">
        <MPermissionChecker
          :permission="$constants.MONITOR_SETTINGS_UPDATE_PERMISSION"
        >
          <MSwitch
            id="schedule-switch"
            :checked="item.enabled"
            checked-children="ON"
            un-checked-children="OFF"
            @change="update({ ...item, enabled: $event })"
          />
        </MPermissionChecker>
        <FlotoGridActions
          :edit-permission-name="$constants.MONITOR_SETTINGS_UPDATE_PERMISSION"
          :delete-permission-name="
            $constants.MONITOR_SETTINGS_DELETE_PERMISSION
          "
          :actions="gridItemActions"
          :resource="item"
          class="mr-3 action-btn-handle"
          @edit="edit"
        />
      </template>
    </FlotoPaginatedCrud>
  </FlotoDrawer>
</template>

<script>
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import MonitorScheduleForm from './monitor-schedule-form.vue'
import {
  getMonitorSchedulesApi,
  updateMonitorScheduleApi,
  deleteMonitorScheduleApi,
  getMonitorScheduleByIDApi,
} from '../monitors-api'

export default {
  name: 'MonitorSchedule',
  components: { MonitorScheduleForm, SelectedItemPills },
  props: { monitor: { type: Object, required: true } },
  data() {
    this.gridItemActions = [
      { key: 'edit', name: 'Edit Scheduler', icon: 'pencil' },
      {
        key: 'delete',
        name: 'Delete Scheduler',
        icon: 'trash-alt',
        isDanger: true,
      },
    ]
    this.columns = [
      { key: 'scheduleType', name: 'Scheduler Type', width: '180px' },
      { key: 'onStartDate', name: 'On Maintenance Start Date' },
      { key: 'onSchedules', name: 'On Maintenance Triggers' },
      { key: 'offStartDate', name: 'Off Maintenance Start Date' },
      { key: 'offSchedules', name: 'Off Maintenance Triggers' },
      { key: 'action', name: 'Actions', width: '140px', align: 'right' },
    ]
    this.editingItem = {}
    return {}
  },
  methods: {
    closeDrawer() {
      this.$refs.drawer.hide()
      setTimeout(() => {
        this.$emit('hide')
      }, 350)
    },
    fetchMonitorSchedules() {
      return getMonitorSchedulesApi({
        params: {
          filter: { id: this.monitor.id, 'scheduler.job.type': 'Maintenance' },
        },
      })
    },
    updateSchedule(data) {
      return updateMonitorScheduleApi(this.monitor.id, data)
    },
    deleteSchedule(data) {
      return deleteMonitorScheduleApi(
        this.monitor.id,
        data.id,
        {
          params: { 'scheduler.job.type': 'Maintenance' },
        },
        false
      ).then((monitor) => {
        this.$emit('refresh')
      })
    },
    getDataToEditItem(item) {
      return getMonitorScheduleByIDApi(item.id).then((schedule) => {
        this.editingItem = schedule
        return schedule
      })
    },
  },
}
</script>
