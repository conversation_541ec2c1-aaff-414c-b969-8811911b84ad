/**
 * transform SNMP Device Catalog received from server
 */
import { generateId } from '@utils/id'
import Invert from 'lodash/invert'
import Trim from 'lodash/trim'
import Map from 'lodash/map'
import Constants from '@constants'

export function getDefaultComputationConverter() {
  return {
    converter: [{ key: generateId(), keyName: undefined, value: undefined }],
    computation: {
      keyName: '$$$VALUE$$$',
      operator: undefined,
      value: undefined,
    },
  }
}

export function transformDeviceCatalogForList(catalog) {
  return {
    id: catalog[Constants.ID_PROPERTY],
    name: catalog['snmp.device.catalog.model'],
    vendor: catalog['snmp.device.catalog.vendor'],
    type: catalog['snmp.device.catalog.type'],
    usedCounts: catalog['count'] || 0,
    oid: catalog['snmp.device.catalog.oid'],
    createdBy: catalog['_type'] !== '0' ? 'User' : 'System',
    // monitors: catalog['monitor.ids'] || [],
    // defaultMonitors: Object.freeze(catalog['monitor.ids'] || []),
    canEdit: catalog['_type'] !== '0',
  }
}
export function transformMonitorsForList(log) {
  return {
    id: log[Constants.ID_PROPERTY],
    monitor: log['object.name'],
    ip: log['object.ip'],
    groups: log['object.groups'],
    host: log['object.host'],
    monitorType: log['object.type'],
    severity: log['object.state'],
  }
}

export function transformDeviceCatalogForClient(catalogResult, metricResult) {
  let data = {
    metrics: {},
  }
  data['id'] = catalogResult[Constants.ID_PROPERTY]
  data['oid'] = catalogResult['snmp.device.catalog.oid']
  data['name'] = catalogResult['snmp.device.catalog.model']
  data['vendor'] = catalogResult['snmp.device.catalog.vendor']
  data['monitorType'] = catalogResult['snmp.device.catalog.type']
  data['canEdit'] = true
  data['metrics'] = transformDeviceCatalogMetricGroupClient(metricResult)
  return data
}
export function transformDeviceCatalogMetricGroupClient(catalog) {
  let context = []
  let data = {}
  catalog.forEach(function (element) {
    data = {}
    if (element['oid.group.type'] === 'scalar') {
      data['id'] = element[Constants.ID_PROPERTY]
      data['guid'] = generateId()
      data['name'] = element['oid.group.name']
      data['metricType'] = 'Scalar'
      data['oids'] = transformDeviceCatalogScalarMetricGroupOIDClient(
        element['oid.group.oids'],
        element['oid.group.computations'] || {},
        element['oid.group.converters'] || {},
        element['oid.group.regex'] || {}
      )
      context.push(data)
    } else {
      data['id'] = element[Constants.ID_PROPERTY]
      data['guid'] = generateId()
      data['name'] = element['oid.group.name']
      data['metricType'] = 'Tabular'
      data.groupParentOid = element['oid.group.parent.oid']
      data['oids'] = transformDeviceCatalogTabularMetricGroupOIDClient(
        element['oid.group.oids'],
        element['oid.group.computations'] || {},
        element['oid.group.converters'] || {}
      )
      context.push(data)
    }
  })
  return context
}

export function transformDeviceCatalogTabularMetricGroupOIDClient(
  catalog,
  computation,
  converter
) {
  return Object.keys(catalog).map((field) => ({
    key: generateId(),
    name: catalog[field],
    oid: field,
    computation: transformComputationForClient(computation[field]),
    converter: transformConverterForClient(converter[field]),
  }))
}

export function transformDeviceCatalogScalarMetricGroupOIDClient(
  catalog,
  computation = {},
  converter = {},
  regex = {}
) {
  return Object.keys(catalog).map((field) => ({
    key: generateId(),
    name: catalog[field],
    oid: field,
    ...(computation[field]
      ? { computation: transformComputationForClient(computation[field]) }
      : { computation: getDefaultComputationConverter().computation }),
    ...(converter[field]
      ? { converter: transformConverterForClient(converter[field]) }
      : { converter: getDefaultComputationConverter().converter }),

    ...(regex[field] ? { regex: transformRejexForClient(regex[field]) } : {}),
  }))
}

function transformComputationForClient(computation) {
  const computationWithoutKeyName =
    ((computation || '').toString() || '').replace('$$$VALUE$$$', '') || ''
  const operator = computationWithoutKeyName.charAt(0) || ''
  const value = +computationWithoutKeyName.substr(1)
  if (operator && value) {
    return {
      keyName: '$$$VALUE$$$',
      operator,
      value,
    }
  }
  return getDefaultComputationConverter().computation
}

function transformConverterForClient(converter) {
  let converters = Object.entries(converter || {}).map(([keyName, value]) => {
    return {
      key: generateId(),
      keyName,
      value,
    }
  })
  return converters.length > 0
    ? converters
    : getDefaultComputationConverter().converter
}

function transformRejexForClient(rejex) {
  return rejex
}

export function transformDeviceCatalogForServer(catalog) {
  return {
    'snmp.device.catalog.oid': /^\./.test(Trim(catalog.oid))
      ? Trim(catalog.oid)
      : '.' + Trim(catalog.oid),
    'snmp.device.catalog.model': catalog.name,
    'snmp.device.catalog.type': catalog.monitorType,
    'snmp.device.catalog.vendor': catalog.vendor,
    _type: '1',
    'snmp.device.catalog.id': catalog.id,
  }
}

export function transformDeviceCatalogMetricForServer(id, catalog) {
  if (catalog.metricType.toLowerCase() === 'scalar') {
    return transformDeviceCatalogMetricScalarForServer(id, catalog)
  } else {
    return transformDeviceCatalogMetricTabularForServer(id, catalog)
  }
}

export function transformDeviceCatalogMetricScalarForServer(id, catalog) {
  let data = {
    'oid.group.oids': {},
  }
  const oids = transformMetricScalarGroupOIDForServer(catalog)
  data['oid.group.oids'] = oids.data
  data['oid.group.name'] = catalog['name']
  data['oid.group.type'] = 'scalar'
  data['snmp.device.catalog.id'] = id
  if (Object.keys(oids.computations).length) {
    data['oid.group.computations'] = oids.computations
  }
  if (Object.keys(oids.converters).length) {
    data['oid.group.converters'] = oids.converters
  }
  if (Object.keys(oids.regex).length) {
    data['oid.group.regex'] = oids.regex
  }

  return data
}
export function transformDeviceCatalogMetricTabularForServer(id, catalog) {
  let data = {
    'oid.group.oids': {},
  }
  const oids = transformMetricTabularGroupOIDForServer(catalog)
  data['oid.group.oids'] = oids.data
  if (Object.keys(oids.computations).length) {
    data['oid.group.computations'] = oids.computations
  }
  if (Object.keys(oids.converters).length) {
    data['oid.group.converters'] = oids.converters
  }
  data['oid.group.name'] = catalog['name']
  data['oid.group.type'] = 'tabular'
  data['oid.group.parent.oid'] = catalog.groupParentOid
  data['snmp.device.catalog.id'] = id

  return data
}

export function transformMetricScalarGroupOIDForServer(catalog) {
  let data = {}
  let computations = {}
  let converters = {}
  let regex = {}

  catalog['oids'].forEach(function (item, index) {
    const oid = /^\./.test(Trim(item.oid))
      ? Trim(item.oid)
      : '.' + Trim(item.oid)
    data[oid] = Trim(item.name)

    if (item.computation) {
      if (
        item.computation.keyName &&
        item.computation.operator &&
        item.computation.value
      ) {
        computations[
          oid
        ] = `${item.computation.keyName}${item.computation.operator}${item.computation.value}`
      }
    }

    if (item.converter) {
      const eachItemConvertersData = transformOIDConvertersForServer(
        item.converter
      )
      if (Object.keys(eachItemConvertersData || {}).length) {
        converters[oid] = transformOIDConvertersForServer(item.converter)
      }
    }
    if (item.regex) {
      regex[oid] = item.regex
    }
  })
  return {
    data,
    computations,
    converters,
    regex,
  }
}

export function transformMetricTabularGroupOIDForServer(catalog) {
  let data = {}
  let computations = {}
  let converters = {}
  catalog['oids'].forEach(function (item, index) {
    const oid = /^\./.test(Trim(item.oid))
      ? Trim(item.oid)
      : '.' + Trim(item.oid)
    data[oid] = Trim(item.name)
    if (item.computation) {
      if (
        item.computation.keyName &&
        item.computation.operator &&
        item.computation.value
      ) {
        computations[
          oid
        ] = `${item.computation.keyName}${item.computation.operator}${item.computation.value}`
      }
    }
    if (item.converter) {
      const eachItemConvertersData = transformOIDConvertersForServer(
        item.converter
      )
      if (Object.keys(eachItemConvertersData || {}).length) {
        converters[oid] = transformOIDConvertersForServer(item.converter)
      }
    }
  })
  return { data, computations, converters }
}

function transformOIDConvertersForServer(converters) {
  return converters
    .filter((converter) => {
      return converter.keyName && converter.value
    })
    .reduce(
      (total, item) => ({
        ...total,
        [item.keyName]: item.value,
      }),
      {}
    )
}

export function test(item, index) {
  let data = []
  if (index !== 0) {
    data.push({
      'counter.name': item.name,
      'counter.oid': item.oid,
    })
  }
  return data
}

export function transformDeviceCatalogSearchOIDForScalar(result) {
  const OIDS = Object.keys(result)
  return Map(OIDS, (oid, index) => {
    let item = result[oid][0] || {}
    return {
      id: `${item['oid.name']}-${item['oid']}-${index}`,
      name: item['oid.name'],
      oid: item['oid'],
    }
  })
}

export function transformDeviceCatalogSearchOIDForTabular(result) {
  const OIDS = Object.keys(result)
  function iterateItems(items) {
    const tabularData = items.filter(Boolean)
    return Map(tabularData, (item) => {
      return {
        id: generateId(),
        name: item['oid.name'],
        oid: item['oid'],
        children: iterateItems(item.children || []),
      }
    })
  }
  return iterateItems(
    Map(OIDS, (oid) => {
      const childrens = result[oid] || {}
      const childrensWithaoutParent = childrens.filter((c) => c.oid !== oid)
      return {
        'oid.name': childrens[0] ? childrens[0]['parent.name'] : '',
        oid,
        children: childrensWithaoutParent,
      }
    })
  )
}

export function getQueryparams(data) {
  let log = {
    filter: {},
  }
  log['filter']['vendor'] = data['vendor']
  log['filter']['oid.type'] = data['type'].toLowerCase()
  log['filter']['oid.name'] = data.counterName
  return log
}

export function transformScalarTestEvent(data) {
  return {
    guid: data.guid,
    result: (data.result || []).map((rec) => ({
      id: generateId(),
      oid: rec.oid,
      count: rec.value,
      status: rec.status,
      error: rec.message,
    })),
  }
}

export function transformTabularTestEvent(data) {
  const oidMaps = Invert(data['oid.group.oids'] || {})
  const parentKey = Object.keys(data['result'] || {}).filter(
    (key) => key !== 'invalid.oid'
  )[0]
  const headers = Object.values(data['oid.group.oids']).map((oid) => ({
    key: oidMaps[oid],
    text: oid,
    gridKey: oid.replaceAll(/\./g, '_'),
  }))
  return {
    guid: data.guid,
    headers,
    row: data.result[parentKey].map((test) => {
      const row = {
        id: generateId(),
      }
      Object.keys(oidMaps).map(
        (key) => (row[key?.replaceAll(/\./g, '_')] = test[key])
      )
      return row
    }),
  }
}
