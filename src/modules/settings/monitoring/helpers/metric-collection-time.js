// import GroupBy from 'lodash/groupBy'
import Constants from '@constants'
import SortBy from 'lodash/sortBy'
import { generateId } from '@utils/id'
import DiscoveryTypes from '@src/statics/discovery-type'
import bytesToSize from '@src/filters/bytes'

const METRIC_COLLECTION_TIME_TAB_ORDER = [
  ...DiscoveryTypes[Constants.SERVER],
  ...DiscoveryTypes[Constants.CLOUD],
  ...DiscoveryTypes[Constants.NETWORK],
  ...DiscoveryTypes[Constants.SERVICE_CHECK],
  ...DiscoveryTypes[Constants.VIRTUALIZATION],
  ...DiscoveryTypes[Constants.WIRELESS],
  ...DiscoveryTypes[Constants.OTHER],
  ...DiscoveryTypes[Constants.CONTAINER_ORCHESTRATION],
  Constants.LINUX_PROCESS,
  Constants.WINDOWS_PROCESS,
  Constants.WINDOWS_SERVICE,
  Constants.IBM_AIX_PROCESS,
  Constants.HP_UX_PROCESS,
  'Network Interface',
  'Citrix Xen VM',
  'HyperV VM',
  'VMware ESXi VM',
  'Prism',
  'Nutanix',
  'Nutanix VM',
  'Cisco vManage',
  'Cisco vSmart',
  'Cisco vBond',
  'Cisco vEdge',
  Constants.NETWORK_SERVICE,
  Constants.DOCKER_CONTAINER,
]

export const PLURAL_NAME_MAP = {
  'Windows Service': 'Windows Services',
  'Windows Process': 'Windows Processes',
  'Linux Process': 'Linux Processes',
  'IBM AIX Process': 'IBM AIX Processes',
  'HP-UX Process': 'HP-UX Processes',
  'Solaris Process': 'Solaris Processes',
  Interface: 'Interfaces',
  'Network Interface': 'Network Interfaces',
  'Cisco Wireless Access Point': 'Access Points',
  'Ruckus Wireless Access Point': 'Access Points',
  'Aruba Wireless Access Point': 'Access Points',
  'Network Service': 'Network Services',
  'VMware ESXi VM': 'VMware ESXI VMs',
  'Citrix Xen VM': 'Citrix Xen VMs',
  'HyperV VM': 'HyperV VMs',
  'Linux Directory': 'Linux Directories',
  'Linux File': 'Linux Files',
  'Windows Directory': 'Windows Directories',
  'Windows File': 'Windows Files',
  'IBM AIX Directory': 'IBM AIX Directories',
  'IBM AIX File': 'IBM AIX Files',
  'HP-UX Directory': 'HP-UX Directories',
  'HP-UX File': 'HP-UX Files',
  'Solaris Directory': 'Solaris Directories',
  'Solaris File': 'Solaris Files',
  'IPSLA ICMP Echo': 'WAN Links',
  'IPSLA ICMP Jitter': 'WAN Links',
  'IPSLA Path Echo': 'WAN Links',
  IPSLA: 'WAN Links',
  'Nutanix VM': 'Nutanix VMs',
  'Docker Container': 'Docker Containers',
}

export const COLUMNS = {
  'windows.service': [
    { key: 'name', name: 'Service Name', searchable: true },
    { key: 'description', name: 'Service Description', searchable: true },
  ],
  process: [
    { key: 'name', name: 'Process Name', searchable: true },
    { key: 'commandline', name: 'Commandline Arguments', searchable: true },
  ],
  interface: [
    { key: 'name', name: 'Interface Name', searchable: true },
    { key: 'alias', name: 'Interface Alias', searchable: true },
    { key: 'ip', name: 'Interface IP', searchable: true },
    { key: 'linkType', name: 'Link Type', searchable: true, width: '80px' },
    { key: 'description', name: 'Interface Description', searchable: true },
    {
      key: 'speed',
      name: 'Speed (Bps)',
      searchable: true,
      searchKey: 'speedDisplay',
      width: '300px',
    },
  ],
  'network.service': [{ key: 'name', name: 'Name', searchable: true }],
  vm: [
    { key: 'name', name: 'VM Name', searchable: true },
    { key: 'ip', name: 'IP', searchable: true },
  ],
  wireless: [
    { key: 'name', name: 'Name', searchable: true },
    { key: 'ip', name: 'IP Address', searchable: true },
    { key: 'mac', name: 'Mac Address', searchable: true },
  ],
  fileDirectory: [{ key: 'name', name: 'File/Directory', searchable: true }],
  wanLink: [
    { key: 'name', name: 'name', searchable: true },
    { key: 'sourceIp', name: 'source Ip Address', searchable: true },
    {
      key: 'destinationIp',
      name: 'destinatio Ip Address',
      searchable: true,
    },
    {
      key: 'sourceInterface',
      name: 'Source Interface Name',
      searchable: true,
    },
  ],
  custom: [
    { key: 'name', name: 'Metric Name', searchable: true },
    { key: 'pollTime', name: 'Poll Time (sec)', width: '200px' },
    { key: 'timeout', name: 'Timeout (sec)' },
    { key: 'status', name: 'Status' },
  ],
  hci: [
    { key: 'name', name: 'VM Name', searchable: true },
    { key: 'ip', name: 'IP', searchable: true },
  ],
  sdn: [
    { key: 'name', name: 'VM Name', searchable: true },
    { key: 'ip', name: 'IP', searchable: true },
  ],
  container: [{ key: 'name', name: 'Container Name', searchable: true }],
}

export const SERVER_METRIC_TYPES = [
  Constants.WINDOWS,
  Constants.WINDOWS_CLUSTER,
  Constants.LINUX,
  Constants.IBM_AS_400,
  Constants.IBM_AIX,
  Constants.HP_UX,
  Constants.SOLARIS,
  Constants.AWS_CLOUD,
  Constants.AZURE_CLOUD,
  Constants.OFFICE_365,
  Constants.AMAZON_EC2,
  Constants.AWS_ELB,
  Constants.AMAZON_DYNAMO_DB,
  Constants.AMAZON_EBS,
  Constants.AMAZON_RDS,
  Constants.AMAZON_S3,
  Constants.AMAZON_SNS,
  Constants.AMAZON_CLOUD_FRONT,
  Constants.AWS_AUTO_SCALING,
  Constants.AWS_LAMBDA,
  Constants.AMAZON_SQS,
  Constants.AWS_ELASTIC_BEANSTALK,
  Constants.AMAZON_DOCUMENTDB,
  Constants.AZURE_COSMOS_DB,
  Constants.AZURE_SQL_DATABASE,
  Constants.AZURE_STORAGE,
  Constants.AZURE_VM,
  Constants.AZURE_WEB_APP,
  Constants.AZURE_SERVICE_BUS,
  Constants.AZURE_APPLICATION_GATEWAY,
  Constants.AZURE_FUNCTION,
  Constants.AZURE_LOAD_BALANCER,
  Constants.AZURE_POSTGRESQL_SERVER,
  Constants.AZURE_MYSQL_SERVER,
  Constants.AZURE_VM_SCALE_SET,
  Constants.AZURE_CDN,
  Constants.ONEDRIVE,
  Constants.EXCHANGE_ONLINE,
  Constants.SHAREPOINT_ONLINE,
  Constants.MICROSOFT_TEAMS,
  Constants.SNMP_DEVICE,
  Constants.SWITCH,
  Constants.ROUTER,
  Constants.FIREWALL,
  Constants.LOAD_BALANCER,
  Constants.UPS,
  Constants.PRINTER,
  Constants.WIRELESS_CONTROLLER,
  Constants.HYPER_V,
  Constants.HYPER_V_CLUSTER,
  Constants.VCENTER,
  Constants.VMWARE_ESXI,
  Constants.CITRIX_XEN,
  Constants.CITRIX_XEN_CLUSTER,
  Constants.PING,
  Constants.PORT,
  Constants.URL,
  Constants.RADIUS,
  Constants.NTP,
  Constants.DOMAIN,
  Constants.DNS,
  Constants.FTP,
  Constants.EMAIL,
  Constants.SSL_CERTIFICATE,
  Constants.CISCO_UCS,
  Constants.SYMANTEC_MESSAGING_GATEWAY,
  Constants.IBM_TAPE_LIBRARY,
  Constants.CISCO_WIRELESS,
  Constants.ARUBA_WIRELESS,
  Constants.RUCKUS_WIRELESS,
  Constants.NUTANIX,
  Constants.PRISM,
  Constants.CISCO_VMANAGE,
  Constants.CISCO_VBOND,
  Constants.CISCO_VSMART,
  Constants.CISCO_VEDGE,
  Constants.CISCO_MERAKI,
  Constants.CISCO_MERAKI_RADIO,
  Constants.CISCO_MERAKI_SECURITY,
  Constants.CISCO_MERAKI_SWITCH,
  Constants.HPE,
  Constants.NETAPP,
  Constants.HPE_3PAR,
  Constants.HPE_PRIMERA,
  Constants.HPE_STOREONCE,
  Constants.DELL_EMC_UNITY,
  Constants.CISCO_ACI,
  Constants.WINDOWS_SNMP,
  Constants.LINUX_SNMP,
  Constants.VMWARE_TANZU_KUBERNETES,
  Constants.NSXT,
  Constants.KUBERNETES,
]

export const APPLICATIONS_METRIC_TYPE = [
  Constants.NGINX,
  Constants.APACHE_HTTP,
  Constants.LIGHTTPD,
  Constants.MICROSOFT_IIS,
  Constants.HA_PROXY,
  Constants.ACTIVE_DIRECTORY,
  Constants.BIND9,
  Constants.WINDOWS_DNS,
  Constants.LINUX_DHCP,
  Constants.WINDOWS_DHCP,
  Constants.IBM_WEBSPHERE,
  Constants.ORACLE_WEBLOGIC,
  Constants.APACHE_MQ,
  Constants.IBM_MQ,
  Constants.MSMQ,
  Constants.RABBITMQ,
  Constants.APACHE_TOMCAT,
  Constants.GLASS_FISH_SERVER,
  Constants.WILDFLY,
  Constants.MARIADB,
  Constants.MYSQL,
  Constants.IBM_DB2,
  Constants.SQL_SERVER,
  Constants.POSTGRESQL,
  Constants.MONGODB,
  Constants.SYBASE,
  Constants.SAP_HANA,
  Constants.SAP_MAXDB,
  Constants.ORACLE_DATABASE,
  Constants.EXCHANGE_CLIENT_ACCESS_ROLE,
  Constants.EXCHANGE_EDGE_TRANSPORT_ROLE,
  Constants.EXCHANGE_MAILBOX,
  Constants.EXCHANGE_MAILBOX_ROLE,
  Constants.ZIMBRA,
  Constants.WINDOWS_RDP,
]

export const WINDOWS_SERVICE_METRIC_TYPE = ['Windows Service']

export const NETWORK_SERVICE_METRIC_TYPE = ['Network Service']

export const VM_METRIC_TYPE = ['Citrix Xen VM', 'HyperV VM', 'VMware ESXi VM']

export const HCI_METRIC_TYPE = ['Nutanix VM']

export const SDN_METRIC_TYPE = [
  'Cisco vManage',
  'Cisco vSmart',
  'Cisco vBond',
  'Cisco vEdge',
]

export const WIRELESS_METRIC_TYPE = [
  'Cisco Wireless Access Point',
  'Ruckus Wireless Access Point',
  'Aruba Wireless Access Point',
]

export const FILE_DIRECTORY_METRIC_TYPE = [
  'Linux Directory',
  'Linux File',
  'Windows Directory',
  'Windows File',
  'IBM AIX Directory',
  'IBM AIX File',
  'HP-UX Directory',
  'HP-UX File',
  'Solaris Directory',
  'Solaris File',
]

export const WAN_LINK_TYPE = [
  'IPSLA ICMP Echo',
  'IPSLA ICMP Jitter',
  'IPSLA Path Echo',
]

export const CONTAINER_TYPE = ['Docker Container']

export const PROCESS_METRIC_TYPE = [
  'Windows Process',
  'Linux Process',
  'IBM AIX Process',
  'HP-UX Process',
  'Solaris Process',
]

export const INTERFACE_METRIC_TYPE = ['Interface', 'Network Interface']

export const CUSTOM_METRIC_TYPE = ['Custom', 'Hardware Sensor', 'Email Gateway']

const AGENT_EXCLUDED_METRICS = [
  '.NET App',
  'Windows',
  'Windows CPU Core',
  'Windows Disk',
  'Windows Network Interface',
  'Windows Process',
  'Windows Service',
  'Windows Task Scheduler',
  'Linux',
  'Linux CPU Core',
  'Linux Disk',
  'Linux Network Interface',
  'Linux Process',
  'IBM AIX',
  'IBM AIX CPU Core',
  'IBM AIX Disk',
  'IBM AIX Network Interface',
  'IBM AIX Process',
]

const DEVICE_MONITORING_EXCLUDED_METRICS = ['Object Status', 'IPSLA']

function transformServerMetric(
  category,
  metricItems,
  forContextParams,
  forAgent = false
) {
  const credentialProfile = metricItems[category]
    .map((i) => i['metric.credential.profile'])
    .filter((i) => i !== -1)
  let metricItemsByCategory = metricItems[category] || []
  if (forAgent) {
    metricItemsByCategory = metricItemsByCategory.filter(
      (i) => AGENT_EXCLUDED_METRICS.includes(i['metric.name']) === false
    )
  } else {
    metricItemsByCategory = metricItemsByCategory.filter(
      (i) =>
        DEVICE_MONITORING_EXCLUDED_METRICS.includes(i['metric.name']) === false
    )
  }
  return {
    guid: generateId(),
    name: `${category}`,
    credentialProfile: credentialProfile[0],
    params: metricItems[category][0]['metric.context'],
    items: forContextParams
      ? metricItemsByCategory.map((group) => ({
          context: Object.freeze(group),
        }))
      : metricItemsByCategory.map((group) => ({
          id: group[Constants.ID_PROPERTY] || group['metric.name'],
          name: group['metric.name'],
          timeout: group['metric.context']['timeout'],
          systemName: group['metric.plugin'],
          pollTime: group['metric.polling.time'],
          ...([Constants.STATE_SUSPEND, Constants.STATE_INVALID].includes(
            group['metric.state']
          )
            ? {
                metricState: group['metric.state'],
              }
            : {}),
          status: group['metric.state'] === Constants.STATE_ENABLE,
          ...(category === 'URL'
            ? {
                credentialProfile: group['metric.credential.profile'],
              }
            : {}),

          ...(group?.['metric.context']?.['valid.oids']
            ? {
                showValidOids: true,
                validOids: Object.keys(
                  group?.['metric.context']?.['valid.oids'] || {}
                ).map((oid, index) => ({
                  id: index + 1,
                  oid,
                  metric: group['metric.context']['valid.oids'][oid],
                })),
              }
            : {}),

          context: Object.freeze(group),
        })),
  }
}

function transformServerMetricForServer(metricItem) {
  return metricItem.items.map((i) => ({
    ...i.context,
    ...(i.context['metric.credential.profile'] !== -1
      ? {
          'metric.credential.profile':
            metricItem.credentialProfile || i.credentialProfile,
        }
      : {}),
    'metric.context': {
      ...(i.context['metric.context'] || {}),
      timeout: parseInt(i.timeout),
      ...(i?.validOids?.length || i?.showValidOids
        ? {
            'valid.oids': i?.validOids.reduce((acc, oid) => {
              return {
                ...acc,
                [oid.oid]: oid.metric,
              }
            }, {}),
          }
        : {}),
    },
    'metric.polling.time': parseInt(i.pollTime),
    'metric.state': i.metricState
      ? i.metricState
      : i.status
      ? Constants.STATE_ENABLE
      : Constants.STATE_DISABLE,
    ...(i.name === 'URL'
      ? {
          'metric.credential.profile': i.credentialProfile,
        }
      : {}),
  }))
}

function transformWindowsService(category, metricItems) {
  const metricContext = metricItems[category][0]['metric.context']
  return {
    guid: generateId(),
    name: `${category}`,
    context: Object.freeze({ ...metricItems[category][0] }),
    items: metricContext.objects.map((object) => ({
      id: generateId(),
      selected: false,
      name: object['object.name'],
      description: object['system.service.description'],
      context: Object.freeze({ ...object }),
    })),
  }
}

function transformWindowsServiceMetricForServer(metricItem) {
  return [
    {
      ...metricItem.context,
      'metric.instances': transformSelectedDeleteInstances(metricItem),
      'metric.context': {
        ...(metricItem.context['metric.context'] || {}),
        objects: metricItem.items
          .filter((i) => i.selected === false)
          .map((i) => ({ ...i.context })),
      },
    },
  ]
}

function transformWirelessMetricForServer(metricItem) {
  return [
    {
      ...metricItem.context,
      'metric.instances': transformSelectedDeleteInstances(metricItem),
      'metric.context': {
        ...(metricItem.context['metric.context'] || {}),
        objects: metricItem.items
          .filter((i) => i.selected === false)
          .map((i) => ({ ...i.context })),
      },
    },
  ]
}

function transformWindowsProcess(category, metricItems) {
  const metricContext = metricItems[category][0]['metric.context']
  return {
    guid: generateId(),
    name: `${category}`,
    context: Object.freeze({ ...metricItems[category][0] }),
    items: metricContext.objects.map((object) => {
      const parts = object['object.name'].split('|')
      return {
        id: generateId(),
        selected: false,
        name: parts[0],
        commandline: parts[1],
        context: Object.freeze({ ...object }),
      }
    }),
  }
}

function transformInterface(category, metricItems) {
  const metricContext = metricItems[category][0]['metric.context']
  return {
    guid: generateId(),
    name: `${category}`,
    context: Object.freeze({ ...metricItems[category][0] }),
    items: metricContext.objects.map((object) => ({
      id: generateId(),
      selected: false,
      name: object['interface.name'],
      ip: object['interface.ip.address'],
      alias: object['interface.alias'],
      speed: object['interface.speed.bytes.per.sec'],
      linkType: object['interface.link.type'],
      speedDisplay: bytesToSize(object['interface.speed.bytes.per.sec']),
      description: object['interface.description'],
      context: Object.freeze({ ...object }),
    })),
  }
}

function transformNetworkService(category, metricItems) {
  const metricContext = metricItems[category][0]['metric.context']
  return {
    guid: generateId(),
    name: `${category}`,
    context: Object.freeze({ ...metricItems[category][0] }),
    items: metricContext.objects.map((object) => ({
      id: generateId(),
      selected: false,
      name: object['object.name'],
      statusPill: object['status'],
      context: Object.freeze({ ...object }),
    })),
  }
}

function transformVm(category, metricItems) {
  const metricContext = metricItems[category][0]['metric.context']
  return {
    guid: generateId(),
    name: `${category}`,
    context: Object.freeze({ ...metricItems[category][0] }),
    items: metricContext.objects.map((object) => ({
      id: generateId(),
      selected: false,
      name: object['object.name'],
      ip: object['object.ip'],
      context: Object.freeze({ ...object }),
    })),
  }
}

function transformHci(category, metricItems) {
  const metricContext = metricItems[category][0]['metric.context']
  return {
    guid: generateId(),
    name: `${category}`,
    context: Object.freeze({ ...metricItems[category][0] }),
    items: metricContext.objects.map((object) => ({
      id: generateId(),
      selected: false,
      name: object['object.name'],
      ip: object['object.ip'],
      context: Object.freeze({ ...object }),
    })),
  }
}

// function transformSdn(category, metricItems) {
//   const metricContext = metricItems[category][0]['metric.context']
//   return {
//     guid: generateId(),
//     name: `${category}`,
//     context: Object.freeze({ ...metricItems[category][0] }),
//     items: metricContext.objects.map((object) => ({
//       id: generateId(),
//       selected: false,
//       name: object['object.name'],
//       ip: object['object.ip'],
//       context: Object.freeze({ ...object }),
//     })),
//   }
// }

function transformWireless(category, metricItems) {
  const metricContext = metricItems[category][0]['metric.context']
  return {
    guid: generateId(),
    name: `${category}`,
    context: Object.freeze({ ...metricItems[category][0] }),
    items: metricContext.objects.map((object) => ({
      id: generateId(),
      selected: false,
      name: object['wireless.access.point'],
      ip: object['wireless.access.point.ip.address'],
      mac: object['wireless.access.point.mac.address'],
      context: Object.freeze({ ...object }),
    })),
  }
}

function transformWanLink(category, metricItems) {
  const metricContext = metricItems[category][0]['metric.context']
  return {
    guid: generateId(),
    name: `${category}`,
    context: Object.freeze({ ...metricItems[category][0] }),
    items: metricContext.objects.map((object) => ({
      id: generateId(),
      selected: false,
      name: object['object.name'],
      sourceIp: object['source.ip.address'],
      destinationIp: object['destination.ip.address'],
      sourceInterface: object['source.interface.name'],
      context: Object.freeze({ ...object }),
    })),
  }
}

function transformContainer(category, metricItems) {
  const metricContext = metricItems[category][0]['metric.context']
  return {
    guid: generateId(),
    name: `${category}`,
    context: Object.freeze({ ...metricItems[category][0] }),
    items: metricContext.objects.map((object) => ({
      id: generateId(),
      selected: false,
      name: object['object.name'],
      context: Object.freeze({ ...object }),
    })),
  }
}

function transformSelectedDeleteInstances(metricItem) {
  let deleteItem = []
  metricItem.items
    .filter((i) => i.selected === true)
    .forEach(function (entry) {
      if (entry.context[entry.context['object.type']] !== undefined) {
        deleteItem = [
          ...deleteItem,
          entry.context[entry.context['object.type']],
        ]
      } else {
        deleteItem = [...deleteItem, entry.context['object.name']]
      }
    })
  return deleteItem
}

function transformInterfaceMetricForServer(metricItem) {
  return [
    {
      ...metricItem.context,
      'metric.instances': transformSelectedDeleteInstances(metricItem),
      'metric.context': {
        ...((metricItem.context || {})['metric.context'] || {}),
        objects: metricItem.items
          .filter((i) => i.selected === false)
          .map((i) => ({
            ...i.context,
            'interface.speed.bytes.per.sec': i.speed,
          })),
      },
    },
  ]
}

function transformCustomMetric(category, metricItem) {
  return {
    guid: generateId(),
    name: `${category}`,
    items: (metricItem[category] || []).map((group) => ({
      id: group[Constants.ID_PROPERTY] || group['metric.name'],
      name: group['metric.name'],
      timeout: group['metric.context']['timeout'],
      protocol:
        group['metric.type'] === 'Python' ? undefined : group['metric.type'],
      systemName: group['metric.plugin'],
      credentialProfile: group['metric.credential.profile'],
      pollTime: group['metric.polling.time'],
      status: group['metric.state'] === Constants.STATE_ENABLE,
      context: Object.freeze(group),
    })),
  }
}

export function transfromMetricGroupsForMonitor(
  metricItems,
  forContextParams = false,
  forAgent = false
) {
  const data = {}
  if (Object.keys(metricItems).length) {
    Object.keys(metricItems).forEach((category) => {
      let fn
      if (
        [...SERVER_METRIC_TYPES, ...APPLICATIONS_METRIC_TYPE].indexOf(
          category
        ) >= 0
      ) {
        fn = transformServerMetric
      }
      if (CUSTOM_METRIC_TYPE.indexOf(category) >= 0) {
        fn = transformCustomMetric
      }
      if (INTERFACE_METRIC_TYPE.indexOf(category) >= 0) {
        fn = transformInterface
      }
      if (NETWORK_SERVICE_METRIC_TYPE.indexOf(category) >= 0) {
        fn = transformNetworkService
      }
      if (VM_METRIC_TYPE.indexOf(category) >= 0) {
        fn = transformVm
      }
      if (WIRELESS_METRIC_TYPE.indexOf(category) >= 0) {
        fn = transformWireless
      }
      if (WAN_LINK_TYPE.indexOf(category) >= 0) {
        fn = transformWanLink
      }
      if (HCI_METRIC_TYPE.indexOf(category) >= 0) {
        fn = transformHci
      }
      if (CONTAINER_TYPE.indexOf(category) >= 0) {
        fn = transformContainer
      }
      // if (SDN_METRIC_TYPE.indexOf(category) >= 0) {
      //   fn = transformSdn
      // }
      if (
        [...WINDOWS_SERVICE_METRIC_TYPE, ...FILE_DIRECTORY_METRIC_TYPE].indexOf(
          category
        ) >= 0
      ) {
        fn = transformWindowsService
      }
      if (PROCESS_METRIC_TYPE.indexOf(category) >= 0) {
        fn = transformWindowsProcess
      }
      if (fn) {
        metricItems[category] = SortBy(
          metricItems[category],
          (i) => i['metric.context']['plugin.id']
        )
        data[category] = fn(category, metricItems, forContextParams, forAgent)
      }
    })
  }
  const map = new Map()
  METRIC_COLLECTION_TIME_TAB_ORDER.forEach((i) => {
    if (i in data) {
      map.set(i, data[i])
    }
  })
  SortBy(Object.keys(data)).forEach((category) => {
    if (!map.has(category) && category !== 'Custom') {
      map.set(category, data[category])
    }
  })
  if (data['Custom']) {
    map.set('Custom', data['Custom'])
  }
  return Object.fromEntries(map)
}

export function transfromMetricGroupsForMonitorForServer(
  item,
  forContextParams = false
) {
  if (forContextParams) {
    return Object.keys(item).reduce((commulative, application) => {
      return commulative.concat(
        item[application].items.map((m) => ({
          ...m.context,
          'metric.context': item[application].params,
        }))
      )
    }, [])
  }
  let fn
  if (
    [
      ...SERVER_METRIC_TYPES,
      ...CUSTOM_METRIC_TYPE,
      ...APPLICATIONS_METRIC_TYPE,
    ].indexOf(item.name) >= 0
  ) {
    fn = transformServerMetricForServer
  }
  if (
    [
      ...PROCESS_METRIC_TYPE,
      ...WINDOWS_SERVICE_METRIC_TYPE,
      ...VM_METRIC_TYPE,
      ...FILE_DIRECTORY_METRIC_TYPE,
      ...NETWORK_SERVICE_METRIC_TYPE,
      ...WAN_LINK_TYPE,
      ...HCI_METRIC_TYPE,
      ...SDN_METRIC_TYPE,
      ...CONTAINER_TYPE,
    ].indexOf(item.name) >= 0
  ) {
    fn = transformWindowsServiceMetricForServer
  }
  if (INTERFACE_METRIC_TYPE.indexOf(item.name) >= 0) {
    fn = transformInterfaceMetricForServer
  }
  if (WIRELESS_METRIC_TYPE.indexOf(item.name) >= 0) {
    fn = transformWirelessMetricForServer
  }
  return fn(item, forContextParams)
}

export function getTabMetricType(activeTab) {
  if (
    [...SERVER_METRIC_TYPES, ...APPLICATIONS_METRIC_TYPE].indexOf(activeTab) >=
    0
  ) {
    return 'server'
  } else if (WINDOWS_SERVICE_METRIC_TYPE.indexOf(activeTab) >= 0) {
    return 'windows.service'
  } else if (PROCESS_METRIC_TYPE.indexOf(activeTab) >= 0) {
    return 'process'
  } else if (INTERFACE_METRIC_TYPE.indexOf(activeTab) >= 0) {
    return 'interface'
  } else if (NETWORK_SERVICE_METRIC_TYPE.indexOf(activeTab) >= 0) {
    return 'network.service'
  } else if (VM_METRIC_TYPE.indexOf(activeTab) >= 0) {
    return 'vm'
  } else if (WIRELESS_METRIC_TYPE.indexOf(activeTab) >= 0) {
    return 'wireless'
  } else if (FILE_DIRECTORY_METRIC_TYPE.indexOf(activeTab) >= 0) {
    return 'fileDirectory'
  } else if (WAN_LINK_TYPE.indexOf(activeTab) >= 0) {
    return 'wanLink'
  } else if (CUSTOM_METRIC_TYPE.indexOf(activeTab) >= 0) {
    return 'custom'
  } else if (HCI_METRIC_TYPE.indexOf(activeTab) >= 0) {
    return 'hci'
  } else if (SDN_METRIC_TYPE.indexOf(activeTab) >= 0) {
    return 'sdn'
  } else if (CONTAINER_TYPE.indexOf(activeTab) >= 0) {
    return 'container'
  }
  return undefined
}
