<template>
  <GroupProvider>
    <div class="flex flex-col w-full h-full">
      <MRow class="mb-4 mt-2">
        <MCol :size="12" class="ml-4">
          <h2 class="text-primary"><PERSON>ric Plugin</h2>
        </MCol>
        <MCol class="label-strip text-neutral-light ml-4">
          Define and manage custom or pre-built metric plugins for tailored
          device monitoring. For more information:
          <a
            href="https://docs.motadata.com/motadata-aiops-docs/plugin-management/metric-plugins/overview"
            target="_blank"
          >
            Metric Plugin
            <MIcon name="external-link"></MIcon>
          </a>
        </MCol>
      </MRow>
      <div class="flex-1 min-h-0 flex flex-col">
        <MetricTypeProvider>
          <FlotoPaginatedCrud
            ref="paginatedCrudRef"
            form-width="40%"
            :columns="columns"
            as-table
            :fetch-fn="fetchAllMetricList"
            :update-fn="updateAssginMonitor"
            resource-name="Metric Plugin"
            default-sort="-name"
            :delete-fn="deleteMetric"
          >
            <template v-slot:add-controls="{ filter, resetFilter, searchTerm }">
              <MRow :gutter="0">
                <MCol>
                  <MInput
                    :value="searchTerm"
                    class="search-box"
                    placeholder="Search"
                    name="search"
                    @update="filter"
                  >
                    <template v-slot:prefix>
                      <MIcon name="search" />
                    </template>
                    <template v-if="searchTerm" v-slot:suffix>
                      <MIcon
                        name="times-circle"
                        class="text-neutral-light cursor-pointer"
                        @click="resetFilter"
                      />
                    </template>
                  </MInput>
                </MCol>
                <MPermissionChecker
                  :permission="
                    $constants.PLUGIN_LIBRARY_SETTINGS_CREATE_PERMISSION
                  "
                >
                  <MCol class="text-right">
                    <FlotoLink
                      id="create-metric-plugin-btn"
                      as-button
                      :to="$currentModule.getRoute('create-metric')"
                    >
                      Create Metric Plugin
                    </FlotoLink>
                  </MCol>
                </MPermissionChecker>
              </MRow>
            </template>

            <!-- Assign monitor sliders -->
            <template v-slot:form-header>
              <template v-if="triggerInProgress"> Test Credential </template>
              <template v-else-if="monitorOrGroup === 'Group'">
                {{ drawerType === 'assign' ? 'Assign' : 'Unassign' }} Group
              </template>

              <template v-else>
                {{ drawerType === 'assign' ? 'Assign' : 'Unassign' }} Monitor
              </template>
            </template>
            <template v-slot:form-items="{ item }">
              <template v-if="monitorOrGroup === 'Group'">
                <FlotoFormItem
                  v-if="drawerType === 'assign'"
                  id="group-id"
                  label="Groups"
                >
                  <GroupPicker v-model="selectedItemGroups" multiple />
                </FlotoFormItem>
                <GroupSelection
                  v-else
                  :selected-items="selectedItemGroups"
                  :checked-items="unassignedIds"
                  @add-fields="handleAddNewFields"
                  @remove-fields="handleRemoveFields"
                >
                </GroupSelection>
              </template>
              <template v-else>
                <AssignMonitorWithTest
                  v-if="drawerType === 'assign'"
                  ref="assignMonitorDrawerRef"
                  :resource="item"
                  entity-key="metric.plugin.entities"
                  :fetch-fn="() => getAllAssignableMonitors(item)"
                  :full-resource-fn="getFullMetricPlugin"
                  :event-name="$currentModule.getConfig().METRIC_PLUGIN_TEST"
                  :default-selected="selectedDefaultItemMonitors"
                  @item-selected="canTest = $event.length > 0"
                  @test-finished="handleAssignMonitorTestFinished"
                />
                <MonitorSelection
                  v-else
                  v-model="selectedItemMonitors"
                  :use-padding="false"
                  :default-mode="'all'"
                  :default-value="selectedDefaultItemMonitors"
                  :fetch-fn="getAllRemoveAssignableMonitors"
                />
              </template>
            </template>
            <template v-slot:form-actions="{ cancel, item, submit }">
              <template v-if="!triggerInProgress">
                <MButton class="mr-2" variant="default" @click="cancel">
                  Cancel
                </MButton>
              </template>
              <MPermissionChecker
                :permission="
                  $constants.PLUGIN_LIBRARY_SETTINGS_UPDATE_PERMISSION
                "
              >
                <template v-if="!triggerInProgress">
                  <MButton
                    v-if="
                      monitorOrGroup === 'Monitor' &&
                      drawerType === 'assign' &&
                      !testIsFinished
                    "
                    outline
                    :disabled="!canTest"
                    @click="handleTestAssignMonitorClicked"
                  >
                    Test
                  </MButton>
                  <MButton
                    v-else
                    id="submit-btn-id"
                    :loading="assignMonitorsProcessing"
                    :disabled="
                      assignMonitorsProcessing ||
                      (monitorOrGroup === 'Monitor' &&
                        drawerType === 'assign' &&
                        selectedItemMonitors.length === 0)
                    "
                    @click="updateAssignedMonitor(item, submit)"
                  >
                    {{ drawerType === 'assign' ? 'Assign' : 'Unassign' }}
                    {{ item.monitorOrGroup }}
                  </MButton>
                </template>
              </MPermissionChecker>
            </template>

            <!-- column slots -->
            <template v-slot:name="{ item }">
              <FlotoLink
                class="resource-link"
                :to="
                  $currentModule.getRoute('view-metric', {
                    params: { id: item.id },
                  })
                "
              >
                {{ item.name }}
              </FlotoLink>
            </template>
            <template v-slot:description="{ item }">
              {{ item.description }}
            </template>
            <template v-slot:scriptType="{ item }">
              <MTag :closable="false" rounded variant="default">
                {{ item.scriptType }}
              </MTag>
            </template>
            <template v-slot:metricType="{ item }">
              <MetricTypePicker
                :value="item.metricType"
                :type="item.scriptType"
                placeholder="---"
                :as-input="false"
                only-text
                disabled
              />
            </template>
            <template v-slot:count="{ item }">
              <a>
                <UsedCounts
                  :title="`Used Count for ${item.name}`"
                  :display-count="item.count"
                  :parent-resource-id="item.id"
                  parent-resource-type="metric-plugins"
                  :count-types="[
                    {
                      countType: 'monitor',
                      title: 'Monitors',
                      url: `/settings/metric-plugins/${item.id}/references`,
                      resourceKey: $constants.MONITOR,
                    },
                    {
                      countType: 'group',
                      title: 'Groups',
                      resourceKey: 'Group',
                      url: `/settings/topology-plugins/${item.id}/references`,
                    },
                  ]"
                />
              </a>
            </template>

            <template v-slot:actions="{ item, edit }">
              <FlotoGridActions
                :actions="getMetricActions(item)"
                :resource="item"
                class="mr-3 action-btn-handle"
                :create-permission-name="
                  $constants.PLUGIN_LIBRARY_SETTINGS_CREATE_PERMISSION
                "
                :create-permission-keys="['clone', 'assign', 'remove-assign']"
                :edit-permission-name="
                  $constants.PLUGIN_LIBRARY_SETTINGS_UPDATE_PERMISSION
                "
                :delete-permission-name="
                  $constants.PLUGIN_LIBRARY_SETTINGS_DELETE_PERMISSION
                "
                @clone="handleCloneMetric(item)"
                @assign="showDrawer(edit, 'assign', item)"
                @remove-assign="showDrawer(edit, 'unassign', item)"
                @edit="navigateToEdit(item)"
              />
            </template>
          </FlotoPaginatedCrud>
        </MetricTypeProvider>
      </div>
    </div>
  </GroupProvider>
</template>

<script>
import MetricTypeProvider from '@components/data-provider/metric-type-provider.vue'
import MetricTypePicker from '@components/data-picker/metric-type-picker.vue'
import MonitorSelection from '@components/item-selection/monitor-selection.vue'
import UsedCounts from '@components/used-counts/used-counts.vue'
// import { Metric } from '@src/statics/metric-types.js'
import { getMonitorsApi } from '@modules/settings/monitoring/monitors-api'
import {
  getAllMetricsApi,
  deleteMetricApi,
  getAllAssignableMonitorsApi,
  updateAssignedMonitorsApi,
  getUnassignedMonitorsApi,
  updateUnassignedMonitorsApi,
  getMetricApi,
} from '../metrics-api'
import GroupProvider from '@components/data-provider/group-provider.vue'
import GroupSelection from '@components/item-selection/group-selection.vue'
import AssignMonitorWithTest from '../components/assign-monitor-with-test.vue'
import { transformMetricGroupForServer } from '../helpers/metric-plugin'

export default {
  name: 'MetricList',
  components: {
    MetricTypePicker,
    MetricTypeProvider,
    MonitorSelection,
    UsedCounts,
    GroupProvider,
    GroupSelection,
    AssignMonitorWithTest,
  },
  data() {
    this.gridActions = [
      { key: 'clone', name: 'Clone', icon: 'clone' },
      {
        key: 'assign',
        icon: 'file-check',
        name: 'Assign Monitors',
      },
      {
        key: 'remove-assign',
        icon: 'file-times',
        name: 'Remove Monitors',
      },
      // { key: 'edit', icon: 'pencil', name: 'Edit Metric' },
    ]
    this.gridItemGroupActions = [
      { key: 'clone', name: 'Clone', icon: 'clone' },
      { key: 'assign', icon: 'file-check', name: 'Assign Group' },
      {
        key: 'remove-assign',
        icon: 'file-times',
        name: 'Remove Assigned Group',
      },
      // { key: 'edit', icon: 'pencil', name: 'Edit Metric' },
    ]
    this.editableGridActions = [
      { key: 'clone', name: 'Clone Metric', icon: 'clone' },
      {
        key: 'assign',
        icon: 'file-check',
        name: 'Assign Monitors',
      },
      {
        key: 'remove-assign',
        icon: 'file-times',
        name: 'Remove Monitors',
      },
      { key: 'edit', icon: 'pencil', name: 'Edit Metric' },
      {
        key: 'delete',
        name: 'Delete Metric',
        icon: 'trash-alt',
        isDanger: true,
      },
    ]
    this.editableGridItemGroupActions = [
      { key: 'assign', icon: 'file-check', name: 'Assign Group' },
      {
        key: 'remove-assign',
        icon: 'file-times',
        name: 'Remove Assigned Group',
      },
      { key: 'clone', name: 'Clone Metric', icon: 'clone' },
      // { key: 'schedule', icon: 'schedule', name: 'Schedule Runbook' },
      { key: 'edit', icon: 'pencil', name: 'Edit Metric' },
      {
        key: 'delete',
        icon: 'trash-alt',
        name: 'Delete Metric',
        isDanger: true,
      },
    ]
    this.columns = [
      { key: 'name', name: 'Metric Name', searchable: true, sortable: true },
      {
        key: 'description',
        name: 'Description',
        searchable: true,
        sortable: true,
      },
      {
        key: 'scriptType',
        name: 'Protocol',
        searchable: true,
        sortable: true,
        width: '150px',
      },
      {
        key: 'metricType',
        name: 'Type',
        searchable: true,
        sortable: true,
        width: '150px',
      },
      {
        key: 'count',
        name: 'Used Count',
        searchable: true,
        sortable: true,
        width: '150px',
      },
      { key: 'actions', name: 'Actions', align: 'right', width: '120px' },
    ]
    return {
      selectedItemMonitors: [],
      selectedDefaultItemMonitors: [],
      removeMonitor: [],
      processing: true,
      assignMonitorsProcessing: false,
      selectedItemMonitor: undefined,
      drawerType: 'assign',
      selectedItemGroups: [],
      defaultFormData: {},
      unassignedIds: [],
      monitorOrGroup: undefined,
      triggerInProgress: false,
      testIsFinished: false,
      selectedCredentialProfile: undefined,
      canTest: false,
    }
  },
  methods: {
    getFullMetricPlugin(id) {
      return getMetricApi(id).then((data) =>
        transformMetricGroupForServer(data)
      )
    },
    handleAssignMonitorTestFinished({ monitors, credentialProfile }) {
      this.triggerInProgress = false
      this.testIsFinished = true
      this.selectedItemMonitors = monitors
      this.selectedCredentialProfile = credentialProfile
    },
    handleTestAssignMonitorClicked() {
      if (this.$refs.assignMonitorDrawerRef) {
        if (this.$refs.assignMonitorDrawerRef.getSelectedItems().length > 100) {
          this.$errorNotification({
            message: 'Error',
            description:
              'Testing cannot proceed. Please ensure the selection does not exceed 100 monitors and try again.',
          })
          return
        }
        this.$refs.assignMonitorDrawerRef.triggerTest()
        this.triggerInProgress = true
      }
    },
    getMetricActions(metric) {
      let actions
      if (metric.monitorOrGroup === 'Group') {
        actions = metric.canEdit
          ? this.editableGridItemGroupActions
          : this.gridItemGroupActions
      } else {
        actions = metric.canEdit ? this.editableGridActions : this.gridActions
      }
      actions =
        metric.count > 0
          ? actions.filter((a) => [].indexOf(a.key) === -1)
          : actions
      return actions
    },
    handleAddNewFields(fields) {
      this.unassignedIds = [...this.unassignedIds, ...fields]
    },
    handleRemoveFields(removedFieldIds) {
      this.unassignedIds = this.unassignedIds.filter(
        (f) => removedFieldIds.indexOf(f) === -1
      )
    },
    showDrawer(edit, type, item) {
      this.canTest = false
      this.triggerInProgress = false
      this.testIsFinished = false
      this.selectedItemMonitors = []
      this.selectedDefaultItemMonitors = []
      this.removeMonitor = item
      this.drawerType = type
      this.selectedItemGroups = item.group
      this.monitorOrGroup = item.monitorOrGroup
      this.selectedCredentialProfile = undefined
      edit()
    },
    fetchAllMetricList() {
      return getAllMetricsApi()
    },
    deleteMetric(item) {
      return deleteMetricApi(item.id)
    },
    handleCloneMetric(item) {
      return this.$router.push(
        this.$currentModule.getRoute('create-metric', {
          query: { cloneId: item.id },
        })
      )
    },
    navigateToEdit(item) {
      this.$router.push(
        this.$currentModule.getRoute('edit-metric', {
          params: { id: item.id },
        })
      )
    },
    getMetricJSON(type, metricType) {
      // if (type === this.$constants.CUSTOM) {
      return {
        'object.category': JSON.stringify([
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.OTHER,
          this.$constants.PYTHON,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.WAN_LINK,
          this.$constants.SDN,
          this.$constants.CLOUD,
          this.$constants.STORAGE,
          this.$constants.CONTAINER_ORCHESTRATION,
        ]),
      }
      // }
      // return {
      //   filter: {
      //     'metric.type': Metric[type],
      //     ...(type === this.$constants.SSH &&
      //     [
      //       this.$constants.LINUX,
      //       this.$constants.IBM_AIX,
      //       this.$constants.HP_UX,
      //       this.$constants.SOLARIS,
      //     ].indexOf(metricType) >= 0
      //       ? {
      //           'object.type': [metricType],
      //         }
      //       : {}),
      //     ...(type === this.$constants.POWERSHELL &&
      //     [this.$constants.WINDOWS].indexOf(metricType) >= 0
      //       ? {
      //           'object.type': [metricType],
      //         }
      //       : {}),
      //     'credential.profile.protocol':
      //       type === this.$constants.HTTP
      //         ? [this.$constants.HTTP_HTTPS]
      //         : type === this.$constants.DATABASE
      //         ? [this.$constants.JDBC]
      //         : [type],
      //   },
      // }
    },
    getAllAssignableMonitors(monitor) {
      return getMonitorsApi({
        params: this.getMetricJSON(monitor.scriptType, monitor.metricType),
      }).then((data) => {
        return getAllAssignableMonitorsApi(monitor.id).then((selectedData) => {
          this.selectedItemMonitors = []
          this.selectedDefaultItemMonitors = selectedData
          return { allMonitors: data, selectedMonitors: selectedData }
        })
      })
    },
    updateAssignedMonitor(monitor, submit) {
      this.assignMonitorsProcessing = true
      monitor.monitors =
        monitor.monitorOrGroup === 'Group'
          ? this.selectedItemGroups
          : this.selectedItemMonitors
      this.selectedItemMonitor = monitor
      submit()
    },
    getAllRemoveAssignableMonitors() {
      return getUnassignedMonitorsApi(
        this.getMetricJSON(
          this.removeMonitor.scriptType,
          this.removeMonitor.metricType
        ),
        this.removeMonitor.id
      ).then((data) => {
        this.assignMonitorsProcessing = false
        return data
      })
    },
    updateAssginMonitor() {
      if (this.drawerType === 'assign') {
        let newIds
        if (this.selectedItemMonitor.monitorOrGroup === 'Monitor') {
          newIds = this.selectedItemMonitors.filter((val) => {
            return !this.selectedDefaultItemMonitors.includes(val)
          })
        } else {
          newIds = this.selectedItemGroups.filter((val) => {
            return !this.selectedDefaultItemMonitors.includes(val)
          })
        }
        return updateAssignedMonitorsApi(
          this.selectedItemMonitor,
          newIds,
          this.selectedCredentialProfile
        ).then((data) => {
          this.assignMonitorsProcessing = false
          this.$refs.paginatedCrudRef.fetchData()
          return data
        })
      } else {
        return updateUnassignedMonitorsApi(
          this.removeMonitor,
          this.removeMonitor.monitorOrGroup === 'Monitor'
            ? this.selectedItemMonitors
            : this.unassignedIds
        ).then((data) => {
          this.assignMonitorsProcessing = false
          this.unassignedIds = []
          this.$refs.paginatedCrudRef.fetchData()
          return data
        })
      }
    },
  },
}
</script>
