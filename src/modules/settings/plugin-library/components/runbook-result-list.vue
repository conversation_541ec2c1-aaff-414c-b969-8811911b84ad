<template>
  <div>
    <FlotoDrawer
      :open="open"
      width="50%"
      :scrolled-content="false"
      @hide="closeDrawer"
    >
      <template v-slot:title> Result For {{ runbook.name }} </template>
      <MonitorProvider :search-params="searchParams">
        <FlotoContentLoader :loading="loading" class="h-full w-full px-2">
          <div class="min-h-0 flex flex-col flex-1 w-full m-2">
            <div v-if="data.length" class="mr-2 flex justify-between">
              <MInput
                v-model="searchTerm"
                class="search-box"
                placeholder="Search"
              >
                <template v-slot:prefix>
                  <MIcon name="search" />
                </template>
                <template v-if="searchTerm" v-slot:suffix>
                  <MIcon
                    name="times-circle"
                    class="text-neutral-light cursor-pointer"
                    @click="searchTerm = undefined"
                  />
                </template>
              </MInput>
            </div>
            <!-- <div v-if="showFilter && !fullscreen" class="px-4 my-2 pr-6">
          <Filters
            v-model="filters"
            @change="applyFilter"
            @hide="showFilter = !showFilter"
          />
        </div> -->
            <MGrid
              v-if="data.length"
              ref="runbookListRef"
              class="min-w-0"
              :columns="columns"
              :data="data"
              :paging="false"
              :search-term="searchTerm"
            >
              <template v-slot:monitorName="{ item }">
                <div class="flex items-center">
                  <Severity
                    v-if="item['entity.id']"
                    :object-id="item['entity.id']"
                    disable-tooltip
                    class="mr-2"
                  />
                  <span class="text-ellipsis">
                    <MonitorName :value="item.ip" :row="item">
                      <template v-slot:monitor="{ monitor }">
                        {{ monitor ? monitor.name : '' }}
                      </template>
                    </MonitorName></span
                  >
                </div>
              </template>

              <template v-slot:type="{ item }">
                <MonitorName :value="item.ip" :row="item" ignorelink>
                  <template v-slot:monitor="{ monitor }">
                    <MonitorType
                      :type="monitor ? monitor.type : ''"
                      :center="false"
                    />
                  </template>
                </MonitorName>
              </template>

              <template v-slot:status="{ item }">
                <NCMCredentialStatus :status="item.status" use-status-map />
              </template>

              <template v-slot:timestamp="{ item }">
                {{ Math.round(item.timestamp / 1000) | datetime }}
              </template>
              <template v-slot:viewResult="{ item }">
                <a v-if="item" id="view-result" @click="openViewResult(item)">
                  View Result
                </a>
              </template>
            </MGrid>
            <FlotoNoData v-else />
          </div>
        </FlotoContentLoader>
      </MonitorProvider>
    </FlotoDrawer>

    <ActionResult
      v-if="showActionResultForItem !== null"
      :item="showActionResultForItem"
      :open="showActionResultForItem !== null"
      @close="showActionResultForItem = null"
    />
  </div>
</template>

<script>
import { generateId } from '@utils/id'

import { getRunbookResultAPi } from '@modules/settings/plugin-library/runbook-api'
import MonitorName from '@components/widgets/views/grid/view-more/monitor-name.vue'
import MonitorType from '@components/monitor-type.vue'
import NCMCredentialStatus from '@modules/settings/ncm-settings/components/ncm-credential-status.vue'
import Severity from '@components/severity.vue'

import ActionResult from '../components/action-result.vue'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'

export default {
  name: 'RunbookResultList',
  components: {
    MonitorName,
    MonitorType,
    NCMCredentialStatus,
    Severity,
    ActionResult,
    MonitorProvider,
  },
  props: {
    runbook: {
      type: Object,
      required: true,
    },

    open: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      data: [],
      loading: true,
      guid: generateId(),
      searchTerm: undefined,
      showActionResultForItem: null,
    }
  },
  computed: {
    columns() {
      let columns = [
        {
          key: 'monitorIdArray',
          name: 'Monitor',
          searchable: true,
          sortable: true,
          contextKey: 'monitorContext',
          cellRender: 'monitorName',
          searchKey: 'monitorDisplay',
          sortKey: 'monitorDisplay',
        },
        {
          key: 'ip',
          name: 'ip',
          searchable: true,
          sortable: true,
        },
        {
          key: 'type',
          name: 'type',
          searchable: false,
          sortable: false,
          cellRender: 'type',
        },
        {
          key: 'status',
          name: 'status',
          searchable: true,
          sortable: true,
        },
        {
          key: 'timestamp',
          name: 'Timestamp',
          searchable: true,
          sortable: true,
        },
        {
          key: 'userName',
          name: 'Executed By',
          searchable: true,
          sortable: true,
        },
        {
          key: 'viewResult',
          name: 'Result',
          searchable: true,
          sortable: true,
        },
      ]

      if (this.runbook && this.runbook.monitorOrGroup === 'event.source') {
        columns = columns.filter(
          (c) => !['monitorIdArray', 'type'].includes(c.key)
        )
      }

      return columns
    },
    searchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.OTHER,
          this.$constants.CLOUD,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.SDN,
          this.$constants.STORAGE,
          this.$constants.CONTAINER_ORCHESTRATION,
        ],
      }
    },
  },
  watch: {
    open: {
      immediate: true,
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          if (newValue) {
            this.getResult()
          } else {
            this.closeDrawer()
          }
        }
      },
    },
  },

  beforeDestroy() {
    this.closeDrawer()
  },

  methods: {
    closeDrawer() {
      setTimeout(() => {
        this.$emit('close')
      }, 400)
    },
    getResult() {
      this.loading = true
      return getRunbookResultAPi({ id: this.runbook.id }, true).then((data) => {
        this.data = data
        this.loading = false
      })
    },
    openViewResult(item) {
      this.showActionResultForItem = {
        ...item,
        ignoreAutoFetch: true,
        name: this.runbook.name,
        description: this.runbook.description,
      }
    },
  },
}
</script>
