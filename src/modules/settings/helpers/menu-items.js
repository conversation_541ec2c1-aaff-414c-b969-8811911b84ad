import Constants from '@constants'

export const MenuItems = [
  // My Account
  {
    section: 'My Account',
    icon: 'my-account',
    key: 'my-account',
    permissions: [Constants.MY_ACCOUNT_READ_PERMISSION],
    children: [
      {
        title: 'My Profile',
        key: 'my-profile',
        route(vm) {
          return vm.$modules.getModuleRoute('my-account', 'my-profile')
        },
      },
      {
        title: 'UI Preference',
        key: 'ui-preference',
        route(vm) {
          return vm.$modules.getModuleRoute('my-account', 'ui-preference')
        },
      },
      {
        title: 'License',
        key: 'license',
        route(vm) {
          return vm.$modules.getModuleRoute('my-account', 'license')
        },
      },
    ],
  },

  // User Settings
  {
    section: 'User Settings',
    icon: 'user-settings',
    key: 'users-settings',
    permissions: [
      Constants.USER_SETTINGS_READ_PERMISSION,
      Constants.GROUP_SETTINGS_READ_PERMISSION,
    ],
    children: [
      {
        title: 'User',
        key: 'users-settings',
        permissions: [Constants.USER_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute('users-settings')
        },
      },
      {
        title: 'Personal Access Token',
        key: 'personal-access-token',
        permissions: [Constants.USER_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute(
            'users-settings',
            'personal-access-token'
          )
        },
      },
      {
        title: 'Role',
        key: 'roles',
        permissions: [Constants.USER_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute('users-settings', 'roles')
        },
      },
      {
        title: 'Group',
        key: 'group-settings',
        permissions: [Constants.GROUP_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute('group-settings')
        },
      },
      {
        title: 'Password Settings',
        key: 'password-settings',
        permissions: [Constants.USER_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute(
            'users-settings',
            'password-settings'
          )
        },
      },
      {
        title: 'LDAP Server Settings',
        key: 'ldap-server-settings',
        permissions: [Constants.USER_SETTINGS_READ_PERMISSION],
        licensePermissions: [Constants.LDAP_SERVER_SETTINGS_LICENSE_PERMISSION],

        route(vm) {
          return vm.$modules.getModuleRoute(
            'users-settings',
            'ldap-server-settings'
          )
        },
      },
      {
        title: 'Single Sign-On',
        key: 'single-sign-on',
        permissions: [Constants.USER_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute('users-settings', 'single-sign-on')
        },
        licensePermissions: [Constants.LDAP_SERVER_SETTINGS_LICENSE_PERMISSION],
      },
    ],
  },

  // System Settings
  {
    section: 'System Settings',
    icon: 'system-settings',
    key: 'system-settings',
    permissions: [Constants.SYSTEM_SETTINGS_READ_PERMISSION],
    children: [
      {
        title: 'Two Factor Authentication',
        key: 'two-factor-authentication',
        route(vm) {
          return vm.$modules.getModuleRoute(
            'system-settings',
            'two-factor-authentication'
          )
        },
        licensePermissions: [Constants.LDAP_SERVER_SETTINGS_LICENSE_PERMISSION],
      },
      {
        title: 'Mail Server Settings',
        key: 'mail-server-settings',
        route(vm) {
          return vm.$modules.getModuleRoute(
            'system-settings',
            'mail-server-settings'
          )
        },
      },
      {
        title: 'Proxy Server Settings',
        key: 'proxy-server-settings',
        route(vm) {
          return vm.$modules.getModuleRoute(
            'system-settings',
            'proxy-server-settings'
          )
        },
      },
      {
        title: 'SMS Server Settings',
        key: 'sms-server-settings',
        route(vm) {
          return vm.$modules.getModuleRoute(
            'system-settings',
            'sms-server-settings'
          )
        },
      },
      {
        title: 'Rebranding',
        key: 'rebranding',
        route(vm) {
          return vm.$modules.getModuleRoute('system-settings', 'rebranding')
        },
      },
      {
        title: 'Data Retention',
        key: 'data-retention',
        route(vm) {
          return vm.$modules.getModuleRoute('system-settings', 'data-retention')
        },
      },
      {
        title: 'Deployment Settings',
        key: 'motadata-collector',
        route(vm) {
          return vm.$modules.getModuleRoute('system-settings', 'remote-poller')
        },
      },
      {
        title: 'MAC Address List',
        key: 'mac-address-scanner',
        route(vm) {
          return vm.$modules.getModuleRoute(
            'system-settings',
            'mac-address-scanner'
          )
        },
      },
      {
        title: 'Storage Profile',
        key: 'external-storage-profile',
        route(vm) {
          return vm.$modules.getModuleRoute(
            'system-settings',
            'external-storage-profile'
          )
        },
      },
      {
        title: 'Backup Profile',
        key: 'backup-profile',
        route(vm) {
          return vm.$modules.getModuleRoute('system-settings', 'backup-profile')
        },
      },
      {
        title: 'Rule Based Tags',
        key: 'rule-based-tags-list',
        route(vm) {
          return vm.$modules.getModuleRoute(
            'system-settings',
            'rule-based-tags-list'
          )
        },
        licensePermissions: [Constants.LDAP_SERVER_SETTINGS_LICENSE_PERMISSION],
      },
    ],
  },

  // Policy Settings
  {
    section: 'Policy Settings',
    icon: 'policy-settings',
    key: 'policy-settings',
    permissions: [Constants.POLICY_SETTINGS_READ_PERMISSION],
    children: [
      {
        title: 'Metric Policy',
        key: 'metric',
        route(vm) {
          return vm.$modules.getModuleRoute('policy-settings')
        },
      },
      {
        title: 'Log Policy',
        key: 'log',
        route(vm) {
          return vm.$modules.getModuleRoute('policy-settings', 'log')
        },
        licensePermissions: [Constants.LOG_FLOW_LICENSE_PERMISSION],
      },
      {
        title: 'Flow Policy',
        key: 'flow',
        route(vm) {
          return vm.$modules.getModuleRoute('policy-settings', 'flow')
        },
        licensePermissions: [Constants.LOG_FLOW_LICENSE_PERMISSION],
      },
      {
        title: 'Trap Policy',
        key: 'trap',
        route(vm) {
          return vm.$modules.getModuleRoute('policy-settings', 'trap')
        },
      },
      {
        title: 'NetRoute Policy',
        key: 'netroute',
        route(vm) {
          return vm.$modules.getModuleRoute('policy-settings', 'netroute')
        },
        licensePermissions: [Constants.NETROUTE_LICENSE_PERMISSION],
        isBeta: true,
      },
    ],
  },

  // Discovery Settings
  {
    section: 'Discovery Settings',
    icon: 'network-discovery',
    key: 'network-discovery',
    permissions: [Constants.DISCOVERY_SETTINGS_READ_PERMISSION],
    children: [
      {
        title: 'Credential Profile',
        key: 'credential-profiles',
        route(vm) {
          return vm.$modules.getModuleRoute('network-discovery')
        },
      },
      {
        title: 'Discovery Profile',
        key: 'network-discovery-profiles',
        route(vm) {
          return vm.$modules.getModuleRoute(
            'network-discovery',
            'discovery-profiles'
          )
        },
      },
    ],
  },

  // Monitoring Settings
  {
    section: 'Monitor Settings',
    icon: 'inventory',
    key: 'monitoring',
    permissions: [
      Constants.MONITOR_SETTINGS_READ_PERMISSION,
      Constants.AGENT_SETTINGS_READ_PERMISSION,
      Constants.NETROUTE_SETTINGS_READ_PERMISSION,
    ],
    children: [
      {
        title: 'Device Monitor Settings',
        key: 'device-monitor-settings',
        permissions: [Constants.MONITOR_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute(
            'monitoring',
            'device-monitor-settings'
          )
        },
      },
      {
        title: 'Cloud Monitor Settings',
        key: 'cloud-monitor-settings',
        permissions: [Constants.MONITOR_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute(
            'monitoring',
            'cloud-monitor-settings'
          )
        },
      },
      {
        title: 'Agent Monitor Settings',
        key: 'agent-monitor-settings',
        permissions: [Constants.AGENT_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute(
            'monitoring',
            'agent-monitor-settings'
          )
        },
      },
      {
        title: 'Service Check Monitor Settings',
        key: 'service-check-monitor-settings',
        permissions: [Constants.MONITOR_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute(
            'monitoring',
            'service-check-monitor-settings'
          )
        },
      },
      {
        title: 'Process Monitor Settings',
        key: 'processes',
        permissions: [Constants.MONITOR_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute('monitoring', 'process-list')
        },
      },
      {
        title: 'Service Monitor Settings',
        key: 'services',
        permissions: [Constants.MONITOR_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute('monitoring', 'service-list')
        },
      },
      {
        title: 'File/Directory Settings',
        key: 'file-directory-list',
        permissions: [Constants.MONITOR_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute('monitoring', 'file-directory-list')
        },
      },
      {
        title: 'SNMP Device Catalog',
        key: 'snmp-device-catalog',
        permissions: [Constants.MONITOR_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute('monitoring', 'snmp-device-catalog')
        },
      },
      {
        title: 'Rediscover Settings',
        key: 'rediscover-setting',
        permissions: [Constants.MONITOR_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute('monitoring', 'rediscover-setting')
        },
      },
      {
        title: 'NetRoute Settings',
        key: 'netroute-setting',
        permissions: [Constants.NETROUTE_SETTINGS_READ_PERMISSION],
        licensePermissions: [Constants.NETROUTE_LICENSE_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute('monitoring', 'netroute-setting')
        },
        isBeta: true,
      },
      {
        title: 'Topology Scanner',
        key: 'topology-scanner',
        permissions: [Constants.MONITOR_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute('monitoring', 'topology-scanner')
        },
      },
      {
        title: 'Monitoring Hour',
        key: 'monitoring-hours',
        permissions: [Constants.MONITOR_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute('monitoring', 'business-hours')
        },
      },
      {
        title: 'Custom Monitoring Field',
        key: 'custom-monitoring-field',
        permissions: [Constants.MONITOR_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute('monitoring', 'monitoring-fields')
        },
      },
    ],
  },

  // Ncm Settings
  {
    section: 'Network Config Settings',
    icon: 'ncm',
    key: 'ncm-settings',
    permissions: [Constants.NCM_CREATE_PERMISSION],
    licensePermissions: [Constants.NCM_LICENSE_PERMISSION],
    children: [
      {
        title: 'Device Inventory',
        key: 'device-inventory',
        route(vm) {
          return vm.$modules.getModuleRoute('ncm-settings', 'device-inventory')
        },
      },
      {
        title: 'Device Template',
        key: 'device-template',
        route(vm) {
          return vm.$modules.getModuleRoute('ncm-settings', 'device-template')
        },
      },
    ],
  },
  // Complaice Settings
  {
    section: 'Compliance Settings',
    icon: 'metric-explorer',
    key: 'compliance-settings',
    permissions: [Constants.COMPLIANCE_READ_PERMISSION],
    licensePermissions: [Constants.COMPLIANCE_LICENSE_PERMISSION],
    isBeta: true,

    children: [
      {
        title: 'Compliance Policy',
        key: 'audit-policy',
        route(vm) {
          return vm.$modules.getModuleRoute(
            'compliance-settings',
            'audit-policy'
          )
        },
      },
      {
        title: 'Benchmark',
        key: 'benchmark',
        route(vm) {
          return vm.$modules.getModuleRoute('compliance-settings', 'benchmark')
        },
      },
      {
        title: 'Rules',
        key: 'rules',
        route(vm) {
          return vm.$modules.getModuleRoute('compliance-settings', 'rules')
        },
      },
      // {
      //   title: 'Weighted Calculation',
      //   key: 'weighted-calculation',
      //   route(vm) {
      //     return vm.$modules.getModuleRoute(
      //       'compliance-settings',
      //       'weighted-calculation'
      //     )
      //   },
      // },
    ],
  },
  // SNMP Trap
  {
    section: 'SNMP Trap',
    icon: 'trap-viewer',
    key: 'snmp-trap',
    permissions: [Constants.SNMP_TRAP_READ_PERMISSION],
    children: [
      {
        title: 'SNMP Trap Profile',
        key: 'trap-profiles',
        route(vm) {
          return vm.$modules.getModuleRoute('snmp-trap')
        },
      },
      {
        title: 'SNMP Trap Forwarder',
        key: 'snmp-trap-forwarder',
        route(vm) {
          return vm.$modules.getModuleRoute('snmp-trap', 'trap-forwarding')
        },
      },
      {
        title: 'SNMP Trap Listener',
        key: 'snmp-trap-listener',
        route(vm) {
          return vm.$modules.getModuleRoute('snmp-trap', 'trap-settings')
        },
      },
    ],
  },

  // Log
  {
    section: 'Log Settings',
    icon: 'log',
    key: 'log-settings',
    permissions: [Constants.LOG_SETTINGS_READ_PERMISSION],
    licensePermissions: [Constants.LOG_FLOW_LICENSE_PERMISSION],

    children: [
      {
        title: 'Log Inventory',
        key: 'log-inventory',
        route(vm) {
          return vm.$modules.getModuleRoute('log-settings', 'log-inventory')
        },
      },
      {
        title: 'Log Parser Library',
        key: 'log-parsers',
        route(vm) {
          return vm.$modules.getModuleRoute('log-settings', 'log-parsers')
        },
      },
      {
        title: 'Log Collection Profile',
        key: 'log-collection-profile',
        route(vm) {
          return vm.$modules.getModuleRoute(
            'log-settings',
            'log-collection-profile'
          )
        },
      },
      {
        title: 'Log Forwarder',
        key: 'log-forwarder',
        route(vm) {
          return vm.$modules.getModuleRoute('log-settings', 'log-forwarder')
        },
        licensePermissions: [Constants.LOG_FORWARDER_LICENSE_PERMISSION],
      },
      // {
      //   title: 'Log Directory Path',
      //   key: 'directory-paths',
      //   route(vm) {
      //     return vm.$modules.getModuleRoute('log-settings', 'directory-paths')
      //   },
      // },
    ],
  },

  // Flow
  {
    section: 'Flow Settings',
    icon: 'flow',
    key: 'flow-settings',
    permissions: [Constants.FLOW_SETTINGS_READ_PERMISSION],
    licensePermissions: [Constants.LOG_FLOW_LICENSE_PERMISSION],

    children: [
      {
        title: 'Flow Settings',
        key: 'flow-settings',
        route(vm) {
          return vm.$modules.getModuleRoute('flow-settings', 'flow-settings')
        },
      },
      // {
      //   title: 'IP/IP Range Settings',
      //   key: 'flow-ip-iprange',
      //   route(vm) {
      //     return vm.$modules.getModuleRoute('flow-settings', 'flow-ip-iprange')
      //   },
      // },
      {
        title: 'Sample Rate Settings',
        key: 'sampling-rate',
        route(vm) {
          return vm.$modules.getModuleRoute('flow-settings', 'sampling-rate')
        },
      },
      {
        title: 'Application Mapping',
        key: 'application-mapping',
        route(vm) {
          return vm.$modules.getModuleRoute(
            'flow-settings',
            'application-mapping'
          )
        },
      },
      {
        title: 'Protocol Mapping',
        key: 'protocol-mapping',
        route(vm) {
          return vm.$modules.getModuleRoute('flow-settings', 'protocol-mapping')
        },
      },
      {
        title: 'AS Mapping',
        key: 'as-mapping',
        route(vm) {
          return vm.$modules.getModuleRoute('flow-settings', 'as-mapping')
        },
      },
      {
        title: 'Domain Mapping',
        key: 'domain-mapping',
        route(vm) {
          return vm.$modules.getModuleRoute('flow-settings', 'domain-mapping')
        },
      },
      {
        title: 'Geolocation Mapping',
        key: 'geolocation-mapping',
        route(vm) {
          return vm.$modules.getModuleRoute(
            'flow-settings',
            'geolocation-mapping'
          )
        },
      },
      {
        title: 'IP Mapping',
        key: 'ip-mapping',
        route(vm) {
          return vm.$modules.getModuleRoute('flow-settings', 'ip-mapping')
        },
      },
    ],
  },

  // Plugin Library
  {
    section: 'Plugin Library',
    icon: 'plugin-library',
    key: 'plugin-library',
    permissions: [Constants.PLUGIN_LIBRARY_SETTINGS_READ_PERMISSION],
    licensePermissions: [Constants.PLUGIN_LIBRARY_PERMISSION],

    children: [
      {
        title: 'Runbook',
        key: 'runbooks',
        route(vm) {
          return vm.$modules.getModuleRoute('plugin-library', 'runbooks')
        },
        licensePermissions: [
          Constants.PLUGIN_LIBRARY_RUNBOOK_LICENSE_PERMISSION,
        ],
      },
      {
        title: 'Metric',
        key: 'metrics',
        route(vm) {
          return vm.$modules.getModuleRoute('plugin-library', 'metrics-list')
        },
        licensePermissions: [
          Constants.PLUGIN_LIBRARY_METRIC_LICENSE_PERMISSION,
        ],
      },
      {
        title: 'Topology',
        key: 'topology-plugins',
        route(vm) {
          return vm.$modules.getModuleRoute(
            'plugin-library',
            'topology-plugins-list'
          )
        },
        licensePermissions: [
          Constants.PLUGIN_LIBRARY_TOPOLOGY_LICENSE_PERMISSION,
        ],
      },
      {
        title: 'Log Parser Plugin',
        key: 'log-parsers',
        permissions: [Constants.LOG_SETTINGS_READ_PERMISSION],
        route(vm) {
          return vm.$modules.getModuleRoute('plugin-library', 'log-parser-list')
        },
        licensePermissions: [
          Constants.PLUGIN_LIBRARY_LOG_PARSER_PLUGIN_PERMISSION,
        ],
      },
    ],
  },

  // AIOPS
  {
    section: 'Dependency Mapper',
    icon: 'brain',
    key: 'ai',
    permissions: [Constants.AIOPS_SETTINGS_READ_PERMISSION],
    licensePermissions: [Constants.DEPENDENCY_MAPPING_LICENSE_PERMISSION],

    children: [
      {
        title: 'Parent Child Dependency Mapper',
        key: 'dependency-mapper',
        route(vm) {
          return vm.$modules.getModuleRoute('ai', 'dependency-mapper')
        },
      },
    ],
  },
  // Service Level Objective
  {
    section: 'Service Level Objective',
    icon: 'slo',
    key: 'service-level-objective',
    permissions: [Constants.SLO_READ_PERMISSION],
    children: [
      {
        title: 'SLO Profile',
        key: 'slo-profile',
        route(vm) {
          return vm.$modules.getModuleRoute(
            'service-level-objective',
            'slo-profile'
          )
        },
      },
      // {
      //   title: 'Correction Profile',
      //   key: 'correction-profile',
      //   route(vm) {
      //     return vm.$modules.getModuleRoute(
      //       'service-level-objective',
      //       'correction-profile'
      //     )
      //   },
      // },
    ],
  },
  {
    section: 'Integration',
    icon: 'integration',
    key: 'integration',
    permissions: [Constants.INTEGRATION_READ_PERMISSION],
    children: [
      {
        title: 'Integration Profile',
        key: 'integration-profile',
        route(vm) {
          return vm.$modules.getModuleRoute(
            'integration',
            'integration-profile'
          )
        },
      },
      {
        title: 'Motadata ServiceOps',
        key: 'motadata-serviceops',
        route(vm) {
          return vm.$modules.getModuleRoute(
            'integration',
            'motadata-serviceops'
          )
        },
      },
      {
        title: 'ServiceNow',
        key: 'service-now',
        route(vm) {
          return vm.$modules.getModuleRoute('integration', 'service-now')
        },
        licensePermissions: [Constants.LDAP_SERVER_SETTINGS_LICENSE_PERMISSION],
      },
      {
        title: 'Atlassian Jira',
        key: 'atlassian-jira',
        route(vm) {
          return vm.$modules.getModuleRoute('integration', 'atlassian-jira')
        },
        licensePermissions: [Constants.LDAP_SERVER_SETTINGS_LICENSE_PERMISSION],
      },
      {
        title: 'Microsoft Teams',
        key: 'microsoft-teams',
        route(vm) {
          return vm.$modules.getModuleRoute('integration', 'microsoft-teams')
        },
      },
    ],
  },
]
