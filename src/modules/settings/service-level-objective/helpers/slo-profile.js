// Transformation and helper functions for SLO Profile\
import Moment from 'moment'
import Constants from '@constants'

import { OPERATOR_MAP } from '@/src/components/widgets/constants'
import { isUnitConvertible } from '@/src/utils/unit-checker'
import { AVAILABLE_SLO_TYPES } from '@modules/slo/helpers/slo'
import applyUnit from '@/src/utils/unit-applier'

import {
  transformConditionsForServer,
  transformConditions,
} from '@components/widgets/helper'

// Frequency options for SLO
export const SLO_FREQUENCY_OPTIONS = [
  { key: 'Daily', text: 'Daily' },
  { key: 'Weekly', text: 'Weekly' },
  { key: 'Monthly', text: 'Monthly' },
  { key: 'Quarterly', text: 'Quarterly' },
]

// Calculate tick interval based on frequency to show 24 ticks
export function calculateTickInterval(frequency) {
  const frequencyMap = {
    Daily: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
    Weekly: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
    Monthly: 30 * 24 * 60 * 60 * 1000, // 30 days in milliseconds (approximate)
    Quarterly: 90 * 24 * 60 * 60 * 1000, // 90 days in milliseconds (approximate)
  }

  const totalDuration = frequencyMap[frequency] || frequencyMap['Daily']
  // Calculate interval to get exactly 24 ticks (23 intervals between 24 points)
  return Math.floor(totalDuration / 23)
}

export function transformSLOProfileForList(profile) {
  const context = profile['slo.profile.context'] || {}
  const notificationContext = profile['slo.profile.notification.context'] || {}

  return {
    id: profile.id || profile['slo.profile.id'],
    name: profile['slo.profile.name'],
    description: profile['slo.profile.description'],
    sloType: profile['slo.profile.type'],
    businessServiceName: profile['slo.profile.business.service.name'],
    sloTarget: context['slo.target'],
    sloWarning: context['slo.warning'],
    frequency: context['slo.frequency'],
    state: profile['slo.profile.state'] === 'yes',
    sloFor: context['slo.instance'],
    target: {
      entityType: context['entity.type'],
      entities: context['entities'],
    },
    counter: context['metric'],

    ...(profile['slo.profile.type'] === AVAILABLE_SLO_TYPES.PERFORMANCE
      ? {
          operator:
            context['slo.severity'][Constants.CRITICAL]['policy.condition'],
          value:
            context['slo.severity'][Constants.CRITICAL]['policy.threshold'],
        }
      : {}),

    startDate: profile['slo.profile.start.time'] * 1000,
    startDateFormatted: Moment(profile['slo.profile.start.time'] * 1000).format(
      'DD-MM-YYYY'
    ),
    tags: profile['slo.profile.tags'],
    notificationEmail: [
      ...(notificationContext['slo.profile.email.notification.recipients'] ||
        []),
      ...(
        notificationContext['slo.profile.user.notification.recipients'] || []
      ).map((name) => `@${name}`),
    ],

    ...(context.filters
      ? {
          filters: {
            pre: transformConditions(context.filters['data.filter'] || {}),
            post: transformConditions(
              context.filters['result.filter'] || {},
              true
            ),
          },
        }
      : {}),
    plugins: context.plugins,
    triggerCondition: buildTriggerCondition(profile),
    cycleId: profile['slo.cycle.id'],
    sloProfileId: profile.id,
  }
}

export function transformSLOProfileForServer(profile, handlerMaps) {
  return {
    'slo.profile.name': profile.name,
    'slo.profile.description': profile.description || '',
    'slo.profile.type': profile.sloType,
    'slo.profile.business.service.name': profile.businessServiceName,
    // 'slo.profile.target': profile.sloTarget,
    // 'slo.profile.warning': profile.sloWarning,
    // 'slo.profile.frequency': profile.frequency,

    'slo.profile.context': {
      'entity.type': profile.target?.entityType,
      entities: profile.target?.entities,
      metric: profile.counter,
      'slo.severity': {
        [Constants.CRITICAL]: {
          'policy.condition': profile.operator,
          'policy.threshold': profile.value,
        },
      },
      // 'slo.instance.type': 'system.process',
      'slo.instance': profile.sloFor,
      'slo.target': parseInt(profile.sloTarget),
      'slo.warning': parseInt(profile.sloWarning),
      ...(profile.filters
        ? {
            filters: {
              'data.filter': transformConditionsForServer(profile.filters?.pre),
              'result.filter': transformConditionsForServer(
                profile.filters?.post,
                true
              ),
            },
          }
        : {}),
      'slo.frequency': profile.frequency,
      plugins: profile.plugins,
    },

    'slo.profile.state': profile.state ? 'yes' : 'no',
    'slo.profile.start.time': Moment(profile.startDate).startOf('day').unix(),
    'slo.profile.tags': profile.tags,
    ...(profile.sloType === AVAILABLE_SLO_TYPES.PERFORMANCE
      ? {
          'slo.profile.counter': profile?.counter?.key,
          'slo.profile.operator': profile.operator,
          'slo.profile.value': profile.value,
        }
      : {}),

    'slo.profile.notification.context': {
      'slo.profile.email.notification.recipients':
        profile.notificationEmail?.filter(
          (name) => !name.startsWith('@') && !name.startsWith('/')
        ) || [],
      'slo.profile.user.notification.recipients':
        profile.notificationEmail
          ?.filter((name) => name.startsWith('@'))
          .map((name) => name.replace('@', '')) || [],

      // TODO: Add handler notification recipients id
      'slo.profile.handler.notification.recipients':
        profile.notificationEmail?.filter((name) => name.startsWith('/')),
    },
  }
}

function buildTriggerCondition(profile) {
  const context = profile['slo.profile.context'] || {}

  const metric = context['metric']
  const severity = context['slo.severity'] || {}

  const criticalSeverity = severity[Constants.CRITICAL] || {}
  const operator = OPERATOR_MAP[criticalSeverity['policy.condition']] || ''
  const value = criticalSeverity['policy.threshold'] || ''

  let unitConvertedValue = value

  if (isUnitConvertible(metric, value)) {
    unitConvertedValue = applyUnit(metric, value)
  }

  return `metric value ${operator} ${unitConvertedValue}`
}
