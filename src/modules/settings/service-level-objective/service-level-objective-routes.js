import lazyLoadView from '@router/lazy-loader'
import ContainerView from './views/main'
import configs from './config'

const routePrefix = configs.routePrefix
const moduleName = configs.name
const routeNamePrefix = configs.routeNamePrefix

export default [
  {
    path: `/${routePrefix}`,
    component: ContainerView,
    meta: { moduleName },
    children: [
      {
        path: '',
        name: routeNamePrefix,
        component: () =>
          lazyLoadView(
            import(
              /* webpackChunkName: "service-level-objective" */ './views/slo-profile-list'
            )
          ),
        meta: {},
      },
      {
        path: 'slo-profile',
        name: `${routeNamePrefix}.slo-profile`,
        component: () =>
          lazyLoadView(
            import(
              /* webpackChunkName: "service-level-objective" */ './views/slo-profile-list'
            )
          ),
        meta: {},
      },
      // {
      //   path: 'correction-profile',
      //   name: `${routeNamePrefix}.correction-profile`,
      //   component: () =>
      //     lazyLoadView(
      //       import(
      //         /* webpackChunkName: "service-level-objective" */ './views/correction-profile-list'
      //       )
      //     ),
      //   meta: {},
      // },
    ],
  },
]
