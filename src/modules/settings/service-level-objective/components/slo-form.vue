<template>
  <CounterProvider :search-params="counterSearchParamsForPerformance">
    <InstanceFiltersCounterProvider
      :instance="value.sloFor"
      @loaded="handleCounterReceivedForSlOFor"
    >
      <template v-slot="{ counters, filterCounters }">
        <MonitorProvider :search-params="searchParams">
          <UserProvider>
            <GroupProvider>
              <HandlerProvider ref="handlerProviderRef">
                <div class="mt-4">
                  <MRow>
                    <MCol :size="6">
                      <FlotoFormItem
                        id="slo-name-id"
                        v-model="value.name"
                        placeholder="Must be unique"
                        rules="required"
                        label="SLO Name"
                        auto-focus
                        name="slo-name"
                        :disabled="isView"
                      />
                    </MCol>
                    <MCol :size="6">
                      <FlotoFormItem
                        id="slo-description-id"
                        v-model="value.description"
                        label="SLO Description"
                        name="slo-description"
                        :disabled="isView"
                      />
                    </MCol>
                    <MCol :size="12">
                      <FlotoFormItem label="SLO Type" rules="required">
                        <MRadioGroup
                          v-model="value.sloType"
                          as-button
                          :options="sloTypeOptions"
                          :disabled="isView"
                        />
                      </FlotoFormItem>
                    </MCol>
                    <MCol :size="6">
                      <FlotoFormItem
                        id="business-service-name-id"
                        v-model="value.businessServiceName"
                        rules="required"
                        label="Business Service Name"
                        name="business-service-name"
                        :disabled="isView"
                      />
                    </MCol>
                    <MCol :size="3">
                      <FlotoFormItem
                        id="slo-target-id"
                        v-model="value.sloTarget"
                        :rules="sloTargetRules"
                        label="Target"
                        name="slo-target"
                        addon-after="%"
                        :disabled="isView"
                        class="full-bordered-addon-input"
                        placeholder="Set target"
                      />
                    </MCol>
                    <MCol :size="3">
                      <FlotoFormItem
                        id="slo-warning-id"
                        v-model="value.sloWarning"
                        :rules="sloWarningRules"
                        label="Warning"
                        name="slo-warning"
                        addon-after="%"
                        :disabled="isView"
                        class="full-bordered-addon-input"
                        info-tooltip="Warning must be greater than target"
                        placeholder="Set warning"
                      />
                    </MCol>
                    <MCol :size="6">
                      <FlotoFormItem label="Frequency" rules="required">
                        <FlotoDropdownPicker
                          v-model="value.frequency"
                          class="w-full"
                          :options="frequencyOptions"
                          :disabled="isView"
                        />
                      </FlotoFormItem>
                    </MCol>
                    <MCol :size="6">
                      <FlotoFormItem label="SLO For" rules="required">
                        <FlotoDropdownPicker
                          :value="value.sloFor"
                          class="w-full"
                          :options="sloForOptions"
                          :disabled="isView"
                          @change="handleSloForChange"
                        />
                      </FlotoFormItem>
                    </MCol>

                    <template v-if="value.sloType === 'Performance'">
                      <MCol :size="6">
                        <FlotoFormItem
                          rules="required"
                          label="Counter"
                          :class="{ 'disabled-bordered-dropdown': isView }"
                        >
                          <FlotoDropdownPicker
                            v-model="value.counter"
                            class="w-full"
                            :options="counters"
                            :disabled="isView"
                          />
                        </FlotoFormItem>
                      </MCol>

                      <MCol :size="3">
                        <FlotoFormItem label="Operator" rules="required">
                          <FlotoDropdownPicker
                            v-model="value.operator"
                            class="w-full"
                            :options="thresholdConditionOptions"
                            :disabled="isView"
                          />
                        </FlotoFormItem>
                      </MCol>
                      <MCol :size="3">
                        <FlotoFormItem
                          v-model="value.value"
                          label="Value"
                          :disabled="isView"
                          rules="required"
                        >
                        </FlotoFormItem>
                      </MCol>
                    </template>

                    <MCol :size="12" class="relative">
                      <MonitorGroupSelection
                        v-model="value.target"
                        :labels-props="deviceLabels"
                        rules="required"
                        :source-required="true"
                        :entity-options="entityOptions"
                        :disabled="isView"
                      />
                    </MCol>

                    <MCol v-if="value.sloFor" :size="12" class="my-4 mb-6">
                      <FiltersContainer
                        v-model="value.filters"
                        :max-pre-groups="3"
                        :all-counters="filterCounters"
                        placeholder="Filters"
                        group-type="metric"
                        ignore-fetch-available-unique-values
                        :hard-coded-operator-options="hardCodedOperatorOptions"
                        :get-popup-container="getPopupContainer"
                      />
                    </MCol>

                    <MCol :size="6">
                      <FlotoFormItem label="Start Date" rules="required">
                        <MDatePicker
                          id="select-schedule-start-date"
                          v-model="value.startDate"
                          :show-time="false"
                          :min-date="todaysDate"
                          :allow-clear="false"
                          :disabled="isView"
                        />
                      </FlotoFormItem>
                    </MCol>
                    <MCol :size="12">
                      <FlotoFormItem label="Tags">
                        <LooseTags
                          id="tags"
                          v-model="value.tags"
                          always-text-mode
                          title="Tag"
                          variant="default"
                          rounded
                          class="w-full"
                          :disabled="isView"
                          :tag-type="AvailableTagType.SLO"
                        />
                      </FlotoFormItem>
                    </MCol>
                    <MCol :size="12">
                      <FlotoFormItem label="Notify Team">
                        <UserOrEmailPicker
                          v-model="value.notificationEmail"
                          :full-width="true"
                          class="w-full"
                          name="parameter-name"
                          title="@User or Email or /Handle"
                          always-text-mode
                          disable-justify-around
                          type="email"
                          :disabled="isView"
                        />
                      </FlotoFormItem>
                    </MCol>
                    <MCol :size="12">
                      <span class="text-neutral"> For more information: </span>
                      <!--  TODO: warning update link to SLO Profile -->
                      <a
                        href="https://docs.motadata.com/motadata-aiops-docs/service-level-objective/slo-profile "
                        target="_blank"
                        >Create SLO Profile</a
                      >
                      <MIcon name="external-link" class="ml-1 text-primary" />
                    </MCol>
                  </MRow>
                </div>
              </HandlerProvider>
            </GroupProvider>
          </UserProvider>
        </MonitorProvider>
      </template>
    </InstanceFiltersCounterProvider>
  </CounterProvider>
</template>

<script>
import Moment from 'moment'
import CloneDeep from 'lodash/cloneDeep'

import {
  FILTER_CONDITION_DEFAULT_DATA,
  WidgetTypeConstants,
  AVAILABLE_GROUP_TYPES,
  DATA_TYPE_OPERATORS,
} from '@components/widgets/constants'

import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import CounterProvider from '@components/data-provider/counter-provider.vue'
import InstanceFiltersCounterProvider from '@/src/components/data-provider/instance-filters-counter-provider.vue'
// import CounterPicker from '@components/data-picker/counter-picker.vue'

import MonitorGroupSelection from '@components/widgets/monitor-or-group-selection.vue'
import GroupProvider from '@components/data-provider/group-provider.vue'
import UserProvider from '@components/data-provider/user-provider.vue'
import HandlerProvider from '@/src/components/data-provider/handler-provider.vue'
import LooseTags from '@components/loose-tags.vue'
import UserOrEmailPicker from '@components/data-picker/user-or-email-picker.vue'
import FiltersContainer from '@components/filters/filters-container.vue'

import { thresholdConditionOptions } from '@modules/settings/policy-settings/helpers/dropdown-options'

import { AvailableTagType } from '@modules/settings/monitoring/helpers/tag-helper'

import { AVAILABLE_SLO_TYPES } from '@modules/slo/helpers/slo'
import { SLO_FREQUENCY_OPTIONS } from '@modules/settings/service-level-objective/helpers/slo-profile'

export default {
  name: 'SloForm',
  components: {
    MonitorProvider,
    CounterProvider,
    MonitorGroupSelection,
    GroupProvider,
    LooseTags,
    UserOrEmailPicker,
    UserProvider,
    HandlerProvider,
    // CounterPicker,
    InstanceFiltersCounterProvider,
    FiltersContainer,
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    isView: {
      type: Boolean,
      default: false,
    },
    resetForm: {
      type: Function,
      required: true,
    },
  },
  data() {
    this.todaysDate = Moment().add(1, 'day')

    this.WidgetTypeConstants = WidgetTypeConstants
    this.AVAILABLE_GROUP_TYPES = AVAILABLE_GROUP_TYPES
    this.AvailableTagType = AvailableTagType

    this.counterSearchParamsForSlOFor = {
      'visualization.group.type': AVAILABLE_GROUP_TYPES.AVAILABILITY,
    }
    this.counterSearchParamsForPerformance = {
      'visualization.group.type': AVAILABLE_GROUP_TYPES.METRIC,
    }
    this.deviceLabels = {
      entityTypeLabel: 'Source Filter',
      entitiesLabel: 'Source',
    }

    this.SLO_FOR_OPTIONS = [
      { key: 'monitor', text: 'Monitor' },
      { key: 'interface', text: 'Interface' },
      { key: 'vm', text: 'VM' },
      { key: 'access point', text: 'AP' },
      { key: 'link', text: 'Link' },
      { key: 'container', text: 'Container' },
      { key: 'process', text: 'Process' },
      { key: 'service', text: 'Service' },
    ]

    this.searchParams = {
      category: [
        this.$constants.SERVER,
        this.$constants.NETWORK,
        this.$constants.OTHER,
        this.$constants.CLOUD,
        this.$constants.VIRTUALIZATION,
      ],
    }
    this.sloTypeOptions = [
      { label: 'Availability', value: AVAILABLE_SLO_TYPES.AVAILABILITY },
      { label: 'Performance', value: AVAILABLE_SLO_TYPES.PERFORMANCE },
      // { label: 'Security', value: 'security' },
      // { label: 'Compliance', value: 'compliance' },
      // { label: 'Other', value: 'other' },
    ]
    this.frequencyOptions = SLO_FREQUENCY_OPTIONS

    this.thresholdConditionOptions = thresholdConditionOptions
    return {
      sloForOptions: this.SLO_FOR_OPTIONS,
    }
  },
  computed: {
    entityOptions() {
      const sloFor = this.value.sloFor
      const entityOptions = [
        { key: 'monitor', text: 'Monitor', inputType: 'monitor' },
        { key: 'group', text: 'Group', inputType: 'group' },
        { key: 'tag', text: 'Tag', inputType: 'tags' },
      ]
      if (sloFor === 'monitor') {
        return entityOptions
      }
      return entityOptions.filter((option) => option.key !== 'tag')
    },
    sloWarningRules() {
      return {
        required: true,
        numeric: true,
        ...(this.value.sloTarget || this.value.sloTarget === 0
          ? {
              min_value:
                typeof +this.value.sloTarget === 'number'
                  ? +this.value.sloTarget + 1
                  : undefined,
              max_value: 100,
            }
          : {}),
      }
    },
    sloTargetRules() {
      return {
        required: true,
        numeric: true,
        min_value: 0,
        max_value: 100,
      }
    },
    hardCodedOperatorOptions() {
      return DATA_TYPE_OPERATORS.string
    },
  },
  methods: {
    getPopupContainer() {
      if (!this.$el) {
        return undefined
      }
      return document.body
    },
    // handleCounterReceivedForSlOFor(counters) {
    //   this.sloForOptions = Array.from(counters.values()).map((counter) => ({
    //     key: counter.key,
    //     text: counter.name,
    //   }))
    // },
    handleSloForChange(sloFor) {
      this.resetForm({
        ...this.value,
        sloFor,
        filters: {
          pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
          post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
        },
      })
    },
    handleCounterChange(counter) {
      this.resetForm({ ...this.value, counter })
    },
    getHandlerMaps() {
      if (this.$refs.handlerProviderRef) {
        return this.$refs.handlerProviderRef.gethandlerMaps()
      }
      return {}
    },
    filterFnForSloFor(counterMap) {
      const computedMap = new Map()
      Array.from(counterMap.keys()).map((c) => {
        const counterKey =
          c.indexOf('~') >= 0 ? `${c.split('~')[0]}~status` : 'status'
        const counter = { ...counterMap.get(c) } // Clone the counter object

        // Modify the cloned object
        const modifiedCounter = {
          ...counter,
          key: counterKey.replace('~status', ''),
          counterName: counterKey.replace('~status', ''),
          name: counterKey.replace('~status', ''),
        }

        computedMap.set(counterKey, modifiedCounter)
      })
      return Object.freeze(
        Array.from(computedMap.values()).map((c) => ({
          ...c,
          key: c.key === 'status' ? 'monitor' : c.key.replace('.status', ''),
          text:
            c.key === 'status'
              ? 'monitor'
              : c.key.replace(/~/g, '.').replace('.status', ''),
        }))
      )
    },
    filterFnForCounter(counterMap) {
      const counterMapClone = Object.freeze(counterMap)

      if (this.value.sloFor) {
        const computedMap = new Map()
        Array.from(counterMapClone.keys()).map((c) => {
          const counter = counterMapClone.get(c)
          if (this.value?.sloFor?.key === 'monitor') {
            if (!counter.instanceType) {
              computedMap.set(c, counter)
            }
          } else {
            if (counter.instanceType === this.value?.sloFor?.key) {
              computedMap.set(c, counter)
            }
          }
        })
        return Object.freeze(Array.from(computedMap.values()))
      } else {
        return []
      }
    },
    handleCounterReceivedForSlOFor(res) {
      const plugins = res.plugins || []

      this.resetForm({
        ...this.value,
        plugins,
      })
    },
  },
}
</script>
