<template>
  <MonitoringHourProvider>
    <div class="mt-4">
      <MRow>
        <MCol :size="6">
          <FlotoFormItem
            id="correction-profile-name-id"
            v-model="value.name"
            placeholder="Must be unique"
            rules="required"
            label="Correction Profile Name"
            auto-focus
            name="correction-profile-name"
            :disabled="isView"
          />
        </MCol>

        <MCol :size="6">
          <FlotoFormItem label="Category" rules="required">
            <FlotoDropdownPicker
              v-model="value.category"
              class="w-full"
              :options="categoryOptions"
              :disabled="isView"
            />
          </FlotoFormItem>
        </MCol>

        <MCol :size="12">
          <FlotoFormItem label="Recurring Type" rules="required">
            <MRadioGroup
              v-model="value.recurringType"
              as-button
              :options="recurringTypeOptions"
              :disabled="isView"
            />
          </FlotoFormItem>
        </MCol>

        <MCol v-if="value.recurringType === 'once'" :size="12">
          <MultipleFormItems
            v-model="value.onceInput"
            :show-icon="false"
            :max-items="4"
            :item-template="defaultOnceInputTemplate"
            add-btn-text="Add Date and Time"
            class="w-full"
          >
            <template
              v-slot="{
                item,
                remove,
                add,
                index,
                total,
                canAdd,
                isLastItem,
                update,
              }"
            >
              <MRow :gutter="16">
                <MCol :size="6">
                  <DateTimePopover
                    :value="item.dateAndTime"
                    rule-required
                    :input-attributes="{
                      label: index === 0 ? 'Date and Time' : '',
                      placeholder: 'Select Date & Time',
                      class: 'w-full',
                    }"
                    class="w-full"
                    @change="update({ ...(item || {}), dateAndTime: $event })"
                  />
                </MCol>

                <MCol auto-size class="flex items-center">
                  <a
                    v-if="total > 1"
                    :class="{ 'mb-4': index > 0 }"
                    @click="remove"
                  >
                    <MIcon
                      name="times-circle"
                      class="text-secondary-red"
                      size="lg"
                    />
                  </a>
                  <a
                    v-if="isLastItem && canAdd"
                    class="ml-2"
                    :class="{ 'mb-4': index > 0 }"
                    @click="add"
                  >
                    <MIcon name="plus-circle" class="text-primary" size="lg" />
                  </a>
                </MCol>
              </MRow>
            </template>
          </MultipleFormItems>
        </MCol>
        <MCol v-else-if="value.recurringType === 'monitoring hours'" :size="6">
          <FlotoFormItem label="Monitoring Hour" rules="required">
            <MonitoringHourPicker
              id="monitoring-hour-picker"
              v-model="value.monitorHourProfile"
            />
          </FlotoFormItem>
        </MCol>

        <template v-else>
          <MCol :size="6">
            <FlotoFormItem label="Start Date" rules="required">
              <MDatePicker
                id="select-schedule-start-date"
                v-model="value.startDate"
                :show-time="false"
                :allow-clear="false"
              />
            </FlotoFormItem>
          </MCol>
          <MCol :size="6">
            <FlotoFormItem label="Hours" rules="required">
              <Timepicker
                id="select-schedule-time"
                v-model="value.times"
                :multiple="true"
              />
            </FlotoFormItem>
          </MCol>

          <MCol :size="12">
            <MRow :gutter="16">
              <MCol :size="3">
                <MRow :gutter="0">
                  <MCol :size="12">
                    <FlotoFormItem
                      id="duration-id"
                      v-model="value.duration"
                      rules="required|numeric"
                      label="Duration"
                      name="duration"
                      :disabled="isView"
                      class="input-addon-after-dropdown"
                    >
                      <FlotoFormItem
                        slot="addonAfter"
                        label=" "
                        rules="required"
                        class="no-label-form-item"
                      >
                        <FlotoDropdownPicker
                          v-model="value.durationUnit"
                          :options="durationUnitOptions"
                          :disabled="isView"
                        />
                      </FlotoFormItem>
                    </FlotoFormItem>
                  </MCol>
                </MRow>
              </MCol>
              <MCol :size="3">
                <MRow :gutter="0">
                  <MCol :size="12">
                    <FlotoFormItem
                      id="repeat-every-id"
                      v-model="value.repeatEvery"
                      rules="required|numeric"
                      label="Repeat Every"
                      name="Repeat Every"
                      :disabled="isView"
                      class="input-addon-after-dropdown"
                    >
                      <FlotoFormItem
                        slot="addonAfter"
                        label=" "
                        rules="required"
                        class="no-label-form-item"
                      >
                        <FlotoDropdownPicker
                          v-model="value.repeatEveryUnit"
                          :options="repeatEndsOptions"
                          :disabled="isView"
                        />
                      </FlotoFormItem>
                    </FlotoFormItem>
                  </MCol>
                </MRow>
              </MCol>
            </MRow>
          </MCol>

          <MCol v-if="value.repeatEvery === 'week'" :size="6">
            <FlotoFormItem label="Days" rules="required">
              <FlotoDropdownPicker
                v-model="value.weekDay"
                class="w-full"
                :options="weekDaysOptions"
                :disabled="isView"
              />
            </FlotoFormItem>
          </MCol>

          <template v-if="value.repeatEvery === 'month'">
            <MCol :size="6">
              <FlotoFormItem label="Month" rules="required">
                <FlotoDropdownPicker
                  v-model="value.month"
                  class="w-full"
                  :options="monthOptions"
                  :disabled="isView"
                />
              </FlotoFormItem>
            </MCol>
            <MCol :size="6">
              <FlotoFormItem label="Dates" rules="required">
                <FlotoDropdownPicker
                  v-model="value.dates"
                  class="w-full"
                  :options="monthDaysOptions"
                  :disabled="isView"
                />
              </FlotoFormItem>
            </MCol>
          </template>

          <MCol :size="6">
            <FlotoFormItem label="Repeat Ends" rules="required">
              <FlotoDropdownPicker
                v-model="value.repeatEnds"
                class="w-full"
                :options="repeatEndsOptions"
                :disabled="isView"
              />
            </FlotoFormItem>
          </MCol>
        </template>

        <MCol :size="12">
          <FlotoFormItem
            id="correction-profile-name-id"
            v-model="value.remarks"
            placeholder="Enter Remarks"
            label="Remarks"
            name="remarks"
            :disabled="isView"
          />
        </MCol>
      </MRow>
    </div>
  </MonitoringHourProvider>
</template>

<script>
import Moment from 'moment'

// import TimeRangePicker from '@components/widgets/time-range-picker.vue'
import Timepicker from '@components/time-picker.vue'

import DateTimePopover from '@components/common/date-time-popover.vue'
import MultipleFormItems from '@components/multiple-form-items.vue'

import MonitoringHourProvider from '@components/data-provider/monitoring-hour-provider.vue'
import {
  WEEK_DAYS_OPTIONS,
  MONTH_OPTIONS,
  MONTH_DAYS,
} from '@components/schedule-input/helper'

export default {
  name: 'CorrectionProfileForm',
  components: {
    MultipleFormItems,
    // TimeRangePicker,
    DateTimePopover,
    MonitoringHourProvider,
    Timepicker,
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    isView: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.todaysDate = Moment()

    this.defaultOnceInputTemplate = { dateAndTime: undefined }

    this.repeatEndsOptions = [
      { text: 'Never', key: 'never' },
      { text: 'On date', key: 'on date' },
      { text: 'After no. of times', key: 'after no. of times' },
    ]
    this.categoryOptions = [
      { text: 'Public Holiday', key: 'public holidays' },
      { text: 'Business Maintenance', key: 'scheduled maintenance' },
      { text: 'Scheduled Deployment', key: 'scheduled deployment' },
      { text: 'Out of Business Hours', key: 'out of business hours' },
      { text: 'Other', key: 'other' },
    ]

    this.recurringTypeOptions = [
      { label: 'Once', value: 'once' },
      { label: 'Monitoring Hours', value: 'monitoring hours' },
      { label: 'repeating', value: 'repeating' },
    ]
    this.durationUnitOptions = [
      { text: 'Seconds', key: 'seconds' },
      { text: 'Minutes', key: 'minutes' },
      { text: 'Hours', key: 'hours' },
      { text: 'Days', key: 'days' },
    ]
    this.repeatEveryUnitOptions = [
      { text: 'Day', key: 'day' },
      { text: 'Week', key: 'week' },
      { text: 'Month', key: 'month' },
    ]

    this.weekDaysOptions = WEEK_DAYS_OPTIONS
    this.monthOptions = MONTH_OPTIONS
    this.monthDaysOptions = MONTH_DAYS
    return {}
  },
}
</script>
