import api from '@api'
import {
  transformSLOProfileForList,
  transformSLOProfileForServer,
} from './helpers/slo-profile'

const SLO_PROFILE_API_URL = '/settings/slo-profiles'
const SLO_PROFILE_CYCLES_API_URL = '/settings/slo-cycles'

export function fetchSLOProfilesApi() {
  return api.get(SLO_PROFILE_API_URL).then((data) => {
    return (data.result || []).map(transformSLOProfileForList)
  })
}

export function getSLOProfileApi(id) {
  return api
    .get(SLO_PROFILE_API_URL + '/' + id)
    .then((data) => transformSLOProfileForList(data.result))
}

export function createSLOProfile<PERSON>pi(payload, handlerMaps) {
  return api
    .post(
      SLO_PROFILE_API_URL,
      transformSLOProfileForServer(payload, handlerMaps)
    )
    .then((response) => {
      return getSLOProfileApi(response.id).then((data) => ({ ...data }))
    })
}

export function updateSLOProfileApi(payload, handlerMaps) {
  return api
    .put(
      SLO_PROFILE_API_URL + '/' + payload.id,
      transformSLOProfileForServer(payload, handlerMaps)
    )
    .then((response) => {
      return getSLOProfileApi(payload.id).then((data) => ({ ...data }))
    })
}

export function deleteSLOProfileApi(payload) {
  return api.delete(SLO_PROFILE_API_URL + '/' + payload.id)
}

export function getSLOProfileCyclesApi(id) {
  return api.get(SLO_PROFILE_CYCLES_API_URL + '/' + id).then((data) => {
    const result = data.result

    return transformSLOProfileCycle(result)
  })
}

export function getSLOProfileCyclesApiByFilter(sloId) {
  return api
    .get(SLO_PROFILE_CYCLES_API_URL, {
      params: {
        filter: { key: 'slo.profile.id', value: [sloId] },
      },
    })
    .then((data) => {
      return (data.result || []).map(transformSLOProfileCycle)
    })
}

function transformSLOProfileCycle(result) {
  return {
    entities: result.entities,
    cycleId: result.id,
    sloCycleActive: result['slo.cycle.active'],
    sloCycleEndTime: result['slo.cycle.end.time'],
    sloCycleStartTime: result['slo.cycle.start.time'],
    sloProfileId: result['slo.profile.id'],
    sloAcceptableViolationTime: result['slo.cycle.acceptable.violation.time'],
    sloCycleName: result['slo.cycle.name'],
  }
}
