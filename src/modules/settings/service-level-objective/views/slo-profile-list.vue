<template>
  <div class="flex flex-col flex-1 min-h-0">
    <FlotoPaginatedCrud
      ref="paginatedCrudRef"
      :default-item="defaultSLOItem"
      default-sort="-name"
      resource-name="SLO Profile List"
      :columns="columns"
      :fetch-fn="getAllSLOProfiles"
      :create-fn="createSLOProfile"
      :update-fn="updateSLOProfile"
      :delete-fn="deleteSLOProfile"
      as-table
    >
      <template v-slot:name="{ item, activateItem }">
        <a class="text-ellipsis" @click="activateItem(item)">
          {{ item.name }}
        </a>
      </template>

      <template
        v-slot:add-controls="{ create, filter, resetFilter, searchTerm }"
      >
        <MRow>
          <MCol>
            <MInput
              :value="searchTerm"
              class="search-box"
              placeholder="Search"
              name="search-slo-profile"
              @update="filter"
            >
              <template v-slot:prefix>
                <MIcon name="search" />
              </template>
              <template v-if="searchTerm" v-slot:suffix>
                <MIcon
                  name="times-circle"
                  class="text-neutral-light cursor-pointer"
                  @click="resetFilter"
                />
              </template>
            </MInput>
          </MCol>
          <MCol class="text-right">
            <MButton
              id="create-slo-profile-btn-id"
              class="text-right ml-4"
              @click="create"
            >
              Create SLO Profile
            </MButton>
          </MCol>
        </MRow>
      </template>
      <template v-slot:state="{ item, update }">
        <MSwitch
          :checked="item.state"
          class="mr-2"
          checked-children="ON"
          un-checked-children="OFF"
          @change="update({ ...item, state: $event })"
        />
      </template>
      <template v-slot:actions="{ edit, item }">
        <FlotoGridActions
          :actions="gridActions"
          :edit-permission-name="$constants.SLO_UPDATE_PERMISSION"
          :delete-permission-name="$constants.SLO_DELETE_PERMISSION"
          :resource="item"
          @edit="edit"
          @delete="deleteSLOProfile"
        />
      </template>
      <template v-slot:form-header="{ item }">
        <template v-if="item.id"> Edit SLO Profile </template>
        <template v-else> Create SLO Profile </template>
      </template>

      <template v-slot:form-items="{ item, resetForm }">
        <SloForm
          ref="sloFormRef"
          :value="item"
          :is-view="false"
          :reset-form="resetForm"
        />
      </template>
      <template v-slot:form-actions="{ submit, processing, item, resetForm }">
        <span class="mandatory mt-5"
          ><span class="text-secondary-red">*</span> fields are mandatory</span
        >
        <MButton
          id="credential-profile-reset-btn"
          variant="default"
          @click="resetForm(item.id ? undefined : {})"
          >Reset</MButton
        >

        <MButton
          id="credential-profile-submit-btn"
          :loading="processing"
          class="ml-2"
          @click="submit"
        >
          {{ item.isCloning ? 'Clone' : item.id ? 'Update' : 'Create' }}
          SLO Profile
        </MButton>
      </template>
      <template v-slot="{ activeItem, resetActiveItem }">
        <FlotoDrawer :open="Boolean(activeItem)" @hide="resetActiveItem">
          <template v-slot:title>View SLO Profile</template>
          <SloForm
            v-if="Boolean(activeItem)"
            :value="activeItem"
            :is-view="true"
          />
        </FlotoDrawer>
      </template>
    </FlotoPaginatedCrud>
  </div>
</template>

<script>
import {
  fetchSLOProfilesApi,
  createSLOProfileApi,
  updateSLOProfileApi,
  deleteSLOProfileApi,
} from '../slo-profile-api'
import SloForm from '../components/slo-form.vue'
export default {
  name: 'SLOProfileList',
  components: {
    SloForm,
  },
  data() {
    return {
      defaultSLOItem: {
        sloType: 'Availability',
      },
      gridActions: [
        // { key: 'edit', icon: 'pencil', name: 'Edit SLO Profile' },
        {
          key: 'delete',
          icon: 'trash-alt',
          name: 'Delete SLO Profile',
          isDanger: true,
        },
      ],
      columns: [
        { key: 'name', name: 'SLO Name', searchable: true, sortable: true },
        { key: 'sloType', name: 'Type', searchable: true, sortable: true },
        {
          key: 'frequency',
          name: 'Frequency',
          searchable: true,
          sortable: true,
        },
        { key: 'sloTarget', name: 'Target', searchable: true, sortable: true },
        {
          key: 'businessServiceName',
          name: 'Business Service Name',
          searchable: true,
          sortable: true,
        },
        {
          key: 'startDateFormatted',
          name: 'Start Date',
          searchable: true,
          sortable: true,
          sortKey: 'startDate',
        },

        { key: 'state', name: 'Status', sortable: false },
        { key: 'actions', name: 'Action', sortable: false },
      ],
    }
  },
  methods: {
    getAllSLOProfiles() {
      return fetchSLOProfilesApi()
    },
    createSLOProfile(payload) {
      const handlerMaps = this.$refs.sloFormRef
        ? this.$refs.sloFormRef.getHandlerMaps()
        : {}
      return createSLOProfileApi(payload, handlerMaps)
    },
    updateSLOProfile(payload) {
      const handlerMaps = this.$refs.sloFormRef
        ? this.$refs.sloFormRef.getHandlerMaps()
        : {}
      return updateSLOProfileApi(payload, handlerMaps)
    },
    deleteSLOProfile(payload) {
      return deleteSLOProfileApi(payload)
    },
  },
}
</script>
