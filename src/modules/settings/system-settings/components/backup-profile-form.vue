<template>
  <MRow class="mb-4">
    <MCol :size="6">
      <FlotoFormItem
        v-model="item.name"
        label="Profile Name"
        rules="required"
        placeholder="Must be unique"
        name="name"
      />
    </MCol>
    <MCol :size="6" class="create-group-action-panel">
      <FlotoFormItem
        label="Database Type"
        rules="required"
        :info-tooltip="$message('backup_profile_database_type')"
      >
        <FlotoDropdownPicker
          v-model="item.dbType"
          :options="databaseTypeOptions"
          :disabled="Boolean(item.id)"
        />
      </FlotoFormItem>
    </MCol>
    <MCol v-if="item.dbType === 'Report DB'" :size="6">
      <FlotoFormItem
        label="Datastore Type"
        rules="required"
        :info-tooltip="$message('backup_profile_datastore_type')"
      >
        <FlotoDropdownPicker
          v-model="item.datastoreType"
          :options="datastoreTypeOptions"
          multiple
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="10">
      <FlotoFormItem label="Storage Profile Name" rules="required">
        <StorageProfilePicker
          v-model="item.storageId"
          allow-create
          :default-form-data="defaultFormData"
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="12">
      <FlotoFormItem>
        <ScheduleInput
          v-model="item.schedule"
          :time-options="externalOptions"
          :excluded-schedule-options="['Once']"
          :use-single-selection="true"
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="12">
      <FlotoFormItem id="add-email-btn" label="Notify via Email">
        <FlotoTagsPicker
          v-model="item.email"
          :full-width="true"
          always-text-mode
          type="email"
          placeholder="Email Recipients"
          title="Email Recipients"
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="12">
      <FlotoFormItem id="add-number-btn" label="Notify via SMS">
        <FlotoTagsPicker
          v-model="item.sms"
          :full-width="true"
          type="mobile_number"
          always-text-mode
          placeholder="SMS Recipients"
          title="SMS Recipients"
        />
      </FlotoFormItem>
    </MCol>

    <MCol :size="12">
      <span class="text-neutral"> For more information: </span>
      <a
        href="https://docs.motadata.com/motadata-aiops-docs/backup-restore-management/overview"
        target="_blank"
        >Backup Profile</a
      >
      <MIcon name="external-link" class="ml-1 text-primary" />
    </MCol>
  </MRow>
</template>

<script>
import { authComputed } from '@/src/state/modules/auth'
import StorageProfilePicker from '@components/data-picker/storage-profile-picker.vue'
import ScheduleInput from '@components/schedule-input/index.vue'

export default {
  name: 'BackupProfileForm',
  components: {
    StorageProfilePicker,
    ScheduleInput,
  },
  props: {
    item: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    this.databaseTypeOptions = [
      {
        text: 'Config DB',
        key: 'Config DB',
      },
      {
        text: 'Report DB',
        key: 'Report DB',
      },
    ]
    this.datastoreTypeOptions = [
      {
        text: 'Metric',
        key: 'metric',
      },
      {
        text: 'Log',
        key: 'log',
      },
      {
        text: 'Trap',
        key: 'trap',
      },
      {
        text: 'Alert',
        key: 'alert',
      },
      {
        text: 'NCM',
        key: 'config.history',
      },
      {
        text: 'System Events',
        key: 'system.event',
      },
      {
        text: 'NetRoute',
        key: 'netroute',
      },
    ]
    this.externalOptions = ['00:00', '06:00', '12:00'].map((i) => ({
      key: i,
      text: i,
      id: i.replace(':', ''),
    }))
    return {}
  },
  computed: {
    ...authComputed,
    defaultFormData() {
      if (this.directoryMetadata) {
        return {
          localPath: `${
            this.directoryMetadata?.['current.directory.path'] +
            this.directoryMetadata?.['path.separator']
          }config-management`,
        }
      } else {
        return { localPath: '' }
      }
    },
  },
}
</script>
