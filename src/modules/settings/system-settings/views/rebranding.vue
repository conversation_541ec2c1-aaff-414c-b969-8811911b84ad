<template>
  <div class="px-6 py-4">
    <MRow :gutter="16">
      <MCol :size="6">
        <div class="items-center bordered rounded">
          <div class="mx-4">
            <h5 class="text-primary mt-4 m-0">Logo</h5>
            <small id="message" class="text-neutral">
              Supported File Formats are png, jpg (Recommended Size: 150x50)
            </small>
          </div>
          <div>
            <div
              class="bg-neutral-lightest my-4 rounded flex items-center mx-4"
            >
              <MRow :gutter="0" class="flex-1">
                <MCol class="my-4 flex items-center justify-center flex-1 mx-4">
                  <div
                    class="img-preview mx-4 flex-1 flex items-center justify-center text-center"
                  >
                    <img id="rebranding-logo" :src="lightPreview" />
                  </div>
                  <MPermissionChecker
                    :permission="$constants.SYSTEM_SETTINGS_UPDATE_PERMISSION"
                  >
                    <FileDropper
                      id="browse-logo"
                      v-model="lightLogo"
                      :allowed-extensions="['png', 'jpg']"
                      mode="attachment"
                      button-text="Browse Logo"
                      :max-files="1"
                      :max-allowed-file-size="0.2"
                    >
                      <MButton> Browse Logo </MButton>
                    </FileDropper>
                  </MPermissionChecker>
                </MCol>
              </MRow>
            </div>
            <div class="my-4 mx-4">
              <MButton v-if="lightLogo" @click="handleUpdateLightLogo">
                Update
              </MButton>
            </div>
          </div>
        </div>
      </MCol>
      <MCol :size="6">
        <div class="items-center bordered rounded">
          <div class="mx-4">
            <h5 class="text-primary mt-4 m-0">Logo (Dark Theme)</h5>
            <small class="text-neutral">
              Supported File Formats are png, jpg (Recommended Size: 150x50)
            </small>
          </div>
          <div data-theme="dark-theme">
            <div
              class="bg-neutral-lightest my-4 rounded flex items-center mx-4"
            >
              <MRow :gutter="0" class="flex-1">
                <MCol class="my-4 flex items-center justify-center flex-1 mx-4">
                  <div
                    class="img-preview mx-4 flex-1 flex items-center justify-center text-center"
                  >
                    <img id="rebranding-logo" :src="darkPreview" />
                  </div>
                  <MPermissionChecker
                    :permission="$constants.SYSTEM_SETTINGS_UPDATE_PERMISSION"
                  >
                    <FileDropper
                      id="browse-logo"
                      v-model="darkLogo"
                      :allowed-extensions="['png', 'jpg']"
                      mode="attachment"
                      button-text="Browse Logo"
                      :max-files="1"
                      :max-allowed-file-size="0.2"
                    >
                      <MButton> Browse Logo </MButton>
                    </FileDropper>
                  </MPermissionChecker>
                </MCol>
              </MRow>
            </div>
            <div class="my-4 mx-4">
              <MButton v-if="darkLogo" @click="handleUpdateDarkLogo">
                Update
              </MButton>
            </div>
          </div>
        </div>
      </MCol>
    </MRow>
  </div>
</template>

<script>
import FileDropper from '@components/file-dropper.vue'
import { BrandingMethods } from '@state/modules/branding'
import { getBradingApi, updateBrandingApi } from '../branding-api'

export default {
  name: 'Rebranding',
  components: { FileDropper },
  data() {
    return {
      formData: {},
      lightLogo: undefined,
      darkLogo: undefined,
    }
  },
  computed: {
    lightPreview() {
      if (this.lightLogo) {
        const logo = this.lightLogo[0]
        return `/download?id=${logo.result}&file.name=${logo.name}`
      }
      return this.formData.lightLogoUrl
    },
    darkPreview() {
      if (this.darkLogo) {
        const logo = this.darkLogo[0]
        return `/download?id=${logo.result}&file.name=${logo.name}`
      }
      return this.formData.darkLogoUrl
    },
  },
  created() {
    getBradingApi().then((data) => {
      this.formData = data
    })
  },
  methods: {
    ...BrandingMethods,
    handleUpdateLightLogo() {
      this.handleUpdateLogo(this.lightLogo, false).then(
        () => (this.lightLogo = undefined)
      )
    },
    handleUpdateDarkLogo() {
      this.handleUpdateLogo(this.darkLogo, true).then(
        () => (this.darkLogo = undefined)
      )
    },
    handleUpdateLogo(files, isDark) {
      return updateBrandingApi({
        rebrandingId: this.formData.id,
        ...(isDark
          ? {
              darkLogoId: files[0].result,
              darkLogoName: files[0].name,
            }
          : {
              darkLogoId: this.formData.darkLogoId,
              darkLogoName: this.formData.darkLogoName,
            }),
        ...(!isDark
          ? {
              logoId: files[0].result,
              logoName: files[0].name,
            }
          : {
              logoId: this.formData.logoId,
              logoName: this.formData.logoName,
            }),
      }).then((data) => {
        if (data) {
          this.refreshBranding()
          this.formData = data
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
.img-preview {
  display: inline-block;
  height: 100px;
  background: var(--page-background-color);
  border: 2px dashed var(--border-color);

  img {
    height: 100%;
  }

  @apply rounded py-1 px-4;
}
</style>
