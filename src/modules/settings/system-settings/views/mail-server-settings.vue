<template>
  <FlotoContentLoader :loading="loading">
    <div
      class="flex h-100 flex-col user-profile flex-1 mb-5 center mt-4 overflow-y-auto overflow-x-hidden"
    >
      <FlotoForm
        ref="formRef"
        layout="horizontal"
        @submit="onMailServerSubmit"
        @reset="onMailServerReset"
      >
        <MRow class="form-feild-align">
          <MCol :size="12" class="ml-6">
            <h2 class="text-primary">Mail Server Settings</h2>
          </MCol>
          <MCol :size="12" class="label-strip text-neutral-light ml-6 mb-4">
            Configure mail servers for seamless email notifications from
            Motadata AIOps. For more information:
            <a
              href="https://docs.motadata.com/motadata-aiops-docs/system-settings-module/mail-server-settings"
              target="_blank"
            >
              Mail Server Settings
              <MIcon name="external-link"></MIcon>
            </a>
          </MCol>
          <MCol :size="5" class="ml-6">
            <FlotoFormItem
              id="smtp-server-id"
              v-model="mailServer.mailServer"
              placeholder="e.g. smtp.google.com"
              :label-col="{ xxl: 4, xl: 6, lg: 6 }"
              rules="required"
              label="SMTP Server"
              auto-focus
              name="smtp-server"
            />
          </MCol>
        </MRow>
        <MRow class="form-feild-align">
          <MCol :size="5" class="ml-6">
            <FlotoFormItem
              id="smtp-server-port-id"
              :label-col="{ xxl: 4, xl: 6, lg: 6 }"
              label="Use Proxy Server"
              :info-tooltip="$message('proxy_server')"
            >
              <MSwitch
                id="auto-sync-id"
                v-model="mailServer.mailServerUserProxyServer"
                :checked="mailServer.mailServerUserProxyServer"
                checked-children="ON"
                un-checked-children="OFF"
            /></FlotoFormItem>
          </MCol>
        </MRow>
        <MRow class="form-feild-align">
          <MCol :size="5" class="ml-6">
            <FlotoFormItem
              label="Security Type"
              rules="required"
              :label-col="{ xxl: 4, xl: 6, lg: 6 }"
            >
              <MRadioGroup
                id="security-type-id"
                v-model="mailServer.mailServerSecurityType"
                as-button
                :options="[
                  {
                    value: 'None',
                    label: 'None',
                  },
                  {
                    value: 'SSL',
                    label: 'SSL',
                  },
                  {
                    value: 'TLS',
                    label: 'TLS',
                  },
                ]"
              />
              <slot />
            </FlotoFormItem>
          </MCol>
        </MRow>
        <MRow class="form-feild-align">
          <MCol :size="5" class="ml-6">
            <FlotoFormItem
              id="smtp-server-port-id"
              v-model="mailServer.mailServerPort"
              :label-col="{ xxl: 4, xl: 6, lg: 6 }"
              rules="required|port"
              label="SMTP Server Port"
              name="smtp-server-port"
            />
          </MCol>
        </MRow>
        <MRow class="form-feild-align">
          <MCol :size="5" class="ml-6">
            <FlotoFormItem
              label="Authentication Type"
              rules="required"
              :label-col="{ xxl: 4, xl: 6, lg: 6 }"
            >
              <MRadioGroup
                id="security-type-id"
                v-model="mailServer.mailServerAuthenticationType"
                as-button
                :options="mailServerAuthenticationTypeOptions"
              />
              <slot />
            </FlotoFormItem>
          </MCol>
        </MRow>

        <MRow class="form-feild-align">
          <MCol :size="5" class="ml-6">
            <FlotoFormItem
              id="email-id"
              v-model="mailServer.mailServerEmail"
              :label-col="{ xxl: 4, xl: 6, lg: 6 }"
              :info-tooltip="$message('mail_server_email')"
              placeholder="Valid email address"
              rules="email"
              label="From Email"
              name="email"
            />
          </MCol>
        </MRow>

        <MRow v-if="mailServer.mailServerAuthenticationType !== 'oauth'">
          <MCol :size="5" class="ml-6 form-feild-align">
            <FlotoFormItem
              id="smtp-server-requires-authentication-id"
              label="Authentication Requires"
              rules="required"
              :label-col="{ xxl: 4, xl: 6, lg: 6 }"
            >
              <MSwitch
                id="smtp-server-requires-authentication-btn-id"
                v-model="mailServer.mailServerEnableAuth"
                :checked="mailServer.mailServerEnableAuth"
                checked-children="ON"
                un-checked-children="OFF"
              />
            </FlotoFormItem>
          </MCol>
        </MRow>

        <MRow
          v-if="
            mailServer.mailServerEnableAuth ||
            mailServer.mailServerAuthenticationType === 'oauth'
          "
          class="form-feild-align"
        >
          <MCol :size="5" class="ml-6">
            <FlotoFormItem
              id="username-id"
              v-model="mailServer.mailServerUsername"
              :label-col="{ xxl: 4, xl: 6, lg: 6 }"
              rules="required"
              label="User Name"
              name="username"
            />
          </MCol>
        </MRow>

        <MRow
          v-if="
            mailServer.mailServerEnableAuth &&
            mailServer.mailServerAuthenticationType === 'basic'
          "
          class="form-feild-align"
        >
          <MCol :size="5" class="ml-6">
            <PasswordInput
              id="password-id"
              v-model="mailServer.mailServerPassword"
              validation-label="Password"
              :label-col="{ xxl: 4, xl: 6, lg: 6 }"
              class="mb-0"
              name="password"
              rules="required"
              label="Password"
              vid="password"
            >
              <template v-slot:prefix>
                <MIcon name="unlock-alt" />
              </template>
            </PasswordInput>
          </MCol>
        </MRow>

        <MRow
          v-if="mailServer.mailServerAuthenticationType === 'oauth'"
          class="form-feild-align"
        >
          <MCol :size="5" class="ml-6">
            <CredentialProvider
              :search-params="credentialSearchParams"
              :filter-fn="filterApplicableCredentialProfiles"
            >
              <FlotoFormItem
                id="credential-id"
                label="Credential Profiles"
                rules="required"
                :label-col="{ xxl: 4, xl: 6, lg: 6 }"
              >
                <CredentialPicker
                  v-model="mailServer.credentials"
                  allow-create
                  :available-protocols="[$constants.HTTP_HTTPS]"
                  :default-form-data="defaultFormDataForCredentialPicker"
                />
              </FlotoFormItem>
            </CredentialProvider>
          </MCol>
        </MRow>

        <MRow>
          <MCol class="ml-6">
            <div
              v-show="testMessage"
              id="test-message"
              class="mt-3 float-l credential-successfully"
              :class="{
                'text-secondary-red': !isTestSuccessful,
                'text-secondary-green': isTestSuccessful,
              }"
            >
              <MIcon
                :name="isTestSuccessful ? 'check-circle' : 'times-circle'"
              />
              {{ testMessage }}
              <TestError :error="testErrorStack" size="xs" />
            </div>
          </MCol>
        </MRow>

        <MRow slot="submit" slot-scope="{ reset }" class="text-right mb-5">
          <MCol>
            <MPermissionChecker
              :permission="$constants.SYSTEM_SETTINGS_UPDATE_PERMISSION"
            >
              <MButton
                id="reset-btn"
                variant="default"
                class="mr-2"
                @click="reset"
              >
                Reset
              </MButton>
            </MPermissionChecker>

            <MButton
              id="test-btn"
              variant="primary"
              :outline="true"
              class="mr-2"
              :loading="testProcessing"
              @click="triggerTestConfigurations"
            >
              Test
            </MButton>
            <MPermissionChecker
              :permission="$constants.SYSTEM_SETTINGS_UPDATE_PERMISSION"
            >
              <MButton
                id="configure-btn"
                :disabled="!isTestSuccessful"
                variant="primary"
                :loading="processing"
                type="submit"
              >
                Save Mail Server Settings
              </MButton>
            </MPermissionChecker>
          </MCol>
        </MRow>
      </FlotoForm>
    </div>
    <FlotoConfirmModal
      v-if="showTestEmailInputModal"
      hide-icon
      variant="info"
      open
      @hide="showTestEmailInputModal = false"
    >
      <template v-slot:message>
        <FlotoForm :show-notification="false" @submit="runTest">
          <FlotoFormItem
            id="recipient-email-id"
            v-model="testEmail"
            auto-focus
            validation-label="Recipient Email"
            rules="required|email"
            label="Recipient Email"
            name="recipient-email"
          />
          <template v-slot:submit="{ submit }">
            <MRow>
              <MCol class="text-right">
                <MButton
                  variant="default"
                  @click="showTestEmailInputModal = false"
                >
                  Cancel
                </MButton>
                <MButton
                  id="send-test-email-id"
                  :loading="testProcessing"
                  class="ml-2"
                  @click="submit"
                >
                  Send Test Email
                </MButton>
              </MCol>
            </MRow>
          </template>
        </FlotoForm>
      </template>
      <template v-slot:action-container>
        <span />
      </template>
    </FlotoConfirmModal>
  </FlotoContentLoader>
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import Bus from '@utils/emitter'
import TestError from '@components/error-info.vue'
import PasswordInput from '@components/password-input.vue'
import { generateId } from '@utils/id'

import {
  fetchMailServerConfigurationApi,
  updateMailServerConfigurationApi,
} from '../mail-server-api'
import { transformMailServerConfigurationForServer } from '../helpers/mail-server-settings'
import CredentialPicker from '@components/data-picker/credential-picker.vue'
import CredentialProvider from '@components/data-provider/credential-provider.vue'

export default {
  name: 'MailServerSettings',
  components: {
    PasswordInput,
    TestError,
    CredentialPicker,
    CredentialProvider,
  },
  inject: ['ScreenBlockerContext'],
  data() {
    this.untouchedMailServer = {}
    this.guid = undefined
    this.mailServerAuthenticationTypeOptions = [
      {
        value: 'basic',
        text: 'Basic',
      },
      {
        value: 'oauth',
        text: 'OAuth2.0',
      },
    ]
    this.defaultFormDataForCredentialPicker = {
      credentialProfileProtocol: this.$constants.HTTP_HTTPS,
      authenticationType: 'oauth',
      disabledAuthenticationType: true,
      redirectURL: `${window.location.origin}/oauth-callback`,
      timeout: 60,
      grantType: 'Authorization Code',
      scopes: [
        {
          key: generateId(),
        },
      ],
    }
    return {
      mailServer: {
        mailServerEnableAuth: false,
      },
      processing: false,
      loading: true,
      testProcessing: false,
      isTestSuccessful: false,
      testMessage: null,
      testErrorStack: null,
      testEmail: undefined,
      showTestEmailInputModal: false,
      resetKey: false,
    }
  },
  computed: {
    credentialSearchParams() {
      return {
        key: 'credential.profile.protocol',
        value: [this.$constants.HTTP_HTTPS],
      }
    },
  },
  watch: {
    'mailServer.mailServerSecurityType': function (value, oldValue) {
      if (oldValue !== undefined && !this.resetKey) {
        let port
        if (value === 'None') {
          port = 25
        } else if (value === 'SSL') {
          port = 465
        } else if (value === 'TLS') {
          port = 587
        }
        this.isTestSuccessful = false
        this.testMessage = null
        this.testErrorStack = null
        this.mailServer = {
          ...this.mailServer,
          mailServerPort: port,
        }
      } else if (value !== oldValue) {
        this.isTestSuccessful = false
        this.testMessage = null
      }
      this.resetKey = false
    },
    mailServer: {
      handler() {
        this.isTestSuccessful = false
      },
    },
    'mailServer.mailServerPort': 'onFormDirty',
    'mailServer.mailServerEnableAuth': 'onFormDirty',
    'mailServer.mailServerUsername': 'onFormDirty',
    'mailServer.mailServerPassword': 'onFormDirty',
    'mailServer.mailServer': 'onFormDirty',
    'mailServer.mailServerAuthenticationType': 'onFormDirty',
  },
  created() {
    this.fetchMailServer()
    const handler = (payload) => {
      if (payload.guid !== this.guid) {
        return
      }
      this.ScreenBlockerContext.unblock()
      this.testProcessing = false
      if (payload.status === this.$constants.EVENT_SUCCESS_STATUS) {
        this.isTestSuccessful = true
        this.testMessage = payload.message
      } else {
        this.isTestSuccessful = false
        this.testErrorStack = payload.error
        this.testMessage = payload.message
      }
    }
    Bus.$on(this.$currentModule.getConfig().MAIL_SERVER_TEST_EVENT, handler)

    this.$once('hook:beforeDestroy', () => {
      Bus.$off(this.$currentModule.getConfig().MAIL_SERVER_TEST_EVENT, handler)
    })
  },
  methods: {
    filterApplicableCredentialProfiles(item) {
      return item.authenticationType === 'oauth'
    },
    onFormDirty(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.isTestSuccessful = false
        this.testMessage = null
      }
    },
    onMailServerSubmit() {
      this.processing = true
      updateMailServerConfigurationApi(this.mailServer)
        .then(() => {
          this.untouchedMailServer = CloneDeep(this.mailServer || {})

          this.$successNotification({
            message: this.$message('save_mail_server_setting'),
          })
        })
        .finally(() => (this.processing = false))
    },
    onMailServerReset() {
      this.resetKey = true
      this.mailServer = CloneDeep(this.untouchedMailServer)
      this.isTestSuccessful = false
      this.testMessage = null
      this.testErrorStack = null
    },
    triggerTestConfigurations() {
      this.$refs.formRef.validate().then((response) => {
        this.testEmail = ''
        this.showTestEmailInputModal = true
      })
    },
    resetTestForm() {
      this.testEmail = ''
    },
    runTest() {
      this.showTestEmailInputModal = false
      this.testProcessing = true
      this.testMessage = null
      this.testErrorStack = null
      this.ScreenBlockerContext.block(this.$message('mail_test'))
      this.guid = generateId()
      Bus.$emit('server:event', {
        'event.type': this.$currentModule.getConfig().MAIL_SERVER_TEST_EVENT,
        'event.context': {
          target: this.testEmail,
          ...transformMailServerConfigurationForServer(this.mailServer),
          guid: this.guid,
        },
      })
    },
    async fetchMailServer() {
      this.loading = true
      const result = await fetchMailServerConfigurationApi()
      this.untouchedMailServer = CloneDeep(result || {})
      this.mailServer = result
      this.loading = false
    },
  },
}
</script>
