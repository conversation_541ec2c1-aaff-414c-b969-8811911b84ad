<template>
  <div class="flex h-100 flex-col flex-1">
    <MRow class="mb-4 mt-2">
      <MCol :size="12" class="ml-4">
        <h2 class="text-primary">Storage Profile</h2>
      </MCol>
      <MCol class="label-strip text-neutral-light ml-4">
        Configure and manage external storage locations for secure and redudant
        data backups. For more information:
        <a
          href="https://docs.motadata.com/motadata-aiops-docs/backup-restore-management/storage-profile"
          target="_blank"
        >
          Storage Profile
          <MIcon name="external-link"></MIcon>
        </a>
      </MCol>
    </MRow>
    <FlotoPaginatedCrud
      class="h-100"
      as-table
      resource-name="Storage Profile"
      :default-item="defaultItem"
      :columns="columns"
      :fetch-fn="fetchExternalStorageProfiles"
      :update-fn="updateExternalStorageProfile"
      :delete-fn="deleteExternalStorageProfile"
      :create-fn="createExternalStorageProfile"
    >
      <template
        v-slot:add-controls="{
          create,
          filter,
          resetFilter,
          searchTerm,
          processing,
        }"
      >
        <MRow>
          <MCol>
            <MInput
              :value="searchTerm"
              class="search-box"
              placeholder="Search"
              name="storage-search"
              @update="filter"
            >
              <template v-slot:prefix>
                <MIcon name="search" />
              </template>
              <template v-if="searchTerm" v-slot:suffix>
                <MIcon
                  name="times-circle"
                  class="text-neutral-light cursor-pointer"
                  @click="resetFilter"
                />
              </template>
            </MInput>
          </MCol>
          <MPermissionChecker
            :permission="$constants.SYSTEM_SETTINGS_CREATE_PERMISSION"
          >
            <MCol class="text-right">
              <MButton
                id="create-storage-btn"
                :loading="processing"
                @click="createStorageProfile(create)"
              >
                Create Storage Profile
              </MButton>
            </MCol>
          </MPermissionChecker>
        </MRow>
      </template>

      <template v-slot:form-header="{ item }">
        {{ item.id ? 'Edit' : 'Create' }} Storage Profile
      </template>

      <template v-slot:form-items="{ item }">
        <ExternalStorageForm :value="item" @form-dirty="formDirty" />

        <!-- <div class="w-full flex">
          <span class="text-neutral mr-1"> For more information: </span>

          <a
            href="https://docs.motadata.com/motadata-aiops-docs/backup-restore-management/overview"
            target="_blank"
          >
            Storage Profile

            <MIcon name="external-link"></MIcon>
          </a>
        </div> -->
      </template>

      <template
        v-slot:form-actions="{ resetForm, submit, processing, item, validate }"
      >
        <span class="mandatory mt-5"
          ><span class="text-secondary-red">*</span> fields are mandatory</span
        >
        <MButton
          id="reset-btn"
          variant="default"
          @click="resetForm(item.id ? undefined : {})"
        >
          Reset
        </MButton>
        <ExternalStorageFormTest
          ref="testref"
          :context="item"
          :processing="processing"
          :validate="validate"
          :submit="submit"
        />
      </template>

      <template v-slot:name="{ item, activateItem }">
        <a @click="activateItem(item)">
          {{ item.name }}
        </a>
      </template>

      <template v-slot:destination="{ item }">
        <span class="flex items-center">
          <MonitorType
            :type="item.destination === 'SCP/SFTP' ? 'SFTP' : item.destination"
            class="mr-2 mt-1"
          />
          {{ item.destination }}
        </span>
      </template>

      <template v-slot:count="{ item }">
        <UsedCounts
          class="text-center"
          :title="`Used Count for ${item.name}`"
          :display-count="item.count"
          :parent-resource-id="item.id"
          parent-resource-type="storage-profiles"
          :count-types="[
            {
              countType: 'Configuration',
              title: `NCM Devices`,
              resourceKey: 'Configuration',
            },
            {
              countType: 'Backup Profile',
              title: `Backup Profile`,
              resourceKey: 'Backup Profile',
            },
          ]"
        />
      </template>

      <template v-slot:actions="{ edit, item }">
        <FlotoGridActions
          :actions="gridItemActions(item)"
          :resource="item"
          :edit-permission-name="$constants.SYSTEM_SETTINGS_UPDATE_PERMISSION"
          :delete-permission-name="$constants.SYSTEM_SETTINGS_DELETE_PERMISSION"
          @edit="edit"
        />
      </template>

      <template v-slot="{ activeItem, resetActiveItem }">
        <FlotoDrawer
          :open="Boolean(activeItem)"
          width="50%"
          @hide="resetActiveItem"
        >
          <template v-slot:title> View Storage Profile </template>
          <ExternalStorageForm
            v-if="Boolean(activeItem)"
            :value="activeItem"
            :is-view="true"
          />
        </FlotoDrawer>
      </template>
    </FlotoPaginatedCrud>
  </div>
</template>

<script>
import UsedCounts from '@components/used-counts/used-counts.vue'
import {
  fetchExternalStoragesApi,
  createExternalStorageApi,
  updateExternalStorageApi,
  deleteExternalStorageApi,
} from '../external-storage-profile-api'
import ExternalStorageForm from '../components/external-storage-form.vue'
import ExternalStorageFormTest from '../components/external-storage-form-test.vue'
import MonitorType from '@components/monitor-type.vue'
import { authComputed } from '@/src/state/modules/auth'

export default {
  name: 'ExternalStorageProfile',
  components: {
    UsedCounts,
    ExternalStorageForm,
    MonitorType,
    ExternalStorageFormTest,
  },

  inject: {
    ScreenBlockerContext: { default: {} },
  },

  data() {
    this.preAppliedPermissions = [
      'my-account-settings:read',
      'my-account-settings:read-write',
    ]
    this.defaultUserItem = {
      adminAccess: true,
      permissions: [
        'my-account-settings:read',
        'my-account-settings:read-write',
        'dashboards:read',
        'widgets:read',
        'monitor-settings:read',
        'group-settings:read',
      ],
    }
    this.actions = [
      { key: 'edit', name: 'Edit Storage Profile', icon: 'pencil' },
      {
        key: 'delete',
        name: 'Delete Storage Profile',
        icon: 'trash-alt',
        isDanger: true,
      },
    ]
    return {
      columns: [
        {
          key: 'name',
          name: 'Storage Profile Name',
          searchable: true,
          sortable: true,
        },

        {
          key: 'count',
          name: 'Used Count',
          searchable: true,
          sortable: true,
          width: '140px',
          align: 'center',
        },

        {
          key: 'destination',
          name: 'Storage Destination',
          searchable: true,
          sortable: true,
        },

        {
          name: 'Actions',
          key: 'actions',
          align: 'right',
          width: '120px',
        },
      ],
    }
  },
  computed: {
    ...authComputed,
    defaultItem() {
      if (this.directoryMetadata) {
        return {
          localPath: `${
            this.directoryMetadata?.['current.directory.path'] +
            this.directoryMetadata?.['path.separator']
          }config-management`,
        }
      } else {
        return { localPath: '' }
      }
    },
  },
  methods: {
    createExternalStorageProfile(payload) {
      this.formDirty()
      return createExternalStorageApi(payload)
    },
    fetchExternalStorageProfiles() {
      return fetchExternalStoragesApi()
    },
    updateExternalStorageProfile(payload) {
      this.formDirty()
      return updateExternalStorageApi(payload)
    },
    deleteExternalStorageProfile(payload) {
      return deleteExternalStorageApi(payload)
    },
    createStorageProfile(create) {
      create()
    },
    formDirty() {
      if (this.$refs.testref) {
        this.$refs.testref.formDirty()
      }
    },
    gridItemActions(item) {
      if (item.isDefault) {
        return []
      }
      return this.actions
    },
  },
}
</script>
