import { generateId } from '@utils/id'
import Uniq from 'lodash/uniq'
import Trim from 'lodash/trim'

export const instancelevelOption = [
  'vm',
  'interface',
  'access point',
  'process',
  'service',
]
export function transformRuleBasedTagsForServer(item) {
  const tagKeyValue =
    item.tagOperation === 'Assign'
      ? (item.tagKeyValue || []).map((keyValue) => {
          return keyValue.assignTagKey && Trim(keyValue.assignTagValue)
            ? `${Trim(keyValue.assignTagKey).toLowerCase()}:${Trim(
                keyValue.assignTagValue
              ).toLowerCase()}`
            : Trim(keyValue.assignTagKey).toLowerCase()
        })
      : item.removeTags

  return {
    id: item.id,
    'tag.rule.name': item.ruleName,
    'tag.rule.description': item.description,
    'tag.rule.triggered.time': item.lastExecutionTime,
    'tag.rule.type': item.rulesAppliesTo,
    'tag.rule.operation': item.tagOperation,
    'tag.rule.tags': Uniq([...tagKeyValue]),
    'tag.rule.inclusive.condition': item.inclusion === 'include' ? 'yes' : 'no',

    'tag.rule.conditions': (item.conditions || []).map((condition, index) => ({
      'data.point': condition.counter,
      operator: condition.operator,
      value: condition.value,
      filter: index === 0 ? 'NONE' : condition.jointCondition,
    })),
  }
}

export function transformRuleBasedTagsForClient(item) {
  return {
    id: item.id,
    ruleName: item['tag.rule.name'],
    description: item['tag.rule.description'],
    lastExecutionTime: item['tag.rule.triggered.time'],
    rulesAppliesTo: item['tag.rule.type'],
    tagOperation: item['tag.rule.operation'],
    ...(item['tag.rule.operation'] === 'Assign'
      ? {
          tagKeyValue: (item['tag.rule.tags'] || []).map((tag) => {
            const tagValue = tag?.split(':')
            return {
              key: generateId(),
              assignTagKey: tagValue[0],
              assignTagValue: tagValue[1],
            }
          }),
        }
      : {
          removeTags: item['tag.rule.tags'] || [],
        }),

    tags: item['tag.rule.tags'],

    inclusion:
      item['tag.rule.inclusive.condition'] === 'yes' ? 'include' : 'exclude',
    conditions: (item['tag.rule.conditions'] || []).map((condition) => ({
      key: generateId(),
      counter: condition?.['data.point'],
      operator: condition?.operator,
      value: condition['value'],
      jointCondition: condition.filter,
    })),

    usedCount: item['count'],
  }
}

export const counterList = {
  monitor: [
    'object.host',
    'object.ip',
    'object.name',
    'object.type',
    'object.category',
    'object.system.oid',
    'object.vendor',
  ],

  interface: [
    'interface.alias',
    'interface.description',
    'interface.index',
    'interface.name',
    'interface.ip.address',
  ],

  'access point': [
    'wireless.access.point',
    'wireless.access.point.mac.address',
    'wireless.access.point.ip.address',
    'object.name',
  ],

  vm: ['object.type', 'object.name', 'object.ip'],

  process: ['object.name'],

  service: ['object.name'],
}
