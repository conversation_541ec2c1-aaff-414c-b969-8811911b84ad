<template>
  <MetricExplorerProvider
    ref="metricExplorerProviderRef"
    :default-counters="defaultCounters"
  >
    <CounterProvider
      :search-params="counterSearchParams"
      @counters="handleCounterReceived"
    >
      <template v-slot="{ loading }">
        <MRow class="min-h-0 flex-1 __panel metric-explorer-bg" :gutter="0">
          <MCol :size="3" class="h-full pr-2 -my-2 border-right">
            <div class="flex-col flex-1 min-h-0 h-full flex">
              <MetricExplorerSavedViewProvider
                :monitor="monitor"
                @select-view="setSelectedMetrics"
              >
                <FlotoContentLoader :loading="loading">
                  <CounterSelector
                    ref="counterSelectionRef"
                    :object-id="objectId"
                    :can-add-counter="addedCharts < maxCharts"
                    allow-instance-selection
                    :category="category"
                    :default-selected-instance="
                      instanceType !== 'application' ? instanceType : undefined
                    "
                    for-monitor-template
                    :selected-metric-explorer-view="selectedView"
                    @add-counter="handleAddCounterByButton"
                    @select="setSelectedMetrics"
                    @selectionChange="selectedView = $event"
                    @select-view="setSelectedMetrics"
                    @create="onCreateMetricExplorerView"
                  />
                </FlotoContentLoader>
              </MetricExplorerSavedViewProvider>
            </div>
          </MCol>

          <MCol :size="9" class="flex-col min-h-0 overflow-auto h-full">
            <ScrollableExport ref="scrollableExportRef">
              <template v-slot="{ convertToImage }">
                <div
                  ref="actionContainerRef"
                  class="flex flex-col min-h-0 h-full dashboard-container metric-explorer-bg"
                >
                  <div
                    v-if="!disableControls"
                    class="flex items-center min-w-0 justify-end pr-2"
                  >
                    <div class="material-input flex items-center mb-1">
                      <MRadioGroup
                        v-model="chartType"
                        as-button
                        :options="chartTypeOptions"
                        class="chart-selector-radio mr-4 relative"
                      >
                        <template v-slot:option="{ option }">
                          <WidgetTypeIcon
                            :widget-type="option.icon"
                            :tooltip="option.text"
                            :size="28"
                            class="relative"
                            :selected="option.value === chartType"
                          />
                        </template>
                      </MRadioGroup>
                      <TimeRangePicker v-model="timeline" />
                      <MButton
                        v-if="!disableCapture"
                        variant="neutral-lightest"
                        title="Capture"
                        class="mx-2 squared-button"
                        @click="
                          convertToImage(
                            $refs.scrollContainerRef,
                            'metric-explorer'
                          )
                        "
                      >
                        <MIcon name="image" class="excluded-header-icon" />
                      </MButton>
                      <span v-else class="mx-2" />
                      <MButton
                        class="squared-button"
                        variant="neutral-lightest"
                        title="Clear All"
                        @click="removeAll"
                      >
                        <MIcon name="clear-all" class="excluded-header-icon" />
                      </MButton>
                    </div>
                  </div>
                  <div
                    ref="scrollContainerRef"
                    class="overflow-auto pr-1 metric-explorer-bg"
                  >
                    <div
                      v-for="(counter, index) in selectedCounters"
                      :key="counter ? counter.counter : index"
                      :style="{
                        height: `${Math.max(singleChartHeight, 250)}px`,
                      }"
                      class="chart-placeholder"
                    >
                      <div
                        v-if="counter"
                        class="h-full flex flex-col flex-1 min-h-0"
                      >
                        <Capture>
                          <template v-slot="{ capture }">
                            <MetricChart
                              allow-close
                              :counter="counter.key"
                              :object-id="objectId"
                              :chart-options="counter.chartOptions"
                              :chart-type="
                                chartType.indexOf('stacked') >= 0
                                  ? chartType.split('-').pop()
                                  : chartType
                              "
                              :instance="counter.instance"
                              :instance-type="counter.instanceType"
                              :stacked="chartType.indexOf('stacked') >= 0"
                              show-aggrigation-vertical
                              :unit="counter.unit"
                              :timeline="timeline"
                              :color="counter.color"
                              :event="$constants.UI_WIDGET_RESULT_EVENT"
                              sync-group="syncedChart"
                              :overlay-options="counter.overlayOptions"
                              :current-arithmetic-operation="
                                counter.selectedArithmeticOperation
                              "
                              @add-counter-to-explorer="
                                handleAddCounterToExplorerAndMerge
                              "
                              @remove="handleRemoveChart(counter)"
                              @apply-chart-changes="
                                applyChartChanges($event, counter, index)
                              "
                            >
                              <template v-slot:chart-actions="slotProps">
                                <span class="text-right flex">
                                  <ArithmeticOperationDropdown
                                    v-if="slotProps.hasData"
                                    :value="counter.selectedArithmeticOperation"
                                    :allow-clear="true"
                                    :max-level="4"
                                    class="w-full"
                                    for-metric-explorer
                                    :min-width="200"
                                    placement="bottomLeft"
                                    @change="
                                      applyArithmeticOperation(
                                        $event,
                                        counter,
                                        index
                                      )
                                    "
                                  />
                                  <FlotoGridActions
                                    v-if="slotProps.hasData"
                                    :actions="actions"
                                    :resource="{}"
                                    @compare="
                                      showModel('compare', counter, index)
                                    "
                                    @anomaly="
                                      showModel('anomaly', counter, index)
                                    "
                                    @forecast="
                                      showModel('forecast', counter, index)
                                    "
                                    @outlier="
                                      showModel('outlier', counter, index)
                                    "
                                    @remove="handleRemoveChart(counter)"
                                    @share="capture"
                                  >
                                    <template v-slot:trigger>
                                      <MButton
                                        class=""
                                        variant="transparent"
                                        :rounded="false"
                                        shape="circle"
                                        title="Actions"
                                      >
                                        <MIcon name="ellipsis-v" />
                                      </MButton>
                                    </template>
                                  </FlotoGridActions>
                                </span>
                              </template>
                            </MetricChart>
                          </template>
                        </Capture>
                      </div>
                      <div
                        v-else
                        class="chart-placeholder pb-6 flex items-center justify-center my-2 flex-col"
                        @drop="dropSeries($event, index)"
                        @dragover="allowDropSeries"
                      >
                        <h3 class="m-0 text-neutral-light">
                          <MIcon name="chart" size="2x" />
                        </h3>
                        <h3 class="m-0 text-neutral-light">
                          Drop metric here to view trend
                        </h3>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </ScrollableExport>
          </MCol>
          <div v-if="tempChartCounter" style="display: none">
            <MetricChart
              :counter="tempChartCounter.key"
              :object-id="tempChartCounter.objectId"
              :instance="tempChartCounter.instance"
              :instance-type="tempChartCounter.instanceType"
              :timeline="timeline"
              :color="tempChartCounter.color"
              :event="$constants.UI_WIDGET_RESULT_EVENT"
              :remove-series-on-remove="false"
              @series-registered="tempChartCounter = undefined"
            />
          </div>

          <template v-if="modalProps">
            <div>
              <CompareModal
                v-if="modalType === 'compare'"
                v-bind="modalProps"
                :timeline="timeline"
                @cancel="cancelOpenModel"
              >
                <template v-slot:current-chart-info="slotProps">
                  <SeriesInfo
                    v-bind="slotProps"
                    hide-color
                    style="
                      margin-top: 10px;
                      margin-right: 10px;
                      margin-left: 50px;
                    "
                  />
                </template>
                <template v-slot:previous-chart-info="slotProps">
                  <SeriesInfo
                    v-bind="slotProps"
                    hide-color
                    style="
                      margin-top: 10px;
                      margin-right: 10px;
                      margin-left: 50px;
                    "
                  />
                </template>
              </CompareModal>
              <AnomalyModal
                v-else-if="modalType === 'anomaly'"
                v-bind="modalProps"
                :timeline="timeline"
                @cancel="cancelOpenModel"
                @apply-chart-changes="applyChartChanges"
              >
                <template v-slot="slotProps">
                  <SeriesInfo
                    v-bind="slotProps"
                    hide-color
                    style="
                      margin-top: 10px;
                      margin-right: 10px;
                      margin-left: 50px;
                    "
                  />
                </template>
              </AnomalyModal>
              <ForecastModal
                v-else-if="modalType === 'forecast'"
                v-bind="modalProps"
                :timeline="timeline"
                @cancel="cancelOpenModel"
                @apply-chart-changes="applyChartChanges"
              >
                <template v-slot="slotProps">
                  <SeriesInfo
                    v-bind="slotProps"
                    hide-color
                    style="
                      margin-top: 10px;
                      margin-right: 10px;
                      margin-left: 50px;
                    "
                  />
                </template>
              </ForecastModal>
              <OutlierModal
                v-else-if="modalType === 'outlier'"
                v-bind="modalProps"
                :timeline="timeline"
                @cancel="cancelOpenModel"
              >
                <template v-slot="slotProps">
                  <SeriesInfo
                    v-bind="slotProps"
                    hide-color
                    style="
                      margin-top: 10px;
                      margin-right: 10px;
                      margin-left: 50px;
                    "
                  />
                </template>
              </OutlierModal>

              <ArithmeticModal
                v-else-if="modalType === 'arithmetic'"
                v-bind="modalProps"
                :timeline="timeline"
                @cancel="cancelOpenModel"
                @apply-chart-changes="applyChartChanges"
              >
                <template v-slot="slotProps">
                  <SeriesInfo
                    v-bind="slotProps"
                    hide-color
                    style="
                      margin-top: 10px;
                      margin-right: 10px;
                      margin-left: 50px;
                    "
                  />
                </template>
              </ArithmeticModal>
            </div>
          </template>
        </MRow>
      </template>
    </CounterProvider>
  </MetricExplorerProvider>
</template>

<script>
import FindIndex from 'lodash/findIndex'
import { generateId } from '@utils/id'
import Bus from '@utils/emitter'

import { authComputed } from '@state/modules/auth'

import CounterProvider from '@components/data-provider/counter-provider.vue'
import TimeRangePicker from '@components/widgets/time-range-picker.vue'
import { colors } from '@utils/chart-colors'
import {
  WidgetTypeConstants,
  QUERY_TYPE_OPTIONS,
} from '@components/widgets/constants'
import ScrollableExport from '@components/scrollable-export.vue'
import WidgetTypeIcon from '@components/widgets/widget-type-icon/widget-type-icon.vue'
import CounterSelector from './counter-selector/counter-selector.vue'
import MetricChart from './metric-chart.vue'
import MetricExplorerProvider from './metric-explorer-provider.vue'
import ArithmeticOperationDropdown from '@components/widgets/widget-group-form/arithmetic-operation-dropdown.vue'

import AnomalyModal from '@components/metric-explorer/anomaly-modal.vue'
import CompareModal from '@components/metric-explorer/compare-modal.vue'
import ForecastModal from '@components/metric-explorer/forecast-modal.vue'
import OutlierModal from '@components/metric-explorer/outlier-modal.vue'
import ArithmeticModal from '@components/metric-explorer/arithmetic-modal.vue'
import SeriesInfo from '@modules/metric-explorer/components/series-info.vue'
import Capture from '@components/chart/capture.vue'

import MetricExplorerSavedViewProvider from '@modules/metric-explorer/components/metric-explorer-saved-view-provider.vue'
import { createExplorerApi, updateExplorerApi } from '@api/explorer-api'

export default {
  name: 'MetricExplorer',
  components: {
    CounterProvider,
    CounterSelector,
    MetricChart,
    MetricExplorerProvider,
    WidgetTypeIcon,
    TimeRangePicker,
    ScrollableExport,
    ArithmeticOperationDropdown,
    AnomalyModal,
    CompareModal,
    ForecastModal,
    OutlierModal,
    ArithmeticModal,
    SeriesInfo,
    Capture,
    MetricExplorerSavedViewProvider,
  },
  inject: {
    deviceTemplateContext: {
      default: {
        setMetricExplorer(e) {
          return e
        },
      },
    },
  },
  inheritAttrs: false,
  props: {
    objectId: {
      type: Number,
      required: true,
    },
    maxCharts: {
      type: Number,
      default: 8,
    },
    defaultCounters: {
      type: Array,
      default() {
        return []
      },
    },
    category: {
      type: String,
      default: undefined,
    },
    instance: {
      type: String,
      default: undefined,
    },
    instanceType: {
      type: String,
      default: undefined,
    },
    disableCapture: {
      type: Boolean,
      default: false,
    },
    disableControls: {
      type: Boolean,
      default: false,
    },
    monitor: {
      type: Object,
      required: true,
    },
  },
  data() {
    this.chartTypeOptions = [
      { value: 'area', text: 'area', icon: WidgetTypeConstants.AREA },
      { value: 'line', text: 'line', icon: WidgetTypeConstants.LINE },
      {
        value: 'stacked-line',
        text: 'stacked line',
        icon: WidgetTypeConstants.STACKED_LINE,
      },
      {
        value: 'stacked-area',
        text: 'stacked area',
        icon: WidgetTypeConstants.STACKED_AREA,
      },
    ]
    return {
      selectedCounters: new Array(this.maxCharts),
      timeline: {
        selectedKey: '-6h',
      },
      chartType: 'line',
      singleChartHeight: false,
      tempChartCounter: undefined,
      modalType: null,
      modalProps: null,
      selectedArithmeticOperation: undefined,
      showModelFor: null,
      selectedView: undefined,
    }
  },
  computed: {
    ...authComputed,

    actions() {
      let actions = [
        { key: 'compare', name: 'Compare', icon: 'chart' },
        { key: 'anomaly', name: 'Anomaly', icon: 'line' },
        // { key: 'outlier', name: 'Outlier', icon: 'outlier' },
        { key: 'forecast', name: 'Forecast', icon: 'forecast' },
        { key: 'share', name: 'Share', icon: 'share-alt' },

        { key: 'remove', name: 'Remove', icon: 'times-circle', isDanger: true },
      ]

      if (
        !this.hasLicensePermission(this.$constants.ANOMALY_OUTLINER_FORECAST)
      ) {
        actions = actions.filter((action) => !['outlier'].includes(action.key))
      }

      if (
        !this.hasLicensePermission(this.$constants.ANOMALY_LICENSE_PERMISSION)
      ) {
        actions = actions.filter((action) => !['anomaly'].includes(action.key))
      }
      if (
        !this.hasLicensePermission(this.$constants.FORECAST_LICENSE_PERMISSION)
      ) {
        actions = actions.filter((action) => !['forecast'].includes(action.key))
      }

      return actions
    },
    counterSearchParams() {
      return {
        'visualization.group.type': 'metric',
        'entity.type': 'Monitor',
        entities: [this.objectId],
      }
    },
    addedCharts() {
      return this.selectedCounters.filter((c) => c !== undefined).length
    },
    addedCounters() {
      return this.selectedCounters.map((c) => c && c.counter).filter(Boolean)
    },
  },
  watch: {
    selectedView: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          if (this.deviceTemplateContext) {
            this.deviceTemplateContext.setMetricExplorer({
              selectedMetricExplorerView: newValue,
            })
          }
        }
      },
    },
  },
  mounted() {
    this.singleChartHeight = parseInt(getComputedStyle(this.$el).height) / 5
    if (this.deviceTemplateContext) {
      this.deviceTemplateContext.setMetricExplorer({
        timeline: {
          selectedKey: '-6h',
        },
        chartType: 'area',
        granularity: {
          value: 5,
          unit: 'm',
          queryType: QUERY_TYPE_OPTIONS.AGGREGATION,
        },
      })
    }

    Bus.$on('metric-explorer:save-view', this.createMetricExplorerView)
    Bus.$on('metric-explorer:update-view', this.updateMetricExplorerView)

    this.$once('hook:beforeDestroy', () => {
      Bus.$off('metric-explorer:save-view', this.createMetricExplorerView)
      Bus.$off('metric-explorer:update-view', this.updateMetricExplorerView)
    })
  },
  activated() {
    if (this.__counters) {
      this.addDefaultCounters()
    }
  },
  beforeDestroy() {
    this.__counters = null
  },
  methods: {
    exportAsImage() {
      this.$refs.scrollableExportRef.capture(
        this.$refs.scrollContainerRef,
        'metric-explorer'
      )
    },
    handleAddCounterToExplorerAndMerge(counter) {
      const totalKeys = Object.keys(
        this.$refs.metricExplorerProviderRef.serieses
      ).filter(
        (key) => this.$refs.metricExplorerProviderRef.serieses[key]
      ).length
      this.tempChartCounter = {
        ...counter,
        objectId: this.objectId,
        color: colors[totalKeys % colors.length],
      }
    },
    getRootPopupContainer() {
      if (this.$refs.actionContainerRef) {
        return this.$refs.actionContainerRef
      }
      return undefined
    },
    getScrollContainer() {
      if (this.$refs.scrollContainerRef) {
        return this.$refs.scrollContainerRef
      }
    },
    handleCounterReceived(counters) {
      this.__counters = counters
      this.addDefaultCounters()
    },
    addDefaultCounters() {
      const counters = this.__counters
      if (this.defaultCounters.length > 0) {
        const data = this.defaultCounters
          .map((c, index) => {
            if (counters.has(c)) {
              const counter = counters.get(c)
              return {
                ...counter,
                counter: counter.counterName,
                color: colors[index],
                objectId: this.objectId,
              }
            }
            return false
          })
          .filter(Boolean)
        this.selectedCounters = Object.freeze([
          ...data,
          ...new Array(this.maxCharts - data.length),
        ])
      }
    },
    handleAddCounter(counter, indexToInsert, shouldCloseModel = false) {
      const index = FindIndex(this.selectedCounters, {
        counter: counter.counter,
        ...(counter.instance ? { instance: counter.instance } : {}),
      })
      if (index === -1) {
        // const indexToInsert = FindIndex(
        //   this.selectedCounters,
        //   (c) => c === undefined
        // )
        this.selectedCounters = Object.freeze([
          ...this.selectedCounters.slice(0, indexToInsert),
          {
            ...counter,
            ...(this.instance ? { instance: this.instance } : {}),
            counter: `${counter.counterName}`,
            color: colors[indexToInsert],
          },
          ...this.selectedCounters.slice(indexToInsert + 1),
        ])

        if (shouldCloseModel) {
          setTimeout(() => {
            this.modalProps = null
            this.showModelFor = null
            this.cancelOpenModel()
          }, 200)
        }
      }
    },
    handleRemoveChart(counter) {
      const index = FindIndex(this.selectedCounters, {
        counter: counter.counter,
        ...(counter.instance ? { instance: counter.instance } : {}),
      })
      if (index !== -1) {
        this.selectedCounters = [
          ...this.selectedCounters.slice(0, index),
          undefined,
          ...this.selectedCounters.slice(index + 1),
        ]
      }
    },
    allowDropSeries(ev) {
      if (this.$refs.metricExplorerProviderRef.draggingContext) {
        ev.preventDefault()
      }
      // let data = ev.dataTransfer.getData('text')
      // if (data.indexOf('series-merge') === -1) {
      //   return
      // }
      // data = JSON.parse(data)
      // if (this.mergedCounters.includes(data.counter)) {
      //   return
      // }
    },
    handleAddCounterByButton(item) {
      const index = FindIndex(this.selectedCounters, (i) => !i || !i.counter)
      this.handleAddCounter(item, index)
    },
    dropSeries(ev, index) {
      ev.preventDefault()
      const data = this.$refs.metricExplorerProviderRef.draggingContext.data
      if (data.type !== 'add-series-to-metric-explorer') {
        return
      }
      this.handleAddCounter(data.counter, index)
    },
    removeAll() {
      this.selectedCounters = new Array(this.maxCharts)
    },
    cancelOpenModel() {
      this.modalType = null
    },
    showModel(type, counter, index) {
      this.modalType = type
      this.modalProps = {
        counter: counter.key,
        instance: counter.instance,
        instanceType: counter.instanceType,
        unit: counter.unit,
        color: counter.color,
        objectId: counter.objectId,
        selectedArithmeticOperation: this.selectedArithmeticOperation,
        index: index,
      }

      this.showModelFor = counter
    },
    applyArithmeticOperation(value, counter, index) {
      if (value) {
        this.modalType = 'arithmetic'
        this.selectedArithmeticOperation = value
        this.modalProps = {
          counter: counter.key,
          instance: counter.instance,
          instanceType: counter.instanceType,
          unit: counter.unit,
          color: counter.color,
          objectId: counter.objectId,
          selectedArithmeticOperation: value,
          index: index,
        }
        this.showModelFor = counter
      } else {
        this.applyChartChanges(
          counter,
          {
            selectedArithmeticOperation: undefined,
          },
          index
        )
      }
    },
    applyChartChanges(options, counter, index) {
      this.handleRemoveChart(counter || this.showModelFor)

      setTimeout(() => {
        this.handleAddCounter(
          {
            ...(counter || this.showModelFor),

            ...(options
              ? {
                  overlayOptions: options.overlayOptions,
                }
              : {}),
            ...(options
              ? {
                  selectedArithmeticOperation:
                    options.selectedArithmeticOperation,
                }
              : {}),
          },
          index !== undefined ? index : this.modalProps.index,
          true
        )
      }, 100)
    },
    createMetricExplorerView(formData) {
      const selectedMetricExplorerChart = this.selectedCounters.filter(
        (c) => c && c.key
      )

      if (!selectedMetricExplorerChart.length) {
        setTimeout(() => {
          Bus.$emit('metric-explorer:view-saved')
        }, 500)

        this.$errorNotification({
          message: 'Error',
          description: 'Please add at least one metric for trend analysis',
          key: 'formError',
        })

        return {}
      }

      let metricExplorerMergedCounterContext

      if (this.$refs.metricExplorerProviderRef) {
        metricExplorerMergedCounterContext =
          this.$refs.metricExplorerProviderRef.getAllMergeCountersMap()
      }

      const metricExplorerPreference = {
        timeline: this.timeline,
        chartType: this.chartType,
        granularity: this.deviceTemplateContext?.metricExplorer?.granularity,
        metricExplorerMergedCounterContext,
      }

      return createExplorerApi({
        ...formData,
        ...metricExplorerPreference,
        selectedCharts: selectedMetricExplorerChart,
        objectId: this.monitor?.id,
        objectType: this.monitor?.type,
      })
        .then((res) => {
          // this.selectedView = res

          Bus.$emit('metric-explorer:view-saved', res)

          if (this.$refs.counterSelectionRef) {
            this.$refs.counterSelectionRef.setActiveTab('saved-view')
          }

          return res
        })
        .catch(() => {
          Bus.$emit('metric-explorer:view-saved')
        })
    },

    updateMetricExplorerView() {
      const selectedMetricExplorerChart = this.selectedCounters.filter(
        (c) => c && c.key
      )

      if (!selectedMetricExplorerChart.length) {
        setTimeout(() => {
          Bus.$emit('metric-explorer:view-update')
        }, 500)

        this.$errorNotification({
          message: 'Error',
          description: 'Please add at least one metric for trend analysis',
          key: 'formError',
        })

        return {}
      }

      let metricExplorerMergedCounterContext

      if (this.$refs.metricExplorerProviderRef) {
        metricExplorerMergedCounterContext =
          this.$refs.metricExplorerProviderRef.getAllMergeCountersMap()
      }

      const metricExplorerPreference = {
        timeline: this.timeline,
        chartType: this.chartType,
        granularity: this.deviceTemplateContext?.metricExplorer?.granularity,
      }

      return updateExplorerApi({
        ...(this.selectedView || {}),
        ...metricExplorerPreference,
        selectedCharts: selectedMetricExplorerChart,
        objectId: this.monitor?.id,
        objectType: this.monitor?.type,
        metricExplorerMergedCounterContext,
      })
        .then((res) => {
          Bus.$emit('metric-explorer:view-update', res)

          this.$successNotification({
            message: 'Successful',
            description: this.$message('metric_explorer_update'),
          })
        })
        .catch(() => {
          Bus.$emit('metric-explorer:view-update')
        })
    },
    setSelectedMetrics(savedViewContext) {
      this.selectedView = savedViewContext

      const preferenceContext = {}
      if (savedViewContext) {
        if (savedViewContext?.timeline) {
          preferenceContext.timeline = savedViewContext?.timeline
        }
        if (savedViewContext?.chartType) {
          preferenceContext.chartType = savedViewContext?.chartType
        }
        if (savedViewContext?.granularity) {
          preferenceContext.granularity = savedViewContext?.granularity
        }
      }

      if (this.deviceTemplateContext) {
        this.deviceTemplateContext.setMetricExplorer({
          ...(preferenceContext || {}),
        })
      }

      this.removeAll()

      this.selectedCounters = [
        ...(savedViewContext.selectedCharts || []).map((chart, index) => ({
          color: colors[index],
          counter: chart.counterRawName.replace(/~^/g, '.'),
          counterName: chart.counterRawName,
          dataType: ['Numeric'],
          guid: generateId(),
          isStatusCounter: false,
          key: chart.counterRawName,
          metricPlugins: [],
          name: chart.counterRawName,
          objectId: this.objectId || chart.objectId,
          ...(chart.instanceType ? { instanceType: chart.instanceType } : {}),
          ...(chart.instance ? { instance: chart.instance } : {}),
          ...(chart.overlayOptions
            ? { overlayOptions: chart.overlayOptions }
            : {}),

          ...(chart.selectedArithmeticOperation
            ? { selectedArithmeticOperation: chart.selectedArithmeticOperation }
            : {}),
        })),
        ...this.selectedCounters.slice(
          (savedViewContext.selectedCharts || []).length
        ),
      ]
    },
    onCreateMetricExplorerView() {
      this.removeAll()
      this.selectedView = undefined
    },
  },
}
</script>
<style>
.metric-explorer-bg {
  background-color: var(--page-background-color) !important;
}
</style>
