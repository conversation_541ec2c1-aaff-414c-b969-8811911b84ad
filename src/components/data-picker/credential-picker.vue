<template>
  <SelectedItemPills v-if="disabled" :value="resolvedValue" v-bind="$attrs" />
  <div v-else class="flex items-center">
    <template v-if="!colSizeMap">
      <FlotoDropdownPicker
        id="credential-profile-picker-id"
        :options="options"
        :value="value"
        v-bind="$attrs"
        :disabled="disabled"
        :searchable="searchable"
        :multiple="multiple"
        :disabled-options="disabledOptions"
        @change="handleChange"
        v-on="listeners"
      />

      <MButton
        v-if="allowCreate"
        id="create-credential-btn-id"
        outline
        class="text-right ml-4"
        @click="openCredentialSlider"
      >
        Create Credential Profile
      </MButton>
    </template>

    <template v-else>
      <MCol :size="colSizeMap.pickerSize">
        <FlotoDropdownPicker
          id="credential-profile-picker-id"
          :options="options"
          :value="value"
          v-bind="$attrs"
          :disabled="disabled"
          :searchable="searchable"
          :multiple="multiple"
          :disabled-options="disabledOptions"
          @change="handleChange"
          v-on="listeners"
        />
      </MCol>
      <MCol :size="colSizeMap.btnSize">
        <MButton
          v-if="allowCreate"
          id="create-credential-btn-id"
          outline
          class="text-right ml-4"
          @click="openCredentialSlider"
        >
          Create Credential Profile
        </MButton>
      </MCol>
    </template>

    <FlotoDrawerForm
      v-if="allowCreate"
      id="create-credential-id"
      :open="openDrawer"
      @reset="resetCredential"
      @submit="onCreateCredentialsSubmit"
      @cancel="hideDrawer"
    >
      <template v-slot:header> Create Credential Profile </template>
      <template v-slot:actions="{ submit, reset, validate }">
        <MRow>
          <span class="mandatory mt-5"
            ><span class="text-secondary-red">*</span> fields are
            mandatory</span
          >
          <MCol class="text-right">
            <MButton id="reset-credential-btn" variant="default" @click="reset">
              Reset
            </MButton>
            <CredentialProfileTest
              :context="credentialsEntry"
              :validate="validate"
            />
            <MButton
              id="create-credential-profile-btn-id"
              class="ml-2"
              :loading="creatingCredential"
              @click="submit"
            >
              Create Credentials Profile
            </MButton>
          </MCol>
        </MRow>
      </template>
      <CredentialsForm
        :value="credentialsEntry"
        :available-protocols="availableProtocols"
        :can-change-sub-type="Boolean(subType)"
        :reset-form="resetCredentialForm"
      />
    </FlotoDrawerForm>
  </div>
</template>

<script>
import CredentialsForm from '@modules/settings/network-discovery/components/credential-profile-form.vue'
import { createCredentialProfileApi } from '@modules/settings/network-discovery/credential-profile-api'
import { customCredentialFilter } from '@modules/settings/network-discovery/helpers/credential-profile'
import CredentialProfileTest from '@modules/settings/network-discovery/components/credential-profile-test.vue'
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import { authComputed } from '@state/modules/auth'

export default {
  name: 'CredentialPicker',
  components: { CredentialsForm, CredentialProfileTest, SelectedItemPills },
  inject: {
    credentialContext: { default: { options: new Map() } },
  },
  model: {
    event: 'change',
  },
  props: {
    value: { type: [Number, Array], default: undefined },
    protocolFilter: { type: [Array, String], default: undefined },
    multiple: { type: Boolean, default: false },
    subType: { type: [Array, String], default: undefined },
    defaultFormData: {
      type: Object,
      default() {
        return {}
      },
    },
    availableProtocols: {
      type: Array,
      default() {
        return []
      },
    },
    allowCreate: { type: Boolean, default: false },
    disabled: {
      type: Boolean,
      default: false,
    },
    // eslint-disable-next-line
    searchable: { type: Boolean, default: true },

    customFilterType: {
      type: String,
      default: undefined,
    },
    colSizeMap: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      creatingCredential: false,
      openDrawer: null,
      credentialsEntry: { ...this.defaultFormData },
    }
  },
  computed: {
    ...authComputed,
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.credentialContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .filter(Boolean)
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
    },
    options() {
      if (this.disabled) {
        return
      }

      const protocolFilter = this.protocolFilter
      let options = Array.from(this.credentialContext.options.values())

      if (this.customFilterType) {
        options = customCredentialFilter(options, this.customFilterType)
      }

      if (protocolFilter) {
        return options?.filter((item) => {
          if (!protocolFilter) {
            return true
          }
          return Array.isArray(protocolFilter)
            ? protocolFilter.indexOf(item.protocol) >= 0
            : protocolFilter === item.protocol ||
                item.protocol
                  .toLowerCase()
                  .indexOf(protocolFilter.toLowerCase()) >= 0
        })
      }

      return options
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
    disabledOptions() {
      if (this.disabled) {
        return []
      }
      const typeFilter = this.subType
      const items = Array.from(this.credentialContext.options.values())
      if (typeFilter) {
        return items.filter((i) => i.type !== typeFilter).map(({ key }) => key)
      }
      return items
    },
  },
  methods: {
    handleChange(value) {
      this.$emit('change', value)
    },
    openCredentialSlider() {
      this.openDrawer = true
      this.credentialsEntry = { ...this.defaultFormData }
    },
    hideDrawer() {
      this.openDrawer = null
      this.credentialsEntry = {}
    },
    resetCredential(event) {
      this.credentialsEntry = { ...this.defaultFormData, ...(event || {}) }
    },
    resetCredentialForm(formContext) {
      if (formContext) {
        this.credentialsEntry = { ...formContext }
      }
    },
    async onCreateCredentialsSubmit() {
      this.creatingCredential = true
      try {
        const profile = await createCredentialProfileApi(
          this.credentialsEntry,
          this.sessionId
        )
        this.creatingCredential = false
        this.openDrawer = null
        if (this.credentialContext.add) {
          this.credentialContext.add({
            key: profile.id,
            name: profile.credentialProfileName,
            protocol: profile.credentialProfileProtocol,
            type: profile.subType,
            ...(profile.credentialProfileProtocol === this.$constants.SSH
              ? {
                  isCliEnable: profile.isCliEnable,
                }
              : {}),
          })
        }
        if (this.multiple) {
          this.$emit('change', [...(this.value || []), profile.id])
        } else {
          this.$emit('change', profile.id)
        }
      } catch (e) {
        this.creatingCredential = false
      }
    },
  },
}
</script>
