<template>
  <SelectedItemPills
    v-if="disabled"
    :value="resolvedValue"
    v-bind="$attrs"
    :wrap="'wrap' in $attrs ? $attrs.wrap : true"
  />
  <span v-else class="create-group">
    <a
      v-if="allowCreate"
      id="create-new-group"
      class="text-right link-label"
      @click="openGroupSlider"
    >
      Create Group
    </a>
    <HierarchyPicker
      :value="value"
      name-property="groupName"
      :multiple="multiple"
      :disabled="disabled"
      :included-options="includedGroupsByName"
      v-bind="$attrs"
      :options="options"
      @change="handleChange"
    >
      <template v-slot:before-scroll-view>
        <div
          v-if="multiple && (options || []).length && !disabledSelectAll"
          class="w-full"
        >
          <span style="margin-left: 42px">
            <MCheckbox :checked="isAllGroupSelected" @change="selectAllGroup"
              >Select All
            </MCheckbox>
          </span>

          <MDivider class="mb-0 mt-2" />
        </div>
      </template>
    </HierarchyPicker>

    <FlotoDrawerForm
      v-if="allowCreate"
      :open="openDrawer"
      @submit="onCreateGroupSubmit"
      @cancel="hideDrawer"
      @reset="resetGroupForm"
    >
      <template v-slot:header> Create Group </template>
      <template v-slot:actions="{ submit, reset }">
        <MRow :gutter="0">
          <span class="mandatory"
            ><span class="text-secondary-red">*</span> fields are
            mandatory</span
          >
          <MCol class="text-right">
            <span>
              <MButton
                id="reset-group-btn"
                variant="default"
                class="mr-2"
                @click="reset"
              >
                Reset
              </MButton>
            </span>
            <MButton
              id="submit-group-btn"
              :loading="creatingGroup"
              @click="submit"
            >
              Create Group
            </MButton>
          </MCol>
        </MRow>
      </template>
      <GroupForm :value="groupEntity" />
    </FlotoDrawerForm>
  </span>
</template>

<script>
import UniqBy from 'lodash/uniqBy'

import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import { createGroupApi } from '@modules/settings/group-settings/group-api'
import HierarchyPicker from './hierarchy-picker.vue'
import GroupForm from '@modules/settings/group-settings/components/group-form.vue'

export default {
  name: 'GroupPicker',
  components: { GroupForm, HierarchyPicker, SelectedItemPills },
  inject: { groupContext: { default: { options: new Map() } } },
  model: {
    event: 'change',
  },
  props: {
    value: { type: [Number, Array, Object], default: undefined },
    multiple: { type: Boolean, default: false },
    allowCreate: { type: Boolean, default: false },
    disabled: {
      type: Boolean,
      default: false,
    },
    includedGroupsByName: {
      type: Array,
      default: undefined,
    },
    showCustomOnly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      openDrawer: null,
      groupEntity: {},
      creatingGroup: false,
    }
  },
  computed: {
    disabledSelectAll() {
      return this.includedGroupsByName?.length
    },
    resolvedValue() {
      if (!this.disabled) {
        return
      }
      const optionsMap = this.groupContext.options
      const value = Array.isArray(this.value) ? this.value : [this.value]
      return value
        .map((v) => (optionsMap.has(v) ? optionsMap.get(v).name : undefined))
        .filter(Boolean)
    },
    options() {
      if (this.disabled) {
        return []
      }

      if (this.showCustomOnly) {
        const groups = Array.from(this.groupContext.options.values()) || []
        const customGroups = groups.filter((g) => g.isCustom)
        const parentIds = customGroups.map((g) => g.parentId).filter(Boolean)
        let finalResult = []

        parentIds.forEach((id) => {
          finalResult.push(
            ...this.addGroupsRecursive(this.groupContext.options, id)
          )
        })

        finalResult = UniqBy(finalResult, 'key').map((g) => {
          return {
            ...g,
            ...(!g.isCustom ? { disabled: true } : {}),
          }
        })

        // UniqBy(
        //   parentIds.reduce((acc, id) => {
        //     return [
        //       ...acc,
        //       ...this.addGroupsRecursive(this.groupContext.options, id),
        //     ]
        //   }, []),
        //   'key'
        // ).map((g) => {
        //   return {
        //     ...g,
        //     ...(!g.isCustom ? { disabled: true } : {}),
        //   }
        // })
        return UniqBy(customGroups.concat(finalResult), 'key')
      }
      return Array.from(this.groupContext.options.values())
    },
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
    includedGroupsOptionsByName() {
      const options = this.options
      if (this.includedGroupsByName) {
        options.filter((group) =>
          this.includedGroupsByName.includes(group.groupName || group.name)
        )
      }
      if (this.showCustomOnly) {
        options.filter((g) => g.isCustom)
      }

      return options
    },

    isAllGroupSelected() {
      if (this.showCustomOnly) {
        return (
          Array.isArray(this.value) &&
          this.options.filter((g) => !g.disabled).length === this.value.length
        )
      }
      return Array.isArray(this.value)
        ? this.includedGroupsOptionsByName.length === this.value.length
        : false
    },
  },
  methods: {
    openGroupSlider() {
      this.openDrawer = true
    },
    hideDrawer() {
      this.openDrawer = null
      this.groupEntity = {}
    },
    resetGroupForm() {
      this.groupEntity = {}
    },
    handleChange(change) {
      if (change) {
        if (this.multiple) {
          this.$emit('change', change)
        } else {
          this.$emit('change', change.key)
        }
      }
    },
    async onCreateGroupSubmit() {
      let checkRules =
        (this.groupEntity.ipRange !== undefined &&
          this.groupEntity.ipRange.length) ||
        (this.groupEntity.types !== undefined &&
          this.groupEntity.types.length) ||
        (this.groupEntity.vendor !== undefined &&
          this.groupEntity.vendor.length) ||
        (this.groupEntity.nameContains !== undefined &&
          this.groupEntity.nameContains.length)
      if (
        this.groupEntity.autoAssign !== undefined &&
        this.groupEntity.autoAssign === true &&
        checkRules !== undefined &&
        !checkRules
      ) {
        this.$errorNotification({
          message: 'Error',
          description: 'At least one group auto assignment rule is required',
        })
        return
      }
      this.creatingGroup = true
      try {
        const result = await createGroupApi(this.groupEntity)
        this.creatingGroup = false
        this.openDrawer = null
        if (this.groupContext.refresh) {
          this.groupContext.refresh()
          if (this.multiple) {
            this.$emit('change', [...(this.value || []), result.id])
          } else {
            this.$emit('change', result.id)
          }
        }
      } catch (e) {
        this.creatingGroup = false
      }
    },
    selectAllGroup(event) {
      let allGroups = this.options
      if (this.showCustomOnly) {
        allGroups = allGroups.filter((g) => !g.disabled)
      }

      this.$emit('change', event ? allGroups.map((g) => g.key) : [])
    },

    addGroupsRecursive(parentIdMap, parentId, result = []) {
      // Check if parent ID exists in the map
      if (!parentIdMap.has(parentId)) {
        return result
      }

      const group = parentIdMap.get(parentId)
      // Add the current group to the result
      result.push(group)

      // Check if parent ID is falsyvalue (indicating root group)
      if (!group.parentId) {
        return result
      }

      // Recursively add parent groups
      return this.addGroupsRecursive(parentIdMap, group.parentId, result)
    },
  },
}
</script>
