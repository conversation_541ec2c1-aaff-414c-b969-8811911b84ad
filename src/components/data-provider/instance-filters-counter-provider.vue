<script>
import api from '@api'

export default {
  name: 'InstanceFiltersCounterProvider',
  inject: { counterContext: { default: { options: new Map() } } },

  props: {
    instance: {
      type: String,
      default: undefined,
    },
    showLoader: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      result: {},
      loading: true,
    }
  },
  computed: {
    filterCounters() {
      return this.result?.filters?.map((f) => ({
        key: f,
        name: f,
      }))
    },
    instanceArray() {
      return this.result?.counters || []
    },
    counters() {
      if (this.instance && this.instance === 'monitor') {
        return Array.from(this.counterContext.options.values()).filter(
          (c) => !c.instanceType
        )
      }
      return Array.from(this.counterContext.options.values()).filter((c) =>
        this.instanceArray.includes(c.instanceType)
      )
    },
  },

  watch: {
    instance: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.fetchCounters(newVal)
        } else {
          this.result = {}
          this.loading = false
        }
      },
    },
  },

  methods: {
    fetchCounters(instance) {
      this.loading = true

      return api
        .get('/misc/counters', {
          params: {
            filter: {
              instance: this.instance,
            },
          },
        })
        .then((res) => {
          this.result = res.result
          this.$emit('loaded', res.result)
          this.loading = false
        })
    },
  },

  render(h) {
    if (this.showLoader) {
      if (this.loading) {
        return h('div', { class: 'flex flex-1 items-center justify-center' }, [
          h('MLoader'),
        ])
      }
    }
    return this.$scopedSlots.default({
      loading: this.loading,
      filterCounters: this.filterCounters,
      counters: this.counters,
    })
  },
}
</script>
