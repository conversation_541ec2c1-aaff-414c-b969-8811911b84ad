import FindIndex from 'lodash/findIndex'
import { generateId } from '@utils/id'
import { FILTER_CONDITION_DEFAULT_DATA } from './constants'
import { transformWidgetForServer } from './translator'

export default class WidgetContextBuilder {
  _widget = undefined

  addGroup(groupType) {
    this._widget = {
      widgetProperties: {},
      ...this._widget,
      groups: [
        ...((this._widget || {}).groups || []),
        {
          type: groupType,
          filters: {},
        },
      ],
    }
    return this
  }

  appendToGroup(groupType, obj) {
    const groupIndex = FindIndex(this._widget.groups, { type: groupType })
    if (groupIndex !== -1) {
      this._widget = {
        ...this._widget,
        groups: [
          ...this._widget.groups.slice(0, groupIndex),
          { ...this._widget.groups[groupIndex], ...obj },
          ...this._widget.groups.slice(groupIndex + 1),
        ],
      }
    }
    return this
  }

  checkHasGroup() {
    if (!this._widget) {
      throw new Error('please add group first')
    }
  }

  setTimeline(timeline) {
    this.checkHasGroup()
    this._widget.timeRange = timeline
    return this
  }

  addCounterToGroup({
    counter,
    aggrigateFn,
    entityType,
    entities,
    type,
    entityKeys,
    statisticalFunc,
    additionalDataForCounter,
  }) {
    this.checkHasGroup()
    let group = this._widget.groups[0]
    let groupIndex = 0
    if (type) {
      groupIndex = FindIndex(this._widget.groups, { type })
      if (groupIndex !== -1) {
        group = this._widget.groups[groupIndex]
      } else {
        groupIndex = 0
      }
    }
    const updatedGroup = {
      ...group,
      counters: [
        ...(group.counters || []),
        {
          aggrigateFn,
          entityKeys,
          counter: { key: counter },
          ...(entityType && entities
            ? {
                target: {
                  entityType,
                  entities,
                },
              }
            : {}),
          ...(statisticalFunc ? { arithmeticOperation: statisticalFunc } : {}),

          ...(additionalDataForCounter
            ? {
                additionalDataForCounter,
              }
            : {}),
        },
      ],
    }
    this._widget = {
      ...this._widget,
      groups: [
        ...this._widget.groups.slice(0, groupIndex),
        updatedGroup,
        ...this._widget.groups.slice(groupIndex + 1),
      ],
    }
    return this
  }

  addEntities(entityType, entities) {
    this.checkHasGroup()

    const group = this._widget.groups[0]
    if (['log', 'flow', 'alert'].includes(group.type)) {
      group.target = { entityType, entities }
    } else {
      let counters = group.counters || []
      counters = counters.map((c) => {
        if (!c.target) {
          c.target = {
            entityType,
            entities,
          }
        }
        return c
      })
      group.counters = counters
    }
    this._widget.groups = [group]
    return this
  }

  addInstance(instanceType, instance) {
    this.addPreFilterGroup({
      key: generateId(),
      condition: 'and',
      inclusion: 'include',
      conditions: [
        {
          key: generateId(),
          operand: instanceType,
          operator: '=',
          value: instance,
        },
      ],
    })
    return this
  }

  setPreFilter(filter) {
    this.checkHasGroup()

    const group = this._widget.groups[0]
    group.filters = {
      ...(group.filters || {}),
      pre: filter,
    }
    return this
  }

  setPostFilter(filter) {
    this.checkHasGroup()

    const group = this._widget.groups[0]
    group.filters = {
      ...(group.filters || {}),
      post: filter,
    }
    return this
  }

  addPreFilterGroup(filter, condition = 'and', type) {
    this.checkHasGroup()
    let group = this._widget.groups[0]
    let groupIndex = 0
    if (type) {
      groupIndex = FindIndex(this._widget.groups, { type })
      if (groupIndex !== -1) {
        group = this._widget.groups[groupIndex]
      } else {
        groupIndex = 0
      }
    }

    let pre = (group.filters || {}).pre
    if (!pre) {
      pre = { ...FILTER_CONDITION_DEFAULT_DATA, condition, groups: [filter] }
    } else {
      pre.condition = condition
      pre.groups.push(filter)
    }
    group.filters = {
      ...(group.filters || {}),
      pre,
    }
    this._widget.groups = [
      ...this._widget.groups.slice(0, groupIndex),
      group,
      ...this._widget.groups.slice(groupIndex + 1),
    ]
    return this
  }

  addDrilldownFilterGroup(filter, condition = 'and', type) {
    this.checkHasGroup()
    let group = this._widget.groups[0]
    let groupIndex = 0
    if (type) {
      groupIndex = FindIndex(this._widget.groups, { type })
      if (groupIndex !== -1) {
        group = this._widget.groups[groupIndex]
      } else {
        groupIndex = 0
      }
    }

    let drillDownFilters = (group.filters || {}).drillDownFilters
    if (!drillDownFilters) {
      drillDownFilters = {
        ...FILTER_CONDITION_DEFAULT_DATA,
        condition,
        groups: [filter],
      }
    } else {
      drillDownFilters.condition = condition
      drillDownFilters.groups.push(filter)
    }
    group.filters = {
      ...(group.filters || {}),
      drillDownFilters: drillDownFilters,
    }
    this._widget.groups = [
      ...this._widget.groups.slice(0, groupIndex),
      group,
      ...this._widget.groups.slice(groupIndex + 1),
    ]
    return this
  }

  addPostFilterGroup(filter, condition = 'and', type) {
    this.checkHasGroup()
    let group = this._widget.groups[0]
    let groupIndex = 0
    if (type) {
      groupIndex = FindIndex(this._widget.groups, { type })
      if (groupIndex !== -1) {
        group = this._widget.groups[groupIndex]
      } else {
        groupIndex = 0
      }
    }

    let post = (group.filters || {}).post
    if (!post) {
      post = { ...FILTER_CONDITION_DEFAULT_DATA, condition, groups: [filter] }
    } else {
      post.condition = condition
      post.groups.push(filter)
    }
    group.filters = {
      ...(group.filters || {}),
      post,
    }
    this._widget.groups = [
      ...this._widget.groups.slice(0, groupIndex),
      group,
      ...this._widget.groups.slice(groupIndex + 1),
    ]
    return this
  }

  setWidgetProperties(properties) {
    this.checkHasGroup()

    this._widget.widgetProperties = {
      ...(this._widget.widgetProperties || {}),
      ...properties,
    }
    return this
  }

  addPostFilter(filter, condition, type) {
    this.checkHasGroup()
    let group = this._widget.groups[0]
    let groupIndex = 0
    if (type) {
      groupIndex = FindIndex(this._widget.groups, { type })
      if (groupIndex !== -1) {
        group = this._widget.groups[groupIndex]
      } else {
        groupIndex = 0
      }
    }

    let post = (group.filters || {}).post
    if (!post) {
      post = { ...FILTER_CONDITION_DEFAULT_DATA, condition, groups: [filter] }
    } else {
      post.condition = condition
      post.groups.push(filter)
    }
    group.filters = {
      ...(group.filters || {}),
      post: post,
    }
    this._widget.groups = [
      ...this._widget.groups.slice(0, groupIndex),
      group,
      ...this._widget.groups.slice(groupIndex + 1),
    ]
    return this
  }

  addResultBy(keys, type) {
    this.checkHasGroup()
    let group = this._widget.groups[0]
    let groupIndex = 0
    if (type) {
      groupIndex = FindIndex(this._widget.groups, { type })
      if (groupIndex !== -1) {
        group = this._widget.groups[groupIndex]
      } else {
        groupIndex = 0
      }
    }
    const resultBy = Array.isArray(keys)
      ? keys.map((k) => k.toLowerCase())
      : keys.toLowerCase()
    const updatedGroup = {
      ...group,
      resultBy,
    }
    this._widget = {
      ...this._widget,
      groups: [
        ...this._widget.groups.slice(0, groupIndex),
        updatedGroup,
        ...this._widget.groups.slice(groupIndex + 1),
      ],
    }
    return this
  }

  setCategory(category) {
    this.checkHasGroup()

    this._widget = {
      ...this._widget,
      category,
    }
    return this
  }

  setWidgetType(widgetType) {
    this.checkHasGroup()

    this._widget = {
      ...this._widget,
      widgetType,
    }
    return this
  }

  setTimeLine(timeline) {
    this.checkHasGroup()

    this._widget = {
      ...this._widget,
      timeRange: timeline,
    }
    return this
  }
  setGranularity(granularity) {
    this._widget = {
      ...this._widget,
      granularity: granularity,
    }
    return this
  }

  reset() {
    this._widget = undefined
  }

  generateWidgetDefinition(extra = {}) {
    this.checkHasGroup()

    return { ...transformWidgetForServer(this._widget), ...extra }
  }

  setExtraData(extraData = {}) {
    this._widget = {
      ...this._widget,
      joinQueryContext: extraData,
    }
    return this
  }

  getContext() {
    return this._widget
  }
}
