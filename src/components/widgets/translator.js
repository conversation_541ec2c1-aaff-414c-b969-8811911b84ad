import CloneDeep from 'lodash/cloneDeep'
import Uniq from 'lodash/uniq'
import Flatten from 'lodash/flatten'
import Constants from '@constants'
import { generateId } from '@utils/id'
import {
  FILTER_CONDITION_DEFAULT_DATA,
  WidgetTypeConstants,
  HEALTH_GROUP_TYPE,
  MIN_EVENT_COUNT,
  AVAILABLE_GROUP_TYPES,
} from './constants'
import {
  convertTimeLine,
  convertTimeLineForServer,
  transformConditionsForServer,
  transformConditions,
  additionalGroupForStreamWidget,
  additionalDataForCategory,
  additionalDataForGroup,
} from './helper'

import {
  AvailableReportCategories,
  hardcodedGridViewIds,
} from '@/src/modules/report/helpers/report'

export function transformWidget(widget) {
  return {
    id: widget[Constants.ID_PROPERTY],
    isMultiTrendReport: Boolean(widget['multi.trend']),
    enableGridView: widget['enable.grid.view'] === 'yes',
    ...(widget['container.type']
      ? { containerType: widget['container.type'] }
      : {}),
    name: widget['visualization.name'],
    ...([
      WidgetTypeConstants.CHART,
      WidgetTypeConstants.ANOMALY,
      WidgetTypeConstants.FORECAST,
    ].includes(widget['visualization.category'])
      ? {
          granularity: {
            value: widget['visualization.granularity']
              ? widget['visualization.granularity'].split(' ')[0] || 5
              : 5,
            unit: widget['visualization.granularity']
              ? widget['visualization.granularity'].split(' ')[1] || 'm'
              : 'm',
          },
        }
      : {}),
    isAvailabilityWidget: Boolean(
      (widget['visualization.data.sources'] || []).find(
        (g) => g.type === 'availability'
      )
    ),
    description: widget['visualization.description'],
    timeRange: convertTimeLine(widget['visualization.timeline'] || {}),
    usePivotGroupBy: widget['visualization.group.type'] === 'pivot',
    category:
      widget['child.visualization'] === WidgetTypeConstants.HORIZONTAL_TOPN
        ? WidgetTypeConstants.TOPN
        : (widget['container.type'] === 'report' &&
            widget['visualization.data.sources']?.[0]?.type ===
              'hourly.status.flap') ||
          (widget['container.type'] === 'report' &&
            widget['visualization.data.sources']?.[0]?.type ===
              'status.flap') ||
          (widget['container.type'] === 'report' &&
            widget['visualization.data.sources']?.[0]?.type === 'log' &&
            widget['visualization.data.sources']?.[0]?.['data.points']?.every(
              (datapoint) => datapoint.aggregator === ''
            ) &&
            widget['secondery.category'] !==
              WidgetTypeConstants.EVENT_HISTORY) ||
          (widget['container.type'] === 'report' &&
            widget['visualization.data.sources']?.[0]?.type === 'metric' &&
            widget['visualization.data.sources']?.[0]?.['data.points']?.every(
              (datapoint) => datapoint.aggregator === ''
            ) &&
            widget['secondery.category'] === WidgetTypeConstants.POLLING_GRID &&
            widget['visualization.category'] === WidgetTypeConstants.CHART) ||
          hardcodedGridViewIds.includes(widget['report.id'])
        ? WidgetTypeConstants.GRID
        : widget['visualization.type'] === WidgetTypeConstants.METRO_TILE &&
          widget['visualization.data.sources']?.find(
            (g) => g.type === 'policy' && g.category !== 'metric'
          )
        ? WidgetTypeConstants.GAUGE
        : widget['secondery.category'] === WidgetTypeConstants.EVENT_HISTORY
        ? WidgetTypeConstants.EVENT_HISTORY
        : widget['secondery.category'] === WidgetTypeConstants.HEATMAP
        ? WidgetTypeConstants.HEATMAP
        : widget['visualization.category'],

    widgetType: widget['child.visualization']
      ? widget['child.visualization']
      : widget['visualization.secondary.type']
      ? widget['visualization.secondary.type']
      : widget['visualization.type'],

    childVisualization: widget['child.visualization'],
    isPredefined: widget._type === '0',
    groups: (widget['visualization.category'] === WidgetTypeConstants.STREAM
      ? (widget['visualization.data.sources'] || []).filter(
          (g) => g.type === 'policy.flap' || g.type === 'policy'
        )
      : widget['visualization.data.sources'] || []
    ).map((group) => transformWidgetGroup(group, widget)),
    timeRangeInclusive:
      (widget['visualization.timeline'] || {})[
        'visualization.time.range.inclusive'
      ] === 'yes',
    widgetProperties: transformProperties(widget),
    ...(widget.granularity ? { granularity: widget.granularity } : {}),
    hideEmptyWidget: widget['visualization.empty.view'] === 'no',
    ...(widget['report.id'] ? { reportId: widget['report.id'] } : {}),
    primaryResultBy: widget['visualization.result.by'],
    ...(widget['archived']
      ? { showArchivedMonitors: widget['archived'] === 'yes' }
      : {}),

    ...(widget['visualization.extra.columns']
      ? { extraColumns: widget['visualization.extra.columns'] }
      : {}),
    visualizationTags: widget['visualization.tags'],
    eventCount: widget['max.records'],
    ...(widget['is.log.to.report']
      ? { isLogToReport: widget['is.log.to.report'] }
      : {}),
    ...(widget['visualization.timeline.preference']
      ? {
          useWidgetTime: widget['visualization.timeline.preference'] === 'yes',
        }
      : {}),
  }
}

function transformWidgetGroup(group, widget) {
  const useIndividualResultBy = widget['visualization.data.sources']?.length > 1
  let shouldUseRootTarget =
    group.type === 'availability' &&
    widget.category === WidgetTypeConstants.GAUGE
  const rootCounter = group['data.points'][0] || {}
  let counters = (
    group['availability.by'] ? group['availability.by'] : group['data.points']
  )
    .filter((point) => point.aggregator !== 'sparkline')
    .map((point) => ({
      counter: {
        key: point['data.point'],
        ...(point['data.point'].indexOf('~') >= 0
          ? { instanceType: point['data.point'].split('~')[0] }
          : {}),
      },
      key: generateId(),
      ...(point['visualization.result.by']
        ? { resultBy: point['visualization.result.by'][0] }
        : {}),
      target: {
        entityType: shouldUseRootTarget
          ? rootCounter['entity.type']
          : point['entity.type'],
        entities: shouldUseRootTarget ? rootCounter.entities : point.entities,
      },
      aggrigateFn: point.aggregator,
      arithmeticOperation: point['statistical.func'],

      ...(group['availability.by']
        ? {
            filterCounter: group['data.points']
              .map((c) => c['data.point'])
              .filter(Boolean),
          }
        : {}),
    }))

  if (widget['secondery.category'] === WidgetTypeConstants.EVENT_HISTORY) {
    counters = counters.filter((point) => point.counter.key !== 'message')
  }
  const corelatedCounters = (group['correlated.data.points'] || []).map(
    (point) => ({ type: point.type, counter: point['data.point'] })
  )
  let instance
  if (
    [WidgetTypeConstants.ANOMALY, WidgetTypeConstants.FORECAST].includes(
      widget['visualization.category']
    ) ||
    (widget['container.type'] === 'report' &&
      widget['visualization.data.sources']?.[0]?.type === 'metric' &&
      widget['visualization.data.sources']?.[0]?.['data.points']?.every(
        (datapoint) => datapoint.aggregator === ''
      ) &&
      widget['secondery.category'] === WidgetTypeConstants.POLLING_GRID &&
      widget['visualization.category'] === WidgetTypeConstants.CHART)
  ) {
    if (
      group.filters['data.filter'] &&
      group.filters['data.filter'].groups &&
      group.filters['data.filter'].groups.length &&
      group.filters['data.filter'].groups[0].conditions.length
    ) {
      instance = group.filters['data.filter'].groups[0].conditions[0].value
    }
  }
  return {
    type:
      widget.category === WidgetTypeConstants.ACTIVE_ALERT &&
      group.type === 'policy.stream'
        ? 'policy'
        : group.type,
    ...(instance ? { instance } : {}),
    ...(group['join.type'] ? { join: group['join.type'] } : {}),
    ...([
      WidgetTypeConstants.HEATMAP,
      WidgetTypeConstants.ACTIVE_ALERT,
    ].includes(widget['visualization.category'])
      ? {
          resultBy: 'monitor',
        }
      : {}),
    ...(([
      WidgetTypeConstants.HEATMAP,
      WidgetTypeConstants.ACTIVE_ALERT,
    ].includes(widget['visualization.category']) ||
      (widget['container.type'] === 'report' && group.type === 'status.flap') ||
      (widget['container.type'] === 'report' &&
        group.type === 'hourly.status.flap')) &&
    group.status
      ? { status: group.status }
      : {}),
    filters: {
      pre: transformConditions(group.filters['data.filter'] || {}),
      post: transformConditions(group.filters['result.filter'] || {}, true),
      drillDownFilters: transformConditions(
        group.filters['drill.down.filter'] || {}
      ),
    },
    ...(group['object.type']
      ? { instance: { key: group['object.type'] } }
      : {}),
    ...(widget['visualization.result.by']
      ? {
          resultBy:
            ['log', 'flow', 'health', HEALTH_GROUP_TYPE].includes(group.type) &&
            Array.isArray(widget['visualization.result.by'])
              ? widget['visualization.result.by']
              : widget['visualization.result.by'][0],
        }
      : {}),

    ...(useIndividualResultBy &&
    (group['visualization.result.by'] || widget['visualization.result.by'])
      ? {
          resultBy:
            (['log', 'flow'].includes(group.type) &&
            Array.isArray(group['visualization.result.by'])
              ? group['visualization.result.by']
              : group['visualization.result.by']?.[0]) ||
            (['log', 'flow'].includes(group.type) &&
            Array.isArray(widget['visualization.result.by'])
              ? widget['visualization.result.by']
              : widget['visualization.result.by']?.[0]),
        }
      : group['visualization.result.by'] || widget['visualization.result.by']
      ? {
          resultBy:
            widget['visualization.category'] === WidgetTypeConstants.HEATMAP
              ? group['visualization.result.by'] ||
                widget['visualization.result.by']
              : ['log', 'flow', 'health', HEALTH_GROUP_TYPE].includes(
                  group.type
                ) && Array.isArray(widget['visualization.result.by'])
              ? widget['visualization.result.by']
              : widget?.['visualization.result.by']?.[0],
        }
      : {}),
    ...(widget['visualization.timeline'] &&
    widget['visualization.timeline']['receive.timestamp'] === 'yes'
      ? { useReceivedTime: true }
      : {}),
    ...(group.type !== 'alert' && group.type !== 'policy' ? { counters } : {}),
    ...(group.type === 'alert' ||
    group.type === 'policy' ||
    group.type === 'policy.flap' ||
    group.type === 'policy.stream'
      ? {
          ...(group.policies || group['data.points'].policies
            ? {
                alertIds: group.policies
                  ? group.policies
                  : group['data.points'].policies,
              }
            : {}),
          ...(group.tags || group['data.points'].tags
            ? { tags: group.tags ? group.tags : group['data.points'].tags }
            : {}),
          ...(group.severity || group['data.points'].severity
            ? {
                severity: group.severity
                  ? group.severity
                  : group['data.points'].severity,
              }
            : {}),
        }
      : {}),
    ...([
      'alert',
      'log',
      'flow',
      'policy',
      'policy.flap',
      'policy.stream',
    ].includes(group.type) && group['data.points'][0]
      ? {
          category: group.category,
          target: (counters[0] || {}).target,

          // alertBy: group.alertBy,
        }
      : {}),
    corelatedCounters,

    ...(group?.['additional.columns']?.length > 0
      ? { additionalColumns: group['additional.columns'] }
      : {}),

    ...(group?.['time.interval']
      ? { timeInterval: group[['time.interval']] }
      : {}),

    ...(group.category !== 'metric' &&
    widget['visualization.category'] === WidgetTypeConstants.STREAM
      ? {
          counters,
          useExternalCounters: true,
        }
      : {}),
  }
}

function transformProperties(widget) {
  if (
    [
      WidgetTypeConstants.CHART,
      WidgetTypeConstants.ANOMALY,
      WidgetTypeConstants.FORECAST,
    ].includes(widget['visualization.category']) &&
    ![WidgetTypeConstants.EVENT_HISTORY].includes(
      widget['secondery.category']
    ) &&
    !(
      [WidgetTypeConstants.POLLING_GRID].includes(
        widget['secondery.category']
      ) &&
      [WidgetTypeConstants.CHART].includes(widget['visualization.category'])
    )
  ) {
    const properties = widget['visualization.properties']['chart'] || {}

    if (
      (widget['container.type'] === 'report' &&
        widget['visualization.data.sources']?.[0]?.type ===
          'hourly.status.flap') ||
      (widget['container.type'] === 'report' &&
        widget['visualization.data.sources']?.[0]?.type === 'status.flap') ||
      (widget['container.type'] === 'report' &&
        widget['visualization.data.sources']?.[0]?.type === 'log' &&
        widget['visualization.data.sources']?.[0]?.['data.points']?.every(
          (datapoint) => datapoint.aggregator === ''
        ))
    ) {
      const properties = widget['visualization.properties']['grid'] || {}
      return transformGridProperties(properties)
    }
    return transformChartProperties(properties)
  } else if (
    widget['visualization.category'] === WidgetTypeConstants.GAUGE ||
    widget['visualization.type'] === WidgetTypeConstants.KPI_GAUGE ||
    widget['visualization.type'] === WidgetTypeConstants.METRO_TILE
  ) {
    const properties = widget['visualization.properties']['gauge'] || {}
    return transformGaugeProperties(properties)
  } else if (
    widget['visualization.category'] === WidgetTypeConstants.GRID ||
    widget['visualization.category'] === WidgetTypeConstants.STREAM ||
    widget['visualization.category'] === WidgetTypeConstants.ACTIVE_ALERT ||
    widget['secondery.category'] === WidgetTypeConstants.EVENT_HISTORY ||
    ([WidgetTypeConstants.POLLING_GRID].includes(
      widget['secondery.category']
    ) &&
      [WidgetTypeConstants.CHART].includes(widget['visualization.category'])) ||
    (widget['container.type'] === 'report' &&
      widget['visualization.data.sources']?.[0]?.type === 'status.flap') ||
    (widget['container.type'] === 'report' &&
      widget['visualization.data.sources']?.[0]?.type === 'hourly.status.flap')
  ) {
    const properties = widget['visualization.properties']['grid'] || {}
    return transformGridProperties(properties)
  } else if (
    widget['visualization.category'] === WidgetTypeConstants.TOPN &&
    widget['secondery.category'] !== WidgetTypeConstants.HEATMAP
  ) {
    return transformTopNProperties(widget['visualization.properties'], widget)
  } else if (
    widget['visualization.category'] === WidgetTypeConstants.HEATMAP ||
    widget['secondery.category'] === WidgetTypeConstants.HEATMAP
  ) {
    const properties = widget['visualization.properties']['map'] || {}
    return {
      showCounts: properties['show.counts'] === 'yes',
      selectedColorPalette: properties['color.palette'],
    }
  } else if (widget['visualization.category'] === WidgetTypeConstants.SANKEY) {
    return {}
  } else if (
    widget['visualization.category'] === WidgetTypeConstants.FREE_TEXT
  ) {
    const properties = widget['visualization.properties']['free.text'] || {}
    return transformFreeTextProperties(properties)
  }
  return {}
}

function transformColumn(column) {
  const style = column.style || {}
  return {
    rawName: column.name,
    name: column.name.replace(/[~^]/g, '.'),
    displayName:
      column.type === 'sparkline' ? column.title || ' ' : column.title,

    ...(column.type === 'sparkline'
      ? {
          headerCell: 'sparklineHeader',
        }
      : {}),
    hidden: column.show === 'no',
    sortable: column.sortable !== 'no',
    resizable: column.resizable !== 'no',
    selectable: column.selectable !== 'no',
    alias: column.alias,
    disable: column.disable !== 'no',
    orderable: column.orderable !== 'no',
    classes: style.classes,
    orderIndex: column.position,
    ...(column.cellRender ? { cellRender: column.cellRender } : {}),

    align: column.alignment,
    type: column.type,
    renderMethod: style['render.method'],
    width: style['width.percent'] && `${style['width.percent']}%`,
    ...(style['inline.chart'] && style['inline.chart'].type
      ? {
          slot: style['inline.chart'].type,
          slotClasses: style['inline.chart'].classes || [],
        }
      : {}),
    ...(column.computed === 'yes'
      ? {
          slot: 'computed',
          formula: {
            ...(column.formula || {}),
          },
        }
      : {}),
    ...(style.icon
      ? {
          iconName: style.icon.name,
          iconPosition: style.icon.placement || 'prefix',
          iconClasses: style.icon.classes,
          iconConditions: style.icon.conditions,
        }
      : {}),
    colorConfig: (style['color.conditions'] || []).map((item) => ({
      operator: item['operator'],
      value: item['value'],
      colorType: item['color.type'],
      color: item.color,
    })),
    valueDisplayConfig: (style['value.display.operations'] || []).map(
      (item) => ({
        operator: item['operator'],
        comparedValue: item['value.to.compare'],
        operatorType: item['operation.type'],
        value: item.value,
      })
    ),
  }
}

function transformChartProperties(properties) {
  return {
    sortingSetting: {
      topCount: (properties.sorting || {}).limit || 10,
      direction: (properties.sorting || {}).order,
    },
    styleSetting: {
      rotation: properties['rotation.angle'],
      legendEnabled: properties['chart.legend'] === 'yes',
      verticalLegend: properties['vertical.legend'] === 'yes',
      lineWidth: properties['line.width'] || 2,
      // ellipsisEnabled: false,
      pieDataLabelsEnabled: properties['chart.label'] === 'yes',

      xAxisTitle: (properties['axis.titles'] || {}).x,
      yAxisTitle: (properties['axis.titles'] || {}).y,
      zAxisTitle: (properties['axis.titles'] || {}).z,
      chartOptions: properties['highchart.settings'] || {},
      dataLabelEnabled: properties['data.label.enabled'] === 'yes',
      pointsEnabled: properties['points.enabled'] === 'yes',
      pointSize: properties['point.size'] || 4,
      lineStyle: properties['line.style'] || 'solid',
      ...(properties['line.style'] === 'dash'
        ? {
            dashPattern: properties['dash.pattern'] || '3, 3',
          }
        : {}),
      ...(properties['stack.groups'] && properties['stack.groups'].length
        ? {
            stacks: properties['stack.groups'].map((group, index) => ({
              group: `group-${index}`,
              counters: group['data.points'],
            })),
          }
        : {}),

      markerProperty: properties?.['marker.property']?.length
        ? properties['marker.property'].map((marker) => ({
            markerType: marker['marker.type'],
            markerThreshold:
              marker['marker.type'] === 'line'
                ? marker['marker.threshold']
                : undefined,
            start:
              marker['marker.type'] === 'range'
                ? marker['marker.threshold']?.[0]
                : undefined,
            end:
              marker['marker.type'] === 'range'
                ? marker['marker.threshold']?.[1]
                : undefined,
            markerColor: marker['marker.color'],
            markerLineType: marker['marker.line.type'],
            markerLabel: marker['marker.label'],
            key: generateId(),
          }))
        : [{ key: generateId() }],
    },
  }
}

function transformGridProperties(properties) {
  return {
    searchable: properties.searchable !== 'no',
    allowColumnSelection: properties['column.selection'] === 'yes',
    useWidgetColumnsOnly:
      properties['visualization.grid.properties.required'] === 'yes',

    showHeader: properties['header'] !== 'no',
    view: properties['view'],
    style: properties.style || {},
    layout: properties.layout,
    layoutSorting: properties['layout.sorting'],
    headerStyle: {
      fontSize: (properties.style || {})['header.font.size'] || 'small',
    },
    columnSettings: ((properties || {}).columns || []).map((c, index) =>
      transformColumn({ ...c, position: c.position || index + 1 })
    ),
  }
}

function transformTopNProperties(properties, widget) {
  const chartProperties = {
    headerStyle: {
      fontSize:
        ((properties.grid || {}).style || {})['header.font.size'] || 'small',
    },
    styleSetting: transformChartProperties(properties.chart || {}).styleSetting,
    sortingSetting:
      properties.chart && properties.chart.sorting
        ? {
            topCount: properties.chart.sorting.limit,
            direction: properties.chart.sorting.order,
            column: properties.chart.sorting.column.replace(/\^/g, '.'),
          }
        : {},
  }
  if (widget['visualization.type'] === WidgetTypeConstants.GRID) {
    const sparklineCounter = (
      ((widget['visualization.data.sources'] || [])[0] || {})['data.points'] ||
      []
    ).find((point) => point.aggregator === 'sparkline')
    let sparklineType = 'sparkline'
    let sparklineColor = '#099dd9'
    if (sparklineCounter) {
      const column = properties.grid.columns.find(
        (c) =>
          c.name.replace(/\.sparkline$/, '') === sparklineCounter['data.point']
      )
      if (column) {
        sparklineType =
          ((column.style || {})['inline.chart'] || {}).type || 'sparkline'
        sparklineColor =
          ((column.style || {})['inline.chart'] || {}).color || '#099dd9'
      }
    }
    return {
      ...(widget['child.visualization'] === WidgetTypeConstants.HORIZONTAL_TOPN
        ? chartProperties
        : {}),
      ...transformGridProperties(properties.grid),
      sortingSetting: {
        topCount: properties.grid.sorting.limit,
        direction: properties.grid.sorting.order,
        column: properties.grid.sorting.column.replace(/\^/g, '.'),
        ...(sparklineCounter
          ? {
              showSparklineChart: true,
              sparklineChartType: sparklineType,
              sparklineColor,
            }
          : {}),
      },
    }
  } else {
    return chartProperties
  }
}

function transformGaugeProperties(properties) {
  const style = properties.style || {}
  const criticalColor = (style['color.conditions'] || [])[0] || {}
  const majorColor = (style['color.conditions'] || [])[1] || {}
  const warningColor = (style['color.conditions'] || [])[2] || {}
  return {
    layout: style.layout,
    drilldown: properties['drill.down'],
    view: style.type || 'number',
    fontSize: style['font.size'] || 'medium',
    textAlign: style['text.align'] || 'left',
    titleText: properties['title.text'],
    legendEnabled: style['chart.legend'] === 'yes',
    pieDataLabelsEnabled: style['chart.label'] === 'yes',
    classes: style.classes,
    severityCounter: style['color.data.point'],
    criticalColor: {
      color: criticalColor.color || '#f04e3e',
      condition: criticalColor.operator,
      conditionValue: criticalColor.value || 0,
    },
    majorColor: {
      color: majorColor.color || '#f58518',
      condition: majorColor.operator,
      conditionValue: majorColor.value || 0,
    },
    warningColor: {
      color: warningColor.color || '#f5bc18',
      condition: warningColor.operator,
      conditionValue: warningColor.value || 0,
    },
    ...(style.icon
      ? {
          iconName: style.icon.name,
          iconPosition: style.icon.placement,
          classes: style.icon.classes,
        }
      : {}),
    ...(properties.header
      ? {
          header: {
            title: properties.header.title,
            ...(properties.header.style
              ? {
                  style: {
                    fontSize: properties.header.style['font.size'],
                    icon: properties.header.style.icon,
                  },
                }
              : {}),
            counters: (properties.header['data.points'] || []).map((c) => ({
              title: c.label,
              counter: c.value,
            })),
          },
        }
      : {}),
    ...(properties.footer
      ? {
          footer: {
            title: properties.footer.title,
            ...(properties.footer.style
              ? {
                  style: {
                    fontSize: properties.footer.style['font.size'],
                  },
                }
              : {}),
            counters: (properties.footer['data.points'] || []).map((c) => ({
              title: c.label,
              counter: c.value,
              slot: c.type,
            })),
          },
        }
      : {}),
  }
}

function transformFreeTextProperties(properties) {
  return {
    fontSize:
      properties['font.size'] === 'auto' ? 'auto' : properties['font.size'],
    textAlign: properties['text.align'],
    fontColor: properties.color || 'default',
    textToDisplay: properties['display.text'],
  }
}

/**
 *
 * SERVER format conversion starts here
 *
 */

export function transformWidgetForServer(widget) {
  const granularity = widget.granularity
  return {
    ...((widget.reportId || widget.reportId === 0) && widget.isStaticPreview
      ? {
          'report.id': widget.reportId,
          ...(widget.visualizationTags
            ? { 'visualization.tags': widget.visualizationTags }
            : {}),

          ...(widget.executeScript
            ? {
                'report.type': 'custom.script',
                'visualization.type': 'custom.script',
                'report.script.type': widget.reportScriptType,
                'report.script': widget.reportScript,
                'visualization.result.by': widget.customResultby,
                'entity.type': (widget.sourceSelection || {}).entityType,
                entities: (widget.sourceSelection || {}).entities,
                ...(widget.timeRange
                  ? {
                      'visualization.timeline': {
                        ...convertTimeLineForServer(widget.timeRange || {}),
                        ...((widget.groups &&
                          (widget.groups[0] || {}).useReceivedTime) ||
                        (widget.timeRange || {}).useReceivedTime
                          ? { 'receive.timestamp': 'yes' }
                          : {}),
                        'visualization.time.range.inclusive':
                          widget.timeRangeInclusive ? 'yes' : 'no',
                      },
                    }
                  : {}),
              }
            : {}),
        }
      : {
          id: widget.id || -1,
          ...(widget.useWidgetTime
            ? {
                'visualization.timeline.preference': 'yes',
              }
            : { 'visualization.timeline.preference': 'no' }),
          ...(widget.containerType
            ? { 'container.type': widget.containerType }
            : {}),

          'visualization.name': widget.name,
          'visualization.description': widget.description,
          ...([
            WidgetTypeConstants.CHART,
            WidgetTypeConstants.ANOMALY,
            WidgetTypeConstants.FORECAST,
          ].includes(widget.category) && granularity
            ? {
                'visualization.granularity': `${granularity.value} ${granularity.unit}`,
              }
            : {}),
          ...([
            WidgetTypeConstants.ANOMALY,
            WidgetTypeConstants.FORECAST,
          ].includes(widget.category) && granularity
            ? {
                'visualization.result.type': 1,
              }
            : {}),
          ...(widget.timeRange
            ? {
                'visualization.timeline': {
                  ...convertTimeLineForServer(widget.timeRange || {}),
                  ...((widget.groups &&
                    (widget.groups[0] || {}).useReceivedTime) ||
                  (widget.timeRange || {}).useReceivedTime
                    ? { 'receive.timestamp': 'yes' }
                    : {}),
                  'visualization.time.range.inclusive':
                    widget.timeRangeInclusive ? 'yes' : 'no',
                },
              }
            : {}),
          'visualization.category': widget.customDefinationCategory
            ? widget.customDefinationCategory
            : widget.category === WidgetTypeConstants.GAUGE &&
              widget.widgetType === WidgetTypeConstants.METRO_TILE &&
              widget?.groups?.find(
                (g) =>
                  g.type === 'policy' &&
                  !['metric', AVAILABLE_GROUP_TYPES.NETROUTE_METRIC].includes(
                    g.category
                  )
              )
            ? WidgetTypeConstants.GRID
            : widget.category === WidgetTypeConstants.EVENT_HISTORY
            ? WidgetTypeConstants.CHART
            : widget.category === WidgetTypeConstants.HEATMAP &&
              widget?.groups?.find((g) => g.type === 'metric')
            ? WidgetTypeConstants.TOPN
            : widget.category,

          'visualization.type':
            widget.category === WidgetTypeConstants.GRID
              ? widget.widgetType || WidgetTypeConstants.GRID
              : widget.category === WidgetTypeConstants.SANKEY
              ? WidgetTypeConstants.GRID
              : widget.category === WidgetTypeConstants.HEATMAP &&
                widget?.groups?.find((g) => g.type === 'metric')
              ? WidgetTypeConstants.GRID
              : widget.widgetType,
          'visualization.data.sources': [
            ...widget.groups.map((group) =>
              transformWidgetGroupForServer(group, widget)
            ),
            ...(widget.category === WidgetTypeConstants.STREAM &&
            widget?.groups?.find((g) => g.category === 'metric')
              ? additionalGroupForStreamWidget(
                  widget.category,
                  widget.groups[0]
                )
              : []),
          ],
          ...(widget.widgetType === WidgetTypeConstants.HORIZONTAL_TOPN
            ? {
                'visualization.type': 'Grid',
                'visualization.child.categories': ['Grid', 'Chart'],
                'child.visualization': 'Horizontal TopN',
                'visualization.granularity': { value: 5, unit: 'm' },
              }
            : {}),

          ...(widget.category === WidgetTypeConstants.HEATMAP &&
          widget?.groups?.find((g) => g.type === 'metric')
            ? {
                'visualization.type': 'Grid',
                'child.visualization': widget.widgetType,
                'secondery.category': widget.category,
              }
            : {}),

          ...(widget.category === WidgetTypeConstants.TOPN &&
          widget.widgetType === WidgetTypeConstants.TREE_VIEW
            ? {
                'visualization.type':
                  widget.groups[0].type === 'metric'
                    ? WidgetTypeConstants.GRID
                    : WidgetTypeConstants.PIE,
                'child.visualization': WidgetTypeConstants.TREE_VIEW,
              }
            : {}),
          ...(widget.category === WidgetTypeConstants.TOPN &&
          widget.widgetType === WidgetTypeConstants.PACKED_BUBBLE_CHART
            ? {
                'visualization.type': WidgetTypeConstants.PIE,
                'child.visualization': WidgetTypeConstants.PACKED_BUBBLE_CHART,
              }
            : {}),
          ...(widget.category === WidgetTypeConstants.TOPN &&
          ![
            WidgetTypeConstants.PACKED_BUBBLE_CHART,
            WidgetTypeConstants.HORIZONTAL_TOPN,
            WidgetTypeConstants.TREE_VIEW,
          ].includes(widget.widgetType) &&
          widget.childVisualization
            ? {
                'child.visualization': '',
              }
            : {}),
          'visualization.properties': transformPropertiesForServer(widget),
          ...((widget.groups || []).length > 0 && widget.groups[0].resultBy
            ? widget.category === WidgetTypeConstants.CHART &&
              (widget.groups[0].counters || []).length > 1 &&
              widget.groups[0].type === 'availability'
              ? {}
              : {
                  'visualization.result.by': Array.isArray(
                    widget.groups[0].resultBy
                  )
                    ? widget.groups[0].resultBy
                    : [widget.groups[0].resultBy],
                }
            : { 'visualization.result.by': [] }),
          ...(widget.granularity ? { granularity: widget.granularity } : {}),
          ...(widget.hideEmptyWidget
            ? {
                'visualization.empty.view': widget.hideEmptyWidget
                  ? 'no'
                  : 'yes',
              }
            : {}),

          ...([WidgetTypeConstants.EVENT_HISTORY].includes(widget.category)
            ? { ...(additionalDataForCategory[widget.category] || {}) }
            : {}),

          ...([WidgetTypeConstants.STREAM].includes(widget.category) &&
          widget?.groups?.find((g) => g?.category === 'metric')
            ? {
                ...(additionalDataForCategory[widget.category] || {}),
                category: widget?.groups?.[0]?.category,
              }
            : {}),

          ...([WidgetTypeConstants.STREAM].includes(widget.category) &&
          !['metric'].includes(widget?.groups?.[0]?.category)
            ? {
                ...(additionalDataForCategory['event.stream'] || {}),
                category: widget?.groups?.[0]?.category,
              }
            : {}),

          ...(widget.joinQueryContext ? widget.joinQueryContext : {}),
          ...(widget.showArchivedMonitors
            ? { archived: widget.showArchivedMonitors ? 'yes' : 'no' }
            : {}),
          ...(widget.extraColumns
            ? { 'visualization.extra.columns': widget.extraColumns }
            : {}),

          ...(widget.visualizationTags
            ? {
                'visualization.tags': widget['visualizationTags'],
                ...additionalDataForCategory['tag'],
              }
            : {}),

          ...(widget.reportType === AvailableReportCategories.EVENT_HISTORY
            ? {
                ...additionalDataForCategory[
                  AvailableReportCategories.EVENT_HISTORY
                ],
              }
            : {}),
          ...(widget.reportType === AvailableReportCategories.AVAILABILITY
            ? {
                ...additionalDataForCategory[
                  AvailableReportCategories.AVAILABILITY
                ],
              }
            : {}),

          ...([WidgetTypeConstants.EVENT_HISTORY].includes(widget.category) &&
          ![AvailableReportCategories.EVENT_HISTORY].includes(widget.reportType)
            ? {
                'max.records': Number(widget.eventCount) || MIN_EVENT_COUNT,
              }
            : {}),
          ...(widget.category === WidgetTypeConstants.EVENT_HISTORY ||
          [
            AvailableReportCategories.EVENT_HISTORY,
            AvailableReportCategories.POLLING_REPORT,
          ].includes(widget.reportType)
            ? {
                'secondery.category': [
                  AvailableReportCategories.POLLING_REPORT,
                ].includes(widget.reportType)
                  ? WidgetTypeConstants.POLLING_GRID
                  : widget.category,
              }
            : {}),

          ...(widget.isLogToReport
            ? { 'is.log.to.report': widget.isLogToReport }
            : {}),
        }),
  }
}

function transformWidgetGroupForServer(group, widget) {
  const corelatedCounters = (group.corelatedCounters || []).map((point) => ({
    type: point.type,
    'data.point': point.counter,
  }))

  // const additionalCounters = group.additionalCounters || []
  if (
    ['alert', 'policy', 'policy.stream', 'policy.flap'].includes(group.type)
  ) {
    const defaultFilter = CloneDeep(FILTER_CONDITION_DEFAULT_DATA)
    if (
      [WidgetTypeConstants.HEATMAP, WidgetTypeConstants.ACTIVE_ALERT].includes(
        widget['visualization.category']
      )
    ) {
      group.resultBy = 'monitor'
    }
    return {
      type:
        group.type === 'alert'
          ? widget.category === WidgetTypeConstants.ACTIVE_ALERT
            ? 'policy.stream'
            : 'policy'
          : widget.category === WidgetTypeConstants.ACTIVE_ALERT
          ? 'policy.stream'
          : group.type,
      category: group.category,
      // alertBy: group.alertBy,

      ...(group.join ? { 'join.type': group.join } : {}),
      ...(group.type === 'alert.flap'
        ? group.resultBy
          ? { 'visualization.result.by': Flatten([group.resultBy]) }
          : {}
        : {
            'visualization.result.by': Uniq(
              (Array.isArray(group.resultBy)
                ? group.resultBy || []
                : [group.resultBy].filter(Boolean)
              ).concat(
                [
                  WidgetTypeConstants.HEATMAP,
                  WidgetTypeConstants.GAUGE,
                ].includes(widget.category) && group.type !== 'policy.stream'
                  ? group.excludeSeverityResultBy
                    ? []
                    : [WidgetTypeConstants.GAUGE].includes(widget.category) &&
                      group.category !== 'metric' &&
                      group.type === 'policy'
                    ? ['severity']
                    : ['severity']
                  : []
              )
            ),
          }),
      filters: {
        'data.filter':
          group.filters && group.filters.pre
            ? transformConditionsForServer(group.filters.pre)
            : transformConditionsForServer(defaultFilter),
        ...(group.filters && group.filters.post
          ? {
              'result.filter': transformConditionsForServer(
                group.filters.post,
                true
              ),
            }
          : {}),
      },
      ...(group.alertIds ? { policies: group.alertIds } : {}),
      ...(group.tags ? { tags: group.tags } : {}),
      ...(group.severity && (group.severity || []).length > 0
        ? { severity: group.severity }
        : {}),
      'data.points':
        group.type === 'policy.stream' ||
        group.type === 'policy.flap' ||
        group.useExternalCounters
          ? (group.counters || []).map((c) => ({
              'data.point': c.counter.key,
              aggregator: c.aggrigateFn === '__NONE__' ? '' : c.aggrigateFn,
              'entity.type': (group.target || {}).entityType,
              entities: (group.target || {}).entities,
            }))
          : [
              {
                'data.point': 'severity',
                aggregator: 'count',
                'entity.type': (group.target || {}).entityType,
                entities: (group.target || {}).entities,
              },
            ],
      ...(corelatedCounters.length > 0
        ? { 'correlated.data.points': corelatedCounters }
        : {}),

      ...(group.additionalUntouchedRequestChunk
        ? { ...group.additionalUntouchedRequestChunk }
        : {}),
    }
  }
  const rootCounter = group.counters[0] || {}
  let counters = group.counters || []

  // add extra counter for event history
  if (widget.category === WidgetTypeConstants.EVENT_HISTORY) {
    counters = [
      ...counters,
      {
        ...(counters?.[0] || {}),
        counter: {
          key: 'message',
        },
      },
    ]
  }
  let transformFn
  let shouldUseRootTarget =
    group.type === 'availability' &&
    widget.category === WidgetTypeConstants.GAUGE
  if (['log', 'flow', 'compliance.trail'].includes(group.type)) {
    counters = counters.filter((c) => c.counter && c.counter.key)
    transformFn = (counter) => ({
      'data.point': counter.counter.key,
      aggregator: counter.aggrigateFn === '__NONE__' ? '' : counter.aggrigateFn,
      ...(group.target ? { 'entity.type': group.target.entityType } : {}),
      ...(group.target ? { entities: group.target.entities } : {}),
      ...(counter.additionalDataForCounter || {}),
    })
  } else {
    counters = counters.filter(
      (c) => c.counter && c.counter.key && c.aggrigateFn
    )
    transformFn = (counter) => ({
      ...(counter.entityKeys ? { 'entity.keys': counter.entityKeys } : {}),
      'data.point': counter.counter.key.replace(/\.sparkline/, ''),
      aggregator: counter.aggrigateFn === '__NONE__' ? '' : counter.aggrigateFn,
      'entity.type': shouldUseRootTarget
        ? rootCounter.target
          ? rootCounter.target.entityType
          : undefined
        : counter.target
        ? counter.target.entityType
        : undefined,
      entities: shouldUseRootTarget
        ? rootCounter.target
          ? rootCounter.target.entities
          : undefined
        : counter.target && counter.target.entities
        ? Array.isArray(counter.target.entities)
          ? counter.target.entities
          : [counter.target.entities]
        : shouldUseRootTarget
        ? rootCounter.target
          ? rootCounter.target.entities
          : undefined
        : undefined,

      ...(counter.arithmeticOperation &&
      [
        WidgetTypeConstants.CHART,
        WidgetTypeConstants.GRID,
        WidgetTypeConstants.TOPN,
        WidgetTypeConstants.GAUGE,
      ].includes(widget.category)
        ? { 'statistical.func': counter.arithmeticOperation }
        : {}),

      ...(counter.additionalDataForCounter || {}),
    })
  }
  const sparklineCounter =
    (widget.widgetProperties.sortingSetting || {}).showSparklineChart &&
    widget.category === WidgetTypeConstants.TOPN &&
    widget.widgetType === WidgetTypeConstants.GRID
      ? [
          {
            'data.point': widget.widgetProperties.sortingSetting.column.replace(
              /(\.|\^)(min|max|avg|sum|last)$/,
              ''
            ),
            aggregator: 'sparkline',

            ...(rootCounter.target
              ? { 'entity.type': rootCounter.target.entityType }
              : {}),

            ...(rootCounter.target
              ? { entities: rootCounter.target.entities }
              : {}),
          },
        ]
      : []
  return {
    type: group.type,
    ...(group.category ? { category: group.category } : {}),
    ...(group.join ? { 'join.type': group.join } : {}),
    ...(group.sortingColumn ? { 'sorting.column': group.sortingColumn } : {}),
    ...(group.instance ? { 'object.type': group.instance.key } : {}),
    ...(([
      WidgetTypeConstants.HEATMAP,
      WidgetTypeConstants.ACTIVE_ALERT,
    ].includes(widget.category) &&
      group.type === 'availability') ||
    widget.reportType === AvailableReportCategories.AVAILABILITY_ALERT ||
    widget.reportType === AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY
      ? {
          status: group.status || [],
        }
      : {}),
    filters: {
      'data.filter': transformConditionsForServer(group.filters.pre),
      'result.filter': transformConditionsForServer(group.filters.post, true),
      'drill.down.filter': transformConditionsForServer(
        group.filters.drillDownFilters
      ),
    },
    ...(widget.category === WidgetTypeConstants.GAUGE
      ? {}
      : group.resultBy &&
        (widget.category !== WidgetTypeConstants.CHART ||
          group.counters.length <= 1)
      ? { 'visualization.result.by': Flatten([group.resultBy]) }
      : {}),
    'data.points': [...counters.map(transformFn), ...sparklineCounter],
    ...(corelatedCounters.length > 0
      ? { 'correlated.data.points': corelatedCounters }
      : {}),
    ...(group?.additionalColumns?.length > 0
      ? { 'additional.columns': group.additionalColumns }
      : {}),
    //  this condtion is special for the avalibiloity reports
    ...(group?.counters?.[0]?.filterCounter
      ? {
          'availability.by': [
            ...counters.map(transformFn),
            ...sparklineCounter,
          ],
          'data.points': [
            ...transformAvalibilityReportDataPoints(
              group.counters[0].filterCounter,
              group.counters[0],
              transformFn
            ),
          ],
        }
      : {}),

    ...(group?.timeInterval ? { 'time.interval': group.timeInterval } : {}),
    ...(group.additionalUntouchedRequestChunk
      ? { ...group.additionalUntouchedRequestChunk }
      : {}),

    ...(widget.visualizationTags ? { ...additionalDataForGroup['tag'] } : {}),
    ...([
      AvailableReportCategories.EVENT_HISTORY,
      AvailableReportCategories.AVAILABILITY,
    ].includes(widget.reportType) ||
    widget.category === WidgetTypeConstants.EVENT_HISTORY
      ? {
          ...additionalDataForGroup[
            AvailableReportCategories.EVENT_HISTORY || widget.category
          ],
        }
      : {}),
  }
}

function transformPropertiesForServer(widget) {
  if (
    [
      WidgetTypeConstants.CHART,
      WidgetTypeConstants.ANOMALY,
      WidgetTypeConstants.FORECAST,
    ].includes(widget.category) &&
    ![
      AvailableReportCategories.EVENT_HISTORY,
      AvailableReportCategories.POLLING_REPORT,
    ].includes(widget.reportType || widget.category)
  ) {
    const properties = widget.widgetProperties || {}
    return {
      chart: {
        ...transformChartPropertiesForServer(properties.styleSetting || {}),
        sorting: {
          ...(/^\d+$/.test(String((properties.sortingSetting || {}).topCount))
            ? {
                limit: (properties.sortingSetting || {}).topCount || 10,
              }
            : {
                limit: 10,
              }),
          order: (properties.sortingSetting || {}).direction || 'desc',
        },
      },
    }
  } else if (
    widget.category === WidgetTypeConstants.GAUGE ||
    widget.widgetType === WidgetTypeConstants.KPI_GAUGE
  ) {
    const properties = widget.widgetProperties || {}
    return {
      gauge: transformGaugePropertiesForServer(properties),
    }
  } else if (
    [
      WidgetTypeConstants.GRID,
      WidgetTypeConstants.STREAM,
      WidgetTypeConstants.ACTIVE_ALERT,
      WidgetTypeConstants.EVENT_HISTORY,
    ].includes(widget.category) ||
    [
      AvailableReportCategories.EVENT_HISTORY,
      AvailableReportCategories.POLLING_REPORT,
    ].includes(widget.reportType)
  ) {
    const properties = widget.widgetProperties || {}
    return {
      grid: transformGridPropertiesForServer(properties),
    }
  } else if (widget.category === WidgetTypeConstants.TOPN) {
    return transformTopNPropertiesForServer(widget.widgetProperties, widget)
  } else if (widget.category === WidgetTypeConstants.SANKEY) {
    const firstCounter = ((widget.groups[0] || {}).counters || [])[0] || {}
    const sortingColumn = `${(firstCounter.counter || {}).key}.${
      firstCounter.aggrigateFn
    }`
    widget.widgetProperties = {
      ...widget.widgetProperties,
      sortingSetting: {
        ...(widget.widgetProperties.sortingSetting || {}),
        topCount: (widget.widgetProperties.sortingSetting || {}).topCount || 10,
        direction:
          (widget.widgetProperties.sortingSetting || {}).direction || 'desc',
        column:
          sortingColumn ||
          (widget.widgetProperties.sortingSetting || {}).column,
      },
    }
    return transformTopNPropertiesForServer(widget.widgetProperties, widget)
  } else if (widget.category === WidgetTypeConstants.HEATMAP) {
    const hasMetricGroup = widget?.groups?.find((g) => g.type === 'metric')
    return {
      map: {
        'show.counts': widget.widgetProperties.showCounts ? 'yes' : 'no',
        ...(hasMetricGroup
          ? {
              'color.palette': widget.widgetProperties.selectedColorPalette,
            }
          : {}),
      },
      ...(hasMetricGroup
        ? {
            grid: {
              sorting: {
                limit: 100,
                order: 'desc',
                column: `${widget?.groups?.[0]?.counters[0]?.counter?.key}.${widget?.groups?.[0]?.counters[0]?.aggrigateFn}`,
              },
            },
          }
        : {}),
    }
  } else if (widget.category === WidgetTypeConstants.FREE_TEXT) {
    const properties = widget.widgetProperties || {}
    return {
      'free.text': transformFreeTextPropertiesForServer(properties),
    }
  }
  return {}
}

function transformChartPropertiesForServer(properties) {
  return {
    'rotation.angle': properties.rotation,
    'chart.legend': properties.legendEnabled ? 'yes' : 'no',
    'vertical.legend': properties.verticalLegend ? 'yes' : 'no',
    'line.width': properties.lineWidth,
    // ellipsisEnabled: false,
    'chart.label': properties.pieDataLabelsEnabled ? 'yes' : 'no',
    'data.label.enabled': properties.dataLabelEnabled ? 'yes' : 'no',
    'points.enabled': properties.pointsEnabled ? 'yes' : 'no',
    'point.size': properties.pointSize,
    'line.style': properties.lineStyle,
    ...(properties.lineStyle === 'dash'
      ? {
          'dash.pattern': properties.dashPattern,
        }
      : {}),

    ...(properties.xAxisTitle || properties.yAxisTitle || properties.zAxisTitle
      ? {
          'axis.titles': {
            ...(properties.xAxisTitle ? { x: properties.xAxisTitle } : {}),
            ...(properties.yAxisTitle ? { y: properties.yAxisTitle } : {}),
            ...(properties.zAxisTitle ? { z: properties.zAxisTitle } : {}),
          },
        }
      : {}),
    'highchart.settings': properties.chartOptions || {},
    ...(properties.stacks && properties.stacks.length
      ? {
          'stack.groups': properties.stacks.map((group, index) => ({
            'data.points': group.counters,
          })),
        }
      : {}),

    ...(properties?.markerProperty
      ? {
          'marker.property': properties?.markerProperty?.map((property) => {
            return {
              'marker.type': property.markerType,
              'marker.threshold':
                property.markerType === 'line'
                  ? property.markerThreshold
                  : [property.start, property.end],
              'marker.color': property.markerColor,
              'marker.line.type': property.markerLineType,
              'marker.label': property.markerLabel,
            }
          }),
        }
      : {}),
  }
}

function transformGridPropertiesForServer(properties) {
  return {
    'visualization.grid.properties.required': properties.useWidgetColumnsOnly
      ? 'yes'
      : 'no',
    searchable: properties.searchable === false ? 'no' : 'yes',
    'column.selection': properties.allowColumnSelection ? 'yes' : 'no',
    header: properties.showHeader === false ? 'no' : 'yes',
    view: properties['view'],
    layout: properties.layout,
    ...(properties.layout && properties.layout !== WidgetTypeConstants.GRID
      ? { 'layout.sorting': properties.layoutSorting }
      : {}),
    style: {
      ...(properties.style || {}),
      'header.font.size': (properties.headerStyle || {}).fontSize || 'medium',
    },
    columns: (properties.columnSettings || []).map((c, proprties) =>
      transformColumnForServer(c, properties)
    ),
  }
}

function transformTopNPropertiesForServer(properties, widget) {
  const firstGroup = widget.groups[0] || {}
  let firstCounterKey
  if (firstGroup.counters) {
    const firstCounter = firstGroup.counters[0] || {}
    if (firstCounter.counter && firstCounter.counter.key) {
      firstCounterKey = `${firstCounter.counter.key}.${firstCounter.aggrigateFn}`
    }
  }
  if (
    widget.groups &&
    widget.groups[0] &&
    (widget.groups[0].type === 'alert' || widget.groups[0].type === 'policy')
  ) {
    firstCounterKey = 'severity.count'
  }
  if (
    widget.widgetType === WidgetTypeConstants.GRID ||
    widget.widgetType === WidgetTypeConstants.HORIZONTAL_TOPN ||
    widget.category === WidgetTypeConstants.SANKEY ||
    (widget.widgetType === WidgetTypeConstants.TREE_VIEW &&
      widget.groups[0].type === 'metric')
  ) {
    return {
      ...((properties.sortingSetting || {}).showSparklineChart
        ? { sparkline: 'yes' }
        : {}),
      ...(widget.widgetType === WidgetTypeConstants.HORIZONTAL_TOPN
        ? {
            chart: {
              ...transformChartPropertiesForServer(
                properties.styleSetting || {}
              ),
              sorting: {
                ...(/^\d+$/.test(
                  String((properties.sortingSetting || {}).topCount)
                )
                  ? {
                      limit: (properties.sortingSetting || {}).topCount || 10,
                    }
                  : {
                      limit: 10,
                    }),
                order: (properties.sortingSetting || {}).direction || 'asc',
                column: (
                  (properties.sortingSetting || {}).column ||
                  firstCounterKey ||
                  ''
                ).replace(/\^/g, '.'),
              },
            },
          }
        : {}),
      grid: {
        ...transformGridPropertiesForServer(properties, widget),
        sorting: {
          ...(/^\d+$/.test(String((properties.sortingSetting || {}).topCount))
            ? {
                limit: (properties.sortingSetting || {}).topCount
                  ? (properties.sortingSetting || {}).topCount > 100
                    ? 100
                    : (properties.sortingSetting || {}).topCount
                  : 10,
              }
            : {
                limit: 10,
              }),
          order: (properties.sortingSetting || {}).direction || 'desc',
          column: (
            (properties.sortingSetting || {}).column ||
            firstCounterKey ||
            ''
          ).replace(/\^/g, '.'),
        },
      },
    }
  } else {
    return {
      chart: {
        ...transformChartPropertiesForServer(properties.styleSetting || {}),
        sorting: {
          ...(/^\d+$/.test(String((properties.sortingSetting || {}).topCount))
            ? {
                limit: (properties.sortingSetting || {}).topCount || 10,
              }
            : {
                limit: 10,
              }),
          order: (properties.sortingSetting || {}).direction || 'asc',
          column: (
            (properties.sortingSetting || {}).column ||
            firstCounterKey ||
            ''
          ).replace(/\^/g, '.'),
        },
      },
    }
  }
}

function transformGaugePropertiesForServer(properties) {
  const criticalColor = properties.criticalColor || {}
  const majorColor = properties.majorColor || {}
  const warningColor = properties.warningColor || {}
  return {
    ...(properties.drilldown ? { 'drill.down': properties.drilldown } : {}),
    ...(properties.titleText ? { 'title.text': properties.titleText } : {}),
    style: {
      layout: properties.layout,
      'chart.legend': properties.legendEnabled ? 'yes' : 'no',
      'chart.label': properties.pieDataLabelsEnabled ? 'yes' : 'no',
      type: properties.view || 'number',
      'font.size': properties.fontSize || 'medium',
      'text.align': properties.textAlign || 'left',
      ...(properties.severityCounter
        ? { 'color.data.point': properties.severityCounter }
        : {}),
      classes: properties.classes,
      ...(properties.iconName
        ? {
            icon: {
              name: properties.iconName,
              placement: properties.iconPosition,
              classes: properties.classes,
            },
          }
        : {}),
      'color.conditions': [
        {
          color: criticalColor.color,
          operator: criticalColor.condition,
          value: criticalColor.conditionValue,
        },
        {
          color: majorColor.color,
          operator: majorColor.condition,
          value: majorColor.conditionValue,
        },
        {
          color: warningColor.color,
          operator: warningColor.condition,
          value: warningColor.conditionValue,
        },
      ],
    },
    ...(properties.header
      ? {
          header: {
            title: properties.header.title || '',
            ...(properties.header.style
              ? {
                  style: {
                    'font.size': properties.header.style.fontSize,
                    icon: properties.header.style.icon,
                  },
                }
              : {}),
            'data.points': (properties.header.counters || []).map((c) => ({
              label: c.title,
              value: c.counter,
            })),
          },
        }
      : {}),
    ...(properties.footer
      ? {
          footer: {
            title: properties.footer.title || '',
            ...(properties.footer.style
              ? {
                  style: {
                    'font.size': properties.footer.style.fontSize,
                  },
                }
              : {}),
            'data.points': (properties.footer.counters || []).map((c) => ({
              label: c.title,
              value: c.counter,
              type: c.slot,
            })),
          },
        }
      : {}),
  }
}

function transformColumnForServer(column, properties) {
  return {
    name: column.rawName ? column.rawName.replace(/\^/g, '.') : column.name,
    title: column.displayName,
    show: column.hidden ? 'no' : 'yes',
    sortable: column.sortable === false ? 'no' : 'yes',
    disable: column.disable ? 'yes' : 'no',
    resizable: column.resizable === false ? 'no' : 'yes',
    selectable: column.selectable === false ? 'no' : 'yes',
    orderable: column.orderable === false ? 'no' : 'yes',
    position: column.orderIndex,
    ...(column.cellRender ? { cellRender: column.cellRender } : {}),
    alignment: column.align,
    type: column.type,
    ...(column.alias ? { alias: column.alias } : {}),
    ...(column.slot === 'computed'
      ? {
          computed: 'yes',
          formula: column.formula,
        }
      : {}),
    style: {
      ...(column.renderMethod ? { 'render.method': column.renderMethod } : {}),
      ...(column.width ? { 'width.percent': parseInt(column.width) } : {}),
      ...(column.classes ? { classes: column.classes } : {}),
      ...(column.iconName
        ? {
            icon: {
              name: column.iconName,
              placement: column.iconPosition,
              classes: column.iconClasses,
              ...(column.iconConditions
                ? { conditions: column.iconConditions }
                : {}),
            },
          }
        : {}),
      ...((column.slot && column.slot !== 'computed') ||
      column.type === 'sparkline'
        ? {
            'inline.chart': {
              type:
                (properties.sortingSetting || {}).sparklineChartType ||
                column.slot,
              ...(column.slotClasses && column.slotClasses.length > 0
                ? { classes: column.slotClasses }
                : {}),
              color:
                (properties.sortingSetting || {}).sparklineColor || '#099dd9',
            },
          }
        : {}),
      ...((column.colorConfig || []).length > 0
        ? {
            'color.conditions': (column.colorConfig || []).map((item) => ({
              operator: item['operator'],
              value: item['value'],
              'color.type': item.colorType,
              color: item.color,
            })),
          }
        : {}),
      ...((column.valueDisplayConfig || []).length > 0
        ? {
            'value.display.operations': (column.valueDisplayConfig || []).map(
              (item) => ({
                operator: item['operator'],
                'value.to.compare': item.comparedValue,
                'operation.type': item.operatorType,
                value: item.value,
              })
            ),
          }
        : {}),
    },
  }
}

function transformAvalibilityReportDataPoints(
  counters = [],
  commanContext = {},
  transformFn
) {
  // const commanContextForCounter = Pick(commanContext, ['target', 'counter'])

  return counters
    .map((counterKey) => ({
      ...commanContext,
      counter: {
        ...commanContext.counter,

        key: counterKey,
        counterName: counterKey,
        name: counterKey,
        text: counterKey,
      },
      aggrigateFn: counterKey.includes('percent') ? 'avg' : 'sum',
    }))
    .map(transformFn)
    .filter(Boolean)
}

function transformFreeTextPropertiesForServer(properties) {
  return {
    'font.size': properties.fontSize || 'auto',
    'text.align': properties.textAlign || 'left',
    color: properties.fontColor || 'default',
    'display.text': properties.textToDisplay,
  }
}
