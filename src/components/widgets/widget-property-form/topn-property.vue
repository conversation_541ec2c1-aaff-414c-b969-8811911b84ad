<template>
  <div class="flex flex-col px-2 flex-1 min-h-0 w-full">
    <MRow class="min-h-0">
      <MCol :size="12" class="mb-2">
        <MTab v-model="currentTab">
          <MTabPane key="style" tab="Style" />
          <MTabPane
            v-if="isGrid || isHorizontalTopn"
            key="column-setting"
            tab="Column Setting"
          />
          <MTabPane key="sorting" tab="Sorting" force-render />
          <MTabPane
            v-if="!isReportPreview"
            key="timeline-preference"
            tab="Timeline Preference"
          />
        </MTab>
      </MCol>
    </MRow>
    <div
      class="flex flex-col flex-1 min-h-0 overflow-y-auto"
      :class="{
        'overflow-x-hidden': !allowXOverFlow,
        'overflow-x-auto': allowXOverFlow,
      }"
    >
      <ColumnSettings
        v-if="currentTab === 'column-setting'"
        v-model="columnSettings"
        :columns="columns"
        :tag-options="tagOptions"
        :should-show-tag-dropdown="showTagDropdown"
      />
      <TimelinePreferenceProperty
        v-else-if="currentTab === 'timeline-preference'"
        v-bind="$attrs"
        :widget="widget"
        @update:widget="$emit('update:widget', $event)"
      />
      <ChartStyleProperty
        v-else-if="currentTab === 'style' && !isGrid"
        v-model="styleSetting"
        hide-tab
        :widget-type="widgetType"
        :is-report-preview="isReportPreview"
        :is-topn-chart-property="true"
        :counter-length="counterLength"
      >
        <template v-slot:visulization-type-options>
          <FlotoFormItem>
            <MRadioGroup
              v-model="innerWidgetType"
              as-button
              :options="widgetTypeOptions"
              class="chart-selector-radio mt-4"
            >
              <template v-slot:option="{ option }">
                <div class="flex items-center justify-center h-full">
                  <WidgetTypeIcon
                    :widget-type="option.value"
                    :tooltip="option.text"
                    :size="28"
                    :selected="option.value === widgetType"
                  />
                </div>
              </template>
            </MRadioGroup>
            <!-- <FlotoDropdownPicker
              v-model="innerWidgetType"
              as-button
              :options="widgetTypeOptions"
              class="chart-type-picker"
              :searchable="false"
              :allow-clear="false"
            >
              <template v-slot:before-menu-text="{ item }">
                <WidgetTypeIcon
                  :widget-type="item.key"
                  :tooltip="item.text"
                  :size="15"
                />
              </template>
            </FlotoDropdownPicker>-->
          </FlotoFormItem>
        </template>
      </ChartStyleProperty>
      <GridHeaderStyle
        v-else-if="currentTab === 'style' && isGrid"
        v-model="headerStyle"
      >
        <template v-slot:visulization-type-options>
          <MCol :size="12">
            <FlotoFormItem>
              <!-- <FlotoDropdownPicker
                v-model="innerWidgetType"
                as-button
                :options="widgetTypeOptions"
                class="chart-type-picker"
                :searchable="false"
                :allow-clear="false"
              >
                <template v-slot:before-menu-text="{ item }">
                  <WidgetTypeIcon
                    :widget-type="item.key"
                    :tooltip="item.text"
                    :size="15"
                  />
                </template>
              </FlotoDropdownPicker>-->
              <MRadioGroup
                v-model="innerWidgetType"
                as-button
                :options="widgetTypeOptions"
                class="chart-selector-radio mt-4"
              >
                <template v-slot:option="{ option }">
                  <div class="flex items-center justify-center h-full">
                    <WidgetTypeIcon
                      :widget-type="option.value"
                      :tooltip="option.text"
                      :size="28"
                      :selected="option.value === widgetType"
                    />
                  </div>
                </template>
              </MRadioGroup>
            </FlotoFormItem>
          </MCol>
        </template>
      </GridHeaderStyle>
      <MCol
        v-if="showSparklineOption && currentTab === 'style' && isGrid"
        :size="12"
        class="my-2"
      >
        <SparklineStyle v-model="sortingSetting" />
      </MCol>
      <TopnSortingProperty
        v-show="currentTab === 'sorting'"
        v-model="sortingSetting"
        :group-by-columns="groupByColumns"
        :columns="columns"
        :show-sparkline-option="showSparklineOption"
      />
    </div>
  </div>
</template>

<script>
import Bus from '@utils/emitter'
import { WidgetTypeConstants, AVAILABLE_GROUP_TYPES } from '../constants'
import TopnSortingProperty from './topn-sorting-property.vue'
import GridHeaderStyle from './grid-header-style.vue'
import ChartStyleProperty from './chart-style-property.vue'
import ColumnSettings from './column-settings.vue'
import WidgetTypeIcon from '../widget-type-icon/widget-type-icon.vue'
import SparklineStyle from './sparkline-style.vue'
import { AvailableReportCategories } from '@modules/report/helpers/report'
import { POLICY_GRID_DEFAULT_COLUMN_SETTINGS } from '@components/widgets/helper'
import TimelinePreferenceProperty from './timeline-preference-property.vue'
export default {
  name: 'TopNProperty',
  components: {
    TopnSortingProperty,
    ChartStyleProperty,
    GridHeaderStyle,
    ColumnSettings,
    WidgetTypeIcon,
    SparklineStyle,
    TimelinePreferenceProperty,
  },
  inheritAttrs: false,
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default: undefined,
    },
    widgetType: {
      type: String,
      required: true,
    },
    columns: {
      type: Array,
      default: undefined,
    },
    groupByColumns: {
      type: Array,
      default: undefined,
    },
    widget: {
      type: Object,
      default: undefined,
    },
    isReportPreview: {
      type: Boolean,
      default: false,
    },
    allowXOverFlow: {
      type: Boolean,
      default: false,
    },
    isArchivedEnabled: {
      type: Boolean,
      default: false,
    },
    tagOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    this.ignoreRowNames = ['object.type', 'object.vendor', 'object.ip']
    return {
      currentTab: 'style',
      insertSolidGaugeView: false,
      insertTreeViewView: false,
    }
  },
  computed: {
    isPolicyTopN() {
      return (
        this.widget?.groups?.[0]?.type === 'policy' ||
        this.widget?.groups?.[0]?.type === 'policy.flap'
      )
    },
    counterLength() {
      let counterLength = 0
      this.widget?.groups?.forEach((group) => {
        counterLength += group.counters?.length || 0
      })
      return counterLength
    },
    widgetTypeOptions() {
      return [
        WidgetTypeConstants.AREA,
        WidgetTypeConstants.LINE,
        WidgetTypeConstants.HORIZONTAL_BAR,
        WidgetTypeConstants.VERTICAL_BAR,
        WidgetTypeConstants.PIE,
        ...(this.insertSolidGaugeView
          ? [WidgetTypeConstants.TOPN_SOLID_GAUGE_VIEW]
          : []),
        WidgetTypeConstants.GRID,
        ...(this.insertTreeViewView
          ? [
              WidgetTypeConstants.TREE_VIEW,
              ...(!this.isReportPreview
                ? [WidgetTypeConstants.PACKED_BUBBLE_CHART]
                : []),
            ]
          : []),

        ...(this.isReportPreview &&
        this.groupType === 'metric' &&
        !this.isArchivedEnabled
          ? [WidgetTypeConstants.HORIZONTAL_TOPN]
          : []),
      ].map((t) => ({ value: t, text: t }))
    },
    isGrid() {
      return this.widgetType === WidgetTypeConstants.GRID
    },
    isHorizontalTopn() {
      return this.widgetType === WidgetTypeConstants.HORIZONTAL_TOPN
    },
    showSparklineOption() {
      const group = this.widget.groups[0]
      return (
        group.type === 'metric' &&
        this.widgetType === WidgetTypeConstants.GRID &&
        !/last$/.test(this.sortingSetting.column || '')
      )
    },
    innerWidgetType: {
      get() {
        return this.widgetType
      },
      set(type) {
        if (
          type === WidgetTypeConstants.HORIZONTAL_TOPN ||
          type === WidgetTypeConstants.PACKED_BUBBLE_CHART ||
          this.widgetType === WidgetTypeConstants.HORIZONTAL_TOPN ||
          this.widgetType === WidgetTypeConstants.PACKED_BUBBLE_CHART
        ) {
          this.$nextTick(() => {
            Bus.$emit('widget.generate.preview')
          })
        } else if (
          type === WidgetTypeConstants.TREE_VIEW ||
          this.widgetType === WidgetTypeConstants.TREE_VIEW
        ) {
          this.$nextTick(() => {
            Bus.$emit('widget.generate.preview')
          })
        } else if (
          [
            WidgetTypeConstants.AREA,
            WidgetTypeConstants.LINE,
            WidgetTypeConstants.HORIZONTAL_BAR,
            WidgetTypeConstants.VERTICAL_BAR,
            WidgetTypeConstants.TOPN_SOLID_GAUGE_VIEW,
          ].includes(this.widgetType)
        ) {
          if (
            type === WidgetTypeConstants.PIE ||
            type === WidgetTypeConstants.GRID
          ) {
            this.$nextTick(() => {
              Bus.$emit('widget.generate.preview')
            })
          }
        } else if (this.widgetType === WidgetTypeConstants.PIE) {
          if (
            type === WidgetTypeConstants.GRID ||
            [
              WidgetTypeConstants.AREA,
              WidgetTypeConstants.LINE,
              WidgetTypeConstants.HORIZONTAL_BAR,
              WidgetTypeConstants.VERTICAL_BAR,
              WidgetTypeConstants.HORIZONTAL_TOPN,
              WidgetTypeConstants.TOPN_SOLID_GAUGE_VIEW,
            ].includes(type)
          ) {
            this.$nextTick(() => {
              Bus.$emit('widget.generate.preview')
            })
          }
        } else if (this.widgetType === WidgetTypeConstants.GRID) {
          if (
            type === WidgetTypeConstants.PIE ||
            [
              WidgetTypeConstants.AREA,
              WidgetTypeConstants.LINE,
              WidgetTypeConstants.HORIZONTAL_BAR,
              WidgetTypeConstants.VERTICAL_BAR,
              WidgetTypeConstants.TOPN_SOLID_GAUGE_VIEW,
            ].includes(type)
          ) {
            this.$nextTick(() => {
              Bus.$emit('widget.generate.preview')
            })
          }
        }
        this.columnSettings = []
        this.resetColumnSettings(type)
        this.$emit('change-widget-type', type)
      },
    },
    columnSettings: {
      get() {
        return (this.value || {}).columnSettings || []
      },
      set(columnSettings) {
        this.$emit('change', { ...(this.value || {}), columnSettings })
      },
    },
    styleSetting: {
      get() {
        return (this.value || {}).styleSetting || {}
      },
      set(styleSetting) {
        this.$emit('change', { ...(this.value || {}), styleSetting })
      },
    },
    headerStyle: {
      get() {
        return (this.value || {}).headerStyle || {}
      },
      set(headerStyle) {
        this.$emit('change', { ...(this.value || {}), headerStyle })
      },
    },
    sortingSetting: {
      get() {
        return (this.value || {}).sortingSetting || {}
      },
      set(sortingSetting) {
        if (/last$/.test(sortingSetting.column || '')) {
          sortingSetting.showSparklineChart = false
        }
        this.$emit('change', { ...(this.value || {}), sortingSetting })
      },
    },
    showSparklineChart: {
      get() {
        return this.value?.sortingSetting?.showSparklineChart
      },
      set(showSparklineChart) {
        this.$emit('change', {
          ...(this.value || {}),
          sortingSetting: {
            ...(this?.value?.sortingSetting || {}),
            showSparklineChart,
          },
        })
      },
    },
    sparklineChartType: {
      get() {
        return this.value?.sortingSetting?.sparklineChartType
      },
      set(sparklineChartType) {
        this.$emit('change', {
          ...(this.value || {}),
          sortingSetting: {
            ...(this?.value?.sortingSetting || {}),
            sparklineChartType,
          },
        })
      },
    },
    sparklineColor: {
      get() {
        return this.value?.sortingSetting?.sparklineColor
      },
      set(sparklineColor) {
        this.$emit('change', {
          ...(this.value || {}),
          sortingSetting: {
            ...(this?.value?.sortingSetting || {}),
            sparklineColor,
          },
        })
      },
    },
    sparklineTypeOptions() {
      return [
        { value: 'sparkline', text: 'Line' },
        { value: 'sparkarea', text: 'Area' },
        { value: 'sparkbar', text: 'Bar' },
      ]
    },
    groupType() {
      return this.widget?.groups?.[0]?.type
    },
    showTagDropdown() {
      return (
        (this.widget?.groups.find((g) =>
          ['metric', 'availability'].includes(g.type)
        ) &&
          [WidgetTypeConstants.GRID, WidgetTypeConstants.TOPN].includes(
            this.widget.category
          ) &&
          ![AvailableReportCategories.POLLING_REPORT].includes(
            this.widget.reportType
          )) ||
        [
          AvailableReportCategories.AVAILABILITY,
          AvailableReportCategories.PERFORMANCE,
          // AvailableReportCategories.INVENTORY,
          // AvailableReportCategories.AVAILABILITY_ALERT,
        ].includes(this.widget.reportType)
      )
    },
  },
  watch: {
    columns: {
      immediate: true,
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          if (newValue.length === 0) {
            this.$nextTick(() => {
              this.$emit('change', {
                ...(this.value || {}),
                columnSettings: [],
              })
            })

            return
          }
          const existingColumnsMap = {}
          this.columnSettings.forEach((c) => {
            existingColumnsMap[c.name] = c
          })
          const columns = newValue.map(({ rawName }) =>
            rawName.replace(/\^/g, '.')
          )
          let nonGroupByColumns = columns
          if (this.groupByColumns && this.groupByColumns.length) {
            const groupColumns = this.groupByColumns
            nonGroupByColumns = columns.filter(
              (c) =>
                groupColumns.includes(c) === false &&
                !this.ignoreRowNames.includes(c) &&
                !/\.sparkline$/.test(c)
            )
          }
          if (
            !this.sortingSetting.column ||
            columns.includes(this.sortingSetting.column) === false
          ) {
            const sortingColumn = newValue.find(
              (c) => c.rawName.replace(/\^/g, '.') === nonGroupByColumns[0]
            )
            if (sortingColumn) {
              this.$nextTick(() => {
                this.$emit('change', {
                  ...(this.value || {}),
                  sortingSetting: {
                    ...this.sortingSetting,
                    column: sortingColumn.rawName.replace(/\^/g, '.'),
                  },
                })
              })
            }
          }
          this.columnSettings = newValue.map((c) => ({
            ...c,
            resizable: true,
            orderable: true,
            sortable: true,
            hidden: c.hidden || false,
            ...(existingColumnsMap[c.name] || {}),
            name: c.name.replace(/[~^]/g, '.'),
            ...(/sparkline$/.test(c.name)
              ? {
                  type: 'sparkline',
                  displayName: c.displayName || ' ',
                  headerCell: 'sparklineHeader',
                }
              : {}),
          }))
        }
      },
    },
    'widget.groups': {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          if (
            (((newValue || [])[0] || {}).counters || []).length &&
            newValue[0].counters.length === 1 &&
            ((newValue[0].counters[0].counter || {}).key || '')
              .toLowerCase()
              .includes('percent')
          ) {
            this.insertSolidGaugeView = true
          } else {
            this.insertSolidGaugeView = false
            if (
              this.widget.widgetType ===
              WidgetTypeConstants.TOPN_SOLID_GAUGE_VIEW
            ) {
              this.$emit('change-widget-type', WidgetTypeConstants.AREA)
            }
          }
          if (
            ((((newValue || [])[0] || {}).counters || []).length &&
              newValue[0].counters.length === 1 &&
              ((newValue[0].counters[0].counter || {}).dataType || '').includes(
                'numeric'
              ) &&
              [
                'metric',
                'availability',
                AVAILABLE_GROUP_TYPES.NETROUTE_METRIC,
              ].includes(newValue[0].type)) ||
            (['flow', 'log'].includes(newValue[0].type) &&
              newValue[0].counters.length === 1 &&
              !this.isReportPreview) ||
            ['alert', 'policy'].includes(newValue[0].type)
          ) {
            this.insertTreeViewView = true
          } else {
            this.insertTreeViewView = false
            if (this.widget.widgetType === WidgetTypeConstants.TREE_VIEW) {
              this.$emit('change-widget-type', WidgetTypeConstants.AREA)
            }
          }
          let counters
          if (newValue[0].type === 'alert' || newValue[0].type === 'policy') {
            counters = []
          } else {
            counters = newValue[0].counters.filter((c) =>
              c.counter ? c.counter.key : c
            )
          }
          if (this.groupByColumns) {
            const groupColumns = this.groupByColumns
            counters = counters.filter(
              (c) =>
                groupColumns.includes(c.counter ? c.counter.key : c) ===
                  false &&
                !this.ignoreRowNames.includes(c.counter ? c.counter.key : c)
            )
          }
          if (
            this.sortingSetting.column &&
            counters.length > 0 &&
            !counters.find(
              (c) =>
                `${c?.counter?.key?.replace(/[~^]/g, '.') || c}.${
                  c.aggrigateFn
                }` === this.sortingSetting.column
            )
          ) {
            this.$emit('change', {
              ...(this.value || {}),
              sortingSetting: {
                ...(this.sortingSetting || {}),
                column: `${
                  counters[0].counter ? counters[0].counter.key : counters[0]
                }^${counters[0].counter ? counters[0].aggrigateFn : 'sum'}`,

                showSparklineChart:
                  counters?.[0]?.aggrigateFn &&
                  counters?.[0]?.aggrigateFn === 'last'
                    ? false
                    : (this.sortingSetting || {}).showSparklineChart,
              },
            })
          }
        }
      },
      immediate: true,
    },
    isArchivedEnabled: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          if (newValue) {
            this.$emit('change-widget-type', WidgetTypeConstants.AREA)
          }
        }
      },
    },
  },
  methods: {
    resetColumnSettings(type) {
      if (this.isPolicyTopN && type === WidgetTypeConstants.GRID) {
        Bus.$emit(
          'update:widget-column-settings',
          POLICY_GRID_DEFAULT_COLUMN_SETTINGS
        )
      }
    },
  },
}
</script>
