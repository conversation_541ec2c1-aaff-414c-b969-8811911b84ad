<template>
  <Component
    :is="formComponent"
    v-model="innerValue"
    :columns="columns"
    :widget-type="widgetType"
    :widget-category="widgetCategory"
    v-bind="$attrs"
    @change-widget-type="$emit('change-widget-type', $event)"
    @change-result-by="$emit('change-result-by', $event)"
    @update:widget="$emit('update:widget', $event)"
  />
</template>

<script>
import { WidgetTypeConstants } from '../constants'
import ChartProperty from './chart-property.vue'
import GridProperty from './grid-property.vue'
import TopnProperty from './topn-property.vue'
import GaugeProperty from './gauge-property.vue'
import MapProperty from './map-property.vue'
import SankeyProperty from './sankey-property.vue'
import FreeTextProperty from './free-text-property.vue'

export default {
  name: 'WidgetPropertyForm',
  inheritAttrs: false,
  model: { event: 'change' },
  props: {
    widgetCategory: {
      type: String,
      required: true,
    },
    widgetType: {
      type: String,
      default: undefined,
    },
    columns: { type: Array, default: undefined },
    value: {
      type: Object,
      default: undefined,
    },
  },
  computed: {
    formComponent() {
      if (
        [
          WidgetTypeConstants.CHART,
          WidgetTypeConstants.ANOMALY,
          WidgetTypeConstants.FORECAST,
        ].includes(this.widgetCategory)
      ) {
        return ChartProperty
      }
      if (this.widgetCategory === WidgetTypeConstants.GRID) {
        return GridProperty
      }
      if (this.widgetCategory === WidgetTypeConstants.TOPN) {
        return TopnProperty
      }
      if (this.widgetCategory === WidgetTypeConstants.GAUGE) {
        return GaugeProperty
      }
      if (this.widgetCategory === WidgetTypeConstants.STREAM) {
        return GridProperty
      }
      if (this.widgetCategory === WidgetTypeConstants.ACTIVE_ALERT) {
        return GridProperty
      }
      if (this.widgetCategory === WidgetTypeConstants.EVENT_HISTORY) {
        return GridProperty
      }
      if (
        this.widgetCategory === WidgetTypeConstants.MAP_VIEW ||
        this.widgetCategory === WidgetTypeConstants.HEATMAP
      ) {
        return MapProperty
      }
      if (this.widgetCategory === WidgetTypeConstants.SANKEY) {
        return SankeyProperty
      }
      if (this.widgetCategory === WidgetTypeConstants.FREE_TEXT) {
        return FreeTextProperty
      }
      return undefined
    },
    innerValue: {
      get() {
        return this.value || {}
      },
      set(value) {
        this.$emit('change', value)
      },
    },
  },
}
</script>
