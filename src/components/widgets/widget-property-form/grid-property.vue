<template>
  <div class="flex flex-col px-2 flex-1 min-h-0 w-full">
    <MRow class="min-h-0">
      <MCol :size="12" class="mb-2">
        <MTab v-model="currentTab">
          <MTabPane key="style" tab="Style" />
          <MTabPane key="column-setting" tab="Column Setting" />
          <MTabPane
            v-if="shouldShowEventCount"
            key="event-count"
            tab="Event Count"
          />
          <MTabPane
            v-if="shouldShowTimeRangeSelection && shouldAllowTimeLinePreference"
            key="timeline-preference"
            tab="Timeline Preference"
          />
        </MTab>
      </MCol>
    </MRow>
    <div
      class="flex flex-col flex-1 min-h-0 overflow-y-auto"
      :class="{
        'overflow-x-hidden': !allowXOverFlow,
        'overflow-x-auto': allowXOverFlow,
      }"
    >
      <ColumnSettings
        v-if="currentTab === 'column-setting'"
        v-model="columnSettings"
        :allow-x-over-flow="allowXOverFlow"
        :columns="columns"
        :tag-options="tagOptions"
        :should-show-tag-dropdown="showTagDropdown"
      />
      <TimelinePreferenceProperty
        v-else-if="currentTab === 'timeline-preference'"
        :widget="widget"
        v-bind="$attrs"
        @update:widget="$emit('update:widget', $event)"
      />

      <MRow v-else-if="currentTab === 'event-count'" class="w-full" :gutter="0">
        <MCol :size="3">
          <FlotoFormItem
            v-model="eventCount"
            label="Count"
            placeholder="Count"
            class="ml-2 mt-4"
            rules="numeric|max_value:1000|min_value:1"
          />
        </MCol>
      </MRow>

      <template v-else>
        <MRadioGroup
          v-if="isSingleNumericCounterSelected && shouldShowLayoutOptions"
          v-model="layout"
          as-button
          :options="layoutOptions"
          class="my-2"
        >
          <template v-slot:option="{ option }">
            <WidgetTypeIcon
              :widget-type="option.value"
              :tooltip="option.text"
              :size="28"
              :selected="option.value === value.layout"
            />
          </template>
        </MRadioGroup>
        <GridHeaderStyle
          v-if="value.layout !== 'key-value'"
          v-model="headerStyle"
        />

        <FlotoFormItem
          v-if="isSingleNumericCounterSelected && value.layout === 'key-value'"
          label="Sorting"
          class="mt-3"
        >
          <MRadioGroup
            v-model="layoutSorting"
            as-button
            :options="layoutSortingOptions"
          />
        </FlotoFormItem>

        <!-- <MCheckbox
          v-if="isSingleNumericCounterSelected"
          :checked="value.layout === 'key-value'"
          class="m-5"
          @change="handleChecked(value.layout === 'key-value' ? false : true)"
          >Large Font</MCheckbox
        > -->
      </template>
    </div>
  </div>
</template>

<script>
import ColumnSettings from './column-settings.vue'
import GridHeaderStyle from './grid-header-style.vue'
import {
  WidgetTypeConstants,
  AVAILABLE_GROUP_TYPES,
} from '@components/widgets/constants'
import { AvailableReportCategories } from '@modules/report/helpers/report'
import WidgetTypeIcon from '../widget-type-icon/widget-type-icon.vue'
import TimelinePreferenceProperty from './timeline-preference-property.vue'

export default {
  name: 'GridProperty',
  components: {
    ColumnSettings,
    GridHeaderStyle,
    WidgetTypeIcon,
    TimelinePreferenceProperty,
  },
  inject: {
    widgetFormContext: {
      default: {
        setEventCounts() {},
        formData: {},
      },
    },
  },
  inheritAttrs: false,
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default: undefined,
    },
    columns: {
      type: Array,
      default: undefined,
    },
    widgetId: {
      type: Number,
      default: undefined,
    },
    widget: {
      type: Object,
      required: true,
    },
    allowXOverFlow: {
      type: Boolean,
      default: false,
    },
    tagOptions: {
      type: Array,
      default: () => [],
    },
    shouldShowTimeRangeSelection: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.layoutOptions = [
      WidgetTypeConstants.GRID,
      WidgetTypeConstants.KEY_VALUE_LAYOUT,
    ].map((o) => {
      return {
        value: o,
        text: o,
      }
    })
    this.layoutSortingOptions = [
      {
        value: 'desc',
        text: 'Top',
      },
      {
        value: 'asc',
        text: 'Last',
      },
    ]
    return {
      currentTab: 'style',
      isSingleNumericCounterSelected: false,
    }
  },
  computed: {
    eventCount: {
      get() {
        return this.widgetFormContext.formData.eventCount
      },
      set(count) {
        this.widgetFormContext.setEventCounts(count)
      },
    },
    shouldShowEventCount() {
      return (
        [WidgetTypeConstants.EVENT_HISTORY].includes(this.widget.category) &&
        this.widget.containerType !== 'report'
      )
    },
    columnSettings: {
      get() {
        return (this.value || {}).columnSettings || []
      },
      set(columnSettings) {
        this.$emit('change', { ...(this.value || {}), columnSettings })
      },
    },
    headerStyle: {
      get() {
        return (this.value || {}).headerStyle
      },
      set(headerStyle) {
        this.$emit('change', { ...(this.value || {}), headerStyle })
      },
    },
    layout: {
      get() {
        return this.value.layout || WidgetTypeConstants.GRID
      },
      set(layout) {
        this.$emit('change', {
          ...(this.value || {}),
          layout:
            layout === WidgetTypeConstants.KEY_VALUE_LAYOUT
              ? WidgetTypeConstants.KEY_VALUE_LAYOUT
              : WidgetTypeConstants.GRID,
        })
      },
    },
    layoutSorting: {
      get() {
        return this.value.layoutSorting || 'desc'
      },
      set(layoutSorting) {
        if (this.layout === WidgetTypeConstants.KEY_VALUE_LAYOUT) {
          this.$emit('change', {
            ...(this.value || {}),
            layoutSorting,
          })
        }
      },
    },
    showTagDropdown() {
      return (
        (this.widget?.groups?.find((g) =>
          ['metric', 'availability'].includes(g.type)
        ) &&
          [WidgetTypeConstants.GRID, WidgetTypeConstants.TOPN].includes(
            this.widget.category
          ) &&
          ![AvailableReportCategories.POLLING_REPORT].includes(
            this.widget.reportType
          )) ||
        [
          AvailableReportCategories.AVAILABILITY,
          AvailableReportCategories.PERFORMANCE,
          AvailableReportCategories.INVENTORY,
          // AvailableReportCategories.AVAILABILITY_ALERT,
        ].includes(this.widget.reportType)
      )
    },
    shouldShowLayoutOptions() {
      return (
        ![WidgetTypeConstants.EVENT_HISTORY].includes(this.widget.category) &&
        !this.isNetRouteGroup
      )
    },
    isNetRouteGroup() {
      return (
        this.widget?.groups?.[0]?.type === AVAILABLE_GROUP_TYPES.NETROUTE_METRIC
      )
    },
    shouldAllowTimeLinePreference() {
      return ![
        WidgetTypeConstants.EVENT_HISTORY,
        WidgetTypeConstants.STREAM,
      ].includes(this.widget.category)
    },
  },
  watch: {
    columns: {
      immediate: true,
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          const shouldStopShorting =
            this.widget &&
            this.widget.reportType &&
            [
              AvailableReportCategories.AVAILABILITY_ALERT,
              AvailableReportCategories.METRIC_ALERT,
              AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
            ].includes(this.widget.reportType)
          const existingColumnsMap = {}
          this.columnSettings.forEach((c) => {
            existingColumnsMap[c.name] = c
          })
          this.$emit('change', {
            ...(this.value || {}),
            columnSettings: this.columns.map((c) => ({
              ...c,
              ...(this.widgetId
                ? {}
                : {
                    resizable: true,
                    orderable: true,
                    sortable: true,
                    iconPosition: 'prefix',
                    hidden: c.hidden || false,
                  }),
              ...(existingColumnsMap[c.name] || {}),
              name: c.name.replace(/[~^]/g, '.'),

              ...(shouldStopShorting ? { sortable: false } : {}),
            })),
          })
        }
      },
    },
    'widget.groups': {
      immediate: true,
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          if (
            (((newValue || [])[0] || {}).counters || []).length &&
            newValue[0].counters.length === 1 &&
            ((newValue[0].counters[0].counter || {}).dataType || []).includes(
              'numeric'
            )
          ) {
            if (
              (((newValue || [])[0] || {}).type === 'metric' &&
                !['monitor', 'group', 'tag'].includes(
                  ((newValue || [])[0] || {}).resultBy
                )) ||
              this.widget.reportType ||
              this.widget?.containerType === 'report'
            ) {
              this.isSingleNumericCounterSelected = false
              this.$emit('change', {
                ...(this.value || {}),
                layout: undefined,
              })
              return
            }
            this.isSingleNumericCounterSelected = true
          } else {
            this.isSingleNumericCounterSelected = false

            this.$emit('change', {
              ...(this.value || {}),
              layout: undefined,
            })
          }
        }
      },
    },
  },
  // methods: {
  //   handleChecked(event) {
  //     this.$emit('change', {
  //       ...(this.value || {}),
  //       layout: event ? 'key-value' : undefined,
  //     })
  //   },
  // },
}
</script>
