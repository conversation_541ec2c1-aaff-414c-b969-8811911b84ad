<template>
  <div class="flex flex-col flex-1">
    <MRow>
      <MCol :size="4" class="flex items-center">
        <FlotoFormItem
          label="Use Custom Sticky Timeline"
          :info-tooltip="$message('use_custom_sticky_timeline')"
        >
          <MSwitch
            v-model="useCustomStickyTimelineEnabled"
            checked-children="ON"
            un-checked-children="OFF"
          />
        </FlotoFormItem>
      </MCol>
      <MCol
        v-if="useCustomStickyTimelineEnabled"
        class="flex flex-col"
        :size="8"
      >
        <label class="text-neutral-light mb-1">Timeline Preview</label>
        <TimeRangePicker
          :value="customStickyTimeline"
          :disabled="true"
          :bordered="true"
          :allow-clear="false"
          :hide-selected-time="true"
        />
      </MCol>
    </MRow>
  </div>
</template>

<script>
import TimeRangePicker from '@components/widgets/time-range-picker.vue'

export default {
  name: 'TimelinePreferenceProperty',
  components: {
    TimeRangePicker,
  },
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      default: undefined,
    },
  },
  computed: {
    useCustomStickyTimelineEnabled: {
      get() {
        return (this.widget || {}).useWidgetTime
      },
      set(value) {
        this.$emit('update:widget', {
          ...(this.widget || {}),
          useWidgetTime: value,
        })
      },
    },
    customStickyTimeline: {
      get() {
        return (this.widget || {}).timeRange
      },
    },
  },
}
</script>
