<template>
  <MonitorProvider :search-params="searchParams">
    <GroupProvider>
      <MRow
        class="items-center"
        :gutter="useMargin ? 16 : 16"
        :style="{
          marginRight: useMargin ? '75px !important' : 'unset',
        }"
      >
        <!-- <MCol
          v-if="
            [AvailableReportCategories.ACTIVE_ALERTS].includes(
              $attrs['report-category']
            )
          "
          :size="1"
        >
          <FlotoFormItem rules="required" label="Alert By">
            <FlotoDropdownPicker
              v-model="alertBy"
              placeholder="Alert By"
              as-input
              :options="alertByOptions"
            />
          </FlotoFormItem>
        </MCol> -->

        <MCol :size="$attrs['report-category'] ? 2 : 1">
          <FlotoFormItem
            rules="required"
            label="Policy Type"
            :class="{
              'disabled-bordered-dropdown': isCategoryFilterDisabled,
            }"
          >
            <FlotoDropdownPicker
              v-model="category"
              placeholder="Category"
              as-input
              :options="categoryOptions"
              :disabled="isCategoryFilterDisabled"
            />
          </FlotoFormItem>
        </MCol>
        <MCol
          :key="category"
          :size="allowResultBy ? 3 : $attrs['report-category'] ? 4 : 5"
        >
          <MonitorGroupSelection
            v-model="target"
            :disabled="disabledSourceFilter"
            :disabled-source-filter="disabledSourceFilter"
            :entity-options="monitorOrGroupEntityOptions[category]"
            :source-type="category"
            :row-gutter="16"
            :size="{
              type: 4,
              value: 8,
            }"
            show-label
            :report-category="$attrs['report-category']"
          />
        </MCol>
        <MCol :size="2">
          <FlotoFormItem
            label="Policy"
            :class="{
              'disabled-bordered-dropdown': tags.length > 0 || disabled,
            }"
          >
            <FlotoDropdownPicker
              v-model="alertIds"
              placeholder=" "
              :disabled="tags.length > 0 || disabled"
              :options="alertOptions"
              :multiple="!tags.length > 0"
            />
          </FlotoFormItem>
        </MCol>
        <MCol :size="2">
          <!-- v-if="
            ![AvailableReportCategories.ACTIVE_ALERTS].includes(
              $attrs['report-category']
            )
          " -->
          <FlotoFormItem
            label="Policy Tag"
            :class="{
              'disabled-bordered-dropdown': alertIds.length > 0 || disabled,
            }"
          >
            <FlotoDropdownPicker
              v-model="tags"
              placeholder=" "
              :disabled="alertIds.length > 0 || disabled"
              :options="tagsOptions"
              :multiple="!alertIds.length > 0"
            />
          </FlotoFormItem>
        </MCol>
        <MCol :size="shouldShowAddionalColumns ? 1 : 2">
          <FlotoFormItem label="Severity">
            <FlotoDropdownPicker
              v-model="severity"
              placeholder=" "
              :options="serverityOptions"
              multiple
            />
          </FlotoFormItem>
        </MCol>
        <MCol :size="shouldShowAddionalColumns ? 1 : 2">
          <FlotoFormItem v-if="allowResultBy" label="Result By">
            <FlotoDropdownPicker
              v-model="resultBy"
              placeholder=" "
              :options="filteredResultByOptions"
              allow-clear
            />
          </FlotoFormItem>
        </MCol>

        <MCol v-if="shouldShowAddionalColumns" :size="2">
          <AddiitionalColumnProvider
            :report-category="$attrs['report-category']"
            :search-params="{
              counter: category,
            }"
          >
            <FlotoFormItem label="Additional Columns">
              <AddiitionalColumnPicker
                id="custom-monitoring-field"
                v-model="additionalColumns"
                multiple
                :allow-clear="true"
              />
            </FlotoFormItem>
          </AddiitionalColumnProvider>
        </MCol>
      </MRow>
    </GroupProvider>
  </MonitorProvider>
</template>

<script>
import Uniq from 'lodash/uniq'
import IsEqual from 'lodash/isEqual'
import Bus from '@utils/emitter'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import GroupProvider from '@components/data-provider/group-provider.vue'
import MonitorGroupSelection from '../monitor-or-group-selection.vue'
import { getAlertOptionsApi } from '../widgets-api'
import { WidgetTypeConstants, AVAILABLE_GROUP_TYPES } from '../constants'

import AddiitionalColumnProvider from '@components/data-provider/additional-column-provider.vue'
import AddiitionalColumnPicker from '@components/data-picker/additional-column-picker.vue'
import { AvailableReportCategories } from '@modules/report/helpers/report'

import {
  DEFAULT_STREAM_CATEGORY_GROUP,
  POLICY_GRID_DEFAULT_COLUMN_SETTINGS,
} from '@components/widgets/helper'

export default {
  name: 'AlertGroup',
  components: {
    MonitorGroupSelection,
    MonitorProvider,
    GroupProvider,
    AddiitionalColumnProvider,
    AddiitionalColumnPicker,
  },
  model: { event: 'change' },
  props: {
    widgetCategory: { type: String, required: true },
    widgetType: { type: String, default: undefined },
    value: { type: Object, default: undefined },
    disabled: { type: Boolean, default: false },
    isEditMode: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.AvailableReportCategories = AvailableReportCategories

    this.alertByOptions = [
      { key: 'monitor', text: 'Monitor' },
      { key: 'interface', text: 'Interface' },
      { key: 'vm', text: 'VM' },
      { key: 'access point', text: 'Access point' },
      { key: 'process', text: 'Process' },
    ]

    this.categoryOptionsList = [
      { key: 'metric', text: 'Metric' },
      { key: 'log', text: 'Log' },
      { key: 'flow', text: 'Flow' },
      { key: 'trap', text: 'Trap' },
      {
        key: AVAILABLE_GROUP_TYPES.NETROUTE_METRIC,
        text: 'Netroute - Source to Destination',
      },
      {
        key: AVAILABLE_GROUP_TYPES.NETROUTE_EVENT,
        text: 'Netroute - Hop to Hop',
      },
    ]
    this.monitorOrGroupEntityOptions = {
      log: [
        { key: 'event.source', text: 'Source Host', inputType: 'source' },
        // {
        //   key: 'source.plugin',
        //   text: 'Source Plugin',
        //   inputType: 'source.plugin',
        // },
        {
          key: 'event.source.type',
          text: 'Source Type',
          inputType: 'source.type',
        },
        { key: 'Group', text: 'Group', inputType: 'group' },
      ],
      flow: [
        { key: 'event.source', text: 'Source Host', inputType: 'source' },
        { key: 'Group', text: 'Group', inputType: 'group' },
      ],
      trap: [
        { key: 'event.source', text: 'Source Host', inputType: 'source' },
        { key: 'Group', text: 'Group', inputType: 'group' },
      ],
      [AVAILABLE_GROUP_TYPES.NETROUTE_METRIC]: [
        { key: 'netroute.id', text: 'Netroute', inputType: 'netroute' },
        // { key: 'tag', text: 'Tag' },
      ],
      [AVAILABLE_GROUP_TYPES.NETROUTE_EVENT]: [
        { key: 'netroute.id', text: 'Netroute', inputType: 'netroute' },
        // { key: 'tag', text: 'Tag' },
      ],
    }
    // this.serverityOptions = [
    //   { key: 'DOWN', text: 'Down' },
    //   { key: 'CRITICAL', text: 'Critical' },
    //   { key: 'MAJOR', text: 'Major' },
    //   { key: 'WARNING', text: 'Warning' },
    //   // { key: 'CLEAR', text: 'Clear' },
    //   { key: 'UNREACHABLE', text: 'Unreachable' },
    // ]
    this.resultByOptions = {
      metric: [
        { key: 'group', text: 'Group' },
        { key: 'object.id', text: 'Monitor' },
        { key: 'severity', text: 'Severity' },
        { key: 'policy.id', text: 'Alert' },
        // { key: 'tag', text: 'Tag' },
      ],
      log: [
        { key: 'group', text: 'Group' },
        { key: 'source', text: 'Source Host' },
        { key: 'severity', text: 'Severity' },
        { key: 'policy.id', text: 'Alert' },
        // { key: 'tag', text: 'Tag' },
      ],
      flow: [
        { key: 'group', text: 'Group' },
        { key: 'source', text: 'Source Host' },
        { key: 'severity', text: 'Severity' },
        { key: 'policy.id', text: 'Alert' },
        // { key: 'tag', text: 'Tag' },
      ],
      trap: [
        { key: 'group', text: 'Group' },
        { key: 'source', text: 'Source Host' },
        { key: 'severity', text: 'Severity' },
        { key: 'policy.id', text: 'Alert' },
        // { key: 'tag', text: 'Tag' },
      ],
      [AVAILABLE_GROUP_TYPES.NETROUTE_METRIC]: [
        { key: 'netroute.id', text: 'Netroute' },
        { key: 'severity', text: 'Severity' },
        { key: 'policy.id', text: 'Alert' },
      ],
      [AVAILABLE_GROUP_TYPES.NETROUTE_EVENT]: [
        { key: 'netroute.id', text: 'Netroute' },
        { key: 'severity', text: 'Severity' },
        { key: 'policy.id', text: 'Alert' },
      ],
    }
    return {
      alertOptions: [],
      tagsOptions: [],
    }
  },
  computed: {
    isCategoryFilterDisabled() {
      return this.isEditMode && this.isAlertStream
    },
    isHeatmap() {
      return [
        WidgetTypeConstants.HEATMAP,
        WidgetTypeConstants.ACTIVE_ALERT,
      ].includes(this.widgetCategory)
    },
    isAlertStream() {
      return this.widgetCategory === WidgetTypeConstants.STREAM
    },
    filteredResultByOptions() {
      let included = ['severity', 'object.id', 'policy.id']
      if (WidgetTypeConstants.HEATMAP === this.widgetCategory) {
        if (this.target && this.target.entityType === 'Group') {
          included = ['object.id', 'group', 'policy.id']
        } else {
          included = ['object.id', 'policy.id']
        }
      }
      if (this.target && this.target.entityType === 'Monitor') {
        included = [...included, 'object.id']
      }
      if (this.target && this.target.entityType === 'Group') {
        included = [...included, 'object.id', 'group']
      }
      if (this.tags.length > 0) {
        included = [...included, 'tag']
      }
      if (this.alertIds.length > 0) {
        included = [...included, 'policy.id']
      }

      if (this.category === AVAILABLE_GROUP_TYPES.NETROUTE_METRIC) {
        included = [...included, 'netroute.id']
      }
      if (this.category === AVAILABLE_GROUP_TYPES.NETROUTE_EVENT) {
        included = [...included, 'netroute.id']
      }

      return included.length > 0
        ? this.resultByOptions[this.category].filter((i) =>
            included.includes(i.key)
          )
        : this.resultByOptions[this.category]
    },
    allowResultBy() {
      return (
        // [WidgetTypeConstants.GRID, WidgetTypeConstants.GAUGE].includes(
        //   this.widgetCategory
        // ) === false
        [
          WidgetTypeConstants.GAUGE,
          WidgetTypeConstants.STREAM,
          WidgetTypeConstants.HEATMAP,
          WidgetTypeConstants.ACTIVE_ALERT,
        ].includes(this.widgetCategory) === false
      )
    },
    searchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.SDN,
          this.$constants.OTHER,
          this.$constants.CLOUD,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.STORAGE,
          this.$constants.CONTAINER_ORCHESTRATION,
        ],
      }
    },
    hasTarget() {
      const target = this.target || {}
      return (target.entities || []).length > 0
    },
    category: {
      get() {
        return (this.value || {}).category
      },
      set(category) {
        this.$emit('change', {
          ...(this.value || {}),
          category,
          target: { entityType: undefined },
          alertIds: undefined,
          tags: undefined,
          severity: undefined,
          resultBy:
            [WidgetTypeConstants.GRID, WidgetTypeConstants.TOPN].includes(
              this.widgetCategory
            ) && ['log', 'flow', 'trap'].includes(category)
              ? 'severity'
              : undefined,
          additionalColumns: undefined,

          ...(this.isAlertStream
            ? { ...DEFAULT_STREAM_CATEGORY_GROUP[category] }
            : {}),
        })

        this.$nextTick(this.generateNewPreview)
      },
    },
    target: {
      get() {
        return (this.value || {}).target
      },
      set(target) {
        this.$emit('change', {
          ...(this.value || {}),
          target,
          alertIds: undefined,
          tags: undefined,
          severity: undefined,
          // resultBy: this.isHeatmap ? 'monitor' : undefined,
          resultBy: undefined,
        })
        this.$nextTick(this.generateNewPreview)
      },
    },
    alertIds: {
      get() {
        return (this.value || {}).alertIds || []
      },
      set(alertIds) {
        this.$emit('change', {
          ...(this.value || {}),
          alertIds,
          tags: undefined,
        })
        this.$nextTick(this.generateNewPreview)
      },
    },
    tags: {
      get() {
        return (this.value || {}).tags || []
      },
      set(tags) {
        this.$emit('change', {
          ...(this.value || {}),
          tags,
          alertIds: undefined,
        })
        this.$nextTick(this.generateNewPreview)
      },
    },

    alertBy: {
      get() {
        return (this.value || {}).alertBy || []
      },
      set(alertBy) {
        this.$emit('change', {
          ...(this.value || {}),
          target: { entityType: undefined },
          alertIds: undefined,
          tags: undefined,
          severity: undefined,
          resultBy: undefined,
          alertBy,
        })
        this.$nextTick(this.generateNewPreview)
      },
    },

    severity: {
      get() {
        return (this.value || {}).severity
      },
      set(severity) {
        this.$emit('change', {
          ...(this.value || {}),
          severity,
        })
        this.$nextTick(this.generateNewPreview)
      },
    },
    resultBy: {
      get() {
        return (this.value || {}).resultBy
      },
      set(resultBy) {
        this.$emit('change', {
          ...(this.value || {}),
          resultBy,
        })
        if (
          resultBy === 'object.id' &&
          this.category === 'metric' &&
          (this.widgetCategory === WidgetTypeConstants.GRID ||
            (this.widgetCategory === WidgetTypeConstants.TOPN &&
              this.widgetType === WidgetTypeConstants.GRID))
        ) {
          Bus.$emit(
            'update:widget-column-settings',
            POLICY_GRID_DEFAULT_COLUMN_SETTINGS
          )
        }
        this.$nextTick(this.generateNewPreview)
      },
    },
    additionalColumns: {
      get() {
        return this.value.additionalColumns || []
      },
      set(additionalColumns) {
        this.$emit('change', { ...this.value, additionalColumns })
        this.$nextTick(this.generateNewPreview)
      },
    },
    categoryOptions() {
      if (
        this.isHeatmap ||
        [AvailableReportCategories.METRIC_ALERT].includes(
          this.$attrs['report-category']
        )
      ) {
        return this.categoryOptionsList.filter((c) => c.key === 'metric')
      } else {
        return this.categoryOptionsList
      }
    },
    shouldShowAddionalColumns() {
      return [
        // AvailableReportCategories.ACTIVE_ALERTS,
        // AvailableReportCategories.AVAILABILITY_ALERT,
        // AvailableReportCategories.METRIC_ALERT,
      ].includes(this.$attrs['report-category'])
    },

    useMargin() {
      return ![
        AvailableReportCategories.ACTIVE_ALERTS,
        // AvailableReportCategories.AVAILABILITY_ALERT,
        AvailableReportCategories.METRIC_ALERT,
      ].includes(this.$attrs['report-category'])
    },
    isActiveAlertWidget() {
      return this.widgetCategory === WidgetTypeConstants.ACTIVE_ALERT
    },
    serverityOptions() {
      let serverity = [
        { key: 'DOWN', text: 'Down' },
        { key: 'CRITICAL', text: 'Critical' },
        { key: 'MAJOR', text: 'Major' },
        { key: 'WARNING', text: 'Warning' },
        { key: 'CLEAR', text: 'Clear' },
        { key: 'UNREACHABLE', text: 'Unreachable' },
      ]

      const disabledSeverityOptions = []

      if (this.isActiveAlertWidget) {
        disabledSeverityOptions.push(this.$constants.CLEAR)
      }

      if (this.category !== 'metric') {
        disabledSeverityOptions.push(
          this.$constants.DOWN,
          this.$constants.UNREACHABLE
        )
      }
      return serverity.filter((s) => !disabledSeverityOptions.includes(s.key))
    },

    disabledSourceFilter() {
      return (
        this.disabled ||
        ([
          WidgetTypeConstants.CHART,
          WidgetTypeConstants.GRID,
          WidgetTypeConstants.TOPN,
          WidgetTypeConstants.GAUGE,
          WidgetTypeConstants.STREAM,
        ].includes(this.widgetCategory) &&
          [
            'log',
            'flow',
            'trap',
            AVAILABLE_GROUP_TYPES.NETROUTE_METRIC,
            AVAILABLE_GROUP_TYPES.NETROUTE_EVENT,
          ].includes(this.category))
      )
    },
  },
  watch: {
    category: function (newValue, oldValue) {
      if (!IsEqual(newValue, oldValue)) {
        this.getAlertOptions()
      }
    },
    'value.target': function (newValue, oldValue) {
      if (!IsEqual(newValue, oldValue)) {
        this.getAlertOptions()
      }
    },
  },
  mounted() {
    this.generateNewPreview()
    this.getAlertOptions()
  },
  beforeDestroy() {
    setTimeout(() => {
      this.generateNewPreview()
    })
  },
  methods: {
    generateNewPreview() {
      Bus.$emit('widget.generate.preview')
      // if (this.allowResultBy && this.resultBy) {
      //   Bus.$emit('widget.generate.preview')
      // }
      // if (!this.allowResultBy) {
      //   Bus.$emit('widget.generate.preview')
      // }
    },
    getAlertOptions() {
      getAlertOptionsApi({
        category: this.category,
      }).then((result) => {
        this.alertOptions = Object.freeze(
          result.map((a) => ({
            key: a.id,
            text: a['name'],
          }))
        )

        let tags = []

        for (const a of result) {
          tags = tags.concat([...(a['tag'] || [])])
        }

        tags = Uniq(tags)

        // Uniq(
        //   result.reduce((prev, a) => [...prev, ...(a['tag'] || [])], [])
        // )
        this.tagsOptions = tags.map((tag) => {
          return {
            key: tag,
            text: tag,
          }
        })
      })
    },
  },
}
</script>
