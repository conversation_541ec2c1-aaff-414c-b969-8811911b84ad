<template>
  <MonitorProvider :search-params="searchParams">
    <GroupProvider>
      <CounterProvider
        :key="widget.category"
        type="Log"
        :search-params="counterSearchParams"
        :widget-category="widgetCategory"
        @counters="setCounters"
        @loaded="isCounterLoaded = true"
      >
        <div v-if="isCounterLoaded" class="flex flex-col flex-1">
          <MRow :gutter="16">
            <MCol :size="12">
              <FlotoFormItem rules="required">
                <MultipleFormItems
                  v-model="counters"
                  :item-template="itemTemplate"
                  :show-icon="false"
                  add-btn-text="Add Counter"
                  :max-items="maxAllowedCounters"
                >
                  <template
                    v-slot="{
                      item,
                      update,
                      remove,
                      add,
                      index,
                      isLastItem,
                      isFirstItem,
                      canAdd,
                    }"
                  >
                    <MRow class="items-center flex-no-wrap" :gutter="16">
                      <MCol :size="3">
                        <FlotoFormItem
                          rules="required"
                          :label="index === 0 ? 'Counter' : undefined"
                        >
                          <CounterPicker
                            :value="(item.counter || {}).key"
                            placeholder="Select Counter"
                            :disabled="disabled"
                            :disabled-options="disabledCounterOptions"
                            @change="
                              update({ ...(item || {}), counter: $event })
                            "
                          />
                        </FlotoFormItem>
                      </MCol>
                      <MCol
                        v-if="shouldShowAggregation"
                        style="max-width: 210px"
                      >
                        <FlotoFormItem
                          rules="required"
                          :label="index === 0 ? 'Aggregation' : undefined"
                        >
                          <FlotoDropdownPicker
                            :value="item.aggrigateFn"
                            :searchable="false"
                            :options="
                              item.counter
                                ? (item.counter.dataType || []).length === 1
                                  ? resultByOptionsKeys.includes(
                                      item.counter.key
                                    )
                                    ? aggrigateOptions.all
                                    : aggrigateOptions[item.counter.dataType[0]]
                                  : aggrigateOptions.all
                                : []
                            "
                            placeholder=" "
                            @change="
                              update({ ...(item || {}), aggrigateFn: $event })
                            "
                          />
                        </FlotoFormItem>
                      </MCol>
                      <MCol
                        v-if="index === 0"
                        :size="shouldShowAddionalColumns ? 3 : 5"
                      >
                        <MonitorGroupSelection
                          v-model="target"
                          :disabled="disabled"
                          :entity-options="entityOptions"
                          source-type="log"
                          :timeline="timeline"
                          :row-gutter="16"
                          :size="{ type: 4, value: 8 }"
                          show-label
                        />
                      </MCol>

                      <MCol v-if="shouldShowResultBy && index === 0" :size="2">
                        <FlotoFormItem label="Result By">
                          <FlotoDropdownPicker
                            id="resultBy"
                            v-model="resultBy"
                            placeholder=" "
                            multiple
                            :options="resultByOptions"
                          ></FlotoDropdownPicker>
                        </FlotoFormItem>
                      </MCol>

                      <MCol v-if="index === 0 && shouldShowAddionalColumns">
                        <AddiitionalColumnProvider
                          :report-category="$attrs['report-category']"
                          :search-params="{
                            counter: item.counter,
                          }"
                        >
                          <FlotoFormItem label="Additional Columns">
                            <AddiitionalColumnPicker
                              id="custom-monitoring-field"
                              v-model="additionalColumns"
                              multiple
                              :allow-clear="true"
                            />
                          </FlotoFormItem>
                        </AddiitionalColumnProvider>
                      </MCol>
                      <MCol
                        style="width: 80px"
                        class="fixed-size flex items-center"
                        :class="{ 'mb-2': index !== 0 }"
                      >
                        <span
                          v-if="!isFirstItem"
                          id="remove-counter"
                          class="mr-2 flex items-center"
                          @click="remove"
                        >
                          <MIcon
                            name="times-circle"
                            class="cursor-pointer text-secondary-red"
                            size="lg"
                          />
                        </span>
                        <span
                          v-if="isLastItem && canAdd"
                          id="add-counter"
                          outline
                          class="flex items-center"
                          @click="add"
                        >
                          <MIcon
                            name="plus-circle"
                            class="text-primary cursor-pointer"
                            size="lg"
                          />
                        </span>
                      </MCol>
                    </MRow>
                  </template>
                </MultipleFormItems>
              </FlotoFormItem>
            </MCol>
            <MCol :size="12" class="mb-3">
              <MRow :gutter="16" style="margin-right: 50px !important">
                <MCol>
                  <FiltersContainer
                    v-if="selectedCounters.length > 0"
                    v-model="filters"
                    group-type="log"
                    :timeline="timeline"
                    :max-post-groups="1"
                    :max-pre-groups="3"
                    :all-counters="filterCounters"
                    placeholder="Filters"
                    :selected-counters="availableCountersForConditions"
                    :excluded-tabs="filtersExcludedTab"
                  />
                </MCol>
                <MCol auto-size class="flex justify-end items-center">
                  <!-- <div class="mr-4 mb-4">
                    <MCheckbox
                      id="auto-assign"
                      v-model="useReceivedTime"
                      :checked="useReceivedTime"
                      >Log Received Time</MCheckbox
                    >
                  </div> -->
                </MCol>
              </MRow>
            </MCol>
          </MRow>
        </div>
        <div v-else class="flex flex-col flex-1 items-center justify-center">
          <MLoader />
        </div>
      </CounterProvider>
    </GroupProvider>
  </MonitorProvider>
</template>

<script>
import UniqBy from 'lodash/uniqBy'
import IsEqual from 'lodash/isEqual'
import CloneDeep from 'lodash/cloneDeep'
import Intersection from 'lodash/intersection'
import Bus from '@utils/emitter'
import { generateId } from '@utils/id'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import GroupProvider from '@components/data-provider/group-provider.vue'
import CounterProvider from '@components/data-provider/counter-provider.vue'
import CounterPicker from '@components/data-picker/counter-picker.vue'
import MultipleFormItems from '@components/multiple-form-items.vue'
import FiltersContainer from '@components/filters/filters-container.vue'
import { WidgetTypeConstants, AGGRIGATION_OPTIONS } from '../constants'
import MonitorGroupSelection from '../monitor-or-group-selection.vue'
import { getResultByOptionsApi } from '../widgets-api'
import AddiitionalColumnProvider from '@components/data-provider/additional-column-provider.vue'
import AddiitionalColumnPicker from '@components/data-picker/additional-column-picker.vue'
// import { AvailableReportCategories } from '@modules/report/helpers/report'

const EXCLUDED_COUNTERS = [
  'source.host',
  'source.plugin',
  'event.received.time',
]

export default {
  name: 'LogGroup',
  components: {
    CounterPicker,
    MonitorProvider,
    CounterProvider,
    GroupProvider,
    MultipleFormItems,
    MonitorGroupSelection,
    FiltersContainer,
    AddiitionalColumnProvider,
    AddiitionalColumnPicker,
  },
  model: { event: 'change' },
  props: {
    value: { type: Object, default: undefined },
    disabled: { type: Boolean, default: false },
    widgetCategory: { type: String, default: undefined },
    useSameTypeOfCounters: { type: Boolean, default: false },
    timeline: { type: Object, required: true },
    widget: {
      type: Object,
      required: true,
    },
  },
  data() {
    this.entityOptions = [
      { key: 'event.source', text: 'Source Host', inputType: 'source' },
      // {
      //   key: 'event.category',
      //   text: 'Source Plugin',
      //   inputType: 'source.plugin',
      // },
      {
        key: 'event.source.type',
        text: 'Source Type',
        inputType: 'source.type',
      },
      { key: 'group', text: 'Group', inputType: 'group' },
    ]
    this.itemTemplate = {}
    this.aggrigateOptions = CloneDeep({
      ...AGGRIGATION_OPTIONS,
      numeric: AGGRIGATION_OPTIONS.numeric
        .filter((i) => ['sum', 'avg'].includes(i.key))
        .concat([{ key: 'count', text: 'Count' }]),
      string: AGGRIGATION_OPTIONS.string.filter(({ key }) => key === 'count'),
      all: AGGRIGATION_OPTIONS.all.filter(({ key }) => key === 'count'),
    })
    return {
      resultByOptions: [],
      resultByOptionsWithPlugins: [],
      allCounters: [],
      isCounterLoaded: false,
    }
  },
  computed: {
    resultByOptionsKeys() {
      return this.resultByOptions.map((i) => i.key)
    },
    shouldShowAggregation() {
      return !this.isEventHistoryGrid
    },
    isEventHistoryGrid() {
      return [WidgetTypeConstants.EVENT_HISTORY].includes(this.widget.category)
    },
    filtersExcludedTab() {
      if (this.isEventHistoryGrid) {
        return ['post']
      }
      return []
    },

    filterCounters() {
      return UniqBy(
        [
          {
            text: 'event.source.type',
            key: 'event.source.type',
          },
          {
            text: 'event.category',
            key: 'event.category',
          },
          {
            text: 'message',
            key: 'message',
          },
        ].concat(this.resultByOptions),
        'key'
      )
    },
    shouldShowResultBy() {
      if (this.isEventHistoryGrid) {
        return false
      }

      return [WidgetTypeConstants.CHART, WidgetTypeConstants.GAUGE].includes(
        this.widgetCategory
      )
        ? WidgetTypeConstants.GAUGE === this.widgetCategory
          ? false
          : this.selectedCounters.length <= 1
        : true
    },
    availableCountersForConditions() {
      const counters = this.counters
      return counters
        .map((c) => ({ ...c.counter, aggrigateFn: c.aggrigateFn }))
        .filter(({ key }) => EXCLUDED_COUNTERS.includes(key) === false)
    },
    counterSearchParams() {
      return {
        'visualization.group.type': 'log',
      }
    },
    selectedCounters() {
      const counters = this.counters
      return counters
        .map((c) => (c.counter || {}).parentId || (c.counter || {}).key)
        .filter(Boolean)
    },
    selectedPluginIds() {
      const selectedCounterNames = this.selectedCounters
      if (selectedCounterNames.length === 0) {
        return []
      }

      const filteredPluginIdByselectedCounter =
        this.resultByOptionsWithPlugins.filter((c) =>
          selectedCounterNames.includes(c.key)
        )

      let pluinIdMap = []

      for (const i of filteredPluginIdByselectedCounter) {
        pluinIdMap = pluinIdMap.concat([
          ...(i.metricPlugins || []).map((id) => `${id}-${i.eventCategory}`),
        ])
      }

      return pluinIdMap

      // this.resultByOptionsWithPlugins
      //   .filter((c) => selectedCounterNames.includes(c.key))
      //   .reduce(
      //     (prev, i) => [
      //       ...prev,
      //       ...(i.metricPlugins || []).map((id) => `${id}-${i.eventCategory}`),
      //     ],
      //     []
      //   )
    },
    disabledCounterOptions() {
      const eventCategory =
        ((this.counters[0] || {}).counter || {}).eventCategory || ''
      let disabledCounters = this.selectedCounters.concat(EXCLUDED_COUNTERS)
      if ([WidgetTypeConstants.EVENT_HISTORY].includes(this.widgetCategory)) {
        disabledCounters.push('message')
      }
      if (eventCategory) {
        disabledCounters = disabledCounters.concat(
          this.resultByOptionsWithPlugins
            .filter((i) => i['eventCategory'] !== eventCategory)
            .map((c) => c.key)
        )
      }
      return disabledCounters
    },
    searchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.OTHER,
          this.$constants.CLOUD,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.SDN,
          this.$constants.STORAGE,
          this.$constants.CONTAINER_ORCHESTRATION,
        ],
      }
    },
    target: {
      get() {
        return (this.value || {}).target
      },
      set(target) {
        this.$emit('change', {
          ...(this.value || {}),
          target,
        })
        this.$nextTick(() => {
          this.generateNewPreview()
        })
      },
    },
    useReceivedTime: {
      get() {
        return (this.value || {}).useReceivedTime
      },
      set(useReceivedTime) {
        this.$emit('change', { ...(this.value || {}), useReceivedTime })
      },
    },
    resultBy: {
      get() {
        return (this.value || {}).resultBy || []
      },
      set(resultBy) {
        if (resultBy.length > this.$constants.LOG_MAX_RESULT_BY) {
          resultBy = resultBy.slice(
            resultBy.length - this.$constants.LOG_MAX_RESULT_BY
          )
        }
        this.$emit('change', { ...(this.value || {}), resultBy })
        this.$nextTick(() => {
          this.generateNewPreview()
        })
      },
    },

    additionalColumns: {
      get() {
        return this.value.additionalColumns || []
      },
      set(additionalColumns) {
        this.$emit('change', { ...this.value, additionalColumns })
        this.$nextTick(this.generateNewPreview)
      },
    },
    counters: {
      get() {
        return (this.value || {}).counters
      },
      set(counters) {
        const firstCounterMetricPlugins =
          ((counters[0] || {}).counter || {}).metricPlugins || []
        let data = counters
          .map((c) =>
            c.counter
              ? Intersection(
                  c.counter.metricPlugins || [],
                  firstCounterMetricPlugins
                ).length
                ? {
                    ...c,
                    ...(c.aggrigateFn
                      ? c.counter &&
                        c.counter.dataType &&
                        this.aggrigateOptions[
                          c.counter.dataType.length === 1
                            ? c.counter.dataType[0]
                            : 'all'
                        ].find(({ key }) => key === c.aggrigateFn)
                        ? {}
                        : {
                            aggrigateFn: (
                              (c.counter || {}).dataType || []
                            ).includes('numeric')
                              ? 'avg'
                              : 'count',
                          }
                      : {
                          aggrigateFn: (
                            (c.counter || {}).dataType || []
                          ).includes('numeric')
                            ? 'avg'
                            : 'count',
                        }),

                    ...(this.isEventHistoryGrid
                      ? { aggrigateFn: '__NONE__' }
                      : {}),
                  }
                : undefined
              : c
          )
          .filter(Boolean)
        let additionalData = this.getCounterChangedData(counters)
        this.$emit('change', {
          ...(this.value || {}),
          counters: data,
          ...additionalData,
        })
        additionalData = null

        let actualCounterLengthOfOldValue = this.value.counters.filter(
          (counterGroup) =>
            counterGroup &&
            counterGroup['counter'] &&
            counterGroup['counter']['counterName'] !== null
        )
        let actualCounterLengthOfNewValue = counters.filter(
          (counterGroup) =>
            counterGroup &&
            counterGroup['counter'] &&
            counterGroup['counter']['counterName'] !== null
        )

        if (
          counters.length === this.value.counters.length ||
          actualCounterLengthOfOldValue.length !==
            actualCounterLengthOfNewValue.length
        ) {
          this.$nextTick(() => {
            this.generateNewPreview()
          })
        }
      },
    },
    filters: {
      get() {
        return (this.value || {}).filters
      },
      set(filters) {
        this.$emit('change', { ...(this.value || {}), filters })
        this.$nextTick(() => {
          this.generateNewPreview()
        })
      },
    },
    selectedEventCategory() {
      const selectedCounterNames = this.selectedCounters
      if (selectedCounterNames.length === 0) {
        return []
      }
      return this.resultByOptionsWithPlugins
        .filter((c) => selectedCounterNames.includes(c.key))
        .map((i) => i.eventCategory)
    },
    shouldShowAddionalColumns() {
      return [
        // AvailableReportCategories.LOG_ANALYTICS
      ].includes(this.$attrs['report-category'])
    },
    maxAllowedCounters() {
      return [WidgetTypeConstants.TOPN].includes(this.widgetCategory) &&
        [
          WidgetTypeConstants.TREE_VIEW,
          WidgetTypeConstants.PACKED_BUBBLE_CHART,
        ].includes(this.widget.widgetType)
        ? 1
        : undefined
    },
  },
  watch: {
    selectedPluginIds(newValue, oldValue) {
      if (!IsEqual(newValue, oldValue)) {
        this.getResultByOptions()
      }
    },
    widgetCategory(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.getResultByOptions()
      }
    },
  },
  created() {
    this.generateNewPreview()
  },
  beforeDestroy() {
    setTimeout(() => {
      this.generateNewPreview()
    })
  },
  methods: {
    async getResultByOptions() {
      const pluginIds = this.selectedPluginIds
      if (pluginIds.length) {
        getResultByOptionsApi(pluginIds).then((data) => {
          this.resultByOptions = Object.freeze(
            (this.widgetCategory === WidgetTypeConstants.MAP_VIEW
              ? data.filter((option) => /(city|country)$/.test(option))
              : data
            ).map((i) => ({ key: i, text: i }))
          )
          this.resultBy = this.resultBy.filter((i) => data.includes(i))
        })
      } else {
        this.resultByOptions = []
      }
    },
    generateNewPreview() {
      // const nonCounterItem = (this.counters || []).filter(
      //   (c) => !(c.counter || {}).key
      // )
      // if (nonCounterItem.length === 0) {
      Bus.$emit('widget.generate.preview')
      // }
    },
    setCounters(optionsMap) {
      this.resultByOptionsWithPlugins = Array.from(optionsMap.values())
      const optionsArr = Array.from(optionsMap.keys())
      this.allCounters = Object.freeze(
        optionsArr
          .filter((k) => EXCLUDED_COUNTERS.includes(k) === false)
          .map((o) => ({ text: o, key: o }))
      )

      if (this.value?.counters?.every((c) => c?.counter?.key)) {
        this.$emit('change', {
          ...(this.value || {}),
          counters: this.value?.counters?.map((c) => {
            const counterkey = c?.counter?.key

            const counterContext = this.resultByOptionsWithPlugins.find(
              (c) => c.key === counterkey
            )

            return {
              ...c,
              counter: counterContext,
            }
          }),
        })
      }

      if (
        this.value?.counters?.[0]?.counter &&
        !this.value.counters[0].counter.key &&
        this.resultByOptionsWithPlugins?.length
      ) {
        let counter = this.resultByOptionsWithPlugins.filter(
          (c) => c.counterName === 'message'
        )[0] || {
          key: 'message',
          counterName: 'message',
          name: 'message',
          isStatusCounter: false,
        }

        if (this.isEventHistoryGrid) {
          counter = this.resultByOptionsWithPlugins[0]
        }
        this.$emit('change', {
          ...(this.value || {}),
          counters: [
            {
              key: generateId(),
              aggrigateFn: 'count',
              counter,
            },
          ],
        })
      }
    },
    getCounterChangedData(counters) {
      let additionalData = {}
      // if chart or gauge then apply permutation
      const counterKeys = counters
        .map((c) => (c.counter || {}).key)
        .filter(Boolean)
      // const selectedKeys = this.selectedCounters
      // const updatedCounterLength = counterKeys.filter(
      //   (k) => selectedKeys.indexOf(k) === -1
      // ).length
      // if (updatedCounterLength) {
      //   additionalData.filters = {
      //     pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
      //     post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
      //   }
      // }
      if (!counters?.[0]?.counter) {
        additionalData.resultBy = []
      }
      if (
        [WidgetTypeConstants.CHART, WidgetTypeConstants.GAUGE].includes(
          this.widgetcategory
        ) &&
        counterKeys.length > 1
      ) {
        additionalData.resultBy = undefined
      }
      additionalData.additionalColumns = undefined

      return additionalData
    },
  },
}
</script>

<style lang="less" scoped>
.filter-viewer {
  background: var(--neutral-lightest);
  border: 1px solid var(--border-color);
}
</style>
