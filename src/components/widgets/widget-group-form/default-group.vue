<template>
  <ObjectTagProvider
    ref="tagProviderRef"
    ignore-watch
    :counter="firstSelectedCounterContext"
  >
    <MonitorProvider
      :search-params="searchParams"
      :fetch-archived-objectes="shouldFetchArchivedObjectes"
      :show-archived-monitors="showArchivedMonitors"
    >
      <GroupProvider>
        <CounterProvider
          ref="counterProviderRef"
          :widget-category="widgetCategory"
          :search-params="counterSearchParams"
          :report-category="$attrs['report-category']"
          :show-loader="showLoader"
          @loaded="isCounterLoaded = true"
          @counters="setCounters"
        >
          <div v-if="isCounterLoaded" class="flex flex-col flex-1">
            <MRow :gutter="16">
              <MCol :size="12">
                <FlotoFormItem rules="required">
                  <MultipleFormItems
                    v-model="counters"
                    :max-items="maxAllowedCounters"
                    :item-template="itemTemplate"
                    :show-icon="false"
                    add-btn-text="Add Counter"
                  >
                    <template
                      v-slot="{
                        item,
                        update,
                        remove,
                        add,
                        index,
                        isLastItem,
                        isFirstItem,
                        canAdd,
                      }"
                    >
                      <DefaultGroupItem
                        :value="item"
                        :group-type="groupType"
                        :all-counters="counters"
                        :item-index="index"
                        :can-select-monitor="
                          canSelectMonitorWithEachCounter || index === 0
                        "
                        :disabled="disabled"
                        :instance-type="instanceType"
                        :counter-data-type="counterDataType"
                        :excluded-counters="excludedCounters"
                        :aggrigate-options="aggrigationOptionsForCounter(item)"
                        :widget-category="widgetCategory"
                        :can-add="isLastItem && canAddNewItem && canAdd"
                        :can-remove="!isFirstItem"
                        :instance="selectedInstance"
                        :scalar-only="
                          !isFirstItem &&
                          !selectedInstance &&
                          selectedCounters.length > 0
                        "
                        :monitor-selection-props="{
                          ...([
                            AvailableReportCategories.POLLING_REPORT,
                          ].includes($attrs['report-category'])
                            ? {
                                multipeEntity: false,
                              }
                            : {}),

                          size: {
                            type: $attrs['disable-type-selector'] ? 0 : 4,
                            value: $attrs['disable-type-selector'] ? 12 : 8,
                          },
                          colSize: $attrs['disable-type-selector']
                            ? 3
                            : isChartWidget || isGridWidget || isTopnWidget
                            ? 4
                            : 5,

                          'show-label': index === 0,
                          'report-category': $attrs['report-category'],
                          'disable-type-selector':
                            $attrs['disable-type-selector'],

                          'show-archived-monitors': showArchivedMonitors,
                          'excluded-entity-type-options': [
                            ...(showArchivedMonitors ? ['Group', 'Tag'] : []),
                            ...([
                              AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
                            ].includes($attrs['report-category'])
                              ? ['Tag']
                              : []),
                          ],

                          counter:
                            ((isHeatmapWidget || isActiveAlertWidget) &&
                              isAvailabilityWidget &&
                              ((item || {}).counter || {}).key !== 'monitor') ||
                            ([
                              AvailableReportCategories.AVAILABILITY,
                              AvailableReportCategories.INVENTORY,
                              AvailableReportCategories.AVAILABILITY_ALERT,
                              AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
                            ].includes($attrs['report-category']) &&
                              ((item || {}).counter || {}).key !== 'monitor')
                              ? { key: '~' }
                              : item.counter,
                        }"
                        v-bind="metricItemAttrs"
                        @change="update"
                        @add="
                          canSelectMonitorWithEachCounter && counters.length > 0
                            ? add({ target: counters[0].target })
                            : add()
                        "
                        @remove="remove"
                      >
                        <MCol v-if="shouldShowInstanceOptions" :size="3">
                          <FlotoFormItem
                            v-if="instanceOptions.length"
                            label="Instance"
                          >
                            <FlotoDropdownPicker
                              v-model="instance"
                              placeholder="Select Instance"
                              :options="instanceOptions"
                              allow-clear
                            />
                          </FlotoFormItem>
                        </MCol>
                        <MCol
                          v-if="
                            index === 0 &&
                            ((isHeatmapWidget && isAvailabilityWidget) ||
                              isActiveAlertWidget ||
                              isStatusFlap) &&
                            (!isChartWidget || selectedCounters.length === 1)
                          "
                          :size="selectedCounters.length === 1 ? 2 : 2"
                        >
                          <FlotoFormItem label="Status">
                            <FlotoDropdownPicker
                              v-model="status"
                              :searchable="false"
                              placeholder=" "
                              multiple
                              :options="statusOptions"
                              :allow-clear="true"
                            >
                              <template
                                v-slot:before-menu-text="{ item: menuItem }"
                              >
                                <Severity
                                  disable-tooltip
                                  :severity="menuItem.key"
                                  class="mx-1"
                                />
                              </template>
                            </FlotoDropdownPicker>
                          </FlotoFormItem>
                        </MCol>
                        <MCol
                          v-if="
                            index === 0 &&
                            !isGaugeWidget &&
                            (!isHeatmapWidget ||
                              (isAvailabilityWidget && isHeatmapWidget) ||
                              (isMetricGroupTypeWidget && isHeatmapWidget)) &&
                            !isActiveAlertWidget &&
                            !isStatusFlap &&
                            (!isChartWidget || selectedCounters.length === 1) &&
                            !shouldHideResultBy
                          "
                          :style="{
                            ...(isChartWidget || isGridWidget || isTopnWidget
                              ? { maxWidth: '210px' }
                              : {}),
                          }"
                          :size="
                            isChartWidget || isGridWidget || isTopnWidget
                              ? undefined
                              : selectedCounters.length === 1
                              ? 2
                              : 2
                          "
                        >
                          <FlotoFormItem
                            :required="!isChartWidget"
                            label="Result By"
                          >
                            <FlotoDropdownPicker
                              v-model="resultBy"
                              placeholder=" "
                              :options="resultByOptions"
                              :allow-clear="isChartWidget || isHeatmapWidget"
                            />
                          </FlotoFormItem>
                        </MCol>

                        <MCol
                          v-if="shouldShowArithmeticOperationDropdown"
                          :style="{
                            ...(shouldShowArithmeticOperationDropdown
                              ? { width: '210px' }
                              : {}),
                          }"
                        >
                          <ArithmeticOperationDropdown
                            v-model="item.arithmeticOperation"
                            :options="
                              !isChartWidget
                                ? [
                                    {
                                      key: 'arithmetic',
                                      name: 'Arithmetic',
                                      groupName: 'Arithmetic',
                                      id: 'arithmetic',
                                    },
                                    {
                                      key: 'log2',
                                      name: 'Log 2',
                                      groupName: 'Arithmetic',
                                      parentId: 'arithmetic',
                                      id: 'log2',
                                    },
                                    {
                                      key: 'log10',
                                      name: 'Log 10',
                                      groupName: 'Arithmetic',
                                      parentId: 'arithmetic',
                                      id: 'log10',
                                    },
                                  ]
                                : undefined
                            "
                            :allow-clear="true"
                            :item="item"
                            :index="index"
                            :max-level="4"
                            class="w-full"
                            @change="
                              update({
                                ...item,
                                arithmeticOperation: $event,
                              })
                            "
                          />
                        </MCol>
                        <MCol v-if="index === 0 && shouldShowAddionalColumns">
                          <AddiitionalColumnProvider
                            :report-category="$attrs['report-category']"
                            :search-params="{
                              counter: item.counter,
                            }"
                          >
                            <FlotoFormItem label="Additional Columns">
                              <AddiitionalColumnPicker
                                id="custom-monitoring-field"
                                v-model="additionalColumns"
                                multiple
                                :allow-clear="true"
                              />
                            </FlotoFormItem>
                          </AddiitionalColumnProvider>
                        </MCol>
                      </DefaultGroupItem>
                    </template>
                  </MultipleFormItems>
                </FlotoFormItem>
              </MCol>
            </MRow>
            <MRow
              class="mt-auto items-center mr-8"
              :gutter="useMargin ? 16 : 0"
              :style="{ marginRight: useMargin ? '70px !important' : 'unset' }"
            >
              <MCol :size="12" class="relative">
                <InstanceMetricProvider
                  v-if="shouldShowFilterAndConditions"
                  ref="instanceMetricProviderRef"
                  :instance="counters[0].counter.instanceType"
                  group-type="metric"
                  :target="
                    counters[0].counter.instanceType && counters[0].target
                  "
                  :timeline="timeline"
                  :should-fetch-all-counters="shouldFetchAllCounters"
                  :fetch-counter-fn="fetchCounters"
                  @data="handleInstanceDataReceived"
                >
                  <FlotoFormItem v-if="!shouldHideFilterContainer">
                    <FiltersContainer
                      v-model="filters"
                      :max-post-groups="1"
                      :max-pre-groups="3"
                      group-type="metric"
                      :use-instance-grid="showInstanceSelector"
                      :selected-counters="availableCountersForConditions"
                      :timeline="timeline"
                      :target="
                        counters[0].counter.instanceType && counters[0].target
                      "
                      placeholder="Filters"
                      :report-category="$attrs['report-category']"
                    />
                  </FlotoFormItem>
                </InstanceMetricProvider>
              </MCol>
            </MRow>
          </div>
          <div v-else class="flex flex-col flex-1 items-center justify-center">
            <MLoader />
          </div>
        </CounterProvider>
      </GroupProvider>
    </MonitorProvider>
  </ObjectTagProvider>
</template>

<script>
// import Pick from 'lodash/pick'
import CloneDeep from 'lodash/cloneDeep'
import UniqBy from 'lodash/uniqBy'
import Bus from '@utils/emitter'
import { generateId } from '@utils/id'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import GroupProvider from '@components/data-provider/group-provider.vue'
import CounterProvider from '@components/data-provider/counter-provider.vue'
import MultipleFormItems from '@components/multiple-form-items.vue'
import InstanceMetricProvider from '@components/data-provider/instance-metric-provider.vue'
import FiltersContainer from '@components/filters/filters-container.vue'
import { getAllowedUnit } from '@utils/unit-checker'
import Severity from '@components/severity.vue'
import DefaultGroupItem from './default-group-item.vue'
import AddiitionalColumnProvider from '@components/data-provider/additional-column-provider.vue'
import AddiitionalColumnPicker from '@components/data-picker/additional-column-picker.vue'
import { AvailableReportCategories } from '@modules/report/helpers/report'
import ObjectTagProvider from '@components/data-provider/object-tag-provider.vue'

import ArithmeticOperationDropdown from './arithmetic-operation-dropdown.vue'

import {
  WidgetTypeConstants,
  AGGRIGATION_OPTIONS,
  FILTER_CONDITION_DEFAULT_DATA,
} from '../constants'

export default {
  name: 'DefaultGroup',
  components: {
    MonitorProvider,
    CounterProvider,
    GroupProvider,
    MultipleFormItems,
    InstanceMetricProvider,
    DefaultGroupItem,
    FiltersContainer,
    Severity,
    AddiitionalColumnProvider,
    AddiitionalColumnPicker,
    ObjectTagProvider,
    ArithmeticOperationDropdown,
  },
  model: { event: 'change' },
  props: {
    groupType: { type: String, default: 'metric' },
    value: { type: Object, default: undefined },
    widgetType: { type: String, default: undefined },
    widgetCategory: { type: String, default: undefined },
    disabled: { type: Boolean, default: false },
    useSameTypeOfCounters: { type: Boolean, default: false },
    counterDataType: { type: Array, default: undefined },
    maxAllowedCounters: {
      type: Number,
      default: 4,
    },
    widget: {
      type: Object,
      required: true,
    },
    timeline: {
      type: Object,
      required: true,
    },
    showArchivedMonitors: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    this.arithmeticOperationOptions = [
      {
        key: 'delta',
        text: 'Value Difference',
      },
      {
        key: 'monotonic',
        text: 'Monotonic Difference',
      },
      {
        key: 'log2',
        text: 'Log 2',
      },
      {
        key: 'log10',
        text: 'Log 10',
      },
      {
        key: 'cumulative.sum',
        text: 'Cumulative Sum',
      },

      // {
      //   key: 'Moving Average 3',
      //   text: 'Moving Average 3',
      // },
      // {
      //   key: 'Moving Average 7',
      //   text: 'Moving Average 7',
      // },
      // {
      //   key: 'Moving Average 15',
      //   text: 'Moving Average 15',
      // },
    ]
    this.AvailableReportCategories = AvailableReportCategories

    this.timeIntervalOptions = [
      {
        key: 'No sampling',
        text: 'No sampling',
      },
      {
        key: 'Hour',
        text: 'Hour',
      },
      {
        key: 'Day',
        text: 'Day',
      },
      {
        key: 'Week',
        text: 'Week',
      },
      {
        key: 'Month',
        text: 'Month',
      },
    ]
    return {
      counterSearchParams: {
        ...([
          AvailableReportCategories.AVAILABILITY,
          AvailableReportCategories.AVAILABILITY_ALERT,
          AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
        ].includes(this.$attrs['report-category'])
          ? {
              'string.counter.required': 'yes',
              'visualization.group.type': 'availability',
            }
          : this.widgetCategory === WidgetTypeConstants.HEATMAP &&
            this.groupType === 'metric'
          ? {
              'visualization.group.type': this.groupType,
              'visualization.category': WidgetTypeConstants.TOPN,
            }
          : {
              'visualization.group.type': this.groupType,
              'visualization.category': this.widgetCategory,
            }),

        ...([AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY].includes(
          this.$attrs['report-category']
        )
          ? {
              'string.counter.required': 'yes',
              'visualization.group.type': 'status.flap',
            }
          : {}),
      },
      isCounterLoaded: false,
      additionalColumnsOptions: [],
      keyValueTags: [],
      resultByOptions: [],
      instanceOptions: [],
    }
  },
  computed: {
    shouldShowArithmeticOperationDropdown() {
      return (
        ([
          WidgetTypeConstants.CHART,
          WidgetTypeConstants.GRID,
          WidgetTypeConstants.TOPN,
          WidgetTypeConstants.GAUGE,
        ].includes(this.widgetCategory) &&
          !this.$attrs['report-category']) ||
        [AvailableReportCategories.PERFORMANCE].includes(
          this.$attrs['report-category']
        )
      )
    },
    shouldHideFilterContainer() {
      return [AvailableReportCategories.POLLING_REPORT].includes(
        this.$attrs['report-category']
      )
    },
    shouldShowInstanceOptions() {
      return [AvailableReportCategories.POLLING_REPORT].includes(
        this.$attrs['report-category']
      )
    },

    shouldFetchAllCounters() {
      return (
        this.isAvailabilityWidget ||
        [AvailableReportCategories.AVAILABILITY].includes(
          this.$attrs['report-category']
        )
      )
    },
    statusOptions() {
      let statusOptions = [
        'Up',
        'Down',
        'Unreachable',
        'Maintenance',
        'Disable',
      ]
      if (
        this.widget.reportType ===
          AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY ||
        this.widget.reportType ===
          AvailableReportCategories.AVAILABILITY_ALERT ||
        (this.groupType === 'availability' && !this.widget.reportType)
      ) {
        statusOptions.push('Unknown')
      }
      return statusOptions.map((status) => ({ key: status, text: status }))
    },
    defaultResultbyOptions() {
      // if (this.$attrs['report-category'] === 'inventory') {
      //   return [{ key: 'monitor', text: 'Monitor' }]
      // }

      const defaultResultbyOptions = [
        {
          text: 'Monitor',
          key: 'monitor',
        },

        ...(!(
          this.isHeatmapWidget &&
          (this.isAvailabilityWidget || this.isMetricGroupTypeWidget)
        ) && !this.showArchivedMonitors
          ? [
              {
                text: 'Group',
                key: 'group',
              },
            ]
          : []),

        ...(!this.showArchivedMonitors &&
        !(this.isHeatmapWidget && this.isMetricGroupTypeWidget)
          ? [
              {
                text: 'Tag',
                key: 'tag',
              },
            ]
          : []),
      ]
      return defaultResultbyOptions.concat(
        this.instanceType
          ? [
              {
                key: this.instanceType,
                text: this.instanceType,
              },
            ]
          : []
      )
      // .concat(
      //   this.keyValueTags?.length
      //     ? UniqBy(
      //         this.keyValueTags.map((tag) => ({
      //           key: tag.splitedKey,
      //           text: tag.splitedKey,
      //         })),
      //         'key'
      //       ).filter(
      //         (t) => !defaultResultbyOptions.find((o) => o.key === t.key)
      //       )
      //     : []
      // )
    },
    shouldShowFilterAndConditions() {
      if (this.isHeatmapWidget && this.isAvailabilityWidget) {
        return false
      }
      if (this.isGaugeWidget && this.isAvailabilityWidget) {
        return false
      }
      if (this.selectedCounters.length === 0) {
        return false
      }
      if (this.isActiveAlertWidget && this.isAvailabilityWidget) {
        return false
      }
      if (
        [
          AvailableReportCategories.INVENTORY,
          AvailableReportCategories.ACTIVE_ALERTS,
          AvailableReportCategories.AVAILABILITY_ALERT,
          AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,

          // AvailableReportCategories.POLLING_REPORT,
        ].includes(this.$attrs['report-category'])
      ) {
        return false
      }
      return true
    },
    isChartWidget() {
      return this.widgetCategory === WidgetTypeConstants.CHART
    },
    isGaugeWidget() {
      return this.widgetCategory === WidgetTypeConstants.GAUGE
    },
    isHeatmapWidget() {
      return this.widgetCategory === WidgetTypeConstants.HEATMAP
    },
    isActiveAlertWidget() {
      return this.widgetCategory === WidgetTypeConstants.ACTIVE_ALERT
    },
    isGridWidget() {
      return (
        (this.widgetCategory === WidgetTypeConstants.TOPN &&
          this.widgetType === WidgetTypeConstants.GRID) ||
        this.widgetCategory === WidgetTypeConstants.GRID
      )
    },
    isTopnWidget() {
      return this.widgetCategory === WidgetTypeConstants.TOPN
    },
    metricItemAttrs() {
      if (this.isGridWidget) {
        return {
          reportCategory: this.$attrs['report-category'],
          hideAggrigationOptions: this.$attrs['hide-aggrigation-options'],
        }
      }
      const counters = this.counters
      return {
        rootCounterType: counters.length
          ? (counters[0].counter || {}).dataType
          : undefined,
        rootAggrigateFn: counters.length
          ? (counters[0] || {}).aggrigateFn
          : undefined,

        reportCategory: this.$attrs['report-category'],
        hideAggrigationOptions: this.$attrs['hide-aggrigation-options'],
      }
    },
    instanceType() {
      const counters = this.counters
      let instance
      if (counters.length) {
        counters.map((e) => {
          if ((e.counter || {}).instanceType) {
            instance = e.counter.instanceType
          }
        })

        return instance
      }
      return undefined
    },
    selectedCounters() {
      const items = this.counters
      return items
        .filter(({ counter }) => counter)
        .map(({ counter }) => counter.key)
    },
    isAvailabilityWidget() {
      return this.groupType === 'availability'
    },
    isMetricGroupTypeWidget() {
      return this.groupType === 'metric'
    },
    canSelectMonitorWithEachCounter() {
      if (this.isGaugeWidget && this.isAvailabilityWidget) {
        return false
      }
      return true
      // return [WidgetTypeConstants.CHART].indexOf(this.widgetCategory) >= 0
    },
    itemTemplate() {
      return {}
      // const monitorInfo = this.monitorSelectionInfo
      // let isMonitorEntity = false
      // if ((monitorInfo.monitors || []).length === 1) {
      //   isMonitorEntity = true
      // }

      // return {
      //   ...(isMonitorEntity
      //     ? {
      //         target: {
      //           entityType: 'Monitor',
      //           entities: monitorInfo.monitors[0],
      //         },
      //       }
      //     : {}),
      // }
    },
    canAddNewItem() {
      if (
        this.widgetType === WidgetTypeConstants.PIE ||
        this.widgetType === WidgetTypeConstants.TOPN_SOLID_GAUGE_VIEW ||
        ([
          WidgetTypeConstants.TREE_VIEW,
          WidgetTypeConstants.PACKED_BUBBLE_CHART,
        ].includes(this.widgetType) &&
          [WidgetTypeConstants.TOPN].includes(this.widgetCategory))
      ) {
        return false
      }
      // const counters = this.counters
      // if (counters.length) {
      //   const monitorInfo = counters[0].target || {}
      //   if (this.widgetCategory === WidgetTypeConstants.CHART) {
      //     if (
      //       monitorInfo.entityType === 'Monitor' &&
      //       (monitorInfo.entities || []).length === 1
      //     ) {
      //       return true
      //     }
      //     return false
      //   }
      //   if ((monitorInfo.entities || []).length >= 1) {
      //     return true
      //   }
      //   return false
      // }
      return true
    },
    showInstanceSelector() {
      // if (this.monitorSelectionInfo.entityType === 'Monitor') {
      //   return this.monitorSelectionInfo.entities.length === 1
      // }
      return true
    },
    selectedCounterType() {
      const counters = this.counters
      const firstCounter = counters.length ? counters[0] : undefined
      if (firstCounter && firstCounter.counter) {
        return firstCounter.counter?.key?.indexOf('~') >= 0
          ? 'instance'
          : 'metric'
      }
      return undefined
    },
    availableCountersForConditions() {
      const counters = this.counters
      return counters.map((c) => ({ ...c.counter, aggrigateFn: c.aggrigateFn }))
    },
    searchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.SDN,
          this.$constants.OTHER,
          this.$constants.CLOUD,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.STORAGE,
          this.$constants.CONTAINER_ORCHESTRATION,
        ],
      }
    },
    selectedInstance() {
      if (this.selectedCounterType === 'instance') {
        const counters = this.counters
        if (counters.length) {
          return (counters[0].counter || {}).instanceType
        }
      }
      return undefined
    },
    monitorSelectionInfo() {
      return ((this.value || {}).counters[0] || {}).target || []
    },
    resultBy: {
      get() {
        return this.value.resultBy
      },
      set(resultBy) {
        this.$emit('change', { ...this.value, resultBy })
        this.$nextTick(this.generateNewPreview)
      },
    },
    status: {
      get() {
        return this.value.status
      },
      set(status) {
        this.$emit('change', { ...this.value, status })
        this.$nextTick(this.generateNewPreview)
      },
    },
    instance: {
      get() {
        return this.value.instance
      },
      set(instance) {
        if (
          [AvailableReportCategories.POLLING_REPORT].includes(
            this.$attrs['report-category']
          )
        ) {
          const filters = {
            pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
            post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
          }
          filters.pre.groups[0].conditions[0] = {
            ...filters.pre.groups[0].conditions[0],
            operator: '=',
            value: instance,
            operand: this.instanceType,
          }
          this.$emit('change', { ...this.value, instance, filters })
          this.$nextTick(this.generateNewPreview)
        }
      },
    },
    counters: {
      get() {
        return (this.value || {}).counters || []
      },
      set(counters) {
        let data = counters.map((c, index) => {
          let aggrigationOptions = this.aggrigationOptionsForCounter(c).map(
            (aggrigation) => aggrigation.key
          )
          return c.counter
            ? {
                ...c,
                ...(c.aggrigateFn ||
                (c.counter &&
                  Boolean(
                    counters
                      .filter(
                        (x, innerIndex) => x.counter && innerIndex !== index
                      )
                      .find(
                        (innerC) =>
                          innerC.counter.counterName === c.counter.counterName
                      )
                  ))
                  ? {
                      ...(!aggrigationOptions.includes(c.aggrigateFn) &&
                      counters.length < 2
                        ? {
                            aggrigateFn: [
                              AvailableReportCategories.POLLING_REPORT,
                            ].includes(this.$attrs['report-category'])
                              ? '__NONE__'
                              : 'avg',
                          }
                        : {}),
                    }
                  : {
                      aggrigateFn: [
                        AvailableReportCategories.POLLING_REPORT,
                      ].includes(this.$attrs['report-category'])
                        ? '__NONE__'
                        : ((c.counter || {}).dataType || []).includes('numeric')
                        ? 'avg'
                        : 'last',
                    }),
              }
            : c
        })
        const additionalData = this.getCounterChangedData(data)
        if (!data[0].counter) {
          data = [data[0]]
        }

        if (
          (data[0].counter || {}).instanceType &&
          Boolean((data[0].target || {}).entities)
        ) {
          this.$nextTick(() => {
            if (this.$refs.instanceMetricProviderRef) {
              this.$refs.instanceMetricProviderRef.setSelectedCounter(
                (data[0].counter || {}).key
              )
            }
          })
        }
        // if first counter is changed and doesn't match others then remove all other counters
        // const resolvedCounters = data
        //   .map(({ counter }) => (counter || {}).dataType)
        //   .filter(Boolean)

        // if (
        //   [WidgetTypeConstants.TOPN].includes(this.widgetCategory) &&
        //   resolvedCounters.length === data.length
        // ) {
        //   this.$emit('change', {
        //     ...(this.value || {}),
        //     counters: [data[0]],
        //     ...additionalData,
        //   })
        // } else {
        let hasSameInstanceForAllCounters = false
        if (this.selectedCounterType === 'instance') {
          hasSameInstanceForAllCounters = data.every((i) =>
            i.counter
              ? i.counter.instanceType === (data[0].counter || {}).instanceType
              : true
          )
        }
        if (!((counters || [])[0] || {}).counter) {
          this.$emit('change', {
            ...(this.value || {}),
            counters: data,
            ...additionalData,
            filters: {
              pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
              post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
            },
          })
        } else if (
          [WidgetTypeConstants.GAUGE].includes(this.widgetCategory) &&
          data[0].counter &&
          data[0].counter.key &&
          getAllowedUnit(data[0].counter.key) !== 'percent'
        ) {
          this.$emit('change-widget-type', WidgetTypeConstants.METRO_TILE)
          this.$emit('change', {
            ...(this.value || {}),
            counters: data,
            ...additionalData,
          })
        } else if (
          this.useSameTypeOfCounters &&
          [WidgetTypeConstants.CHART].includes(this.widgetCategory) === false &&
          ((this.selectedCounterType === 'instance' &&
            !hasSameInstanceForAllCounters) ||
            (this.selectedCounterType === 'metric' &&
              (data[0].counter || {}).instanceType)) // &&
          // resolvedCounters.length === data.length
        ) {
          this.$emit('change', {
            ...(this.value || {}),
            counters: [data[0]],
            ...additionalData,
          })
        } else {
          this.$emit('change', {
            ...(this.value || {}),
            counters: data,
            ...additionalData,
          })
        }
        // }

        let actualCounterLengthOfOldValue = this.value.counters.filter(
          (counterGroup) =>
            counterGroup &&
            counterGroup['counter'] &&
            counterGroup['counter']['counterName'] !== null
        )
        let actualCounterLengthOfNewValue = counters.filter(
          (counterGroup) =>
            counterGroup &&
            counterGroup['counter'] &&
            counterGroup['counter']['counterName'] !== null
        )

        if (
          counters.length === this.value.counters.length ||
          actualCounterLengthOfOldValue.length !==
            actualCounterLengthOfNewValue.length
        ) {
          this.$nextTick(() => {
            this.setTags(counters)
          })
        }
      },
    },
    filters: {
      get() {
        return (this.value || {}).filters
      },
      set(filters) {
        this.$emit('change', { ...(this.value || {}), filters })
        this.$nextTick(this.generateNewPreview)
      },
    },
    // additionalColumns: {
    //   get() {
    //     return this.value.additionalColumns || []
    //   },
    //   set(additionalColumns) {
    //     this.$emit('change', { ...this.value, additionalColumns })
    //     this.$nextTick(this.generateNewPreview)
    //   },
    // },

    timeInterval: {
      get() {
        return this.value.timeInterval || []
      },
      set(timeInterval) {
        this.$emit('change', { ...this.value, timeInterval })
        this.$nextTick(this.generateNewPreview)
      },
    },
    excludedCounters() {
      if (
        this.isAvailabilityWidget &&
        this.widgetType === WidgetTypeConstants.PIE
      ) {
        return [
          ...(this.allCounters
            ?.filter((counter) => (counter.key || '').includes('seconds'))
            ?.map(({ key }) => key) || []),
        ]
      } else {
        return []
      }
    },
    shouldHideResultBy() {
      return [
        AvailableReportCategories.INVENTORY,
        // AvailableReportCategories.AVAILABILITY,
        AvailableReportCategories.AVAILABILITY_ALERT,
        AvailableReportCategories.POLLING_REPORT,
        AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
      ].includes(this.$attrs['report-category'])
    },

    shouldShowAddionalColumns() {
      return [
        // AvailableReportCategories.INVENTORY,
        // AvailableReportCategories.AVAILABILITY,
        // AvailableReportCategories.ACTIVE_ALERTS,
        // AvailableReportCategories.AVAILABILITY_ALERT,
        // AvailableReportCategories.METRIC_ALERT,
        // AvailableReportCategories.PERFORMANCE,
      ].includes(this.$attrs['report-category'])
    },
    useMargin() {
      return ![
        AvailableReportCategories.INVENTORY,
        AvailableReportCategories.ACTIVE_ALERTS,
        AvailableReportCategories.AVAILABILITY_ALERT,
        AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,

        AvailableReportCategories.METRIC_ALERT,
        AvailableReportCategories.PERFORMANCE,
        AvailableReportCategories.AVAILABILITY,
      ].includes(this.$attrs['report-category'])
    },
    isStatusFlap() {
      return (
        this.groupType === 'status.flap' ||
        this.groupType === 'hourly.status.flap'
      )
    },
    firstSelectedCounterContext() {
      const counters = this.counters

      if (
        this.isHeatmapWidget &&
        this.isAvailabilityWidget &&
        counters?.[0]?.counter?.key !== 'monitor'
      ) {
        return {
          key: '~',
        }
      }
      return counters?.[0]?.counter
    },
    shouldFetchArchivedObjectes() {
      return [
        AvailableReportCategories.PERFORMANCE,
        AvailableReportCategories.AVAILABILITY,
      ].includes(this.$attrs['report-category'])
    },
    showLoader() {
      return [AvailableReportCategories.AVAILABILITY].includes(
        this.$attrs['report-category']
      )
    },
  },
  watch: {
    instanceType(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.instance = undefined
        this.instanceOptions = []
      }
    },
    widgetType(newValue, oldValue) {
      if (newValue !== oldValue) {
        if (newValue === WidgetTypeConstants.PIE) {
          this.counters = [this.counters[0]]
        }
      }
    },
    groupType(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.chnageCounterSearchParams()
      }
    },
    widgetCategory(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.chnageCounterSearchParams()
      }
    },

    showArchivedMonitors(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.setTags()
      }
    },
  },
  created() {
    // this.generateNewPreview = Debounce(this.generateNewPreviewRaw, 1000, {
    //   trailing: true,
    // })
    this.generateNewPreview()
  },
  beforeDestroy() {
    setTimeout(() => {
      this.generateNewPreview()
    })
  },
  methods: {
    generateNewPreview() {
      Bus.$emit('widget.generate.preview')
    },
    getCounterChangedData(counters) {
      let additionalData = {}
      if (
        (this.counters[0].counter &&
          'instanceType' in this.counters[0].counter &&
          counters[0].counter &&
          'instanceType' in counters[0].counter &&
          this.counters[0].counter &&
          this.counters[0].counter.instanceType !==
            (counters[0].counter && counters[0].counter.instanceType)) ||
        (counters[0].counter &&
          counters[0].counter.key === 'monitor' &&
          [AvailableReportCategories.AVAILABILITY].includes(
            this.$attrs['report-category']
          ))
      ) {
        additionalData.counters = [counters[0]]
        additionalData = {
          ...additionalData,
          filters: {
            pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
            post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
          },
        }
      }

      // if (this.resultBy) {
      //   const option = this.resultByOptions.find((r) => r.key === this.resultBy)
      //   if (!option) {
      //     additionalData.resultBy = undefined
      //   }
      // }

      // @TODO only for post filter check
      // const counterKeys = counters
      //   .map((c) => (c.counter || {}).key)
      //   .filter(Boolean)
      // const selectedKeys = this.selectedCounters
      // const updatedCounterLength = counterKeys.filter(
      //   (k) => selectedKeys.indexOf(k) === -1
      // ).length
      // if (updatedCounterLength) {
      //   additionalData.filters = {
      //     pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
      //     post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
      //   }
      // }

      additionalData.additionalColumns = undefined
      return additionalData
    },
    setCounters(optionsMap) {
      if (optionsMap && !Array.isArray(optionsMap)) {
        this.allCounters = Array.from(optionsMap.values())
      }

      if (this.allCounters?.length) {
        let counter = this.allCounters.filter(
          (c) =>
            c.counterName ===
            (this.groupType === 'availability'
              ? this.isGaugeWidget
                ? 'monitor.up.count'
                : 'monitor.uptime.percent'
              : 'system.cpu.percent')
        )

        if (
          [AvailableReportCategories.AVAILABILITY].includes(
            this.$attrs['report-category']
          )
        ) {
          this.setTags(undefined, true)
        }

        if (!counter.length) {
          counter = Array.from(optionsMap.values()).filter(
            (i) => i.instanceType !== i.key && !i.instanceType
          )
          if (this.groupType === 'availability') {
            counter = counter.filter((c) => !c.isStatusCounter)
          }
        }

        this.$nextTick(() => {
          setTimeout(() => {
            if (
              this.value?.counters?.[0]?.counter &&
              !this.value.counters[0]?.counter?.key &&
              counter
            ) {
              this.$emit('change', {
                ...(this.value || {}),
                counters: [
                  {
                    ...([AvailableReportCategories.POLLING_REPORT].includes(
                      this.$attrs['report-category']
                    )
                      ? { target: (this.value || {}).counters?.[0]?.target }
                      : {}),

                    key: generateId(),
                    aggrigateFn:
                      this.widgetCategory === WidgetTypeConstants.HEATMAP
                        ? 'last'
                        : [AvailableReportCategories.POLLING_REPORT].includes(
                            this.$attrs['report-category']
                          )
                        ? '__NONE__'
                        : 'avg',
                    counter: counter[0] || {},
                  },
                ],
              })
            }
          }, 100)
        })
      }
    },

    chnageCounterSearchParams() {
      this.counterSearchParams = {
        ...([
          AvailableReportCategories.AVAILABILITY,
          AvailableReportCategories.AVAILABILITY_ALERT,
          AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
        ].includes(this.$attrs['report-category'])
          ? {
              'string.counter.required': 'yes',
              'visualization.group.type': 'availability',
              // 'visualization.group.type': this.groupType,
              // 'visualization.category': this.widgetCategory,
              // 'visualization.type': 'report',
            }
          : this.isHeatmapWidget && this.isMetricGroupTypeWidget
          ? {
              'visualization.group.type': this.groupType,
              'visualization.category': WidgetTypeConstants.TOPN,
            }
          : {
              'visualization.group.type': this.groupType,
              'visualization.category': this.widgetCategory,
            }),
      }
    },
    aggrigationOptionsForCounter(item) {
      const aggrigateOptions = CloneDeep({
        ...AGGRIGATION_OPTIONS,
        ...(WidgetTypeConstants.CHART === this.widgetCategory
          ? {
              numeric: AGGRIGATION_OPTIONS.numeric
                .filter((i) => ['sum', 'avg', 'min', 'max'].includes(i.key))
                .concat([{ key: 'count', text: 'Count' }]),
              all: AGGRIGATION_OPTIONS.numeric
                .filter((i) =>
                  ['sum', 'avg', 'min', 'max', 'count'].includes(i.key)
                )
                .concat([{ key: 'count', text: 'Count' }]),
            }
          : {}),
        string: AGGRIGATION_OPTIONS.string.filter(({ key }) => key === 'last'),
      })
      return this.groupType === 'availability' &&
        (this.isGaugeWidget || this.isHeatmapWidget)
        ? [{ key: 'last', text: 'Last' }]
        : item.counter
        ? (item.counter.dataType || []).length === 1
          ? aggrigateOptions[item.counter.dataType[0]]
          : aggrigateOptions.all
        : []
    },

    setTags(counters, ignoreSetResultBy = false) {
      let counter = counters?.[0]?.counter

      if (
        ((this.isHeatmapWidget || this.isActiveAlertWidget) &&
          this.isAvailabilityWidget &&
          counters?.[0]?.counter?.key !== 'monitor' &&
          ![AvailableReportCategories.INVENTORY].includes(
            this.$attrs['report-category']
          )) ||
        ([
          AvailableReportCategories.AVAILABILITY,
          AvailableReportCategories.AVAILABILITY_ALERT,
          AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
        ].includes(this.$attrs['report-category']) &&
          counters?.[0]?.counter?.key !== 'monitor') ||
        ([AvailableReportCategories.INVENTORY].includes(
          this.$attrs['report-category']
        ) &&
          counters?.[0]?.counter?.isInstanceLevelCounter)
      ) {
        counter = {
          key: '~',
        }
      }
      try {
        if (
          this.$refs.tagProviderRef &&
          !this.showArchivedMonitors &&
          !(this.isHeatmapWidget && this.isMetricGroupTypeWidget)
        ) {
          this.$refs.tagProviderRef.getAvailableTags(counter).then((tagMap) => {
            if (tagMap && !Array.isArray(tagMap)) {
              this.keyValueTags = Array.from(tagMap.values()).filter(
                (tag) => tag.isKeyValueTag
              )

              Bus.$emit('tag-rendered', this.keyValueTags)

              this.resultByOptions = [...this.defaultResultbyOptions].concat(
                this.keyValueTags?.length
                  ? UniqBy(
                      this.keyValueTags.map((tag) => ({
                        key: tag.splitedKey,
                        text: tag.splitedKey,
                      })),
                      'key'
                    ).filter(
                      (t) =>
                        !this.defaultResultbyOptions.find(
                          (o) => o.key === t.key
                        )
                    )
                  : []
              )
              if (!ignoreSetResultBy) {
                this.setResultByAndGenerateNewPreview()
              }
            } else {
              this.resultByOptions = [...this.defaultResultbyOptions]
              this.setResultByAndGenerateNewPreview()
            }
          })
        }
        if (
          this.showArchivedMonitors ||
          (this.isHeatmapWidget && this.isMetricGroupTypeWidget)
        ) {
          this.resultByOptions = [...this.defaultResultbyOptions]
          this.setResultByAndGenerateNewPreview()
        }
      } catch (e) {
        this.resultByOptions = [...this.defaultResultbyOptions]
        this.setResultByAndGenerateNewPreview()
      }
    },
    setResultByAndGenerateNewPreview() {
      if (this.resultBy) {
        const option = this.resultByOptions.find(
          (r) =>
            r.key ===
            (Array.isArray(this.resultBy) ? this.resultBy[0] : this.resultBy)
        )
        if (!option) {
          this.$emit('change', {
            ...(this.value || {}),
            resultBy: undefined,
          })
        }
        if (option && this.isHeatmapWidget && Array.isArray(this.resultBy)) {
          this.$emit('change', {
            ...(this.value || {}),
            resultBy: this.resultBy[0],
          })
        }
      }

      this.$nextTick(() => {
        this.generateNewPreview()
      })
    },
    fetchCounters() {
      return this.$refs?.counterProviderRef?.fetchCountersWithParams({
        'visualization.group.type': 'metric',
        'visualization.category': this.widgetCategory,
      })
    },

    handleInstanceDataReceived(data) {
      const instanceType = this.instanceType
      this.instanceOptions = Object.freeze(
        data.data.map((i) => ({ key: i[instanceType], text: i[instanceType] }))
      )
    },
    getArithmeticOperationOptionsText(operation) {
      return this.arithmeticOperationOptions.find((o) => o.key === operation)
        ?.text
    },
  },
}
</script>

<style lang="less" scoped>
.filter-viewer {
  background: var(--neutral-lightest);
  border: 1px solid var(--border-color);
}
</style>
