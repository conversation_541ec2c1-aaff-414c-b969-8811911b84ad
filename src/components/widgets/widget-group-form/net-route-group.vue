<template>
  <CounterProvider
    :search-params="counterSearchParams"
    @loaded="isCounterLoaded = true"
    @counters="setCounters"
  >
    <div v-if="isCounterLoaded" class="flex flex-col flex-1">
      <MRow :gutter="16">
        <MCol :size="12">
          <FlotoFormItem rules="required">
            <MultipleFormItems
              v-model="counters"
              :max-items="maxAllowedCounters"
              :item-template="itemTemplate"
              :show-icon="false"
              add-btn-text="Add Counter"
            >
              <template
                v-slot="{
                  item,
                  update,
                  remove,
                  add,
                  index,
                  isLastItem,
                  isFirstItem,
                  canAdd,
                }"
              >
                <MRow class="items-center flex-no-wrap" :gutter="16">
                  <MCol :size="3">
                    <FlotoFormItem
                      rules="required"
                      :label="index === 0 ? 'Counter' : undefined"
                    >
                      <CounterPicker
                        :value="(item.counter || {}).key"
                        placeholder="Select Counter"
                        :disabled="disabled"
                        :disabled-options="disabledCounterOptions"
                        @change="updateCounter(item, $event, index, update)"
                      />
                    </FlotoFormItem>
                  </MCol>
                  <MCol style="max-width: 210px">
                    <FlotoFormItem
                      rules="required"
                      :label="index === 0 ? 'Aggregation' : undefined"
                    >
                      <FlotoDropdownPicker
                        :value="item.aggrigateFn"
                        :searchable="false"
                        :options="aggrigationOptionsForCounter(item)"
                        placeholder=" "
                        @change="
                          update({ ...(item || {}), aggrigateFn: $event })
                        "
                      />
                    </FlotoFormItem>
                  </MCol>
                  <MCol :size="5">
                    <MonitorGroupSelection
                      :value="item.target"
                      :disabled="disabled"
                      :entity-options="entityOptions"
                      source-type="netroute"
                      :timeline="timeline"
                      :row-gutter="16"
                      :size="{ type: 4, value: 8 }"
                      :show-label="index === 0"
                      @change="update({ ...(item || {}), target: $event })"
                    />
                  </MCol>
                  <MCol v-if="shouldShowResultBy && index === 0" :size="2">
                    <FlotoFormItem label="Result By">
                      <FlotoDropdownPicker
                        id="resultBy"
                        v-model="resultBy"
                        placeholder=" "
                        :options="resultByOptions"
                        :allow-clear="isChartWidget"
                      ></FlotoDropdownPicker>
                    </FlotoFormItem>
                  </MCol>

                  <MCol
                    style="width: 80px"
                    class="fixed-size flex items-center"
                    :class="{ 'mb-2': index !== 0 }"
                  >
                    <span
                      v-if="!isFirstItem"
                      id="remove-counter"
                      class="mr-2 flex items-center"
                      @click="remove"
                    >
                      <MIcon
                        name="times-circle"
                        class="cursor-pointer text-secondary-red"
                        size="lg"
                      />
                    </span>
                    <span
                      v-if="isLastItem && canAdd"
                      id="add-counter"
                      outline
                      class="flex items-center"
                      @click="add"
                    >
                      <MIcon
                        name="plus-circle"
                        class="text-primary cursor-pointer"
                        size="lg"
                      />
                    </span>
                  </MCol>
                </MRow>
              </template>
            </MultipleFormItems>
          </FlotoFormItem>
        </MCol>
      </MRow>
    </div>
    <div v-else class="flex flex-col flex-1 items-center justify-center">
      <MLoader />
    </div>
  </CounterProvider>
</template>

<script>
import Bus from '@utils/emitter'

import { generateId } from '@utils/id'

// import Intersection from 'lodash/intersection'
import CloneDeep from 'lodash/cloneDeep'

import CounterProvider from '@components/data-provider/counter-provider.vue'
import CounterPicker from '@components/data-picker/counter-picker.vue'

import MultipleFormItems from '@components/multiple-form-items.vue'
import MonitorGroupSelection from '../monitor-or-group-selection.vue'

// import { AvailableReportCategories } from '@modules/report/helpers/report'

import {
  FILTER_CONDITION_DEFAULT_DATA,
  AGGRIGATION_OPTIONS,
  WidgetTypeConstants,
} from '@components/widgets/constants'

export default {
  name: 'NetRouteGroup',
  components: {
    CounterProvider,
    CounterPicker,
    MultipleFormItems,
    MonitorGroupSelection,
  },
  model: { event: 'change' },
  props: {
    groupType: { type: String, default: 'netroute' },
    value: { type: Object, default: undefined },
    widgetType: { type: String, default: undefined },
    widgetCategory: { type: String, default: undefined },
    disabled: { type: Boolean, default: false },
    useSameTypeOfCounters: { type: Boolean, default: false },
    counterDataType: { type: Array, default: undefined },
    widget: {
      type: Object,
      required: true,
    },
    timeline: {
      type: Object,
      required: true,
    },
    maxAllowedCounters: { type: Number, default: 4 },
  },
  data() {
    this.entityOptions = [
      { key: 'netroute.id', text: 'Netroute', inputType: 'netroute' },

      // { key: 'tag', text: 'Tag', inputType: 'tag' },
    ]
    this.itemTemplate = {}
    this.disabledCounterOptions = []
    this.aggrigateOptions = CloneDeep({
      ...AGGRIGATION_OPTIONS,
    })

    this.resultByOptions = [
      { key: 'netroute.id', text: 'Netroute' },
      // {
      //   text: 'Tag',
      //   key: 'tag',
      // },
    ]
    return {
      isCounterLoaded: false,
      allCounters: [],
    }
  },
  computed: {
    isChartWidget() {
      return this.widgetCategory === WidgetTypeConstants.CHART
    },
    selectedCounters() {
      const counters = this.counters
      return counters
        .map((c) => (c.counter || {}).parentId || (c.counter || {}).key)
        .filter(Boolean)
    },
    shouldShowResultBy() {
      return [WidgetTypeConstants.CHART, WidgetTypeConstants.GAUGE].includes(
        this.widgetCategory
      )
        ? WidgetTypeConstants.GAUGE === this.widgetCategory
          ? false
          : this.selectedCounters.length <= 1
        : true
    },
    counterSearchParams() {
      return {
        'visualization.group.type': this.groupType,
        'visualization.category': this.widgetCategory,
      }
    },
    counters: {
      get() {
        return (this.value || {}).counters
      },
      set(counters) {
        // const firstCounterMetricPlugins =
        //   ((counters[0] || {}).counter || {}).metricPlugins || []

        let data = counters
          .map((c) => {
            let aggrigationOptions = this.aggrigationOptionsForCounter(c).map(
              (aggrigation) => aggrigation.key
            )

            return c.counter
              ? {
                  ...c,
                  ...(c.aggrigateFn
                    ? c.counter &&
                      c.counter.dataType &&
                      aggrigationOptions.includes(c.aggrigateFn)
                      ? {}
                      : {
                          aggrigateFn: (
                            (c.counter || {}).dataType || []
                          ).includes('numeric')
                            ? 'avg'
                            : 'count',
                        }
                    : {
                        aggrigateFn: (
                          (c.counter || {}).dataType || []
                        ).includes('numeric')
                          ? 'avg'
                          : 'count',
                      }),
                }
              : c
          })
          .filter(Boolean)

        let additionalData = this.getCounterChangedData(counters)
        this.$emit('change', {
          ...(this.value || {}),
          counters: data,
          ...additionalData,
        })
        additionalData = null

        let actualCounterLengthOfOldValue = this.value.counters.filter(
          (counterGroup) =>
            counterGroup &&
            counterGroup['counter'] &&
            counterGroup['counter']['counterName'] !== null
        )
        let actualCounterLengthOfNewValue = counters.filter(
          (counterGroup) =>
            counterGroup &&
            counterGroup['counter'] &&
            counterGroup['counter']['counterName'] !== null
        )

        if (
          counters.length === this.value.counters.length ||
          actualCounterLengthOfOldValue.length !==
            actualCounterLengthOfNewValue.length
        ) {
          this.$nextTick(() => {
            this.generateNewPreview()
          })
        }
      },
    },

    resultBy: {
      get() {
        return this.value.resultBy
      },
      set(resultBy) {
        this.$emit('change', { ...this.value, resultBy })
        this.$nextTick(this.generateNewPreview)
      },
    },
  },

  created() {
    this.generateNewPreview()
  },
  beforeDestroy() {
    setTimeout(() => {
      this.generateNewPreview()
    })
  },
  methods: {
    updateCounter(item, event, index, update) {
      setTimeout(() => {
        update({ ...(item || {}), counter: event })
      }, index * 100)
    },
    aggrigationOptionsForCounter(item) {
      const aggrigateOptions = CloneDeep({
        ...AGGRIGATION_OPTIONS,
        ...(WidgetTypeConstants.CHART === this.widgetCategory
          ? {
              numeric: AGGRIGATION_OPTIONS.numeric
                .filter((i) => ['sum', 'avg', 'min', 'max'].includes(i.key))
                .concat([{ key: 'count', text: 'Count' }]),
              all: AGGRIGATION_OPTIONS.numeric
                .filter((i) =>
                  ['sum', 'avg', 'min', 'max', 'count'].includes(i.key)
                )
                .concat([{ key: 'count', text: 'Count' }]),
            }
          : {}),
        string: AGGRIGATION_OPTIONS.string.filter(({ key }) => key === 'last'),
      })
      return item.counter
        ? (item.counter.dataType || []).length === 1
          ? aggrigateOptions[item.counter.dataType[0]]
          : aggrigateOptions.all
        : []
    },
    setCounters(optionsMap) {
      if (optionsMap && !Array.isArray(optionsMap)) {
        this.allCounters = Array.from(optionsMap.values())
      }

      if (this.allCounters?.length) {
        // @todo @important @critical set the default counter for netroute group here
        let counter = this.allCounters.filter(
          (c) => c.counterName === 'hop.count'
        )

        if (!counter.length) {
          counter = Array.from(optionsMap.values()).filter(
            (i) => i.instanceType !== i.key
          )
        }

        this.$nextTick(() => {
          setTimeout(() => {
            if (
              this.value?.counters?.[0]?.counter &&
              !this.value.counters[0]?.counter?.key &&
              counter
            ) {
              this.$emit('change', {
                ...(this.value || {}),
                counters: [
                  {
                    key: generateId(),
                    aggrigateFn:
                      this.widgetCategory === WidgetTypeConstants.HEATMAP
                        ? 'last'
                        : 'avg',
                    counter: counter[0] || {},
                  },
                ],
              })
            }
          }, 100)
        })
      }
    },
    getCounterChangedData(counters) {
      let additionalData = {}
      if (
        this.counters[0].counter &&
        'instanceType' in this.counters[0].counter &&
        counters[0].counter &&
        'instanceType' in counters[0].counter &&
        this.counters[0].counter &&
        this.counters[0].counter.instanceType !==
          (counters[0].counter && counters[0].counter.instanceType)
      ) {
        additionalData.counters = [counters[0]]
        additionalData.filters = {
          pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
          post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
        }
      }
    },
    generateNewPreview() {
      Bus.$emit('widget.generate.preview')
    },
  },
}
</script>
