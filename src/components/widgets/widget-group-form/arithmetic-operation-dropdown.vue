<template>
  <FlotoDropdownPicker
    ref="dropdownPickerRef"
    :options="options"
    :value="value"
    :searchable="searchable"
    :multiple="multiple"
    v-bind="$attrs"
    :disabled="disabled"
    @change="handleChange"
    @search="buildFilteredOptions"
    v-on="listeners"
  >
    <template v-slot:trigger>
      <div class="flex items-center cursor-pointer">
        <div
          v-if="!forMetricExplorer"
          :class="{
            ...(!forMetricExplorer
              ? { ...(index !== 0 ? { 'mb-3': true } : { 'mt-2': true }) }
              : {}),
          }"
        >
          <div
            style="line-height: 32px"
            class="flex bordered rounded px-2 items-center text-sm"
          >
            <MIcon name="sigma" size="sm" class="text-sm m-2" />

            <div
              v-if="value && !forMetricExplorer"
              style="max-width: 180px"
              class="text-ellipsis px-1"
            >
              <span>{{ getArithmeticOperationOptionsText(value) }}</span>
            </div>

            <MIcon
              v-if="value && !forMetricExplorer"
              name="close"
              size="sm"
              @click="handleChange()"
            />
          </div>
        </div>

        <div v-else class="bg-neutral-lightest rounded">
          <MIcon name="sigma" size="sm" class="text-sm m-1" />
        </div>
      </div>
    </template>

    <FlotoScrollView class="arithmetic-option-hierarchy">
      <HierarchyExplorer
        :use-bg="false"
        :use-margin="false"
        :hierarchy="filteredOptions"
        class="px-2"
        :default-expanded-keys="expandedKeys"
        style="min-width: unset !important"
        :active-item="{ id: value }"
      >
        <template v-slot="{ item, parent }">
          <div
            v-if="multiple"
            class="flex cursor-pointer"
            :style="
              item.disabled ||
              (disableChildrenWithParent && parent && parent.disabled)
                ? { pointerEvents: 'none', opacity: 0.5 }
                : {}
            "
          >
            <MCheckbox
              :id="item[nameProperty]"
              :checked="
                multiple
                  ? (value || []).indexOf(item.key) >= 0
                  : value === item.key
              "
              :indeterminate="
                interminentMap[item.key]
                  ? interminentMap[item.key] === 'yes'
                  : hasAnyChildSelected(item)
              "
              class="text-ellipsis"
              @change="handleItemSelectionChange($event, item)"
            >
              {{ item[nameProperty] }}
            </MCheckbox>
          </div>
          <span
            v-else
            class="flex cursor-pointer"
            :style="
              item.disabled ||
              (disableChildrenWithParent && parent && parent.disabled)
                ? { pointerEvents: 'none', opacity: 0.5 }
                : {}
            "
            @click="handleItemSelectionChange($event, item)"
          >
            {{ item[nameProperty] }}
          </span>
        </template>
      </HierarchyExplorer>
    </FlotoScrollView>
  </FlotoDropdownPicker>
</template>

<script>
import Intersection from 'lodash/intersection'
import { arrayWorker } from '@/src/workers'
import { flattenRecursive } from '@data/recursive'
import HierarchyExplorer from '@components/sortable/hierarchy-explorer.vue'
import { DEFAULT_ARITHMETIC_OPERATION_OPTIONS } from '../helper'

export default {
  name: 'HierarchyPicker',
  components: { HierarchyExplorer },
  model: {
    event: 'change',
  },
  props: {
    options: {
      type: Array,
      default() {
        return DEFAULT_ARITHMETIC_OPERATION_OPTIONS
      },
    },
    value: { type: [Number, Array, Object, String], default: undefined },
    maxLevel: { type: Number, default: 0 },
    disabled: {
      type: Boolean,
      default: false,
    },
    // eslint-disable-next-line
    searchable: { type: Boolean, default: true },
    multiple: { type: Boolean, default: false },
    nameProperty: { type: String, default: 'name' },
    disabledOptions: { type: Array, default: undefined },
    disableChildrenWithParent: { type: Boolean, default: false },
    hasPreBuiltHieararchy: { type: Boolean, default: false },
    includedOptions: { type: Array, default: undefined },
    index: {
      type: Number,
      default: 0,
    },
    forMetricExplorer: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.interminentMap = {}
    return {
      filteredOptions: [],
      hierarchyOptions: [],
    }
  },
  computed: {
    listeners() {
      const { change, ...listeners } = this.$listeners
      return listeners
    },
    expandedKeys() {
      return this.options.filter((o) => !o.parentId).map((o) => o.key)
    },
  },
  watch: {
    options: async function () {
      await this.buildHierarchyOptions()
      this.buildFilteredOptions()
    },
  },
  async created() {
    await this.buildHierarchyOptions()
    this.buildFilteredOptions()
  },
  methods: {
    async buildHierarchyOptions() {
      let options
      if (this.hasPreBuiltHieararchy) {
        options = this.options
      } else {
        options = await arrayWorker.buildHierarchy(
          this.options,
          'children',
          'key',
          'parentId'
        )
      }
      if (this.includedOptions) {
        const includedOptions = this.includedOptions
        options = options.filter((o) =>
          includedOptions.includes(o.groupName || o.name)
        )
      }
      this.hierarchyOptions = Object.freeze(options)
    },
    hasAnyChildSelected(item) {
      let value = this.value
      if (!value) {
        value = []
      } else {
        value = Array.isArray(value) ? value : [value]
      }
      if (value.indexOf(item.key) >= 0) {
        this.interminentMap[item.key] = 'no'
        return false
      }
      const flatten = item.children
        ? flattenRecursive(item.children, 'children')
        : []
      const childrenIds = flatten.map((i) => i.key)
      const finalValue = !!Intersection(value, childrenIds).length
      this.interminentMap[item.key] = finalValue ? 'yes' : 'no'
      return finalValue
    },
    handleItemSelectionChange(isChecked, item) {
      if (this.multiple) {
        let value = this.value || []
        if (value.indexOf(item.key) >= 0) {
          isChecked = false
        } else {
          isChecked = true
        }
        if (isChecked) {
          value = [...value, item.key]
        } else {
          value = value.filter((v) => v !== item.key)
        }
        this.handleChange(value)
      } else {
        if (this.value === item.key) {
          this.handleChange(item)
        } else {
          this.handleChange(item)
        }
      }
    },
    async buildFilteredOptions(query) {
      const hierarchyOptions = this.hierarchyOptions
      if (query && query !== '') {
        const searchedResults = await arrayWorker.searchRecursive(
          query.toLowerCase(),
          hierarchyOptions,
          'children',
          this.nameProperty
        )
        if (this.maxLevel) {
          this.filteredOptions = await arrayWorker.getRecursiveLevel(
            searchedResults,
            'children',
            this.maxLevel
          )
        } else {
          this.filteredOptions = searchedResults
        }
        return
      }
      this.filteredOptions = hierarchyOptions
    },
    handleChange(value) {
      if (value?.children?.length) {
        return
      }

      this.interminentMap = {}
      this.$emit('change', value?.key)
      if (!this.multiple) {
        this.$refs.dropdownPickerRef.handleHide()
      }
    },
    getArithmeticOperationOptionsText(operation) {
      return this.options.find((o) => o.key === operation)?.name
    },
  },
}
</script>
