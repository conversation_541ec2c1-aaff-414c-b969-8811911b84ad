<template>
  <div
    class="flex flex-1 h-full w-full min-h-0 min-w-0 relative flex-col"
    :class="gridClasses"
  >
    <div v-if="isSearchable" class="my-2 mx-2 flex justify-between">
      <MInput
        v-model="searchTerm"
        class="search-box"
        placeholder="Search"
        name="search"
      >
        <template v-slot:prefix>
          <MIcon name="search" />
        </template>
        <template v-if="searchTerm" v-slot:suffix>
          <MIcon
            name="times-circle"
            class="text-neutral-light cursor-pointer"
            @click="searchTerm = undefined"
          />
        </template>
      </MInput>
    </div>
    <div
      v-if="columns && columns.length && dataMap && dataMap.length"
      class="flex flex-col flex-1 min-h-0 w-full relative min-w-0"
      :style="
        isSearchable ? {} : allowColumnSelection ? { paddingTop: '40px' } : {}
      "
    >
      <div
        v-if="allowColumnSelection"
        class="absolute"
        :style="{ top: isSearchable ? '-40px' : 0, right: 0 }"
      >
        <MPersistedColumns
          v-model="columns"
          :default-value="columns"
          :module-key="`${(widget || {}).id}-widget`"
          :available-columns="availableColumns"
        >
          <template
            v-slot="{
              columns: persistedColumns,
              setColumns: updatePersistedColumns,
            }"
          >
            <ColumnSelector
              :value="persistedColumns"
              :columns="availableColumns"
              :hide-reset-option="true"
              @change="updatePersistedColumns"
            />
          </template>
        </MPersistedColumns>
      </div>
      <VirtualTable
        ref="tableRef"
        class="dashboard-widget-grid w-full min-w-0"
        :class="{ 'hide-header': hideHeader }"
        :search-term="searchTerm"
        :data="dataMap"
        :columns="columns"
        :row-height="gridRowHeight"
        get-resize-width-in-percent
        :is-alert-report="$attrs['is-alert-report']"
        @rowclick="rowclick"
        @column-change="handleUpdateWidgetColumns"
      >
        <template v-slot:sparklineHeader="{ props }">
          <span
            :class="{
              'justify-center': !shouldShowSparkline,
              'w-full': !shouldShowSparkline,
              flex: !shouldShowSparkline,
            }"
          >
            {{ props['title'] }}
            <MTooltip v-if="!shouldShowSparkline">
              <template v-slot:trigger>
                <MIcon
                  name="info-circle"
                  class="ml-1 text-primary-alt text-sm"
                  style="position: relative; top: -1px"
                />
              </template>

              {{ $message('sparkline_limit') }}
            </MTooltip>
          </span>
        </template>

        <template v-slot:defaultCell="{ props }">
          <ColorCodedCell
            :is-preview="isPreview"
            :data-item="props.dataItem"
            :field="props"
            :column="columnConfig[props.field]"
          >
            <span class="text-ellipsis">
              <!-- <template v-if="typeof props.dataItem[props.field] === 'number'">
                {{ props.dataItem[props.field] | numberFormat }}
              </template>
              <template v-else>-->
              <template v-if="isGroupByColumn(props.field)">
                <a @click="navigateToDrilldown(props)">
                  {{ props.dataItem[props.field] }}
                </a>
              </template>
              <template v-else-if="props.dataItem[`${props.field}.formatted`]">
                {{ props.dataItem[`${props.field}.formatted`] }}
              </template>
              <template v-else>
                {{ props.dataItem[props.field] }}
              </template>
              <!-- </template> -->
            </span>
          </ColorCodedCell>
        </template>
        <template v-slot:number="{ props }">
          <ColorCodedCell
            :is-preview="isPreview"
            :data-item="props.dataItem"
            :field="props"
            :column="columnConfig[props.field]"
          >
            <span class="text-ellipsis">
              {{ props.dataItem[props.field] | numberFormat }}
            </span>
          </ColorCodedCell>
        </template>
        <template v-slot:label="{ props }">
          <ColorCodedCell
            :is-preview="isPreview"
            :data-item="props.dataItem"
            :field="props"
            :column="columnConfig[props.field]"
          >
            <div
              v-if="props.dataItem[props.field]"
              class="ant-tag rounded cursor-auto"
              :class="columnConfig[props.field].classes || ['tag-primary']"
            >
              {{ props.dataItem[props.field] }}
            </div>
            <span v-else />
          </ColorCodedCell>
        </template>
        <template v-slot:monitor_type="{ props }">
          <ColorCodedCell
            :is-preview="isPreview"
            :data-item="props.dataItem"
            :field="props"
            :column="columnConfig[props.field]"
          >
            <MonitorType :type="props.dataItem[props.field]" :center="false" />
          </ColorCodedCell>
        </template>
        <template v-slot:alert="{ props }">
          <ColorCodedCell
            :is-preview="isPreview"
            :data-item="props.dataItem"
            :field="props"
            :column="columnConfig[props.field]"
          >
            <Severity
              disable-tooltip
              :severity="props.dataItem.severity"
              :center="false"
              class="mr-1"
            />

            <AlertDrilldown
              v-if="props.dataItem['entity.id']"
              :alert="props.dataItem"
              :field="props.dataItem[props.field]"
              @rowclick="rowclick"
            />
            <template v-else>
              {{ props.dataItem[props.field] }}
            </template>
          </ColorCodedCell>
        </template>
        <template v-slot:netroute_drilldown_slot="{ props }">
          <ColorCodedCell
            :is-preview="isPreview"
            :data-item="props.dataItem"
            :field="props"
            :column="columnConfig[props.field]"
          >
            <NetRouteDrillDown
              v-if="
                props.dataItem['netroute_id'] && props.dataItem['netroute_name']
              "
              :netroute="props.dataItem"
              :field="props.dataItem[props.field]"
              @rowclick="rowclick"
            />
            <template v-else>
              {{ props.dataItem[props.field] }}
            </template>
          </ColorCodedCell>
        </template>
        <template v-slot:status="{ props }">
          <ColorCodedCell
            :is-preview="isPreview"
            :data-item="props.dataItem"
            :field="props"
            :column="columnConfig[props.field]"
          >
            <Status
              :column="columnConfig[props.field]"
              :value="props.dataItem[props.field]"
            />
          </ColorCodedCell>
        </template>
        <template v-slot:ipsla="{ props }">
          <ColorCodedCell
            :is-preview="isPreview"
            :data-item="props.dataItem"
            :field="props"
            :column="columnConfig[props.field]"
          >
            <WanLinkDrilldown
              :hide-title="true"
              show-status
              :is-fullscreen="$attrs['is-fullscreen']"
              :column="columnConfig[props.field]"
              :value="props.dataItem[props.field]"
              :row="props.dataItem"
            />
          </ColorCodedCell>
        </template>
        <template v-slot:container="{ props }">
          <ColorCodedCell
            :data-item="props.dataItem"
            :column="columnConfig[props.field]"
            :is-preview="isPreview"
            :field="props"
            link-icon
          >
            <ContainerDrillDown
              :hide-title="true"
              show-status
              :is-fullscreen="$attrs['is-fullscreen']"
              :column="columnConfig[props.field]"
              :value="props.dataItem[props.field]"
              :row="props.dataItem"
            />
          </ColorCodedCell>
        </template>
        <template v-slot:wanProbe="{ props }">
          <ColorCodedCell
            :is-preview="isPreview"
            :data-item="props.dataItem"
            :field="props"
            :column="columnConfig[props.field]"
          >
            {{ wanProbeMap[props.dataItem[props.field]] }}
          </ColorCodedCell>
        </template>
        <template v-slot:application_name="{ props }">
          <ColorCodedCell
            :is-preview="isPreview"
            :data-item="props.dataItem"
            :field="props"
            :column="columnConfig[props.field]"
          >
            <ApplicationName
              :value="props.dataItem[props.field]"
              :row="props.dataItem"
              :monitor="$attrs.monitor"
            />
          </ColorCodedCell>
        </template>
        <template v-slot:datetime="{ props }">
          <ColorCodedCell
            :is-preview="isPreview"
            :data-item="props.dataItem"
            :field="props"
            :column="columnConfig[props.field]"
            >{{ props.dataItem[props.field] | datetime }}</ColorCodedCell
          >
        </template>
        <template v-slot:ms_datetime="{ props }">
          <ColorCodedCell
            :is-preview="isPreview"
            :data-item="props.dataItem"
            :field="props"
            :column="columnConfig[props.field]"
            >{{ formatDateTime(props.dataItem[props.field]) }}</ColorCodedCell
          >
        </template>
        <template v-slot:duration="{ props }">
          <ColorCodedCell
            :is-preview="isPreview"
            :data-item="props.dataItem"
            :field="props"
            :column="columnConfig[props.field]"
            >{{ props.dataItem[props.field] | duration }}</ColorCodedCell
          >
        </template>
        <template v-slot:sparkline="{ props }">
          <Sparkline
            v-if="shouldShowSparkline"
            :key="props.dataItem.id"
            :column="columnConfig[props.field]"
            :series="props.dataItem[props.field]"
            :type="sparklineChartType"
            :color="sparklineColor"
            :is-preview="isPreview"
          />
          <span v-else class="w-full flex justify-center items-center">
            -
          </span>
        </template>
        <template v-slot:sparkarea="{ props }">
          <Sparkline
            v-if="shouldShowSparkline"
            :key="props.dataItem.id"
            :column="columnConfig[props.field]"
            :series="props.dataItem[props.field]"
            :type="sparklineChartType"
            :color="sparklineColor"
            :is-preview="isPreview"
          />
          <span v-else class="w-full flex justify-center items-center">
            -
          </span>
        </template>
        <template v-slot:sparkbar="{ props }">
          <Sparkline
            v-if="shouldShowSparkline"
            :key="props.dataItem.id"
            :column="columnConfig[props.field]"
            :series="props.dataItem[props.field]"
            :type="sparklineChartType"
            :color="sparklineColor"
            :is-preview="isPreview"
          />
          <span v-else class="w-full flex justify-center items-center">
            -
          </span>
        </template>
        <template v-slot:relative_percentage="{ props }">
          <RelativePercentage
            :row="props.dataItem"
            :field="props"
            :index="props.dataIndex"
            :data="dataMap"
          />
        </template>
        <template v-slot:tag="{ props }">
          <LooseTags
            :value="
              Array.isArray(props.dataItem[props.field])
                ? props.dataItem[props.field]
                : [props.dataItem[props.field]]
            "
            :disabled="true"
          />
        </template>
        <template v-slot:gauge="{ props }">
          <div class="flex flex-col">
            <Progress
              :width="
                props.dataItem[`${props.field}_sort`]
                  ? props.dataItem[`${props.field}_sort`]
                  : isNaN(props.dataItem[`${props.field}`])
                  ? 0
                  : +props.dataItem[`${props.field}`]
              "
              :type="
                (columnConfig[props.field].slotClasses || []).length
                  ? columnConfig[props.field].slotClasses[0]
                  : 'normal'
              "
            />
            <div class="text-neutral" style="font-size: 11px">
              {{
                props.dataItem[`${props.field}_sort`]
                  ? props.dataItem[`${props.field}_sort`]
                  : isNaN(props.dataItem[`${props.field}`])
                  ? 0
                  : +props.dataItem[`${props.field}`]
              }}%
            </div>
          </div>
        </template>
        <template v-slot:severity="{ props }">
          <ColorCodedCell
            :data-item="props.dataItem"
            :field="props"
            :column="columnConfig[props.field]"
            :is-preview="isPreview"
          >
            <Severity
              disable-tooltip
              :severity="props.dataItem[props.field]"
              display-text
              :center="false"
            />
          </ColorCodedCell>
        </template>
        <template v-slot:computed="{ props }">
          <ColorCodedCell
            :data-item="props.dataItem"
            :field="props"
            :column="columnConfig[props.field]"
            :is-preview="isPreview"
          >
            <Computed
              :row="props.dataItem"
              :columns="columns"
              :column="columnConfig[props.field]"
            />
          </ColorCodedCell>
        </template>
        <template v-slot:monitor="{ props }">
          <ColorCodedCell
            :data-item="props.dataItem"
            :column="columnConfig[props.field]"
            :field="props"
            :is-preview="isPreview"
          >
            <template v-if="props.dataItem.rowType === 'pivotGroupHeader'">
              <span class="text-primary">
                {{ props.dataItem.groupHeader }}
              </span>
            </template>
            <MonitorName
              v-else
              :value="props.dataItem[props.field]"
              :row="props.dataItem"
            />
          </ColorCodedCell>
        </template>
        <template v-slot:vm="{ props }">
          <ColorCodedCell
            :data-item="props.dataItem"
            :column="columnConfig[props.field]"
            :field="props"
            :is-preview="isPreview"
          >
            <MonitorName
              :value="props.dataItem[props.field]"
              :row="props.dataItem"
              :for-vm="true"
            />
          </ColorCodedCell>
        </template>
        <template v-slot:cloud="{ props }">
          <ColorCodedCell
            :data-item="props.dataItem"
            :column="columnConfig[props.field]"
            :field="props"
            :is-preview="isPreview"
          >
            <MonitorName
              for-cloud
              :value="props.dataItem[props.field]"
              :row="props.dataItem"
            />
          </ColorCodedCell>
        </template>
        <template v-slot:ipaddress="{ props }">
          <ColorCodedCell
            :data-item="props.dataItem"
            :column="columnConfig[props.field]"
            :field="props"
            :is-preview="isPreview"
          >
            <ViewMoreSlot :row="props.dataItem" :field="props" />
          </ColorCodedCell>
        </template>
        <template v-slot:macaddress="{ props }">
          <ColorCodedCell
            :data-item="props.dataItem"
            :column="columnConfig[props.field]"
            :is-preview="isPreview"
            :field="props"
          >
            <ViewMoreSlot
              :row="props.dataItem"
              :field="props"
              disable-monitor-resolution
            />
          </ColorCodedCell>
        </template>
        <template v-slot:port="{ props }">
          <ColorCodedCell
            :data-item="props.dataItem"
            :column="columnConfig[props.field]"
            :is-preview="isPreview"
            :field="props"
          >
            <ViewMoreSlot
              :row="props.dataItem"
              :field="props"
              disable-monitor-resolution
            />
          </ColorCodedCell>
        </template>
        <template v-slot:query="{ props }">
          <ColorCodedCell
            :data-item="props.dataItem"
            :column="columnConfig[props.field]"
            :is-preview="isPreview"
            :field="props"
          >
            <ViewQuery :data="props.dataItem[props.field]">
              <template v-slot:trigger="{ toggle }">
                <div>
                  <a @click="toggle"> View Query </a>
                </div>
              </template>
              {{ props.dataItem[props.field] }}
            </ViewQuery>
          </ColorCodedCell>
        </template>
        <template v-slot:interface="{ props }">
          <ColorCodedCell
            :data-item="props.dataItem"
            :column="columnConfig[props.field]"
            :is-preview="isPreview"
            :field="props"
            link-icon
          >
            <InterfaceDrilldown
              :hide-title="true"
              show-status
              :is-fullscreen="$attrs['is-fullscreen']"
              :column="columnConfig[props.field]"
              :value="props.dataItem[props.field]"
              :row="props.dataItem"
            />
          </ColorCodedCell>
        </template>
        <template v-slot:process="{ props }">
          <ColorCodedCell
            :data-item="props.dataItem"
            :column="columnConfig[props.field]"
            :is-preview="isPreview"
            :field="props"
            link-icon
          >
            <ProcessDrilldown
              :hide-title="true"
              show-status
              :is-fullscreen="$attrs['is-fullscreen']"
              :column="columnConfig[props.field]"
              :value="props.dataItem[props.field]"
              :row="props.dataItem"
            />
          </ColorCodedCell>
        </template>
        <template v-slot:policy="{ props }">
          <FlotoLink
            class="text-ellipsis"
            :to="
              $modules.getModuleRoute('policy-settings', 'edit-policy', {
                params: {
                  type: props.dataItem['policy_type'],
                  id: props.dataItem.id,
                },
              })
            "
            >{{ props.dataItem[props.field] }}</FlotoLink
          >
        </template>
        <template v-slot:tags="{ props }">
          <Tags
            v-if="props.dataItem[props.field]"
            class="cursor-auto"
            :value="
              Array.isArray(props.dataItem[props.field])
                ? props.dataItem[props.field]
                : [props.dataItem[props.field]]
            "
          />
          <span v-else />
        </template>
        <template v-slot:policy-threshold="{ props }">
          <PolicyThreshold :value="props.dataItem[props.field]" />
        </template>

        <template v-slot:trap_drilldown="{ props }">
          <a @click="navigateToTrapDrilldown(props)">
            {{ props.dataItem[props.field] }}
          </a>
        </template>

        <template v-slot:trap_action="{ props }">
          <a @click="redirectToTrapPolicy(props)"> Create Trap Policy </a>
        </template>

        <template v-slot:selected_item_pills="{ props }">
          <SelectedItemPills
            :value="
              Array.isArray(props.dataItem[props.field])
                ? props.dataItem[props.field]
                : [props.dataItem[props.field]]
            "
          />
        </template>

        <template v-slot:view_more_drawer="{ props }">
          <ColorCodedCell
            :data-item="props.dataItem"
            :column="columnConfig[props.field]"
            :is-preview="isPreview"
            :field="props"
            link-icon
          >
            <ViewMoreDrawer
              :hide-title="true"
              show-status
              :is-fullscreen="$attrs['is-fullscreen']"
              :column="columnConfig[props.field]"
              :value="props.dataItem[props.field]"
              :row="props.dataItem"
              :columns="columns"
            />
          </ColorCodedCell>
        </template>
      </VirtualTable>
    </div>
    <FlotoNoData
      v-else
      hide-svg
      header-tag="h5"
      icon="exclamation-triangle"
      variant="neutral"
    />
  </div>
</template>

<script>
import SortBy from 'lodash/sortBy'
import Uniq from 'lodash/uniq'
import Pick from 'lodash/pick'
import CloneDeep from 'lodash/cloneDeep'
import Bus from '@utils/emitter'

import VirtualTable from '@components/crud/virtual-table.vue'
import MonitorType from '@components/monitor-type.vue'
import ColumnSelector from '@components/column-selector.vue'
import Progress from '@components/progress.vue'
import LooseTags from '@components/loose-tags.vue'
import { gridWorker } from '@/src/workers'
import Severity from '@components/severity.vue'
import RelativePercentage from './relative-percent.vue'
import Sparkline from './sparkline.vue'
import ColorCodedCell from './color-coded-cell.vue'
import ViewMoreSlot from './view-more/view-more.vue'
import Computed from './computed/computed.vue'
import Status from './status.vue'
import MonitorName from './view-more/monitor-name.vue'
import AlertDrilldown from './view-more/alert-drilldown.vue'
import NetRouteDrillDown from './view-more/netroute-drilldown.vue'
import InterfaceDrilldown from '../components/interface-drilldown-slot.vue'
import WanLinkDrilldown from '../components/wan-link-drilldown-slot.vue'
import ProcessDrilldown from '../components/process-drilldown-slot.vue'
import ContainerDrillDown from '../components/container-drilldown-slot.vue'
import Tags from './tags.vue'
import PolicyThreshold from './policy-threshold.vue'
import datetime from '@src/filters/datetime'
import { appendNewFilter } from '@/src/modules/log/helpers/log.helper'
import { WidgetTypeConstants } from '../../constants'
import ApplicationName from './view-more/application-name.vue'
import {
  FLOW_DRILLDOWN_IGNORED_COUNTERS,
  FLOW_DRILLDOWN_REPLACEMENT_COUNTERS_MAP,
} from '@src/components/widgets/helper'
import { FILTER_CONDITION_DEFAULT_DATA } from '@components/widgets/constants'
import ViewQuery from '@components/widgets/views/grid/view-more/view-query.vue'
import { wanProbeMap } from '@src/components/rediscover-results/rediscover-api.js'
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import ViewMoreDrawer from '@components/widgets/views/grid/view-more/view-more-drawer.vue'
import { downloadFile } from '@utils/download'
import exportData from '@modules/settings/monitoring/helpers/export-pdf-csv'
import { UserPreferenceComputed } from '@state/modules/user-preference'

export default {
  name: 'DefaultGrid',
  components: {
    VirtualTable,
    ColorCodedCell,
    ColumnSelector,
    Severity,
    InterfaceDrilldown,
    WanLinkDrilldown,
    ContainerDrillDown,
    ProcessDrilldown,
    RelativePercentage,
    Sparkline,
    MonitorType,
    Progress,
    Computed,
    ViewMoreSlot,
    MonitorName,
    Tags,
    Status,
    LooseTags,
    PolicyThreshold,
    AlertDrilldown,
    ApplicationName,
    ViewQuery,
    SelectedItemPills,
    ViewMoreDrawer,
    NetRouteDrillDown,
  },
  inheritAttrs: false,
  props: {
    isPreview: { type: Boolean, default: false },
    widget: {
      type: Object,
      required: true,
    },
    data: {
      type: [Array, Object],
      default() {
        return []
      },
    },
    ignoreWidgetWatch: {
      type: Boolean,
      default: false,
    },
    guid: {
      type: String,
      required: true,
    },
  },
  data() {
    this.wanProbeMap = wanProbeMap
    return {
      columns: [],
      dataMap: [],
      availableColumns: [],
      searchTerm: undefined,
    }
  },
  computed: {
    ...UserPreferenceComputed,
    columnConfig() {
      const map = {}
      this.columns.forEach((c) => {
        map[c.key] = c
      })
      return map
    },
    sparklineChartType() {
      return ((this.widget.widgetProperties || {}).sortingSetting || {})
        .sparklineChartType
    },
    sparklineColor() {
      return (
        ((this.widget.widgetProperties || {}).sortingSetting || {})
          .sparklineColor || '#099dd9'
      )
    },
    allowColumnSelection() {
      return (
        (this.widget.widgetProperties || {}).allowColumnSelection &&
        this.columns &&
        this.columns.length > 5
      )
    },
    hideHeader() {
      return (this.widget.widgetProperties || {}).showHeader === false
    },
    gridClasses() {
      return ((this.widget.widgetProperties || {}).style || {})['css.classes']
    },
    gridRowHeight() {
      return (
        ((this.widget.widgetProperties || {}).style || {})['row.height.px'] ||
        40
      )
    },
    isSearchable() {
      return (
        (this.widget.widgetProperties || {}).searchable &&
        this.dataMap &&
        this.dataMap.length > 10
      )
    },

    shouldShowSparkline() {
      const timeRange = this.$attrs['time-range'] || this.widget.timeRange
      const durationMillis =
        (timeRange.endDate || 0) - (timeRange.startDate || 0)
      const durationHours = durationMillis / (1000 * 60 * 60)

      return durationHours <= 48
    },
  },
  watch: {
    data: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          this.columns = newValue.columns
          if (this.allowColumnSelection) {
            this.availableColumns = CloneDeep(newValue.columns)
          }
          this.dataMap = Object.freeze(newValue.rows)
          if (this.isPreview) {
            this.$emit(
              'column-received',
              (newValue.responseColumns || []).map((c) => c)
            )
            this.$emit('group-column-received', newValue.groupByColumns)
          }

          if (
            (this.dataMap || []).length === 0 &&
            this.widget.hideEmptyWidget
          ) {
            this.$emit('hide')
          }
        }
      },
      immediate: true,
    },
  },
  created() {
    Bus.$on('grid:export-csv', this.exportCsv)

    if (this.isPreview && !this.ignoreWidgetWatch) {
      this.$watch(
        'widget.widgetProperties.columnSettings',
        async (newValue, oldValue) => {
          if (newValue !== oldValue) {
            const columns = await gridWorker.buildWidgetGridColumns(this.widget)
            this.columns = CloneDeep(columns)
            this.availableColumns = CloneDeep(columns)
            if (this.dataMap && this.dataMap.length) {
              const data = await gridWorker.reEvaluateColorConfigForGridColumns(
                this.data.rows,
                this.widget
              )
              this.dataMap = Object.freeze(data)
            }
          }
        }
      )
      this.$watch(
        'widget.widgetProperties.headerStyle',
        async (newValue, oldValue) => {
          const columns = await gridWorker.buildWidgetGridColumns(this.widget)
          this.columns = CloneDeep(columns)
          this.availableColumns = CloneDeep(columns)
        }
      )
    }

    this.$once('hook:beforeDestroy', () => {
      Bus.$off('grid:export-csv', this.exportCsv)
    })
  },
  methods: {
    navigateToDrilldown(columnInfo) {
      const group = this.widget.groups[0]
      if (group && group.category === 'flow') {
        let resultBy = {}
        const changedCounterKey =
          FLOW_DRILLDOWN_REPLACEMENT_COUNTERS_MAP[
            group?.counters?.[0]?.counter?.key
          ]

        const changedCounters =
          changedCounterKey && group?.counters?.length === 1
            ? {
                counters: [
                  {
                    aggrigateFn: 'sum',
                    counter: {
                      key: changedCounterKey,
                    },
                  },
                ],
              }
            : undefined

        if (
          group?.counters?.find((c) =>
            FLOW_DRILLDOWN_IGNORED_COUNTERS.includes(c?.counter?.key)
          )
        ) {
          return
        }
        for (const counter of group.counters) {
          if (['event.source'].includes((counter.target || {}).entityType)) {
            resultBy[(counter.target || {}).entityType] = [
              ...(resultBy[(counter.target || {}).entityType] || []),

              ...(Array.isArray((counter.target || {}).entities)
                ? (counter.target || {}).entities?.['event.source']?.includes(
                    (counter.target || {}).entityType
                  ) || []
                : Object.keys((counter.target || {}).entities || {}).length
                ? Object.keys((counter.target || {}).entities)
                : []),
            ]
          }
        }

        let filterMap = [
          ...(columnInfo.dataItem[columnInfo.field]
            ? [
                {
                  operand: columnInfo.field.replace(/_/g, '.'),
                  operator: '=',
                  value: columnInfo.dataItem[columnInfo.field],
                },
              ]
            : []),
        ]

        for (const key of Object.keys(resultBy)) {
          const value = Array.isArray(resultBy[key])
            ? Uniq(resultBy[key])
            : [resultBy[key]]

          if (value?.length) {
            filterMap = filterMap.concat([
              {
                operand: key,
                operator: 'in',
                value,
              },
            ])
          }
        }

        resultBy[columnInfo.field.replace(/_/g, '.')] =
          columnInfo.dataItem[columnInfo.field]

        if (Object.keys(resultBy).length) {
          group.filters.drillDownFilters = appendNewFilter(
            filterMap,
            CloneDeep(FILTER_CONDITION_DEFAULT_DATA)
          )
        }
        let data = {
          ...group,
          category: WidgetTypeConstants.CHART,
          chartType: WidgetTypeConstants.AREA,
          countersValue: changedCounterKey
            ? [changedCounterKey]
            : Uniq(group.counters.map((c) => c?.counter?.key)),
          ...(changedCounters || {}),
        }
        if (resultBy['event.source']) {
          data.target = {
            entityType: 'event.source',
            entities: Array.isArray(resultBy['event.source'])
              ? Uniq(resultBy['event.source'])
              : [resultBy['event.source']],
          }
        }

        if (this.$route.name === 'flow.dashboard') {
          this.$emit('drilldown', {
            type: 'flow.explorer',
            query: {
              flow: encodeURIComponent(btoa(JSON.stringify(data))),
              t: encodeURIComponent(
                btoa(JSON.stringify(this.widget.timeRange))
              ),
            },
          })

          return
        } else if (this.$route.name === 'flow.explorer') {
          return
        }

        this.$router.push(
          this.$modules.getModuleRoute('flow', 'explorer', {
            query: {
              flow: encodeURIComponent(btoa(JSON.stringify(data))),
              t: encodeURIComponent(
                btoa(JSON.stringify(this.widget.timeRange))
              ),
            },
          })
        )
      } else if (group && group.category === 'log') {
        let resultBy = {}

        for (const counter of group.counters) {
          if (
            ['event.source', 'event.source.type'].includes(
              (counter.target || {}).entityType
            )
          ) {
            resultBy[(counter.target || {}).entityType] = [
              ...(resultBy[(counter.target || {}).entityType] || []),

              ...(Array.isArray((counter.target || {}).entities)
                ? (counter.target || {}).entities
                : Object.keys((counter.target || {}).entities || {}).length
                ? Object.keys((counter.target || {}).entities)
                : []),
            ]
          }
        }

        resultBy[columnInfo.field.replace(/_/g, '.')] =
          columnInfo.dataItem[columnInfo.field]

        let filterMap = []

        for (const key of Object.keys(resultBy)) {
          filterMap = filterMap.concat([
            {
              operand: key,
              operator: 'in',
              value: Array.isArray(resultBy[key])
                ? resultBy[key]
                : [resultBy[key]],
            },
          ])
        }

        if (Object.keys(resultBy).length) {
          group.filters.drillDownFilters = appendNewFilter(
            filterMap,
            CloneDeep(FILTER_CONDITION_DEFAULT_DATA)
          )
        }
        let data = {
          ...group,
          category: WidgetTypeConstants.CHART,
          chartType: WidgetTypeConstants.AREA,
          countersValue: Uniq(group.counters.map((c) => c?.counter?.key)),
        }
        this.$router.push(
          this.$modules.getModuleRoute('log', 'log-search', {
            query: {
              filter: encodeURIComponent(btoa(JSON.stringify(group.filters))),
              log: encodeURIComponent(btoa(JSON.stringify(data))),
              t: encodeURIComponent(
                btoa(JSON.stringify(this.widget.timeRange))
              ),
            },
          })
        )
      }
    },
    isGroupByColumn(column) {
      const group = this.widget.groups.find((g) =>
        ['log', 'flow'].includes(g.category)
      )
      if (group && column !== 'policy_id') {
        return (group.resultBy || []).includes(column.replace(/_/g, '.'))
      }
      return false
    },
    handleUpdateWidgetColumns(columns) {
      const map = {}

      for (const [index, c] of columns.entries()) {
        map[c.serverName] = Pick(c, ['width', 'orderIndex', 'hidden'])

        if (!map[c.serverName].orderIndex) {
          map[c.serverName].orderIndex = index + 1
        }
      }

      let columnSettings = this.widget.widgetProperties.columnSettings || []
      columnSettings = columnSettings.map((c) => ({ ...c, ...map[c.name] }))
      columnSettings = SortBy(columnSettings, 'orderIndex')
      this.$emit('update-columns', columnSettings)
    },
    formatDateTime(value) {
      return datetime(Math.round(value / 1000))
    },
    rowclick() {
      Bus.$emit('row-click')
    },
    navigateToTrapDrilldown(columnInfo) {
      const rowdata = columnInfo.dataItem

      const trap = {
        ...rowdata,
        severity: rowdata.trap_severity,
        source: rowdata.event_source,
        oid: rowdata.trap_oid,
        enterpriseId: rowdata.trap_enterprise_id_last,
        version: rowdata.trap_version_last,
        vendor: rowdata.trap_vendor_last,
        count: rowdata.trap_message_count,
      }
      const hash = encodeURIComponent(btoa(JSON.stringify(trap)))
      this.$router.push(
        this.$modules.getModuleRoute('trap-viewer', 'trap-viewer-details', {
          params: { hash },
        })
      )
    },
    redirectToTrapPolicy(columnInfo) {
      const item = columnInfo.dataItem

      this.$router.push(
        this.$modules.getModuleRoute('policy-settings', 'create-policy', {
          params: {
            policyType: 'trap',

            drillDownContext: {
              condition: {
                counter: {
                  key: 'trap.oid',
                  counterName: 'trap.oid',
                  name: 'trap.oid',
                  metricPlugins: [500001],
                  eventCategory: 'trap',
                  isStatusCounter: false,
                  dataType: ['string'],
                },
                operator: '=',
                conditionValue: item.trap_oid,
                ...([
                  this.$constants.CRITICAL,
                  this.$constants.MAJOR,
                  this.$constants.WARNING,
                ].includes((item.trap_severity || '').toUpperCase())
                  ? { severity: item.trap_severity.toUpperCase() }
                  : {}),
              },
              entityType: 'event.source',
              entities: [item.event_source],

              // severity: item.severity,
            },
          },
        })
      )
    },
    async exportCsv(guid) {
      if (guid !== this.guid) {
        return
      }
      try {
        // First, get visible columns (not hidden)

        let columnSettings = this.widget.widgetProperties.columnSettings || []

        const orderIndexMap = {}

        for (const [index, c] of columnSettings.entries()) {
          orderIndexMap[c.rawName] = Pick(c, ['orderIndex'])

          if (!orderIndexMap[c.rawName].orderIndex) {
            orderIndexMap[c.rawName].orderIndex = index + 1
          }
        }

        const columns = SortBy(
          this.columns
            .filter(
              (obj) => obj.key && !obj.hidden && !/sparkline$/.test(obj.rawName)
            )
            .map((c) => ({ ...c, ...orderIndexMap[c.rawName] })),
          'orderIndex'
        )

        // Try multiple data sources for export
        let items = []

        // Use the first available data source
        if (
          this.$refs.tableRef &&
          this.$refs.tableRef.items &&
          this.$refs.tableRef.items.length
        ) {
          items = this.$refs.tableRef.items
        }

        // Check if we have data to export
        if (!items || items.length === 0) {
          this.$errorNotification({
            message: 'Export Error',
            description: 'No data available to export',
          })
          return
        }

        let contextData = {}
        if (this.$refs.tableRef) {
          contextData = this.$refs.tableRef.getContextData()
        }

        // Generate a timestamp for the filename
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
        const filename = `${this.widget?.name || 'grid-data'}_${timestamp}.csv`

        this.$successNotification({
          message: 'Export Started',
          description: 'The file will be downloaded once ready',
        })

        // Generate and download the CSV file
        exportData(columns, items, 'csv', contextData, {
          dateTimeFormat: this.dateFormat,
          timezone: this.timezone,
          wanProbeMap,
        })
          .then((blob) => {
            downloadFile(blob, undefined, filename)
          })
          .catch((error) => {
            this.$errorNotification({
              message: 'Export Error',
              description: `Error generating CSV: ${
                error.message || 'Unknown error'
              }`,
            })
          })
      } catch (error) {
        this.$errorNotification({
          message: 'Export Error',
          description: 'Failed to export CSV. Please try again.',
        })
      }
    },
  },
}
</script>
