<template>
  <div v-if="values.length" class="text-ellipsis">
    <span class="text-ellipsis">
      <template v-if="disableMonitorResolution">
        <b v-if="useBoldFont">
          {{ values[0] }}
        </b>
        <template v-else>
          {{ values[0] }}
        </template>
      </template>
      <MonitorName v-else :value="values[0]" />
    </span>
    <MPopover
      v-if="values.length > 1"
      overlay-class-name="readable-content-overlay picker-overlay"
      :get-popup-container="getPopupContainer"
    >
      <template v-slot:trigger>
        <div>
          <a> View More </a>
        </div>
      </template>
      <div class="list" style="min-width: 250px">
        <div
          v-if="useSearch"
          style="
            position: sticky;
            top: 0;
            background: var(--dropdown-background);
          "
        >
          <MInput
            v-model="searchTerm"
            class="search-box"
            placeholder="Search"
            name="search"
          >
            <template v-slot:prefix>
              <MIcon name="search" />
            </template>
            <template v-if="searchTerm" v-slot:suffix>
              <MIcon
                name="times-circle"
                class="text-neutral-light cursor-pointer"
                @click="searchTerm = undefined"
              />
            </template>
          </MInput>
        </div>
        <div v-for="(item, index) in filteredValues" :key="`${item}-${index}`">
          <template v-if="disableMonitorResolution">
            <b v-if="useBoldFont">
              {{ item }}
            </b>
            <template v-else>
              {{ item }}
            </template>
          </template>
          <MonitorName v-else :value="item" />
        </div>
      </div>
    </MPopover>
  </div>
</template>

<script>
import Trim from 'lodash/trim'
import MonitorName from './monitor-name.vue'

export default {
  name: 'ViewMore',
  components: {
    MonitorName,
  },
  props: {
    field: {
      type: String,
      required: true,
    },
    row: {
      type: Object,
      required: true,
    },
    disableMonitorResolution: {
      type: Boolean,
      default: false,
    },
    useBoldFont: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      searchTerm: undefined,
    }
  },
  computed: {
    values() {
      let fieldName = this.field['field']
      return (this.row[fieldName] || '').toString().split(',')
    },
    filteredValues() {
      const searchTerm = this.searchTerm ? Trim(this.searchTerm) : undefined
      if (searchTerm) {
        return this.values
          .slice(1)
          .filter((v) => v.toLowerCase().indexOf(searchTerm.toLowerCase()) >= 0)
      }
      return this.values.slice(1)
    },
    useSearch() {
      return this.values.slice(1).length > 5
    },
  },
  methods: {
    getPopupContainer() {
      return (
        this.$el.closest('.vue-grid-layout') || this.$el.closest('.widget-view')
      )
    },
  },
}
</script>
