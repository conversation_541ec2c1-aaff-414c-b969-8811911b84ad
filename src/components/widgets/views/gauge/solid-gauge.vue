<template>
  <div class="flex flex-1 flex-col justify-between rounded min-h-0">
    <Title
      v-if="!$attrs['for-topn-solid-gauge']"
      :title="solidGaugeTitle"
      :progress="$attrs['progress']"
      :is-preview="$attrs['is-preview']"
      :hide-actions="$attrs.fullscreen || $attrs['hide-actions']"
      :disabled="$attrs.disabled"
      :hide-timeline="$attrs['hide-timeline']"
      :guid="$attrs.guid"
      :is-dashboard-fullscreen="$attrs['is-dashboard-fullscreen']"
      :is-fullscreen="$attrs['is-fullscreen']"
      :for-template="$attrs['for-template']"
      :widget="widget"
      :time-range="$attrs['time-range']"
      :result-error="$attrs['result-error']"
      :header-font-size="headerFontSize"
      :excluded-actions="excludedActions"
      :hide-title="$attrs['hide-title']"
      @fullscreen="makeFullScreen"
      @exit-fullscreen="exitFullScreen"
      v-on="$listeners"
    />
    <FlotoNoData
      v-if="showNoData"
      hide-svg
      header-tag="h5"
      icon="exclamation-triangle"
      variant="neutral"
    />
    <div
      v-else
      class="flex-1 flex min-h-0 flex-col mb-2"
      :class="{
        'px-6': !$attrs['for-topn-solid-gauge'],
        'cursor-pointer': hasDrillDown,
      }"
      @click="$emit('drilldown', selectedCounters)"
    >
      <Gauge :value="value" :gauge-color="color">
        <NumberAnimation
          :style="{
            fontSize: `${textFontSizeValue}rem`,
            lineHeight: 1,
            color,
            fontWeight: 500,
          }"
          :format="numberFormat"
          :from="previousValue"
          :to="extractedValue.value"
          :duration="0.5"
        />
        <span
          :style="{
            fontSize: `${textFontSizeValue}rem`,
            color,
            lineHeight: 1,
            fontWeight: 500,
          }"
          v-text="extractedValue.unit"
        />
      </Gauge>
    </div>
    <div v-if="classes.includes('bottom-title')" class="text-center my-2">
      {{ title }}
    </div>
  </div>
</template>

<script>
import Gauge from '@components/chart/gauge.vue'
import { extractUnitAndValue } from '@utils/unit-checker'
import NumberAnimation from 'vue-number-animation/Number.vue'
import Title from '../components/widget-title.vue'

export default {
  name: 'SolidGauge',
  components: {
    Title,
    Gauge,
    NumberAnimation,
  },
  inheritAttrs: false,
  props: {
    widget: {
      type: Object,
      default() {
        return {}
      },
    },
    headerFontSize: {
      type: String,
      default: undefined,
    },
    isCircle: {
      type: Boolean,
      default: false,
    },
    fontSize: {
      type: String,
      default: undefined,
    },
    titleText: {
      type: String,
      default: undefined,
    },
    title: {
      type: String,
      default: undefined,
    },
    value: {
      type: [String, Number],
      default: undefined,
    },
    formattedValue: {
      type: [String, Number],
      default: undefined,
    },
    iconName: {
      type: String,
      default: undefined,
    },
    iconPosition: {
      type: String,
      default: undefined,
    },
    color: {
      type: String,
      default: undefined,
    },
    makeFullScreen: {
      type: Function,
      default: undefined,
    },
    exitFullScreen: {
      type: Function,
      default: undefined,
    },
    showNoData: {
      type: Boolean,
      default: false,
    },
    forTemplate: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      previousValue: this.value,
      previousFromattedValue: this.formattedValue,
    }
  },
  computed: {
    classes() {
      return ((this.widget || {}).widgetProperties || {}).classes || []
    },
    extractedValue() {
      if (this.formattedValue) {
        return extractUnitAndValue(this.formattedValue)
      }
      return {
        value: this.value,
      }
    },
    excludedActions() {
      return []
    },
    textFontSizeValue() {
      let fontSize
      if (this.fontSize === 'small') {
        fontSize = 1.5625
      } else if (this.fontSize === 'medium') {
        fontSize = 2.1875
      } else if (this.fontSize === 'large') {
        fontSize = 2.4125
      }
      return Math.max(fontSize, 1)
    },
    data() {
      return {
        name: this.title,
        value: [this.value],
        ...(this.formattedValue
          ? { formattedValue: [this.formattedValue] }
          : {}),
        suffix: '%',
      }
    },

    solidGaugeTitle() {
      return this.titleText || this.title
    },
    selectedCounters() {
      return this.widget?.groups?.[0]?.counters?.map((c) => {
        return c?.counter?.key
      })
    },
    hasDrillDown() {
      return this.selectedCounters?.length && this.forTemplate
    },
  },
  watch: {
    value(newValue, oldValue) {
      if (newValue !== oldValue && oldValue) {
        this.previousValue = oldValue
      }
    },
    formattedValue(newValue, oldValue) {
      if (newValue !== oldValue && oldValue) {
        this.previousFromattedValue = oldValue
      }
    },
  },
  methods: {
    numberFormat(number) {
      if (/\.\d+$/.test(number)) {
        return parseFloat(number).toFixed(2)
      }
      return parseInt(number)
    },
  },
}
</script>
