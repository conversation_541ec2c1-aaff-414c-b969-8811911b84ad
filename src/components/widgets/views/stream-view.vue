<template>
  <IncrementalResultProvider
    v-if="guid"
    ref="resultProviderRef"
    :timeline="timeRange"
    :default-guid="guid"
    full-response
    :is-preview="isPreview"
    :server-params="$attrs['server-params']"
    :disable-auto-fetching="true"
    :serverside-widget-defination="serversideWidgetDefination"
    :report-category="$attrs['report-category']"
    @patchRecived="patchRecived"
  >
    <DefaultGridView
      v-if="!loading && !loadingData"
      :data="gridData"
      :widget="widget"
      :is-alert-report="isAlertReport"
      :is-preview="isPreview"
      :guid="guid"
      v-on="$listeners"
    />
    <FlotoContentLoader v-else loading :row-gutter="0" />
  </IncrementalResultProvider>
</template>

<script>
import IncrementalResultProvider from '@components/data-provider/incremental-result-provider.vue'
import Bus from '@utils/emitter'
import buildWidgetResult from '@components/widgets/result-builder'
// import { WidgetTypeConstants } from '@components/widgets/constants'
import {
  transformWidget,
  transformWidgetForServer,
} from '@components/widgets/translator.js'
import DefaultGridView from './grid/default-grid.vue'
import Omit from 'lodash/omit'
import { WidgetTypeConstants } from '../constants'

import { getWidgetApi } from '@/src/components/widgets/widgets-api'

export default {
  name: 'StreamView',
  components: {
    DefaultGridView,
    IncrementalResultProvider,
  },
  props: {
    timeRange: {
      type: Object,
      default: undefined,
    },
    widget: {
      type: Object,
      required: true,
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
    guid: {
      type: String,
      required: true,
    },
    loadingData: {
      type: Boolean,
      required: true,
    },
    data: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      gridData: {},
      gridKey: 1,
      loading: true,
      parentQueryId: null,
      parentSubQueryId: null,
      widgetApiDef: undefined,
      patchCount: 1,
    }
  },
  computed: {
    isAlertReport() {
      return (
        this.$attrs['report-category'] === 'availability.alerts' ||
        this.$attrs['report-category'] === 'metric.alert' ||
        this.$attrs['report-category'] === 'availability.flap.summary'
      )
    },
    serversideWidgetDefination() {
      return transformWidgetForServer(this.widget)
    },
    isEventHistoryWidget() {
      return [WidgetTypeConstants.EVENT_HISTORY].includes(this.widget.category)
    },
  },
  watch: {
    // widget: {
    //   handler: 'changeKey',
    // },
  },

  created() {
    Bus.$on('request-widget-result-incremental', this.requestIncrementalData)
    if (this.data) {
      if (Object.keys(this.data || {}).length) {
        this.patchRecived(this.data)
      }
    }
  },
  beforeDestroy() {
    Bus.$off('request-widget-result-incremental', this.requestIncrementalData)
  },

  methods: {
    requestIncrementalData(guid) {
      // const resolvedResponse = Promise.resolve(response)

      if (this.$refs.resultProviderRef && this.guid === guid) {
        this.loading = true
        this.gridData = {}
        this.$refs.resultProviderRef.setGuid(this.guid)
        this.$refs.resultProviderRef.requestDataRaw(false)
        this.parentQueryId = null
        this.parentSubQueryId = null
        this.patchCount = 1
        this.widgetApiDef = undefined
      }
    },
    async patchRecived(response) {
      if (this.guid === response[this.$constants.UI_EVENT_UUID]) {
        const parentQueryId = response.result.queryMeta.parentQueryId
        const parentSubQueryId = response['sub.query.id']
        const isPolicyFlapWidget =
          (response['visualization.data.sources'] || [])[0].type ===
          'policy.flap'

        if (
          this.parentQueryId &&
          this.parentSubQueryId &&
          this.parentQueryId !== parentQueryId &&
          this.parentSubQueryId !== parentSubQueryId
        ) {
          this.gridData = {}
        }
        this.__combinedData = [
          ...(this.__combinedData || []),
          ...(response.result[WidgetTypeConstants.GRID].data || []),
        ]
        if (response.result[WidgetTypeConstants.GRID].data.length > 0) {
          const grid = await buildWidgetResult(
            {
              ...this.widget,
              category: 'Grid',
              serverCategory: this.widget.category,
            },
            {
              ...response,
              result: {
                ...response.result,
                [WidgetTypeConstants.GRID]: {
                  ...response.result[WidgetTypeConstants.GRID],
                  data: this.__combinedData,
                },
              },
            },
            {
              useWidgetColumnsOnly: false,
            }
          )

          this.gridData = {
            ...grid,
            rows: Object.freeze(grid.rows),
            columns: [...(this.gridData.columns || grid.columns || [])],
            groupByColumns: [
              ...(this.gridData.groupByColumns || grid.groupByColumns || []),
            ],
            responseColumns: [
              ...(this.gridData.responseColumns || grid.responseColumns || []),
            ],
          }
        }

        this.parentQueryId = parentQueryId
        this.parentSubQueryId = parentSubQueryId
        const widgetContext = transformWidget(Omit(response, ['result']))
        this.$nextTick(async () => {
          if (this.isPreview) {
            this.$emit('preview-rendered', true)
          } else {
            if (
              this.isEventHistoryWidget ||
              isPolicyFlapWidget ||
              (response['container.type'] === 'report' &&
                ['status.flap', 'hourly.status.flap'].includes(
                  response['visualization.data.sources']?.[0]?.type
                ))
            ) {
              if (isPolicyFlapWidget || this.isEventHistoryWidget) {
                if (!this.widgetApiDef && this.patchCount === 1) {
                  await getWidgetApi(widgetContext.id).then((w) => {
                    this.widgetApiDef = w
                    this.$emit('update:widget', {
                      ...w,
                      ...(this.timeRange && !w.useWidgetTime
                        ? { timeRange: this.timeRange }
                        : {}),
                    })
                    this.patchCount++
                  })
                } else {
                  this.widgetApiDef = {
                    ...this.widgetApiDef,
                    ...(this.timeRange && !this.widgetApiDef?.useWidgetTime
                      ? { timeRange: this.timeRange }
                      : {}),
                  }

                  this.$emit('update:widget', {
                    ...this.widgetApiDef,
                  })
                  this.patchCount++
                }
              } else {
                this.$emit('update:widget', widgetContext)
              }
            }
            this.$emit('data-rendered', true)
          }

          if (response.result.queryMeta.progress >= 100) {
            this.__combinedData = undefined
            this.$emit('context-received', {
              response: response,
              data: this.gridData,
              widget: widgetContext,
            })
          } else {
            if (
              this.$attrs['report-category'] === 'availability.flap.summary'
            ) {
              return
            }

            this.$refs.resultProviderRef.requestNextBatch(
              this.widgetApiDef?.useWidgetTime
                ? this.widgetApiDef?.timeRange
                : this.timeRange
            )
          }

          setTimeout(() => {
            this.loading = false
          }, 500)
        })
      }
    },
    changeKey() {
      this.gridKey = this.gridKey + 1
    },
  },
}
</script>
