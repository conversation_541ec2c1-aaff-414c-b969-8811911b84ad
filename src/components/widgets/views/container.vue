<template>
  <Capture :widget="widget">
    <template v-slot="{ capture }">
      <WidgetLayout
        :guid="guid"
        :widget="widget"
        :progress="progress"
        :time-range="timeRange"
        :is-loading="loadingData"
        :disabled="disabled"
        :result-error="resultError"
        :font-size="fontSize"
        :is-preview="isPreview"
        :additional-actions="additionalActions"
        :error="error"
        :is-dashboard-fullscreen="isDashboardInFullscreen"
        :excluded-actions="
          loadingData ? __excludedActionsWhileLoading : undefined
        "
        v-bind="$attrs"
        @edit="$emit('edit', widget)"
        @clone="$emit('clone', widget)"
        @remove="$emit('remove')"
        @share="capture"
        @csv-export="handleCsvExport"
      >
        <template
          v-if="!error"
          v-slot="{
            headerFontSize,
            hideActions,
            makeFullScreen,
            exitFullScreen,
            isFullscreen,
            forTemplate,
          }"
        >
          <Transition name="placeholder" mode="out-in">
            <FlotoContentLoader
              v-if="loadingData && shouldRequestWidgetResult"
              loading
              :row-gutter="0"
            />
            <Component
              :is="widgetComponent"
              v-else
              ref="componentRef"
              :widget="widget"
              :guid="guid"
              :progress="progress"
              :is-fullscreen="isFullscreen"
              :time-range="timeRange"
              :header-font-size="headerFontSize"
              :data.sync="data"
              :for-monitor-template="forMonitorTemplate"
              :for-group-template="forGroupTemplate"
              :server-params="serverParams"
              :for-template="forTemplate"
              :is-preview="isPreview"
              :make-full-screen="makeFullScreen"
              :exit-full-screen="exitFullScreen"
              :disabled="disabled"
              :result-error="resultError"
              :hide-actions="hideActions"
              :loading-data="loadingData"
              v-bind="$attrs"
              v-on="listeners"
              @timeline-change="$emit('timeline-change', $event)"
              @edit="$emit('edit', widget)"
              @clone="$emit('clone', widget)"
              @remove="$emit('remove')"
              @share="capture"
            />
          </Transition>
        </template>

        <template v-else v-slot:error>
          <ErrorShower
            :error="error"
            hide-image
            :component="currentComponent"
            :reason="error.message"
            :message="error.message"
            @remove-error="requestWidgetResult"
          />
        </template>
      </WidgetLayout>
    </template>
  </Capture>
</template>

<script>
import { generateId } from '@utils/id'
import IsEqual from 'lodash/isEqual'
import Debounce from 'lodash/debounce'
import Bus from '@utils/emitter'
import Omit from 'lodash/omit'
import Capture from '@components/chart/capture.vue'
import WidgetLayout from './widget-layout.vue'
import { WidgetTypeConstants } from '../constants'
import {
  canRenderWidgetPreview,
  convertTimeLineForServer,
  mergeResultsIfNeeded,
} from '../helper'
import ChartView from './chart-view.vue'
import GridView from './grid-view.vue'
import TopNView from './topn-view.vue'
import GaugeView from './gauge-view.vue'
import SankeyView from './sankey-view.vue'
import HeatmapView from './heatmap-view.vue'
import MapView from './map-view.vue'
import StreamView from './stream-view.vue'
import buildWidgetResult from '../result-builder'
import { transformWidgetForServer, transformWidget } from '../translator'
import CustomView from './custom-view.vue'
import { createQueue } from '../widget-queue'
import AiMlChartView from './ai-ml-chart-view.vue'
import { AvailableReportCategories } from '@modules/report/helpers/report'
import MultiTrendReportCharts from './multi-trend-report-charts.vue'

const TIME_OUT_SEC = 24 * 60 * 60 * 1000
const STREAMING_HEARTBEAT_INTERVAL = 10000

export default {
  name: 'WidgetContainer',
  components: {
    WidgetLayout,
    Capture,
  },
  inject: { SocketContext: { defualt: {} } },
  inheritAttrs: false,
  props: {
    forMonitorTemplate: {
      type: Boolean,
      default: false,
    },
    forGroupTemplate: {
      type: Boolean,
      default: false,
    },
    timeRange: {
      type: Object,
      default: undefined,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    widget: {
      type: Object,
      required: true,
    },
    fontSize: {
      type: String,
      default: 'small',
    },
    isPreview: {
      type: Boolean,
      default: false,
    },
    useInitialRequest: {
      type: Boolean,
      default: false,
    },
    enableDebounce: {
      type: Boolean,
      default: false,
    },
    isDashboardInFullscreen: {
      type: Boolean,
      default: false,
    },
    serverParams: {
      type: Object,
      default() {
        return {}
      },
    },
    watchWidget: {
      type: Boolean,
      default: false,
    },
    queue: {
      type: Object,
      default: undefined,
    },
    disableRefreshInterval: {
      type: Boolean,
      default: false,
    },
    forceScheduledUpdate: {
      type: Boolean,
      default: false,
    },
    enableActiveSessionDebounce: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.__excludedActionsWhileLoading = [
      'export-csv',
      'edit',
      'clone',
      'fullscreen',
      'share',
    ]
    return {
      guid: generateId(),
      data: {},
      loadingData: true,
      error: null,
      queryParentId: null,
      progress: 0,
      resultError: null,
      currentBatch: 1,
      activeSessionBeepCount: 0,
    }
  },
  computed: {
    currentComponent() {
      return this
    },
    listeners() {
      const { edit, remove, clone, ...listeners } = this.$listeners
      return listeners
    },
    additionalActions() {
      if (this.widget.category === WidgetTypeConstants.HEATMAP) {
      }
      return []
    },
    isEventHistoryWidget() {
      return [WidgetTypeConstants.EVENT_HISTORY].includes(this.widget.category)
    },
    widgetComponent() {
      if (this.data && this.data.isMultiTrendReport) {
        return MultiTrendReportCharts
      }
      if (
        [AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY].includes(
          this.$attrs['report-category']
        )
      ) {
        return StreamView
      }
      if (
        [WidgetTypeConstants.EVENT_HISTORY].includes(this.widget.category) ||
        [
          AvailableReportCategories.AVAILABILITY_ALERT,
          AvailableReportCategories.EVENT_HISTORY,
          // AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
        ].includes(this.$attrs['report-category'])
      ) {
        return StreamView
      }
      if (
        [WidgetTypeConstants.ANOMALY, WidgetTypeConstants.FORECAST].includes(
          this.widget.category
        )
      ) {
        return AiMlChartView
      } else if (this.widget.category === WidgetTypeConstants.ACTIVE_ALERT) {
        return GridView
      } else if (this.widget.widgetType === WidgetTypeConstants.STATUS_FLAP) {
        return GridView
      } else if (this.widget.category === WidgetTypeConstants.CHART) {
        return ChartView
      } else if (this.widget.category === WidgetTypeConstants.GRID) {
        return GridView
      } else if (this.widget.category === WidgetTypeConstants.TOPN) {
        return TopNView
      } else if (this.widget.category === WidgetTypeConstants.GAUGE) {
        return GaugeView
      } else if (this.widget.category === WidgetTypeConstants.HEATMAP) {
        return HeatmapView
      } else if (this.widget.category === WidgetTypeConstants.SANKEY) {
        return SankeyView
      } else if (this.widget.category === WidgetTypeConstants.CUSTOM) {
        return CustomView
      } else if (
        [
          WidgetTypeConstants.EVENT_HISTORY,
          WidgetTypeConstants.STREAM,
        ].includes(this.widget.category)
      ) {
        return StreamView
      } else if (this.widget.category === WidgetTypeConstants.MAP_VIEW) {
        return MapView
      }

      return undefined
    },
    shouldRequestWidgetResult() {
      if (
        [
          WidgetTypeConstants.EVENT_HISTORY,
          WidgetTypeConstants.STREAM,
        ].includes(this.widget.category)
      ) {
        return false
      }
      if (
        [
          AvailableReportCategories.AVAILABILITY_ALERT,
          AvailableReportCategories.EVENT_HISTORY,
          AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
        ].includes(this.$attrs['report-category'])
      ) {
        return false
      }
      return true
      // return ![WidgetTypeConstants.STREAM].includes(this.widget.category)
    },
  },
  watch: {
    timeRange: {
      handler(newValue, oldValue) {
        if (!this.isPreview && this.widget.useWidgetTime) {
          return
        }
        if (
          newValue?.selectedKey === oldValue?.selectedKey &&
          newValue?.selectedKey !== 'custom'
        ) {
          return
        }

        if (newValue !== oldValue) {
          if (this.isPreview) {
            this.loadingData = true
            this.data = {}
            this.requestWidgetResultDebounced()
          } else {
            if (
              [
                WidgetTypeConstants.EVENT_HISTORY,
                WidgetTypeConstants.STREAM,
              ].includes(this.widget.category) ||
              [
                AvailableReportCategories.AVAILABILITY_ALERT,
                AvailableReportCategories.EVENT_HISTORY,
                AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
              ].includes(this.$attrs['report-category'])
            ) {
              this.currentBatch = 1
              Bus.$emit('request-widget-result-incremental', this.guid)
            }
            this.loadingData = true
            this.data = {}
            this.sendActiveSessionEvent()
          }
        }
      },
    },
    serverParams(newValue, oldValue) {
      if (!IsEqual(newValue, oldValue)) {
        if (this.isPreview) {
          this.requestWidgetResultDebounced()
        } else {
          if (
            this.enableActiveSessionDebounce &&
            this.sendActiveSessionEventDebounce
          ) {
            this.sendActiveSessionEventDebounce()
          } else {
            this.sendActiveSessionEvent()
          }
        }
      }
    },
  },
  created() {
    this._responseQueue = createQueue(1, 1000)

    if (this.enableActiveSessionDebounce) {
      this.sendActiveSessionEventDebounce = Debounce(
        this.sendActiveSessionEvent,
        30000,
        {
          trailing: true,
          leading: false,
        }
      )
    }

    if (this.enableDebounce) {
      this.requestWidgetResultDebounced = Debounce(
        this.requestWidgetResult,
        700,
        {
          trailing: true,
          leading: false,
        }
      )
    } else {
      this.requestWidgetResultDebounced = this.requestWidgetResult
    }

    const previewHandler = () => {
      if (canRenderWidgetPreview(this.widget)) {
        this.loadingData = true
        this.data = {}
        this.requestWidgetResultDebounced()
      }
    }

    if (this.isPreview) {
      Bus.$on('widget.generate.preview', previewHandler)
    }

    Bus.$on(this.$constants.UI_WIDGET_QUERY_ID_EVENT, this.storeParentQueryId)
    Bus.$on(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleResultReceived)
    Bus.$on(this.$constants.WIDGET_RELOAD_EVENT, this.handleResultReceived)
    Bus.$on('socket:connected', this.requestWidgetResult)
    const refreshHandler = (id) => {
      if (id === this.widget.id) {
        this.requestWidgetResult()
      }
    }
    Bus.$on('widget.refresh', refreshHandler)

    const exportWidgetInitHandler = () => {
      setTimeout(() => {
        Bus.$emit('current:widget:queue', this.queue)
      })
      if (this.loadingData) {
        if (this.queue) {
          this.queue.add(this.requestWidgetResult)
        } else {
          this.requestWidgetResult()
        }
      }
    }

    this.$once('hook:beforeDestroy', () => {
      if (this.__streamingTimer) {
        clearInterval(this.__streamingTimer)
      }
      Bus.$off('widget.generate.preview', previewHandler)
      Bus.$off('socket:connected', this.requestWidgetResult)
      Bus.$off('widgets:layout:ready', this.buildIntersection)
      Bus.$off('widgets:export', exportWidgetInitHandler)
      Bus.$off('widget.refresh', refreshHandler)
      Bus.$off(
        this.$constants.UI_WIDGET_RESULT_EVENT,
        this.handleResultReceived
      )
      Bus.$off(this.$constants.WIDGET_RELOAD_EVENT, this.handleResultReceived)
      Bus.$off(
        this.$constants.UI_WIDGET_QUERY_ID_EVENT,
        this.storeParentQueryId
      )
      if (this._timeoutId) {
        clearTimeout(this._timeoutId)
      }
      if (this.viewPortIntersectionObserver) {
        this.viewPortIntersectionObserver.disconnect()
      }

      if (!this.isPreview) {
        Bus.$emit('server:event', {
          'event.type': this.$constants.UI_WIDGET_INACTIVE_SESSION,
          'event.context': {
            [this.$constants.UI_EVENT_UUID]: this.guid,
            id: this.widget.id,
          },
        })
      }
    })

    // if (this.SocketContext.connected && !this.isPreview) {
    //   this.requestWidgetResult()
    // }

    if (this.isPreview) {
      previewHandler()
    }
    if (this.useInitialRequest) {
      this.requestWidgetResult()
      this.scheduleUpdate()
    }
    if (!this.isPreview) {
      Bus.$on('widgets:layout:ready', this.buildIntersection)
      Bus.$on('widgets:export', exportWidgetInitHandler)
    }

    if (this.watchWidget) {
      this.$watch('widget', this.requestWidgetResult)
    }
  },
  methods: {
    storeParentQueryId(response) {
      if (response[this.$constants.UI_EVENT_UUID] === this.guid) {
        if (response[this.isPreview ? -1 : this.widget.id]) {
          const queryParentId = response[this.isPreview ? -1 : this.widget.id]
          if (this.queryParentId !== queryParentId) {
            this.queryParentId = queryParentId
            if (this.widget.category === WidgetTypeConstants.CHART) {
              this.loadingData = true
              this.data = {}
            }
          }
        }
      }
    },
    sendActiveSessionEvent() {
      if (this.widget.id === -1 && !this.queryParentId) {
        return
      }
      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_WIDGET_ACTIVE_SESSION,
        'event.context': {
          ...(this.widget.id !== -1 ? { id: this.widget.id } : {}),
          ...(this.widget.id === -1 ? { 'query.id': this.queryParentId } : {}),
          [this.$constants.UI_EVENT_UUID]: this.guid,
          'event.context': {
            ...(this.serverParams || {}),
            ...(this.timeRange && !this.widget.useWidgetTime
              ? {
                  'visualization.timeline': convertTimeLineForServer(
                    this.timeRange
                  ),
                }
              : {
                  'visualization.timeline': {},
                }),

            ...(this.widget.useWidgetTime
              ? {
                  'visualization.timeline': convertTimeLineForServer(
                    this.widget.timeRange
                  ),
                }
              : {}),
          },

          count: this.activeSessionBeepCount,
        },
      })

      this.activeSessionBeepCount++
    },
    scheduleUpdate() {
      if (!this.disableRefreshInterval && !this.isPreview) {
        if (this.__streamingTimer) {
          clearInterval(this.__streamingTimer)
          this.__streamingTimer = null
        }
        this.__streamingTimer = setInterval(
          this.sendActiveSessionEvent,
          STREAMING_HEARTBEAT_INTERVAL
        )
      }
    },
    buildIntersection() {
      const el = this.$el
      const _that = this
      this.viewPortIntersectionObserver = new IntersectionObserver(
        function (entries) {
          if (!entries[0].isIntersecting) {
            return
          }
          _that.scheduleUpdate()
          if (_that.loadingData) {
            if (_that.queue) {
              _that.queue.add(_that.requestWidgetResult)
            } else {
              _that.requestWidgetResult()
            }
          } else {
            if (_that.viewPortIntersectionObserver) {
              _that.viewPortIntersectionObserver.unobserve(el)
              _that.viewPortIntersectionObserver.disconnect()
              _that.viewPortIntersectionObserver = null
            }
          }
        },
        {
          threshold: 0.3,
        }
      )
      this.viewPortIntersectionObserver.observe(el)
    },
    requestWidgetResult(loading = true) {
      return new Promise((resolve, reject) => {
        this.resultError = null
        this.__resolveFn = resolve
        this.__rejectFn = reject
        this.loadingData = loading
        this.currentBatch = 1
        this.error = null
        if (this._timeoutId) {
          clearTimeout(this._timeoutId)
        }
        this.progress = 0
        this._timeoutId = setTimeout(() => {
          this.error = new Error('Failed to generate widget! No data received.')
          this.error.widget = this.widget
          reject(this.error)
          this.$emit('widget-error', this.error)
          this.progress = 100
        }, TIME_OUT_SEC)
        if (loading) {
          this.data = {}
        }

        if (
          [
            WidgetTypeConstants.EVENT_HISTORY,
            WidgetTypeConstants.STREAM,
          ].includes(this.widget.category) ||
          [
            AvailableReportCategories.AVAILABILITY_ALERT,
            AvailableReportCategories.EVENT_HISTORY,
            AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
          ].includes(this.$attrs['report-category'])
        ) {
          this.currentBatch = 1
          Bus.$emit('request-widget-result-incremental', this.guid)
        }

        Bus.$emit('server:event', {
          'event.type': this.$constants.UI_WIDGET_RESULT_EVENT,
          'event.context': {
            ...(this.isPreview || this.widget.id === -1
              ? {
                  ...transformWidgetForServer(this.widget),
                  id: -1,
                  ...this.serverParams,
                }
              : {
                  id: this.widget.id,
                  ...this.serverParams,
                  ...(this.timeRange
                    ? {
                        'visualization.timeline': this.widget.useWidgetTime
                          ? convertTimeLineForServer(this.widget.timeRange)
                          : convertTimeLineForServer(this.timeRange),
                      }
                    : {}),
                }),
            [this.$constants.UI_EVENT_UUID]: this.guid,
          },
        })
        if (!this.isPreview) {
          this.sendActiveSessionEvent()
        }
      })
    },
    async handleResultReceived(response) {
      if (
        response.result.queryMeta &&
        response.result.queryMeta.parentQueryId !== this.queryParentId
      ) {
        return
      }
      if (this._timeoutId) {
        clearTimeout(this._timeoutId)
      }
      let progress = response.result.queryMeta.progress
      // keep progress a little bit bounced so that it won't show jerk
      if (progress < 100) {
        if (!this.__progressTimeout) {
          if (this.progress) {
            this.progress = progress
          } else {
            this.__progressTimeout = setTimeout(() => {
              this.progress = progress
            }, 1000)
          }
        } else {
          clearTimeout(this.__progressTimeout)
          this.progress = progress
        }
      } else {
        if (this.__progressTimeout) {
          clearTimeout(this.__progressTimeout)
        }
        this.progress = progress
      }
      if (response.result.error) {
        this.resultError = response.result.error
      } else {
        this.resultError = undefined
      }
      this.error = null
      if (
        ([WidgetTypeConstants.EVENT_HISTORY].includes(
          response['secondery.category']
        ) ||
          [WidgetTypeConstants.STREAM].includes(
            response['visualization.category']
          ) ||
          [
            AvailableReportCategories.AVAILABILITY_ALERT,
            AvailableReportCategories.EVENT_HISTORY,
            AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
          ].includes(this.$attrs['report-category'])) &&
        response[this.$constants.UI_EVENT_UUID] === this.guid
      ) {
        if (this.currentBatch === 1) {
          this.data = response
          this.loadingData = false
          if (this.__resolveFn) {
            this.__resolveFn(response)
          }
        }

        this.currentBatch++

        return
      }
      const widgetContext = transformWidget(Omit(response, ['result']))
      if (!this.__rootWidgetTimeRange) {
        this.__rootWidgetTimeRange = widgetContext.timeRange
      }
      this.$emit('widget-response-received', {
        widget: widgetContext,
        response,
      })
      if (!this.isPreview) {
        this.$emit('update:widget', widgetContext)
      }
      // this is custom script report specific for key 'visualization.group.type'
      if (widgetContext.isMultiTrendReport && !this.customScriptWidgetEmitted) {
        this.$emit('custom-script-widget', widgetContext)
        this.customScriptWidgetEmitted = true
      }
      if (widgetContext.usePivotGroupBy || widgetContext.isMultiTrendReport) {
        this.$emit('update:widget', {
          ...this.widget,
          primaryResultBy: this.widget.primaryResultBy,
          usePivotGroupBy: widgetContext.usePivotGroupBy,
          enableGridView: widgetContext.enableGridView,
          isMultiTrendReport: widgetContext.isMultiTrendReport,
        })
      }

      this._responseQueue.add(async () => {
        const result = await buildWidgetResult(widgetContext, response, {
          timeRange: this.timeRange || this.__rootWidgetTimeRange,
          id: this.widget.id,
          ...this.serverParams,
          ...(this.widget?.widgetProperties?.useWidgetColumnsOnly
            ? {
                useWidgetColumnsOnly:
                  this.widget?.widgetProperties?.useWidgetColumnsOnly,
              }
            : {}),
        })
        this.data = Object.freeze(
          mergeResultsIfNeeded(widgetContext, this.data, result)
        )
        if (this.progress >= 100) {
          this.$emit('widget-result-received', {
            widget: widgetContext,
            data: this.data,
          })
        }
        if (!(result || {})._keepLoading) {
          this.loadingData = false
        }
        this.$nextTick(() => {
          if (this.isPreview) {
            this.$emit('preview-rendered', true)
          } else {
            this.$emit('data-rendered', true)
          }
          if (this.progress >= 100) {
            setTimeout(
              () => {
                this.$emit('context-received', {
                  response: response,
                  data: this.data,
                  widget: widgetContext,
                })
              },
              widgetContext.category === WidgetTypeConstants.GRID ||
                widgetContext.widgetType === WidgetTypeConstants.GRID
                ? 0
                : 1000
            )
          }
        })
      })
      if (this.__resolveFn) {
        this.__resolveFn({ data: this.data, widget: widgetContext })
      }
    },
    handleCsvExport() {
      // If loading is true, we should store the export request and execute it when loading completes
      if (this.loadingData) {
        // Set up a one-time listener to export CSV after data is loaded
        setTimeout(() => {
          this.handleCsvExport()
        }, 500)

        // Remove any existing listener to avoid duplicates

        return
      }

      this.triggerExportOnComponent()
    },
    triggerExportOnComponent() {
      Bus.$emit('grid:export-csv', this.guid)
    },
  },
}
</script>
