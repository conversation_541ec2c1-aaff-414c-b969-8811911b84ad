<template>
  <FlotoDrawer
    ref="drawerRef"
    :scrolled-content="false"
    width="65%"
    :wrap-style="{ zIndex: 998 }"
    :open="open"
    @hide="handleDrawerHide"
  >
    <template v-if="item" v-slot:title> {{ title }} </template>

    <FlotoContentLoader v-if="item" :loading="loading">
      <ObjectDrilldownEmmiter
        :status="appliedStatus"
        @monitor-sidebar="$emit('monitor-sidebar', $event)"
        @template-drilldown="$emit('template-drilldown', $event)"
      >
        <GroupProvider>
          <MonitorTypeProvider :device-types="deviceCategories">
            <ApiSocketGrid
              :widget="widgetDef"
              :columns="columns"
              :counter="selectedCounter"
              :get-fn="
                isAlertEventGauge ? getLogFlowAlertStreamGridDataApi : undefined
              "
              :category="item.widget.category"
              :grid-filters="gridFilters"
              :applied-filters="appliedFilters"
              :is-availability="isAvailability"
              :widget-category="widgetCategory"
              :policy-options="policyOptions"
              :monitor-options="monitorOptions"
              :metric-options="metricOptions"
              :source-options="sourceOptions"
              class="px-4"
              default-sort="-duration"
              @filter-change="handleFilterChange"
            />
          </MonitorTypeProvider>
        </GroupProvider>
      </ObjectDrilldownEmmiter>
    </FlotoContentLoader>
  </FlotoDrawer>
</template>

<script>
import GroupProvider from '@components/data-provider/group-provider.vue'
import ObjectDrilldownEmmiter from '@modules/inventory/components/object-drilldown-emmiter.vue'
import MonitorTypeProvider from '@components/data-provider/monitor-type-provider.vue'
import Constants from '@constants'
import { getMonitorsApi } from '@modules/settings/monitoring/monitors-api'
import { getSourceApi } from '@components/widgets/widgets-api'

import ApiSocketGrid from '@src/components/common/api-socket-grid.vue'
import {
  getAlertOptionsApi,
  getWidgetApi,
} from '@/src/components/widgets/widgets-api'
import { WidgetTypeConstants } from '@components/widgets/constants'
import { buildWidgetForTheGaugeDrilldown } from '@src/components/widgets/helper'
import { transformWidgetForServer } from '@components/widgets/translator.js'
import {
  getLogFlowAlertStreamGrid,
  COLUMNS,
} from '@modules/alert/helpers/alert-helper'
import Uniq from 'lodash/uniq'

export default {
  name: 'GaugeDrilldownDrawer',
  components: {
    ApiSocketGrid,
    GroupProvider,
    ObjectDrilldownEmmiter,
    MonitorTypeProvider,
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    item: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    return {
      isOpen: false,
      loading: true,
      widgetDef: {},
      filteredCounters: [],
      isAlertEventGauge: false,
      appliedFilters: {
        severity: [],
        types: [],
        groups: [],
        tags: [],
        monitors: [],
        metrics: [],
        alertTypes: [],
        eventSources: [],
      },
      policyOptions: [],
      monitorOptions: [],
      metricOptions: [],
      sourceOptions: [],
    }
  },
  computed: {
    deviceCategories() {
      return [
        Constants.SERVER,
        Constants.NETWORK,
        Constants.WIRELESS,
        Constants.VIRTUALIZATION,
        Constants.OTHER,
        Constants.HYPERCONVERGED_INFRASTRUCTURE,
        Constants.SDN,
        Constants.STORAGE,
      ]
    },
    gridFilters() {
      let filters

      if (this.item.drillDownObjectType) {
        filters = [
          ...(filters || []),
          {
            field: 'object_type',
            operator: 'eq',
            value: this.item.drillDownObjectType,
          },
        ]
      }

      if (this.appliedFilters?.monitors?.length) {
        filters = [
          ...(filters || []),
          {
            logic: 'or',
            filters: this.appliedFilters.monitors.map((monitor) => ({
              field: 'monitor',
              operator: 'eq',
              value: monitor,
            })),
          },
        ]
      }

      if (this.appliedFilters?.metrics?.length) {
        filters = [
          ...(filters || []),
          {
            logic: 'or',
            filters: this.appliedFilters.metrics.map((metric) => ({
              field: 'metric',
              operator: 'eq',
              value: metric,
            })),
          },
        ]
      }

      if (this.appliedFilters?.alertTypes?.length) {
        filters = [
          ...(filters || []),
          {
            logic: 'or',
            filters: this.appliedFilters.alertTypes.map((type) => ({
              field: 'alertType',
              operator: 'eq',
              value: type,
            })),
          },
        ]
      }

      if (this.appliedFilters?.severity?.length) {
        filters = [
          ...(filters || []),
          {
            field: 'severity',
            operator: 'array_contains',
            value: this.appliedFilters.severity,
          },
        ]
      }

      if (this.appliedFilters?.types?.length) {
        filters = [
          ...(filters || []),
          {
            field: 'object_type',
            operator: 'array_contains',
            value: this.appliedFilters.types,
          },
        ]
      }

      if (this.appliedFilters?.groups?.length) {
        filters = [
          ...(filters || []),
          {
            logic: 'or',
            filters: this.appliedFilters.groups.map((group) => ({
              field: 'object_groups',
              operator: 'contains',
              value: String(group),
            })),
          },
        ]
      }

      if (this.appliedFilters?.tags?.length) {
        const tagFilters = this.appliedFilters.tags
        const filtersArray = []

        if (this.isAvailability) {
          filtersArray.push({
            logic: 'or',
            filters: tagFilters.map((tag) => ({
              field: 'object_tags',
              operator: 'contains',
              value: tag,
            })),
          })
        } else if (this.widgetCategory === 'metric') {
          filtersArray.push({
            logic: 'or',
            filters: tagFilters.map((tag) => ({
              field: 'policy_tags',
              operator: 'contains',
              value: tag,
            })),
          })
        } else if (
          this.widgetCategory === 'log' ||
          this.widgetCategory === 'flow' ||
          this.widgetCategory === 'trap'
        ) {
          filtersArray.push({
            field: 'tag',
            operator: 'array_contains',
            value: tagFilters,
          })
        }

        filters = [...(filters || []), ...filtersArray]
      }

      if (this.appliedFilters?.eventSources?.length) {
        filters = [
          ...(filters || []),
          ...this.appliedFilters.eventSources.map((source) => ({
            field: 'eventSource',
            operator: 'eq',
            value: source,
          })),
        ]
      }
      // if (this.item.category) {
      //   filters = [
      //     ...(filters || []),
      //     {
      //       field: 'category',
      //       operator: 'eq',
      //       value: this.item.category,
      //     },
      //   ]
      // }

      return filters
    },
    appliedStatus() {
      return (
        this.item?.drilldownSeries?.name &&
        this.item?.drilldownSeries?.name.toUpperCase()
      )
    },
    title() {
      if (this.item?.type === 'availability') {
        return `${(this.filteredCounters?.[0]?.counter?.key || '').replace(
          '~',
          '.'
        )}: ${this.item?.drilldownSeries?.y}`
      } else if (this.item?.type === 'policy') {
        return `Severity: ${this.item?.drilldownSeries?.name}`
      }
      return ''
    },
    isInstaceCounterSelected() {
      return (this.filteredCounters?.[0]?.counter?.key || '').includes('~')
    },
    selectedCounter() {
      return this.filteredCounters?.[0]
    },
    columns() {
      if (this.item?.type === 'availability') {
        return [
          ...(this.isInstaceCounterSelected
            ? [
                {
                  key: 'instance',
                  name: 'instance',
                  searchable: true,
                  sortable: true,
                  cellRender: 'instanceDrilldown',
                },
              ]
            : []),

          {
            key: 'monitor',
            name: 'Monitor',
            searchable: true,
            sortable: true,
            cellRender: 'monitorWithSeverity',
          },
          {
            key: 'object_ip',
            name: 'Ip',
            searchable: true,
            sortable: true,
          },
          {
            key: 'object_type',
            name: 'Type',
            searchable: true,
            sortable: true,
          },
          {
            key: 'object_groups',
            name: 'Groups',
            searchable: true,
            sortable: true,
            contextKey: 'groupContext',
            searchKey: 'groupsDisplay',
            sortKey: 'groupsDisplay',
            exportComputationKey: 'split',
          },
          {
            key: 'object_tags',
            name: 'Tag',
            searchable: true,
            sortable: true,
            cellRender: 'tag_str',
          },

          {
            key: 'duration',
            name: 'Duration',
            searchable: true,
            sortable: true,
            exportType: 'duration',
          },
        ]
      } else if (this.item?.type === 'policy') {
        if (!this.isAlertEventGauge) {
          return [
            {
              key: 'policy_name',
              name: 'Policy Name',
              searchable: true,
              sortable: true,
              cellRender: 'alertDrilldown',
            },
            {
              key: 'monitor',
              name: 'Monitor',
              searchable: true,
              sortable: true,
              cellRender: 'monitorName',
            },
            {
              key: 'instance',
              name: 'instance',
              searchable: true,
              sortable: true,
            },
            {
              key: 'metric',
              name: 'metric',
              searchable: true,
              sortable: true,
            },

            {
              key: 'value',
              name: 'value',
              searchable: true,
              sortable: true,
            },

            {
              key: 'duration',
              name: 'duration',
              searchable: true,
              sortable: true,
              exportType: 'duration',
            },

            {
              key: 'policy_tags',
              name: 'Tags',
              searchable: true,
              sortable: true,
              cellRender: 'tag_str',
            },

            {
              key: 'event_timestamp',
              name: 'Last Seen',
              searchable: true,
              sortable: true,
              cellRender: 'timestemp',
              exportType: 'datetime',
            },

            // {
            //   key: 'duration',
            //   name: 'Last Seen',
            //   searchable: true,
            //   sortable: true,
            // },
          ]
        } else {
          return COLUMNS[this.widgetDef?.groups?.[0]?.category]
            .filter((c) => !['actions'].includes(c.key))
            .map((c) => ({
              ...c,
              hidden: false,
            }))
        }
      }
      return []
    },
    isAvailability() {
      return (
        this.widgetDef?.['visualization.data.sources']?.[0]?.type ===
        'availability'
      )
    },
    widgetCategory() {
      return (
        this.widgetDef?.['visualization.data.sources']?.[0]?.category ||
        this.widgetDef?.groups?.[0]?.category ||
        null
      )
    },
    searchParams() {
      return [
        this.$constants.SERVER,
        this.$constants.NETWORK,
        this.$constants.SDN,
        this.$constants.OTHER,
        this.$constants.CLOUD,
        this.$constants.VIRTUALIZATION,
        this.$constants.SERVICE_CHECK,
        this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
        this.$constants.STORAGE,
      ]
    },
  },
  watch: {
    item: {
      immediate: true,
      handler(newValue) {
        if (newValue) {
          this.isOpen = true
        }
      },
    },
    open(newValue, oldValue) {
      if (newValue) {
        this.getWidgetData()
        if (this.widgetCategory) {
          this.fetchPolicies()
          this.fetchMonitors()
          this.fetchSourceOptions()
        }
      }
    },
    widgetCategory: {
      immediate: true,
      handler(newCategory) {
        if (newCategory) {
          this.fetchPolicies()
          this.fetchMonitors()
          this.fetchSourceOptions()
        }
      },
    },
  },
  async created() {
    this.getWidgetData()
  },
  beforeDestroy() {
    this.handleDrawerHide()
  },
  methods: {
    handleFilterChange(newFilters) {
      this.appliedFilters = { ...newFilters }
    },
    handleDrawerHide() {
      this.isOpen = false
      this.$emit('hide')
      this.loading = true
      this.appliedFilters = {
        severity: [],
        types: [],
        groups: [],
        tags: [],
        monitors: [],
        metrics: [],
        alertTypes: [],
        eventSources: [],
      }
      this.policyOptions = []
      this.monitorOptions = []
      this.metricOptions = []
      this.sourceOptions = []
    },
    fetchPolicies() {
      if (this.widgetCategory) {
        getAlertOptionsApi({
          category: this.widgetCategory,
        }).then((result) => {
          let tags = []
          let metrics = []
          for (const a of result) {
            tags = tags.concat([...(a['tag'] || [])])
            if (a.metric) {
              metrics.push(a.metric)
            }
          }
          tags = Uniq(tags)
          metrics = Uniq(metrics)

          this.policyOptions = tags.map((tag) => ({
            key: tag,
            text: tag,
          }))

          this.metricOptions = metrics.map((metric) => ({
            key: metric,
            text: metric,
          }))
        })
      }
    },
    fetchMonitors() {
      if (this.widgetCategory === 'metric') {
        getMonitorsApi(
          this.searchParams
            ? {
                params: {
                  'object.category': JSON.stringify(this.searchParams),
                },
              }
            : {}
        ).then((result) => {
          this.monitorOptions = result.map((monitor) => ({
            key: monitor.name,
            text: monitor.name,
          }))
        })
      }
    },
    fetchSourceOptions() {
      if (
        this.widgetCategory?.toLowerCase() === Constants.TRAP?.toLowerCase()
      ) {
        getSourceApi('trap').then(({ result }) => {
          this.sourceOptions = Object.keys(result).map((source) => ({
            key: source,
            text: source,
          }))
        })
      }
    },
    getLogFlowAlertStreamGridDataApi() {
      const widget = this.widgetDef
      const groupCategory = widget?.groups?.[0]?.category
      const target = widget?.groups?.[0]?.target
      const severity = widget?.groups?.[0]?.severity
      const alertIds = widget?.groups?.[0]?.alertIds
      const tags = widget?.groups?.[0]?.tags

      return getLogFlowAlertStreamGrid(
        this.item?.timeRange || widget.timeline,
        groupCategory,
        {
          target,
          severity,
          alertIds,
          tags,
        }
      )
    },
    async getWidgetData() {
      if (this.item?.widget.id) {
        this.loading = true

        await getWidgetApi(this.item?.widget?.id).then((widget) => {
          const buildedContext = buildWidgetForTheGaugeDrilldown({
            ...this.item,
            widget,
          })

          let widgetDef = buildedContext.widget

          this.isAlertEventGauge = ['log', 'flow', 'trap'].includes(
            widget?.groups?.[0]?.category
          )

          this.widgetDef = {
            ...(!this.isAlertEventGauge
              ? transformWidgetForServer(widgetDef)
              : widgetDef),
            'drill.down': WidgetTypeConstants.GAUGE,
          }
          this.filteredCounters = buildedContext.filteredCounters

          this.loading = false
        })
      }
    },
  },
}
</script>
