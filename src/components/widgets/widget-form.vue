<template>
  <FlotoForm ref="formRef">
    <MModal
      :open="isModalOpen"
      centered
      width="100%"
      overlay-class-name="widget-form-modal"
    >
      <template v-slot:title>
        <div class="flex justify-between items-center">
          <h5 class="mb-0 text-ellipsis pr-6 inline-flex">
            {{
              defaultValue
                ? defaultValue.clonning
                  ? 'Clone'
                  : defaultValue.id
                  ? 'Edit'
                  : 'Create'
                : 'Create'
            }}
            Widget
          </h5>
          <div class="flex items-center">
            <!-- <div v-if="shouldShowTimeRangeSelection" class="flex flex-col mr-2">
              <span class="text-neutral text-xs">Time Range Inclusive</span>
              <div>
                <MSwitch
                  v-model="formData.timeRangeInclusive"
                  checked-children="ON"
                  un-checked-children="OFF"
                />
              </div>
            </div> -->
            <TimeRangePicker
              v-if="shouldShowTimeRangeSelection"
              v-model="formData.timeRange"
              class="mr-2"
              :hide-custom-time-range="false"
            />
            <!-- <div
                v-if="
                formData.category === WidgetTypeConstants.CHART ||
                formData.category === WidgetTypeConstants.ANOMALY ||
                formData.category === WidgetTypeConstants.FORECAST
              "
              class="inline-flex items-center relative ml-2"
              style="top: 3px"
            >
              <GranularityInput v-model="formData.granularity" />
            </div> -->
            <MButton
              variant="transparent"
              class="ml-2"
              shape="circle"
              :shadow="false"
              @click="$emit('cancel')"
            >
              <MIcon name="times" />
            </MButton>
          </div>
        </div>
      </template>

      <div class="flex flex-1 min-h-0 overflow-y-auto overflow-x-hidden">
        <div class="flex flex-col flex-1 min-h-0 min-w-0">
          <MRadioGroup
            v-if="!isFreeTextWidget"
            v-model="formData.category"
            :options="widgetCategoriesOptions"
            as-button
            :disabled="!!defaultValue.id"
          />
          <div class="flex flex-1 flex-col min-h-0 mt-2">
            <div class="flex" style="flex-shrink: 0; height: 60%">
              <div
                class="h-full flex flex-col mr-2 pt-2"
                :class="{
                  bordered: canRenderProperty,
                  'rounded-lg': canRenderProperty,
                }"
                :style="{
                  width: !canRenderProperty ? '100%' : '55%',
                }"
              >
                <Preview
                  :widget.sync="formData"
                  enable-debounce
                  hide-title
                  @error="handlePreviewError"
                  @preview-rendered="hasPreviewRendered = $event"
                  @column-received="selectedColumns = $event"
                  @group-column-received="groupByColumns = $event"
                />
              </div>
              <div
                v-if="canRenderProperty"
                class="h-full flex flex-col bordered rounded-lg pt-2"
                style="width: 45%"
              >
                <div v-if="!isFreeTextWidget" class="px-2">
                  <MRow class="flex items-center">
                    <MCol :size="6">
                      <FlotoFormItem
                        v-model="formData.name"
                        placeholder="Widget Name"
                        rules="required"
                      />
                    </MCol>
                    <MCol :size="6">
                      <FlotoFormItem
                        v-model="formData.description"
                        placeholder="Widget Description"
                      />
                    </MCol>
                  </MRow>
                </div>
                <template v-if="hasWidgetPropertyConfigurations">
                  <template v-if="hasPreviewRendered">
                    <WidgetPropertyForm
                      v-model="formData.widgetProperties"
                      :columns="selectedColumns"
                      :widget-category="formData.category"
                      :widget-type="formData.widgetType"
                      :widget.sync="formData"
                      :widget-id="formData.id"
                      :group-by-columns="groupByColumns"
                      :tag-options="tagOptions"
                      :time-range="formData.timeRange"
                      :should-show-time-range-selection="
                        shouldShowTimeRangeSelection
                      "
                      @change-widget-type="changeWidgetType"
                      @change-result-by="changeResultBy"
                    />
                  </template>
                  <div v-else class="flex items-center justify-center flex-1">
                    <h5 class="text-primary">Please Select Group</h5>
                  </div>
                </template>
              </div>
            </div>
            <div v-if="!isFreeTextWidget" class="flex flex-col flex-1">
              <WidgetGroupForm
                v-for="(group, index) in formData.groups"
                :key="`${group.type}-${index}`"
                :value="group"
                :category="formData.category"
                :widget-type="formData.widgetType"
                :counter-data-type="counterDataType"
                :widget.sync="formData"
                :timeline="formData.timeRange"
                :type="group.type"
                :is-edit-mode="!!defaultValue.id"
                @change-widget-type="changeWidgetType"
                @change="handleUpdateGroup(index, $event)"
                @remove="handleRemoveGroup(index)"
              />
              <div
                v-if="availableWidgetGroups.length"
                class="my-1 group-btn-holder flex"
              >
                <MButton
                  v-for="group in availableWidgetGroups"
                  :key="group.key"
                  outline
                  class="mr-2 add-group-btn"
                  style="width: 100px"
                  :disabled="
                    disabledGroups.indexOf(group.key) >= 0 ||
                    (addedGroupKeys.includes(group.key) &&
                      formData.category !== WidgetTypeConstants.CHART)
                      ? true
                      : formData.groups.length >= maxAllowedGroups
                  "
                  @click="addNewGroup(group)"
                  >{{ group.name }}</MButton
                >
                <!-- <MButton
                  v-if="formData.groups.length"
                  variant="default"
                  class="ml-auto add-group-btn"
                  @click="removeAllGroups"
                  >Remove All Groups</MButton
                > -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <template v-slot:footer>
        <MButton variant="default" class="mr-2" @click="resetForm(null)"
          >Reset</MButton
        >
        <WidgetJsonEditor disabled :widget="formData" @update="resetForm">
          <template v-slot="{ open: openDefinitionModal }">
            <MButton class="mr-2" variant="error" @click="openDefinitionModal"
              >Edit Widget Definition</MButton
            >
          </template>
        </WidgetJsonEditor>
        <MButton
          :outline="defaultValue && defaultValue.id ? false : true"
          :disabled="!hasPreviewRendered"
          :loading="processing"
          @click="submitForm(null)"
        >
          {{
            defaultValue
              ? defaultValue.clonning
                ? 'Clone'
                : defaultValue.id
                ? 'Update'
                : 'Create'
              : 'Create'
          }}
          Widget
        </MButton>
        <MButton
          v-if="
            (defaultValue && defaultValue.clonning) ||
            (defaultValue && !defaultValue.id)
          "
          :disabled="!hasPreviewRendered || !canCloneWidget"
          :loading="processing"
          @click="submitForm('add')"
        >
          {{
            defaultValue
              ? defaultValue.clonning
                ? 'Clone'
                : defaultValue.id
                ? 'Update'
                : 'Create'
              : 'Create'
          }}
          & Add Widget
        </MButton>
      </template>
    </MModal>
  </FlotoForm>
</template>

<script>
import Uniq from 'lodash/uniq'
import CloneDeep from 'lodash/cloneDeep'
import Bus from '@utils/emitter'
import { authComputed } from '@state/modules/auth'
import TimeRangePicker from './time-range-picker.vue'
import {
  WidgetTypeConstants,
  AvailableWidgetCategories,
  AVAILABLE_GROUPS,
} from './constants'
import {
  getRange,
  getDefaultDataForGroup,
  getWidgetProperties,
  canRenderWidgetPreview,
  overrideWidgetPropertyByWidgetCategory,
  PREMIUM_WIDGET_CATEGORIES,
  getWidgetGroupByChartType,
  POLICY_GRID_DEFAULT_COLUMN_SETTINGS,
} from './helper'
import WidgetJsonEditor from './widget-json-editor.vue'
import WidgetGroupForm from './widget-group-form/widget-group-form.vue'
import WidgetPropertyForm from './widget-property-form/widget-property-form.vue'
import Preview from './preview.vue'
import { createWidgetApi, updateWidgetApi } from './widgets-api'
// import GranularityInput from './granularity-input.vue'

export default {
  name: 'WidgetForm',
  components: {
    TimeRangePicker,
    WidgetJsonEditor,
    WidgetGroupForm,
    WidgetPropertyForm,
    Preview,
    // GranularityInput,
  },
  provide() {
    const widgetFormContext = {
      setGranularity: this.setGranularity,
      setSelectedTags: this.setSelectedTags,
      setEventCounts: this.setEventCounts,
    }
    Object.defineProperty(widgetFormContext, 'formData', {
      enumerable: true,
      get: () => {
        return this.formData
      },
    })
    return { widgetFormContext }
  },
  props: {
    defaultValue: {
      type: Object,
      default: undefined,
    },
    canCloneWidget: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      processing: false,
      hasPreviewRendered: false,
      selectedColumns: [],
      groupByColumns: [],
      isModalOpen: true,
      formData: CloneDeep(
        this.defaultValue || {
          timeRangeInclusive: false,
          category: WidgetTypeConstants.CHART,
          widgetType: WidgetTypeConstants.AREA,
          granularity: {
            value: 5,
            unit: 'm',
          },
          timeRange: {
            selectedKey: 'today',
            ...getRange('today'),
          },
          groups: CloneDeep(
            getWidgetGroupByChartType(WidgetTypeConstants.CHART)
          ),
          widgetProperties: getWidgetProperties(WidgetTypeConstants.CHART),
        }
      ),
      tagOptions: [],
    }
  },
  computed: {
    ...authComputed,
    WidgetTypeConstants() {
      return WidgetTypeConstants
    },
    shouldShowTimeRangeSelection() {
      if (this.isFreeTextWidget) {
        return false
      }
      if (
        [
          WidgetTypeConstants.HEATMAP,
          WidgetTypeConstants.ACTIVE_ALERT,
        ].includes(this.formData.category)
      ) {
        return false
      }
      if (this.formData.category === WidgetTypeConstants.GAUGE) {
        const addedGroupKeys = this.addedGroupKeys
        if (
          addedGroupKeys.includes('alert') ||
          addedGroupKeys.includes('policy') ||
          addedGroupKeys.includes('availability')
        ) {
          return false
        }
      }
      return true
    },
    canRenderProperty() {
      const widget = this.formData
      if (this.isFreeTextWidget) {
        return true
      }
      return canRenderWidgetPreview(widget)
    },
    hasWidgetPropertyConfigurations() {
      return [
        WidgetTypeConstants.CHART,
        WidgetTypeConstants.GRID,
        WidgetTypeConstants.TOPN,
        WidgetTypeConstants.GAUGE,
        WidgetTypeConstants.STREAM,
        WidgetTypeConstants.MAP_VIEW,
        WidgetTypeConstants.HEATMAP,
        WidgetTypeConstants.FORECAST,
        WidgetTypeConstants.ANOMALY,
        WidgetTypeConstants.SANKEY,
        WidgetTypeConstants.ACTIVE_ALERT,
        WidgetTypeConstants.EVENT_HISTORY,
        WidgetTypeConstants.FREE_TEXT,
      ].includes(this.formData.category)
    },
    counterDataType() {
      if (
        [WidgetTypeConstants.HEATMAP].includes(this.formData.category) &&
        this.formData?.groups?.[0]?.type === 'metric'
      ) {
        return ['numeric']
      }
      if (
        [
          WidgetTypeConstants.HEATMAP,
          WidgetTypeConstants.ACTIVE_ALERT,
        ].includes(this.formData.category)
      ) {
        return ['string']
      }
      if (
        this.formData.category === WidgetTypeConstants.GRID ||
        this.formData.widgetType === WidgetTypeConstants.GRID
      ) {
        return ['string', 'numeric']
      }
      return ['numeric']
    },
    maxAllowedGroups() {
      if (this.formData.category === WidgetTypeConstants.CHART) {
        return 2
      }
      return 1
    },
    addedGroupKeys() {
      return (this.formData.groups || []).map((g) => g.type)
    },
    availableWidgetGroups() {
      const widgetCategory = this.formData.category
      let availableGroups = CloneDeep(AVAILABLE_GROUPS)
      if (
        [WidgetTypeConstants.FORECAST, WidgetTypeConstants.ANOMALY].includes(
          widgetCategory
        )
      ) {
        availableGroups = availableGroups.filter((g) => g.key === 'metric')
      }
      if (
        [
          WidgetTypeConstants.CHART,
          WidgetTypeConstants.GRID,
          WidgetTypeConstants.GAUGE,
          WidgetTypeConstants.TOPN,
          WidgetTypeConstants.HEATMAP,
          WidgetTypeConstants.ACTIVE_ALERT,
          WidgetTypeConstants.STREAM,
          WidgetTypeConstants.MAP_VIEW,
        ].indexOf(widgetCategory) === -1
      ) {
        availableGroups = availableGroups.filter(
          (f) => f.key !== 'availability'
        )
      }
      if (widgetCategory === WidgetTypeConstants.HEATMAP) {
        availableGroups = availableGroups.filter(
          (f) =>
            ['metric', 'alert', 'policy', 'availability'].indexOf(f.key) >= 0
        )
      }
      if (widgetCategory === WidgetTypeConstants.ACTIVE_ALERT) {
        availableGroups = availableGroups.filter(
          (f) => ['alert', 'policy', 'availability'].indexOf(f.key) >= 0
        )
      }
      if (widgetCategory === WidgetTypeConstants.SANKEY) {
        availableGroups = availableGroups.filter(
          (f) => ['flow'].indexOf(f.key) >= 0
        )
      }
      if (widgetCategory === WidgetTypeConstants.STREAM) {
        availableGroups = availableGroups.filter(
          (f) => ['alert', 'policy'].indexOf(f.key) >= 0
        )
      }
      if (widgetCategory === WidgetTypeConstants.MAP_VIEW) {
        availableGroups = availableGroups.filter(
          (f) => ['flow', 'log'].indexOf(f.key) >= 0
        )
      }
      if (widgetCategory === WidgetTypeConstants.EVENT_HISTORY) {
        availableGroups = availableGroups.filter(
          (f) => ['log'].indexOf(f.key) >= 0
        )
      }

      if (
        !this.hasLicensePermission(this.$constants.LOG_FLOW_LICENSE_PERMISSION)
      ) {
        availableGroups = availableGroups.filter(
          (f) => !['flow', 'log'].includes(f.key)
        )
      }

      if (widgetCategory === WidgetTypeConstants.FREE_TEXT) {
        return []
      }

      return availableGroups
    },
    disabledGroups() {
      const groupKeys = this.addedGroupKeys
      const category = this.formData.category
      let disabledGroups = []
      // disable availability if chart and has other group
      if (category === WidgetTypeConstants.CHART) {
        if (groupKeys.includes('availability')) {
          disabledGroups = [
            ...disabledGroups,
            ...AVAILABLE_GROUPS.map((g) => g.key),
          ]
        } else if (groupKeys.length > 0) {
          disabledGroups = [...disabledGroups, 'availability']
        }
      }

      return Uniq(disabledGroups)
    },
    widgetCategoriesOptions() {
      let widgetCategories = Object.keys(AvailableWidgetCategories).map(
        (key) => ({
          value: key,
          text: AvailableWidgetCategories[key],
        })
      )

      if (
        !this.hasLicensePermission(
          this.$constants.USE_PREMIUM_WIDGET_CATEGORIES
        )
      ) {
        widgetCategories = widgetCategories.filter(
          (type) => !PREMIUM_WIDGET_CATEGORIES.includes(type.value)
        )
      }

      if (
        !this.hasLicensePermission(this.$constants.ANOMALY_LICENSE_PERMISSION)
      ) {
        widgetCategories = widgetCategories.filter(
          (type) => ![WidgetTypeConstants.ANOMALY].includes(type.value)
        )
      }
      if (
        !this.hasLicensePermission(this.$constants.FORECAST_LICENSE_PERMISSION)
      ) {
        widgetCategories = widgetCategories.filter(
          (type) => ![WidgetTypeConstants.FORECAST].includes(type.value)
        )
      }
      return widgetCategories
    },
    isFreeTextWidget() {
      return this.formData?.category === WidgetTypeConstants.FREE_TEXT
    },
  },
  watch: {
    'formData.category': {
      handler(newValue, oldValue) {
        if (oldValue && newValue !== oldValue) {
          this.formData = {
            ...this.formData,
            ...(newValue === WidgetTypeConstants.CHART
              ? {
                  granularity: {
                    value: 5,
                    unit: 'm',
                  },
                }
              : {}),
            widgetType:
              newValue === WidgetTypeConstants.CHART
                ? WidgetTypeConstants.AREA
                : newValue === WidgetTypeConstants.TOPN
                ? WidgetTypeConstants.AREA
                : newValue === WidgetTypeConstants.GAUGE
                ? WidgetTypeConstants.METRO_TILE
                : newValue === WidgetTypeConstants.MAP_VIEW
                ? WidgetTypeConstants.MAP_VIEW
                : newValue === WidgetTypeConstants.STREAM
                ? WidgetTypeConstants.GRID
                : newValue === WidgetTypeConstants.HEATMAP
                ? WidgetTypeConstants.HEATMAP_PLAIN
                : newValue === WidgetTypeConstants.EVENT_HISTORY
                ? WidgetTypeConstants.GRID
                : undefined,
            groups: CloneDeep(
              getWidgetGroupByChartType(newValue, oldValue, this.formData)
            ),
            widgetProperties: getWidgetProperties(
              newValue,
              overrideWidgetPropertyByWidgetCategory(newValue)
            ),
            visualizationTags: undefined,
            useWidgetTime: false,
            eventCount: [WidgetTypeConstants.EVENT_HISTORY].includes(newValue)
              ? 500
              : undefined,
          }
          this.hasPreviewRendered = false
          setTimeout(() => {
            Bus.$emit('widget.generate.preview')
          }, 0)
        }
      },
    },
    'formData.groups': {
      handler(newValue, oldValue) {
        if (newValue && newValue.length === 0) {
          if (!this.isFreeTextWidget) {
            this.hasPreviewRendered = false
          }
        }
      },
    },
    'formData.timeRangeInclusive': {
      handler() {
        Bus.$emit('widget.generate.preview')
      },
    },
  },
  created() {
    const handler = (tags) => {
      this.tagOptions = tags
    }
    Bus.$on('tag-rendered', handler)

    Bus.$on(
      'update:widget-column-settings',
      this.updateWidgetPropertiesColumnSettings
    )
    this.$once('hook:beforeDestroy', () => {
      Bus.$off('tag-rendered', handler)
      Bus.$off(
        'update:widget-column-settings',
        this.updateWidgetPropertiesColumnSettings
      )
    })
  },
  methods: {
    setGranularity(newGranularity) {
      this.formData.granularity = newGranularity
    },

    setSelectedTags(visualizationTags) {
      this.formData = {
        ...this.formData,
        visualizationTags,
      }
      Bus.$emit('widget.generate.preview')
    },
    setEventCounts(eventCount) {
      this.formData = {
        ...this.formData,
        eventCount,
      }
      Bus.$emit('widget.generate.preview')
    },
    handlePreviewError(error) {
      this.error = error
      this.hasPreviewRendered = false
    },
    addNewGroup(group) {
      let formData = this.formData
      if (formData.groups[group.key]) {
        return
      }
      let groups = [
        ...formData.groups,
        getDefaultDataForGroup(group.key, formData),
      ]
      // if chart and availability group then exclude all other
      if (formData.category === WidgetTypeConstants.CHART) {
        if ('availability' in formData.groups) {
          groups = groups.filter((g) => g.type !== 'availability')
        } else if (group.key === 'availability') {
          groups = groups.filter((g) => g.type === 'availability')
        }
      }
      if (
        formData.category === WidgetTypeConstants.GAUGE &&
        ['alert', 'log', 'flow', 'policy'].includes(group.key)
      ) {
        formData = {
          ...formData,
          widgetType: WidgetTypeConstants.METRO_TILE,
        }
      }
      if (
        formData.category === WidgetTypeConstants.HEATMAP &&
        (group.key === 'alert' ||
          group.key === 'policy' ||
          group.key === 'metric')
      ) {
        formData = {
          ...formData,
          resultBy: 'Monitor',
        }
      }
      if (
        formData.category === WidgetTypeConstants.STREAM &&
        ['alert', 'policy'].includes(group.key)
      ) {
        formData = {
          ...formData,
          widgetType: WidgetTypeConstants.GRID,
        }
      }
      this.formData = {
        ...formData,
        groups,
        widgetProperties: getWidgetProperties(
          formData.category,
          formData.category === WidgetTypeConstants.CHART
            ? this.formData.widgetProperties
            : (formData.category === WidgetTypeConstants.GRID ||
                (formData.category === WidgetTypeConstants.TOPN &&
                  formData.widgetType === WidgetTypeConstants.GRID)) &&
              group.key === 'policy'
            ? {
                ...formData.widgetProperties,
                columnSettings: POLICY_GRID_DEFAULT_COLUMN_SETTINGS,
              }
            : {}
        ),
        visualizationTags: undefined,
      }
    },
    handleUpdateGroup(index, patch) {
      if (index !== -1) {
        this.formData = {
          ...this.formData,
          groups: [
            ...this.formData.groups.slice(0, index),
            { ...patch },
            ...this.formData.groups.slice(index + 1),
          ],

          ...(this.formData?.groups?.[0]?.counters?.[0]?.counter?.key !==
          patch?.counters?.[0]?.counter?.key
            ? { visualizationTags: undefined }
            : {}),
        }
      }

      if (
        this.formData.category === WidgetTypeConstants.TOPN &&
        !this.formData.groups?.[0]?.counters?.[0]?.counter &&
        patch.type !== 'policy'
      ) {
        this.resetTopnProperties()
      }
    },
    handleRemoveGroup(index) {
      const formData = this.formData
      this.formData = {
        ...formData,
        groups: [
          ...formData.groups.slice(0, index),
          ...formData.groups.slice(index + 1),
        ],

        ...(this.formData.category === WidgetTypeConstants.GAUGE &&
        this.addedGroupKeys[index] === 'availability'
          ? {
              widgetProperties: {
                ...this.formData.widgetProperties,
                layout: undefined,
              },
            }
          : {}),

        ...([
          WidgetTypeConstants.TREE_VIEW,
          WidgetTypeConstants.PACKED_BUBBLE_CHART,
        ].includes(this.formData.widgetType) &&
        this.formData.category === WidgetTypeConstants.TOPN
          ? {
              widgetType: WidgetTypeConstants.AREA,
            }
          : {}),
        useWidgetTime: false,
      }
    },
    // removeAllGroups() {
    //   this.formData = {
    //     ...this.formData,
    //     groups: [],
    //   }
    // },
    resetForm(newValue) {
      this.formData = CloneDeep(
        newValue ||
          this.defaultValue || {
            timeRangeInclusive: false,
            category: WidgetTypeConstants.CHART,
            widgetType: WidgetTypeConstants.AREA,
            timeRange: {
              selectedKey: 'today',
              ...getRange('today'),
            },
            groups: [],
            widgetProperties: {},
          }
      )
    },
    submitForm(command) {
      this.$refs.formRef
        .validate()
        .then((result) => {
          this.processing = true
          if (result) {
            return this.formData.id
              ? updateWidgetApi(this.formData)
              : createWidgetApi(this.formData)
          }
        })
        .then((response) => {
          this.isModalOpen = false
          setTimeout(() => {
            if (command === 'add') {
              this.$emit('add', response)
            }
            if (this.formData.id) {
              Bus.$emit('widget.refresh', this.formData.id)
            }

            this.$emit(this.formData.id ? 'updated' : 'created', response)
          }, 400)
        })
        .finally(() => {
          this.processing = false
        })
    },
    changeResultBy(resultBy) {
      if (this.formData.category === WidgetTypeConstants.SANKEY) {
        this.formData = {
          ...this.formData,
          groups: [
            {
              ...(this.formData?.groups[0] || {}),
              resultBy,
            },
          ],
        }
        Bus.$emit('widget.generate.preview')
      }
    },
    changeWidgetType(widgetType) {
      this.formData = {
        ...this.formData,
        widgetType,
        visualizationTags: undefined,

        ...([
          WidgetTypeConstants.TREE_VIEW,
          WidgetTypeConstants.PACKED_BUBBLE_CHART,
        ].includes(widgetType) &&
        this.formData.category === WidgetTypeConstants.TOPN
          ? {
              widgetProperties: {
                ...(this.formData?.widgetProperties || {}),
                sortingSetting: {
                  ...(this.formData?.widgetProperties?.sortingSetting || {}),
                  showSparklineChart: false,
                },
              },
            }
          : {}),
      }
    },
    resetTopnProperties() {
      this.formData = {
        ...this.formData,
        widgetProperties: {
          ...(this.formData?.widgetProperties || {}),
          sortingSetting: {
            ...(this.formData?.widgetProperties?.sortingSetting || {}),
            column: undefined,
          },
        },
      }
    },
    updateWidgetPropertiesColumnSettings(columnSettings) {
      this.formData = {
        ...this.formData,
        widgetProperties: {
          ...this.formData.widgetProperties,
          columnSettings: columnSettings,
        },
      }
      Bus.$emit('widget.generate.preview')
    },
  },
}
</script>
