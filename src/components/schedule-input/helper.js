import Range from 'lodash/range'
import Moment from 'moment'

const WEEK_DAYS = [
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday',
]

export const WEEK_DAYS_OPTIONS = WEEK_DAYS.map((day) => ({
  key: day,
  text: day,
}))

export const MONTH_DAYS = Range(1, 32).map((d) => ({
  key: d,
  text: String(d),
  id: String(d),
}))

export const MONTH_OPTIONS = Range(1, 13)
  .map((m) => Moment(m, 'M').format('MMMM'))
  .map((d) => ({ key: d, text: d }))
