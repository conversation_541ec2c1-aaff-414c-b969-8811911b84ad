<template>
  <div class="flex items-center">
    <div class="-mx-2 flex items-center flex-1">
      <div class="flex-1 mx-2 min-w-0" style="flex-shrink: 0">
        <FlotoFormItem
          rules="required"
          validation-label="Counter"
          :label="itemIndex.startsWith('0') ? 'Counter' : ''"
          :vid="`${itemIndex}-counter`"
        >
          <FlotoDropdownPicker
            v-model="operand"
            :options="operandOptions"
            :disabled-options="disabledOptions"
            placeholder="Select Counter"
            @change="$emit('operand-change', $event)"
          />
        </FlotoFormItem>
      </div>
      <div class="flex-1 mx-2 min-w-0" style="flex-shrink: 0">
        <FlotoFormItem
          rules="required"
          validation-label="Operator"
          :label="itemIndex.startsWith('0') ? 'Select Operator' : ''"
          :vid="`${itemIndex}-operator`"
        >
          <FlotoDropdownPicker
            v-model="operator"
            :options="operatorOptions"
            placeholder="Select Operator"
          />
        </FlotoFormItem>
      </div>
      <div class="flex-1 mx-2 min-w-0" style="flex-shrink: 0">
        <div
          v-if="operator === 'between'"
          class="flex items-center justify-around"
        >
          <FlotoFormItem
            v-model="value"
            rules="required"
            placeholder="From"
            validation-label="From Value"
            class="mr-1"
            :vid="`${itemIndex} from value`"
          />
          <FlotoFormItem
            v-model="toValue"
            rules="required"
            placeholder="To"
            validation-label="To Value"
            :vid="`${itemIndex} to value`"
          />
        </div>
        <FlotoFormItem
          v-else-if="operand === 'Group'"
          rules="required"
          :vid="`${itemIndex}-group`"
        >
          <GroupPicker
            v-model="value"
            multiple
            placeholder="Select Value"
            allow-clear
            placement="bottomLeft"
          />
        </FlotoFormItem>
        <!-- <FlotoFormItem v-else-if="operand === 'source.type'" rules="required">
          <MonitorTypePicker
            v-model="value"
            multiple
            as-input
            placeholder="Select Value"
            placement="bottomLeft"
          />
        </FlotoFormItem>-->
        <FlotoFormItem
          v-else-if="shouldUseDropdownForValue"
          rules="required"
          validation-label="Value"
          class="mt-4"
          :vid="`${itemIndex}-value`"
        >
          <FlotoDropdownPicker
            id="instance-counter-id"
            v-model="value"
            allow-clear
            :options="possibleValueOptions"
            :multiple="shouldUseMultipleValueForDropdown"
          >
            <!-- <template
              v-if="operand === 'source.type'"
              v-slot:before-menu-text="{
                item: optionItem,
              }"
            >
              <MonitorType
                :type="optionItem.key"
                class="mx-1 inline-flex"
                width="20px"
                disable-tooltip
              />
            </template>-->
          </FlotoDropdownPicker>
        </FlotoFormItem>
        <FlotoFormItem
          v-else
          v-model="value"
          class="mt-4"
          validation-label="Value"
          :placeholder="
            useAnyFieldType && operandFieldTypeMap[operand] === 'any'
              ? `Any`
              : `Value ${operator === 'in' ? ' (comma separated)' : ''}`
          "
          :rules="{
            required: useAnyFieldType
              ? operandFieldTypeMap[operand] === 'any'
                ? false
                : true
              : true,
            floatNumber: operand && operandDataTypeMap[operand] === 'Numeric',
          }"
          :vid="`${itemIndex}-value`"
        >
          <template v-if="thresholdOrBaseline === 'baseline'" v-slot:suffix>
            <span class="per-input-box">%</span>
          </template>
        </FlotoFormItem>
      </div>
      <div style="width: 120px" class="mb-3">
        <InstanceGrid
          v-if="
            false &&
            useInstanceGrid &&
            shouldUseMultipleValueForDropdown &&
            instanceGridKeyField === operand
          "
          v-model="value"
          class="inline-flex"
          :operand="operand"
          :key-field="instanceGridKeyField"
          :data="possibleColumnOptions"
        >
          <template v-slot:trigger="{ open }">
            <span class="mr-2 cursor-pointer" @click="open">
              <MIcon name="dashboard" />
            </span>
          </template>
        </InstanceGrid>
        <span v-if="canRemove" id="remove-condition" @click="$emit('remove')">
          <MIcon
            name="times-circle"
            class="cursor-pointer text-secondary-red"
            size="lg"
          />
        </span>
        <span
          v-if="canAdd"
          id="add-condition"
          class="ml-2"
          outline
          @click="$emit('add')"
        >
          <MIcon
            name="plus-circle"
            class="text-primary cursor-pointer add-remove-btn-size"
            size="lg"
          />
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import Trim from 'lodash/trim'
import Uniq from 'lodash/uniq'
import UniqBy from 'lodash/uniqBy'

// import MonitorType from '@components/monitor-type.vue'
import { getOperatorOptions } from '../widgets/helper'
import { DATA_TYPE_OPERATORS } from '../widgets/constants'
import InstanceGrid from './instance-grid.vue'
import { getCounterUniqueValues } from '../widgets/widgets-api'

const EXCLUDED_OPERANDS = ['message']

export default {
  name: 'FilterCondition',
  components: {
    InstanceGrid,
    // MonitorType,
  },
  inheritAttrs: false,
  model: { event: 'change', prop: 'condition' },
  props: {
    groupType: {
      type: String,
      default: undefined,
    },
    timeline: {
      type: Object,
      default: undefined,
    },
    condition: {
      type: Object,
      default: undefined,
    },
    operandOptions: {
      type: Array,
      default() {
        return []
      },
    },
    possibleColumnOptions: {
      type: Object,
      default() {
        return {
          data: [],
          columns: [],
        }
      },
    },
    operandDataTypeMap: {
      type: Object,
      default() {
        return {}
      },
    },
    operandFieldTypeMap: {
      type: Object,
      default() {
        return {}
      },
    },
    canAdd: {
      type: Boolean,
      default: false,
    },
    canRemove: {
      type: Boolean,
      default: false,
    },
    isPostFilter: {
      type: Boolean,
      default: false,
    },
    useInstanceGrid: {
      type: Boolean,
      default: false,
    },
    instanceGridKeyField: {
      type: String,
      default: undefined,
    },
    extractedValueOptions: {
      type: Object,
      default() {
        return {}
      },
    },
    useAnyFieldType: {
      type: Boolean,
      default: false,
    },
    selectedUniqueCounterKeys: {
      type: Array,
      default() {
        return []
      },
    },
    itemIndex: {
      type: String,
      required: true,
    },
    target: {
      type: Object,
      default() {
        return {}
      },
    },
    ignoreFetchAvailableUniqueValues: {
      type: Boolean,
      default: false,
    },
    hardCodedOperatorOptions: {
      type: Array,
      default: undefined,
    },
  },

  data() {
    this.dropdownEqualitiyOptions = getOperatorOptions([
      '=',
      'in',
      'contain',
      'start with',
      'end with',
    ])
    this.dropdownNumericEqualitiyOptions = getOperatorOptions([
      '=',
      'in',
      // 'between',
      '>',
      '<',
      '>=',
      '<=',
    ])
    return {
      instanceSelectedValues: [],
      availableUniqueValues: [],
    }
  },
  computed: {
    shouldUseDropdownForValue() {
      if (this.isPostFilter) {
        return false
      } else if (
        this.extractedValueOptions[this.operand] &&
        ['=', 'in', '!=', 'not in'].includes(this.operator)
      ) {
        return true
      } else if (this.possibleValueOptions.length > 0) {
        return ['=', '!=', 'in', 'not in'].indexOf(this.operator) >= 0
      }
      return false
    },
    shouldUseMultipleValueForDropdown() {
      if (this.isPostFilter) {
        return false
      }
      return ['in', 'not in'].indexOf(this.operator) >= 0
    },
    possibleValueOptions() {
      if (this.isPostFilter) {
        return []
      }
      if (!this.operand) {
        return []
      }
      if (['metric', 'log', 'flow'].includes(this.groupType)) {
        return this.availableUniqueValues
      }
      const operand = this.operand.replace(/[~^]/g, '.')
      if (this.extractedValueOptions[operand]) {
        return this.extractedValueOptions[operand].filter((i) =>
          Boolean(Trim(i.key))
        )
      }
      return Uniq(this.possibleColumnOptions.data.map((i) => i[operand]))
        .map((i) => ({
          key: i,
          text: i,
        }))
        .filter((d) => Boolean(d.key !== undefined) && Boolean(Trim(d.key)))
    },
    thresholdOrBaseline() {
      return (this.condition || {}).thresholdOrBaseline
    },
    operatorOptions() {
      if (this.hardCodedOperatorOptions) {
        return this.hardCodedOperatorOptions
      }
      if (DATA_TYPE_OPERATORS[this.operand]) {
        return DATA_TYPE_OPERATORS[this.operand]
      }
      if (this.extractedValueOptions[this.operand]) {
        if ((this.operandDataTypeMap[this.operand] || []).includes('numeric')) {
          return this.dropdownNumericEqualitiyOptions
        } else {
          return this.dropdownEqualitiyOptions
        }
      }
      if (this.operandDataTypeMap[this.operand]) {
        return this.operand
          ? DATA_TYPE_OPERATORS[
              this.operandDataTypeMap[this.operand].length > 1
                ? 'all'
                : this.operandDataTypeMap[this.operand][0]
            ] || []
          : []
      }
      return []
    },
    operand: {
      get() {
        return (this.condition || {}).operand
      },
      set(operand) {
        this.$emit('change', {
          ...(this.condition || {}),
          operand,
          dataType: this.operandDataTypeMap[operand],
          operator: undefined,
          value: undefined,
          toValue: undefined,
        })
        if (['metric', 'log', 'flow'].includes(this.groupType) && operand) {
          this.$nextTick(() => {
            this.getPossibleValues()
          })
        }
      },
    },
    operator: {
      get() {
        return (this.condition || {}).operator
      },
      set(operator) {
        this.$emit('change', {
          ...(this.condition || {}),
          operator,
          value: undefined,
          toValue: undefined,
        })
      },
    },
    value: {
      get() {
        return (this.condition || {}).value
      },
      set(value) {
        this.$emit('change', { ...(this.condition || {}), value })
      },
    },
    toValue: {
      get() {
        return (this.condition || {}).toValue
      },
      set(toValue) {
        this.$emit('change', { ...(this.condition || {}), toValue })
      },
    },
    disabledOptions() {
      if (this.selectedUniqueCounterKeys.length >= 3) {
        return (
          this.operandOptions.filter(
            (oprand) => !this.selectedUniqueCounterKeys.includes(oprand.key)
          ) || []
        ).map((o) => o.key)
      }

      return []
    },
  },
  mounted() {
    if (['metric', 'log', 'flow'].includes(this.groupType) && this.operand) {
      this.getPossibleValues()
    }
  },
  methods: {
    getPossibleValues() {
      if (this.ignoreFetchAvailableUniqueValues) {
        return
      }
      this.availableUniqueValues = []
      if (EXCLUDED_OPERANDS.includes(this.operand)) {
        return
      }
      getCounterUniqueValues(
        this.groupType,
        this.operand,
        this.timeline,
        this.groupType === 'metric' ? 'last' : 'count',
        this.target
      ).then((data) => {
        this.availableUniqueValues = Object.freeze(UniqBy(data, 'key'))
      })
    },
  },
}
</script>
