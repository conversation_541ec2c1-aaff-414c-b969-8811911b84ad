<template>
  <MonitorTypeProvider
    :device-types="[
      $constants.SERVER,
      $constants.NETWORK,
      $constants.SDN,
      $constants.WIRELESS,
      $constants.VIRTUALIZATION,
      $constants.CLOUD,
      $constants.SERVICE_CHECK,
      $constants.OTHER,
      $constants.HYPERCONVERGED_INFRASTRUCTURE,
    ]"
  >
    <MCol class="slide-filters">
      <MRow class="px-4" style="width: 100%">
        <!-- Commented as per requirements in the ticket MOTADATA-5561 -->
        <!-- <MCol :size="4">
          <FlotoFormItem label="Monitors">
            <FlotoDropdownPicker
              id="used-count-monitor-picker"
              v-model="currentValue.monitorName"
              allow-clear
              :options="monitorOptions"
              multiple
              searchable
            />
          </FlotoFormItem>
        </MCol> -->
        <MCol :size="3">
          <FlotoFormItem label="Groups">
            <GroupPicker
              id="filter-group-picker"
              v-model="currentValue.groups"
              class="w-full"
              multiple
              allow-clear
            />
          </FlotoFormItem>
        </MCol>
        <!-- <MCol v-if="type === 'monitor'" :size="3">
          <FlotoFormItem label="Severity">
            <SeverityPicker
              v-model="currentValue.severity"
              multiple
              class="w-full"
              allow-clear
              :disabled-options="defaultDisabledSeverities"
            />
          </FlotoFormItem>
        </MCol> -->
        <!-- <MCol :size="2">
          <FlotoFormItem label="Tags">
            <FlotoDropdownPicker
              id="tag"
              v-model="currentValue.tags"
              class="w-full"
              :options="tagsOptions"
              multiple
            />
          </FlotoFormItem>
        </MCol> -->
        <MCol v-if="type !== 'Event Source'" :size="3">
          <FlotoFormItem label="Types">
            <MonitorTypePicker
              id="used-count-type-picker"
              v-model="currentValue.types"
              allow-clear
              class="w-full"
              multiple
            />
          </FlotoFormItem>
        </MCol>
        <MCol
          class="flex justify-between ml-auto"
          style="padding-left: 0"
          :size="2"
        >
          <div class="flex-1 items-center mt-2 flex justify-center">
            <MButton
              id="used-count-reset-btn"
              variant="default"
              @click="
                $emit('change', {
                  monitorName: [],
                  types: [],
                  groups: [],
                  severity: [],
                })
              "
            >
              Reset
            </MButton>
            <MButton id="used-count-apply-btn" class="ml-2" @click="apply"
              >Apply</MButton
            >
          </div>
        </MCol>
        <MButton
          id="close-used-count"
          variant="transparent"
          :shadow="false"
          shape="circle"
          style="position: absolute; top: 0; right: 0"
          class="monitor-agent-filter-close"
          @click="$emit('hide')"
        >
          <MIcon name="times" class="text-neutral-light" />
        </MButton>
      </MRow>
    </MCol>
  </MonitorTypeProvider>
</template>

<script>
import MonitorTypePicker from '@components/data-picker/monitor-type-picker.vue'
import MonitorTypeProvider from '@components/data-provider/monitor-type-provider.vue'
// import SeverityPicker from '@components/severity-picker.vue'

export default {
  name: 'MonitorCountFilter',
  components: {
    MonitorTypePicker,
    MonitorTypeProvider,
    // SeverityPicker,
  },
  model: {
    event: 'change',
  },
  props: {
    value: { type: Object, required: true },
    monitorOptions: { type: Array, required: true },
    type: { type: String },
  },
  data() {
    return {
      currentValue: { ...this.value },
    }
  },
  watch: {
    value(newValue) {
      this.currentValue = { ...newValue }
    },
  },
  methods: {
    apply() {
      this.$emit('change', this.currentValue)
    },
  },
}
</script>
