<template>
  <div class="flex flex-1 min-h-0 flex-col">
    <MRow :gutter="0" class="mt-2">
      <MCol v-if="isSearchable">
        <MInput
          v-model="searchTerm"
          class="search-box"
          placeholder="Search"
          name="used-count-search"
        >
          <template v-slot:prefix>
            <MIcon name="search" />
          </template>
          <template v-if="searchTerm" v-slot:suffix>
            <MIcon
              name="times-circle"
              class="text-neutral-light cursor-pointer"
              @click="searchTerm = undefined"
            />
          </template>
        </MInput>
      </MCol>
      <MCol class="text-right">
        <template v-if="allowExport">
          <MButton
            :shadow="false"
            class="squared-button mr-2"
            :rounded="false"
            title="Export As PDF"
            variant="neutral-lightest"
            @click="exportGrid('pdf')"
          >
            <MIcon name="export-pdf" />
          </MButton>
          <MButton
            :shadow="false"
            class="squared-button mr-2"
            :rounded="false"
            variant="neutral-lightest"
            title="Export As CSV"
            @click="exportGrid('csv')"
          >
            <MIcon name="export-csv" />
          </MButton>
        </template>

        <MButton
          v-if="
            ['monitor', 'Configuration', 'agent', 'Event Source'].includes(type)
          "
          id="used-count-filter-btn"
          class="squared-button"
          :shadow="false"
          :rounded="false"
          :variant="showFilters ? 'neutral-lighter' : 'neutral-lightest'"
          @click="showFilters = !showFilters"
        >
          <MIcon name="filter" />
        </MButton>

        <MButton
          v-if="selectedItems.length > 1 && useCustomResourceKey"
          id="filter-btn"
          :shadow="false"
          :rounded="false"
          :variant="showTagForm ? 'neutral-lighter' : 'neutral-lightest'"
          class="squared-button"
          @click="showTagForm = !showTagForm"
        >
          <MIcon name="tag" />
        </MButton>
      </MCol>

      <MCol :size="12" :class="{ 'mt-4': showFilters }">
        <MonitorCountFilter
          v-if="
            showFilters &&
            ['monitor', 'Configuration', 'agent', 'Event Source'].includes(type)
          "
          v-model="appliedFilters"
          :type="type"
          :monitor-options="monitorOptions"
          @change="applyFilter"
          @hide="showFilters = !showFilters"
        />
      </MCol>

      <MCol :size="12" :class="{ 'mt-4': showTagForm }">
        <BulkAddTagFrom
          v-if="showTagForm"
          for-instance-tagging
          :processing="tagProcessing"
          @save="bulksaveTag"
          @hide="showTagForm = !showTagForm"
        />
      </MCol>
    </MRow>
    <ObjectTagProvider
      :counter="{
        key: '~',
      }"
    >
      <div class="flex flex-col min-h-0 flex-1">
        <MGrid
          v-if="!loading"
          ref="gridRef"
          :default-sort="defaultSort"
          :search-term="searchTerm"
          :columns="columns"
          :data="items"
          :filters="filters"
          :selectable="useCustomResourceKey"
          @selection-change="selectedItems = $event"
        >
          <!-- all allowed columns slots if needed -->
          <template v-slot:monitorName="{ item }">
            <div class="flex items-center">
              <Severity
                v-if="item.id"
                :object-id="
                  type === 'Event Source' ? item.monitorName : item.id
                "
                disable-tooltip
                class="mr-2"
              />
              <span class="text-ellipsis">{{ item.monitorName }}</span>
            </div>
          </template>
          <template v-slot:group="{ item }">
            <GroupPicker :value="item.group" multiple disabled />
          </template>
          <template v-slot:monitorType="{ item }">
            <MonitorType disable-tooltip :type="item.monitorType" />
          </template>
          <template v-slot:discoveredMonitors="{ item }">
            <div class="ant-tag rounded used-count-pill cursor-auto">
              {{ item.discoveredMonitors }}
            </div>
          </template>
          <template v-slot:categories="{ item }">
            <SelectedItemPills
              v-if="item.categories.length"
              :value="item.categories"
            />
            <span v-else />
          </template>
          <template v-slot:policyType="{ item }">
            <SelectedItemPills
              v-if="item.policyType"
              :value="[item.policyType]"
            />
            <span v-else />
          </template>

          <template v-slot:tags="{ item }">
            <FlotoFormItem label=" ">
              <ObjectTagPicker
                id="tags"
                :value="item.tags"
                :full-width="true"
                always-text-mode
                title="Tag"
                variant="default"
                rounded
                class="w-full"
                @change="onChangeTag($event, item)"
              />
            </FlotoFormItem>
          </template>
          <template v-slot:tag="{ item }">
            <LooseTags :value="item.tags || []" multiple disabled />
          </template>
          <template v-slot:source="{ item }">
            <AgentPicker :value="item.source" disabled />
          </template>
        </MGrid>
        <FlotoContentLoader
          v-else
          :loading="true"
          class="flex flex-col flex-1"
        />
      </div>
    </ObjectTagProvider>
  </div>
</template>

<script>
import { arrayWorker } from '@/src/workers'
import { workerFn } from '@utils/worker-wrapper'
import Uniq from 'lodash/uniq'

import MonitorType from '@components/monitor-type.vue'
import Severity from '@components/severity.vue'
import MonitorCountFilter from './monitor-count-filter.vue'
import SelectedItemPills from '../dropdown-trigger/selected-item-pills.vue'
// import LooseTags from '@components/loose-tags.vue'
import ObjectTagPicker from '@components/data-picker/object-tag-picker.vue'
import ObjectTagProvider from '@components/data-provider/object-tag-provider.vue'
import BulkAddTagFrom from '@modules/settings/monitoring/components/bulk-add-tag-form.vue'
import exportData from '@modules/settings/monitoring/helpers/export-pdf-csv'
import { downloadFile } from '@utils/download'
import FindIndex from 'lodash/findIndex'
import LooseTags from '@components/loose-tags.vue'
import AgentPicker from '@components/data-picker/agent-picker.vue'

export default {
  name: 'UsedCountList',
  components: {
    MonitorType,
    MonitorCountFilter,
    Severity,
    SelectedItemPills,
    // LooseTags,
    ObjectTagProvider,
    ObjectTagPicker,
    BulkAddTagFrom,
    LooseTags,
    AgentPicker,
  },
  props: {
    type: { type: String, default: undefined },
    columns: { type: Array, required: true },
    fetchFn: { type: Function, required: true },
    defaultSort: { type: String, default: undefined },
    useCustomResourceKey: { type: Boolean, default: false },
    allowExport: { type: Boolean, default: false },
    modalTitle: { type: String, default: '' },
  },
  data() {
    return {
      items: [],
      searchTerm: undefined,
      showFilters: false,
      renderKey: 1,
      monitorOptions: [],
      appliedFilters: {
        // monitorName: [],
        types: [],
        groups: [],
        // severity: [],
      },
      loading: true,
      showTagForm: false,
      selectedItems: [],
      tagProcessing: false,
    }
  },
  computed: {
    isSearchable() {
      const c = this.columns.find((c) => c.searchable)
      return c !== undefined
    },
    filters() {
      let filters
      const value = this.appliedFilters
      // if (value.monitorName.length) {
      //   filters = [
      //     ...(filters || []),
      //     {
      //       field: 'monitorName',
      //       operator: 'array_contains',
      //       value: value.monitorName,
      //     },
      //   ]
      // }
      if (value.groups.length) {
        filters = [
          ...(filters || []),
          {
            field: 'group',
            operator: 'array_contains',
            value: value.groups,
          },
        ]
      }
      // if (value.severity.length) {
      //   filters = [
      //     ...(filters || []),
      //     {
      //       field: 'severity',
      //       operator: 'array_contains',
      //       value: value.severity,
      //     },
      //   ]
      // }
      if (value.types.length) {
        filters = [
          ...(filters || []),
          {
            field: 'monitorType',
            operator: 'array_contains',
            value: value.types,
          },
        ]
      }
      return filters
    },
  },
  created() {
    this.fetchReferences()
  },
  methods: {
    fetchReferences() {
      return this.fetchFn().then(async (response) => {
        if (this.type === 'monitor' && response) {
          const monitorOptions = await arrayWorker.map(
            response,
            workerFn((item) => ({
              text: item.monitorName,
              key: item.monitorName,
            }))
          )
          this.monitorOptions = await arrayWorker.uniqBy(monitorOptions, 'key')
        }
        this.items = Object.freeze(response || [])
        this.loading = false
      })
    },
    applyFilter() {
      this.showFilters = false
    },
    async onChangeTag(event, item, updateParent = true) {
      this.handleUpdateItem(
        {
          ...item,
          tags: event,

          untouchedContext: {
            ...(item.untouchedContext || {}),
            'instance.tags': event,
          },
        },
        updateParent
      )
    },

    async handleUpdateItem(item, updateParent) {
      const itemIndex = await arrayWorker.findIndex(this.items, { id: item.id })
      if (itemIndex >= 0) {
        this.items = Object.freeze([
          ...this.items.slice(0, itemIndex),
          item,
          ...this.items.slice(itemIndex + 1),
        ])
        // if (updateParent) {
        //   this.$emit(
        //     'updateContext',
        //     this.items.map((item) => item.untouchedContext)
        //   )
        // }
      } else {
        // this.items = Object.freeze([...this.items, item])
      }
    },
    async bulksaveTag(tags) {
      this.tagProcessing = true

      if (tags.length) {
        this.$emit('updateDisabledState', true)

        for (const [index, id] of this.selectedItems.entries()) {
          const itemIndex = FindIndex(this.items, { id: id })
          const shouldUpdateParent = index === this.selectedItems.length - 1
          if (itemIndex >= 0) {
            const currentItem = this.items[itemIndex]

            const uniqTag = Uniq([...(currentItem.tags || []), ...tags])

            // this.onChangeTag(
            //   Uniq([...(currentItem.tags || []), ...tags]),
            //   currentItem,
            //   shouldUpdateParent
            // )
            if (itemIndex >= 0) {
              this.items = Object.freeze([
                ...this.items.slice(0, itemIndex),
                {
                  ...currentItem,
                  tags: uniqTag,

                  untouchedContext: {
                    ...(currentItem.untouchedContext || {}),
                    'instance.tags': uniqTag,
                  },
                },
                ...this.items.slice(itemIndex + 1),
              ])
            }
            if (shouldUpdateParent) {
              this.$emit('updateDisabledState', false)
            }
          }
        }
        // this.selectedItems.forEach(async (id, index) => {

        // })
      }
      this.tagProcessing = false
      this.showTagForm = false
      // this.selectedItems = []
    },
    resetSelection() {
      if (this.$refs.gridRef) {
        this.$refs.gridRef.resetSelection()
      }
      this.selectedItems = []
    },

    async exportGrid(type) {
      if (this.$refs?.gridRef) {
        const columns = this.columns.filter((obj) => obj.key && !obj.hidden)
        let items =
          (await this.$refs?.gridRef?.getFilteredDataWithoutTake())?.data || []
        const contextData = this.$refs.gridRef.getContextData()
        this.$successNotification({
          message: 'Success',
          description: `The file will be downloaded once ready`,
        })
        exportData(columns, items, type, contextData).then((blob) => {
          downloadFile(blob, undefined, `${this.modalTitle}.${type}`)
        })
      }
    },
    getSelectedResourceKeyData() {
      return this.items.map((item) => item.untouchedContext)
    },
  },
}
</script>
