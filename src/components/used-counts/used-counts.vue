<template>
  <div class="user-count">
    <slot name="trigger" :show="showModal" :hide="hideModal">
      <MTag
        :closable="false"
        rounded
        class="used-count-pill"
        @click="showModal"
      >
        {{ displayCount }}
      </MTag>
    </slot>
    <UsedCountModal
      v-if="(countTypes || []).length"
      :open="isModalVisible"
      :allowed-tabs="countTypes"
      :parent-resource-type="parentResourceType"
      :parent-resource-id="parentResourceId"
      :api-prefix="apiPrefix"
      :use-custom-resource-key="useCustomResourceKey"
      :allow-export="allowExport"
      :modal-title="title"
      @hide="hideModal"
    >
      <template v-slot:title>
        <div class="flex items-center">
          <div class="flex-1">
            <slot name="title">
              <h4 class="mb-0 text-primary">{{ title }}</h4>
            </slot>
          </div>
        </div>
      </template>
    </UsedCountModal>
  </div>
</template>

<script>
import UsedCountModal from './used-count-modal.vue'
export default {
  name: 'UsedCounts',
  components: { UsedCountModal },
  props: {
    title: { type: String, default: 'Used Counts' },
    displayCount: { type: [Number, String], default: 0 },
    parentResourceId: { type: Number, required: true },
    parentResourceType: { type: String, required: true },
    countTypes: { type: Array, required: true },
    apiPrefix: { type: String, default: 'settings' },
    useCustomResourceKey: { type: Boolean, default: false },
    allowExport: { type: Boolean, default: false },
  },
  data() {
    return {
      isModalVisible: false,
      hasModal: false,
    }
  },
  methods: {
    showModal() {
      this.isModalVisible = true
      this.hasModal = true
    },
    hideModal() {
      this.$emit('hide')
      this.isModalVisible = false
      setTimeout(() => {
        this.hasModal = false
      }, 400)
    },
  },
}
</script>
