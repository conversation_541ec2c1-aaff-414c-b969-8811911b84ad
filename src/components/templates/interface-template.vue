<template>
  <MonitorAlertCountProvider :selected-target="target">
    <template v-slot="{ convertedData }">
      <FlotoDrawer
        wrap-class-name="ant-drawer-close-btn-center interface-template-drawer"
        :open="open"
        width="96%"
        :wrap-style="{ zIndex: zIndex }"
        :get-container="getPopupContainer"
        @hide="handleHide"
      >
        <template v-if="interfaceItem" v-slot:title>
          <div
            class="flex justify-between items-center"
            :style="{ minHeight: '55px' }"
          >
            <div class="flex flex-col w-full">
              <span class="flex min-w-0 text-ellipsis">
                {{ interfaceItem.monitorName }} - {{ interfaceItem.interface }}

                <span
                  v-if="interfaceItem.ip"
                  class="text-neutral-light ml-2 text-sm"
                >
                  | {{ interfaceItem.ip }}
                </span>
                <span
                  v-if="interfaceItem.vendor"
                  class="text-neutral-light ml-2 text-sm"
                >
                  | {{ interfaceItem.vendor }}
                </span>

                <template
                  v-if="
                    interfaceItem.status || interfaceItem.interface_status_last
                  "
                >
                  <span
                    v-if="
                      interfaceItem.vendor ||
                      interfaceItem.interface_status_last
                    "
                    class="text-neutral-light ml-2 text-sm"
                  >
                    | &nbsp;
                    <MStatusTag
                      class="ml-2"
                      :status="
                        interfaceItem.statusFormatted ||
                        interfaceItem.status ||
                        interfaceItem.interface_status_last
                      "
                    />
                  </span>
                </template>
              </span>

              <!-- <TimerangePicker
            v-model="timeline"
            class="mr-2"
            style="z-index: 9999"
            :hide-custom-time-range="false"
           /> -->

              <div class="flex flex-1">
                <div class="inline-flex flex-grow-0 min-w-0 flex-shrink-0">
                  <GroupPicker
                    :value="interfaceItem.groups"
                    disabled
                    :wrap="false"
                  />
                </div>
              </div>
            </div>

            <div
              class="inline-flex flex-grow-0 min-w-0 flex-shrink-0 text-neutral-light justify-center items-center"
            >
              <RadialView
                v-if="convertedData.series[0].data.length"
                :data="convertedData"
                for-monitor-details
                @filter-by-severity="filterBySeverity"
              />

              <MButton
                variant="neutral-lightest"
                title="Export"
                class="mr-2 squared-button"
                @click="exportImage"
              >
                <MIcon name="image" />
              </MButton>
            </div>
          </div>
        </template>

        <div v-if="interfaceItem">
          <MTab v-model="activeTab">
            <MTabPane v-for="tab in tabs" :key="tab.key" :tab="tab.name" />
          </MTab>
        </div>
        <div v-if="interfaceItem" class="flex flex-col flex-1 min-h-0 -mx-4">
          <ScrollableExport
            ref="scrollableExportRef"
            :use-queue="activeTab === 'overview'"
          >
            <TemplateView
              v-if="activeTab === 'overview'"
              ref="templateViewRef"
              :key="interfaceItem.id"
              class="pl-2 mb-4"
              disabled
              template-type="dashboard"
              for-monitor-template
              :time-range="timeline"
              :widget-params="widgetParams"
              :template-id="templateId"
              :grid-row-height="90"
              :default-grid-columns="6"
              disable-auto-column-calculation
              @drilldown="handleDrilldown"
            />

            <div
              v-else-if="activeTab === 'active-policies'"
              ref="policyTemplateRef"
              class="flex pt-2 flex-col flex-1 page-background-color"
            >
              <PolicyTemplate />
            </div>
          </ScrollableExport>
        </div>
      </FlotoDrawer>
    </template>
  </MonitorAlertCountProvider>
</template>

<script>
import { FILTER_CONDITION_DEFAULT_DATA } from '@components/widgets/constants'
import { transformConditionsForServer } from '@components/widgets/helper'
// import TimerangePicker from '../widgets/time-range-picker.vue'
import MonitorAlertCountProvider from '@components/data-provider/monitor-alert-count-provider.vue'
import RadialView from '@/src/components/widgets/views/radial-view.vue'
// import { objectDBWorker } from '@/src/workers'
import Bus from '@utils/emitter'
import ScrollableExport from '@components/scrollable-export.vue'
import PolicyTemplate from '@/src/modules/inventory/views/policy-template.vue'

const INTERFACE_TEMPLATE_ID = 10000000000007

export default {
  name: 'InterfaceTemplate',
  components: {
    // TimerangePicker,
    MonitorAlertCountProvider,
    RadialView,
    ScrollableExport,
    PolicyTemplate,
  },

  inject: {
    layoutContext: {
      default: { isOmniBoxVisible: false },
      policyGridContext: { default: { data: [] } },
    },
  },
  props: {
    interfaceItem: {
      type: Object,
      default: undefined,
    },
    open: {
      type: Boolean,
      default: false,
    },
    isFullscreen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.tabs = [
      {
        key: 'overview',
        name: 'Overview',
      },
      {
        key: 'active-policies',
        name: 'Active Policies',
      },
    ]
    return {
      template: null,
      loading: true,
      zIndex: this.layoutContext.isOmniBoxVisible ? 1054 : 999,
      timeline: {
        selectedKey: 'today',
      },
      activeTab: 'overview',
    }
  },
  computed: {
    widgetParams() {
      const defaultFilter = { ...FILTER_CONDITION_DEFAULT_DATA }
      const interfaceItem = this.interfaceItem
      return {
        'entity.type': 'Monitor',
        entities: [interfaceItem.monitorId],
        'filter.keys': [
          this.interfaceItem.interface || this.interfaceItem.name,
        ],
        filters: {
          'data.filter': transformConditionsForServer({
            ...defaultFilter,
            groups: [
              {
                ...defaultFilter.groups[0],
                conditions: [
                  {
                    operand: 'interface',
                    operator: '=',
                    value:
                      this.interfaceItem.interface || this.interfaceItem.name,
                  },
                ],
              },
            ],
          }),
        },
      }
    },
    target() {
      return {
        id: this.interfaceItem?.monitorId,
        interface: this.interfaceItem?.interface,
      }
    },
    templateId() {
      return INTERFACE_TEMPLATE_ID
    },
    isOmniBoxVisible() {
      return this.layoutContext.isOmniBoxVisible
    },
  },
  watch: {
    open(newValue, oldValue) {
      if (newValue) {
        Bus.$emit('close-edge-tooltip')
        this.timeline = {
          selectedKey: 'today',
        }
        if (newValue !== oldValue) {
          if (newValue) {
            this.zIndex = this.layoutContext.isOmniBoxVisible ? 1054 : 999
          } else {
            this.zIndex = this.layoutContext.isOmniBoxVisible ? 999 : 1054
          }
        }
      }
    },
    layoutContext: {
      deep: true,
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          if (newValue) {
            this.zIndex = this.layoutContext.isOmniBoxVisible ? 999 : 1054
          }
        }
      },
    },
  },
  created() {
    Bus.$on('row-click', this.handleHide)

    this.$once('hook:beforeDestroy', () => {
      Bus.$off('row-click', this.handleHide)
    })
  },
  methods: {
    handleHide() {
      this.template = null
      this.loading = true
      this.$emit('hide')
    },
    getPopupContainer() {
      if (this.isFullscreen) {
        return this.$el.closest('.widget-view')
      }
      return this.$el.closest('.dashboard-container') || document.body
    },
    exportImage() {
      const fileName = `${this.interfaceItem.monitorName} - ${this.interfaceItem.interface}`

      // if parent class is interface-template-drawer, then get the parent
      const drawerHeader = document.querySelector(
        '.interface-template-drawer .ant-drawer-header'
      )

      drawerHeader.style.margin = '0'
      drawerHeader.style.paddingRight = '10px'
      drawerHeader.style.paddingLeft = '10px'
      this.$refs.scrollableExportRef.capture(
        this.activeTab === 'overview'
          ? this.$refs.templateViewRef.getScrollContainer()
          : this.$refs.policyTemplateRef,
        fileName,
        undefined,
        {
          prepend: [drawerHeader],
        }
      )
    },
    handleDrilldown(event) {
      if (event?.type === 'metric.explorer') {
        this.$router.push(
          this.$modules.getModuleRoute('metric-explorer', '', {
            query: {
              counters: encodeURIComponent(
                btoa(JSON.stringify(event.counters))
              ),

              instanceDrillDownContext: encodeURIComponent(
                btoa(
                  JSON.stringify({
                    monitor: this.interfaceItem.monitorId,
                    instanceType: 'interface',
                    instance: this.interfaceItem.interface,
                  })
                )
              ),
            },
          })
        )
      }
    },
    filterBySeverity(event) {
      this.activeTab = 'active-policies'
    },
  },
}
</script>
