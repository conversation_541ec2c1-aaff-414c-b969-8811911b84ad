<template>
  <MLayoutHeader class="header mainAppHeader header-panel absolute w-full">
    <MRow :gutter="0">
      <MCol :size="6" class="flex items-center">
        <FlotoLink class="logo" :to="homeRoute">
          <img id="header-logo" :src="appliedLogo" height="40" />
        </FlotoLink>
      </MCol>
      <MCol :size="6" class="text-right user-box justify-end">
        <MRow :gutter="0" class="justify-end rightContainer items-center h-100">
          <DummyPage v-if="showDummyDataLabel" />
          <MButton
            variant="transparent"
            title="search"
            shape="circle"
            @click="triggerSearch"
          >
            <MIcon name="search" size="lg" />
          </MButton>
          <!-- <MButton
            variant="transparent"
            title="Health Monitoring"
            shape="circle"
            @click="navigateToHealth"
          >
            <MIcon name="health-monitoring" size="lg" />
          </MButton> -->
          <MPermissionChecker
            :permission="$constants.HEALTH_MONITORING_READ_PERMISSION"
          >
            <FlotoLink
              title="Health Monitoring"
              :to="$modules.getModuleRoute('health')"
              as-button
              :variant="$route.name === 'health' ? 'primary' : 'transparent'"
              shape="circle"
              :class="$route.name === 'health' ? 'tag-primary' : ''"
            >
              <MIcon
                name="health-monitoring"
                size="lg"
                :class="$route.name === 'health' ? 'text-primary' : ''"
              />
            </FlotoLink>
          </MPermissionChecker>
          <!-- <FlotoLink
            :to="$modules.getModuleRoute('dashboard', 'socket-playground')"
            as-button
            title="Socket Playground"
            :rounded="false"
            :variant="
              $route.name === 'dashboard.socket-playground'
                ? 'primary'
                : 'transparent'
            "
            shape="circle"
          >
            <MIcon name="plug" size="lg" />
          </FlotoLink> -->
          <!-- <FlotoLink
            id="task-manager"
            title="Task Manager"
            :to="
              $modules.getModuleRoute('task-manager', 'tasks', {
                params: { defaultTab: 'running' },
              })
            "
            as-button
            :variant="
              $route.name === 'task-manager.tasks' ? 'primary' : 'transparent'
            "
            shape="circle"
            :class="$route.name === 'task-manager.tasks' ? 'tag-primary' : ''"
          >
            <MIcon
              name="task-manager"
              size="lg"
              :class="
                $route.name === 'task-manager.tasks' ? 'text-primary' : ''
              "
            />
          </FlotoLink> -->
          <NotificationDropdown />
          <MTag
            :closable="false"
            class="cursor-auto mx-2 tag-primary"
            variant="primary"
          >
            BUILD : {{ version }}
          </MTag>
          <div v-if="loggedIn" class="box user-icon-box">
            <UserDropdown />
          </div>
        </MRow>
      </MCol>
    </MRow>
  </MLayoutHeader>
</template>

<script>
import Bus from '@utils/emitter'
import { BrandingComputed } from '@state/modules/branding'
import { authComputed } from '@state/modules/auth'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import UserDropdown from './user-dropdown.vue'
import NotificationDropdown from './notification-dropdown.vue'
import api from '@api'

export default {
  name: 'Header',
  components: {
    UserDropdown,
    NotificationDropdown,
  },
  data() {
    return {
      version: '',
    }
  },
  computed: {
    ...authComputed,
    ...BrandingComputed,
    ...UserPreferenceComputed,
    appliedLogo() {
      if (this.theme === 'black') {
        return this.darkLogo
      }
      return this.logo
    },

    showDummyDataLabel() {
      return this.$route.meta.dummyData || false
    },
    homeRoute() {
      return this.$modules.getModuleRoute('dashboard')
    },
  },
  async mounted() {
    try {
      const buildVersion = await api.get('/system/motadata-app/version')
      if (buildVersion?.result) {
        this.version = buildVersion.result
      }
    } catch (error) {
      this.version = 'UNKNOWN'
    }
  },
  methods: {
    navigateToHealth() {
      this.$router.push(this.$modules.getModuleRoute('health', ''))
    },
    onMenuPinnedChange() {
      this.$emit('menu-pin-change')
    },
    triggerSearch() {
      Bus.$emit('cmd:search')
    },
  },
}
</script>
