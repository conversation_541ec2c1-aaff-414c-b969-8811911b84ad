<template>
  <div class="inline-name-edit w-full" :title="item[valueKey]">
    <div class="flex items-center">
      <slot name="beforeName" />
      <span v-if="!isEditMode">{{ item[valueKey] }}</span>

      <span v-else>
        <MInput
          ref="inputRef"
          v-model="editingItemName"
          auto-focus
          class="auto-height-input"
          @click.stop.prevent
        >
        </MInput>
      </span>
    </div>

    <template v-if="canShowActionsBtn">
      <div v-if="!isEditMode" class="flex ml-2 items-center">
        <MButton
          v-if="canEditName"
          size="small"
          class="flex items-center justify-center"
          variant="transparent"
          :shadow="false"
          shape="circle"
          @click.stop.prevent="handleEdit"
        >
          <MIcon name="pencil" />
        </MButton>

        <!-- <MButton
        size="small"
        class="flex items-center justify-center"
        variant="transparent"
        :shadow="false"
        shape="circle"
        @click.stop.prevent="showConfirmDeleteModal = true"
      >
      </MButton> -->
        <MIcon
          v-if="hasDeletePermission"
          name="trash-alt"
          class="text-secondary-red"
          @click.stop.prevent="showConfirmDeleteModal = true"
        />
      </div>
      <div v-else class="flex">
        <MButton
          size="small"
          class="flex items-center justify-center"
          variant="transparent"
          :shadow="false"
          shape="circle"
          @click.stop.prevent="showConfirmUpdateModal = true"
        >
          <MIcon name="check" />
        </MButton>

        <MButton
          size="small"
          class="flex items-center justify-center"
          variant="transparent"
          :shadow="false"
          shape="circle"
          @click.stop.prevent="cancelUpdate"
        >
          <MIcon name="times" class="text-secondary-red" />
        </MButton>
      </div>
    </template>

    <FlotoConfirmModal
      v-if="showConfirmDeleteModal"
      open
      no-icon-shadow
      @confirm="deleteItem"
      @hide="showConfirmDeleteModal = false"
    >
      <template v-slot:icon>
        <slot name="confirm-delete-icon">
          <MIcon name="trash-alt" size="2x" class="text-secondary-red" />
        </slot>
      </template>
      <template v-slot:message>
        {{
          $message('confirm', {
            message: $message('delete_resource', {
              resource: `${item[valueKey]} View `,
            }),
          })
        }}?
      </template>
    </FlotoConfirmModal>

    <FlotoConfirmModal
      v-if="showConfirmUpdateModal"
      open
      variant="primary-alt"
      no-icon-shadow
      :width="520"
      @confirm="updateItem"
      @hide="showConfirmUpdateModal = false"
    >
      <template v-slot:message>
        Are you sure you want to update
        {{ item[valueKey] }}
      </template>
    </FlotoConfirmModal>
  </div>
</template>

<script>
import { authComputed } from '@state/modules/auth'

export default {
  name: 'InlineNameEdit',
  props: {
    item: {
      type: Object,
      required: true,
    },
    updateFn: {
      type: Function,
      default: () => Promise.resolve(),
    },
    deleteFn: {
      type: Function,
      default: () => Promise.resolve(),
    },
    valueKey: {
      type: String,
      default: 'name',
    },
    idKey: {
      type: String,
      default: 'id',
    },
    canEditName: {
      type: Boolean,
      default: false,
    },
    canShowActionsBtn: {
      type: Boolean,
      default: false,
    },
    deletePermission: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      isEditMode: false,
      editingItemName: this.item[this.valueKey],
      showConfirmDeleteModal: false,
      showConfirmUpdateModal: false,
    }
  },
  computed: {
    ...authComputed,
    hasDeletePermission() {
      if (this.deletePermission) {
        return this.hasPermission(this.deletePermission)
      }
      return true
    },
  },
  methods: {
    handleEdit() {
      this.isEditMode = true
      this.editingItemName = this.item[this.valueKey]

      this.$nextTick(() => {
        this.$refs.inputRef.focus()
      })
    },
    handleDeleteConfirmation() {
      this.showConfirmDeleteModal = false
      this.deleteItem()
    },
    deleteItem() {
      return this.deleteFn(this.item).then(() => {
        this.$emit('delete-item', this.item)
      })
    },
    updateItem() {
      return this.updateFn(this.item, this.editingItemName).then(() => {
        this.$emit('update-item', this.item)
        this.isEditMode = false
      })
    },
    cancelUpdate() {
      this.isEditMode = false
      this.editingItemName = this.item[this.valueKey]

      this.isEditMode = false
    },
  },
}
</script>

<style lang="less" scoped>
.inline-name-edit {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: space-between;
  white-space: nowrap;
}
</style>
