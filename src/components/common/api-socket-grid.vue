<template>
  <div class="flex flex-1 min-h-0 flex-col min-w-0">
    <div class="flex flex-1 min-h-0 flex-col widget-view __panel">
      <div class="flex flex-1 min-h-0 flex-col overflow-auto w-full">
        <FlotoContentLoader :loading="loading">
          <div
            class="flex flex-1 h-full w-full min-h-0 min-w-0 relative flex-col"
          >
            <div class="my-2 mx-2 flex justify-between">
              <MInput
                v-model="searchTerm"
                class="search-box"
                placeholder="Search"
                name="search"
              >
                <template v-slot:prefix>
                  <MIcon name="search" />
                </template>
                <template v-if="searchTerm" v-slot:suffix>
                  <MIcon
                    name="times-circle"
                    class="text-neutral-light cursor-pointer"
                    @click="searchTerm = undefined"
                  />
                </template>
              </MInput>

              <div v-if="gridData.length" class="flex-1 text-right">
                <MButton
                  :shadow="false"
                  class="squared-button mr-2"
                  :rounded="false"
                  title="Export As PDF"
                  variant="neutral-lightest"
                  @click="exportGrid('pdf')"
                >
                  <MIcon name="export-pdf" />
                </MButton>

                <MButton
                  :shadow="false"
                  class="squared-button mr-2"
                  :rounded="false"
                  variant="neutral-lightest"
                  title="Export As CSV"
                  @click="exportGrid('csv')"
                >
                  <MIcon name="export-csv" />
                </MButton>

                <MButton
                  id="btn-filter"
                  title="Filter"
                  :shadow="false"
                  class="squared-button mr-2"
                  :rounded="false"
                  :variant="
                    showFilters ? 'neutral-lighter' : 'neutral-lightest'
                  "
                  @click="showFilters = !showFilters"
                >
                  <MIcon name="filter" />
                </MButton>
              </div>
            </div>

            <div v-if="showFilters" class="mt-4">
              <MRow>
                <MCol :size="12">
                  <ApiSocketGridFilters
                    v-model="appliedFilters"
                    :widget="widget"
                    :policy-options="policyOptions"
                    :monitor-options="monitorOptions"
                    :metric-options="metricOptions"
                    :source-options="sourceOptions"
                    :is-availability="isAvailability"
                    :widget-category="widgetCategory"
                    @change="handleFilterChange"
                    @hide="showFilters = !showFilters"
                  />
                </MCol>
              </MRow>
            </div>

            <div class="flex flex-col flex-1 min-h-0 w-full relative min-w-0">
              <div
                class="flex flex-1 min-h-0 dashboard-widget-grid w-full min-w-0"
              >
                <MGrid
                  ref="gridRef"
                  :columns="columns"
                  :data="gridData"
                  style="height: 100%; margin-top: 0"
                  :search-term="searchTerm"
                  :default-sort="defaultSort"
                  :filters="gridFilters"
                >
                  <template v-slot:monitorWithSeverity="{ item }">
                    <span class="flex">
                      <Severity
                        v-if="!item.instance"
                        :severity="item.severity"
                        class="mr-2"
                      />

                      <MonitorName :value="item.monitor" :row="item" />
                    </span>
                  </template>

                  <template v-slot:instanceDrilldown="{ item }">
                    <span class="flex">
                      <Severity :severity="item.severity" class="mr-2" />

                      <InstanceName :row="item" :counter="$attrs.counter" />
                    </span>
                  </template>

                  <template v-slot:monitor="{ item }">
                    <span class="flex">
                      <Severity :severity="item.severity" class="mr-2" />

                      <MonitorName :value="item.monitor" :row="item" />
                    </span>
                  </template>

                  <template v-slot:alertDrilldown="{ item }">
                    <span class="flex">
                      <Severity :severity="item.severity" class="mr-2" />
                      <AlertDrilldown
                        :alert="item"
                        :field="item.policy_name"
                        traget-blank
                      />
                    </span>
                  </template>

                  <template v-slot:duration="{ item }">
                    {{ item.duration | duration }}
                  </template>
                  <template v-slot:policy="{ item }">
                    <span class="flex">
                      <Severity :severity="item.severity" class="mr-2" />
                      <AlertDrilldown
                        :alert="item"
                        :field="item.policy"
                        traget-blank
                        is-event-policy-drilldown
                      />
                    </span>
                  </template>

                  <template v-slot:monitorName="{ item }">
                    <span class="flex">
                      <MonitorName :value="item.monitor" :row="item"
                    /></span>
                  </template>
                  <template v-slot:object_user_tags="{ item }">
                    <LooseTags
                      :value="
                        Array.isArray(item.object_user_tags)
                          ? item.object_user_tags
                          : [item.object_user_tags]
                      "
                      :disabled="true"
                    />
                  </template>

                  <template v-slot:tag_str="{ props }">
                    <LooseTags
                      :value="(props.dataItem[props.field] || '').split(',')"
                      :disabled="true"
                    />
                  </template>
                  <template v-slot:tag="{ props }">
                    <LooseTags
                      :value="props.dataItem[props.field] || []"
                      :disabled="true"
                    />
                  </template>
                  <template v-slot:timestemp="{ item }">
                    {{ item.event_timestamp | datetime }}
                  </template>
                  <template v-slot:object_type="{ item }">
                    <MonitorType :type="item.object_type" :center="false" />
                  </template>
                  <template v-slot:type="{ item, props }">
                    <MTag rounded :closable="false" class="tag-primary">
                      {{ item[props.field] }}
                    </MTag>
                  </template>
                  <template v-slot:object_groups="{ item }">
                    <GroupPicker
                      :value="
                        (item.object_groups || '')
                          .split(',')
                          .map((id) => Number(id))
                      "
                      disabled
                      :wrap="false"
                    />
                  </template>
                  <template v-slot:firstSeen="{ item }">
                    {{ item.firstSeen | datetime }}
                  </template>

                  <template v-slot:lastSeen="{ item }">
                    {{ item.lastSeen | datetime }}
                  </template>
                </MGrid>
              </div>
            </div>
          </div>
        </FlotoContentLoader>
      </div>
    </div>
  </div>
</template>

<script>
// import duration from '@/src/filters/duration'

import LooseTags from '@components/loose-tags.vue'
import MonitorType from '@components/monitor-type.vue'
import MonitorName from '@components/widgets/views/grid/view-more/monitor-name.vue'
import InstanceName from '@components/widgets/views/grid/view-more/instance-name.vue'
import Severity from '@components/severity.vue'
import AlertDrilldown from '@components/widgets/views/grid/view-more/alert-drilldown.vue'
import exportData from '@modules/settings/monitoring/helpers/export-pdf-csv'
import ApiSocketGridFilters from './api-socket-grid-filters.vue'
import { getWidgetResponseApi } from '@utils/socket-event-as-api'
import { downloadFile } from '@utils/download'
import { UserPreferenceComputed } from '@state/modules/user-preference'

export default {
  name: 'ApiSocketGrid',
  components: {
    LooseTags,
    MonitorType,
    Severity,
    MonitorName,
    AlertDrilldown,
    InstanceName,
    ApiSocketGridFilters,
  },
  props: {
    widget: {
      type: Object,
      required: true,
      searchTerm: undefined,
    },
    columns: {
      type: Array,
      required: true,
    },
    getFn: {
      type: Function,
      default: undefined,
    },
    defaultSort: {
      type: String,
      default: undefined,
    },
    gridFilters: {
      type: Array,
      default: undefined,
    },
    category: { type: String, required: true },
    appliedFilters: {
      type: Object,
      default: () => ({
        severity: [],
        type: [],
        groups: [],
        tags: [],
      }),
    },
    isAvailability: {
      type: Boolean,
      default: false,
    },
    widgetCategory: {
      type: String,
      default: null,
    },
    policyOptions: {
      type: Array,
      default: () => [],
    },
    monitorOptions: {
      type: Array,
      default: () => [],
    },
    metricOptions: {
      type: Array,
      default: () => [],
    },
    sourceOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      searchTerm: undefined,
      gridData: [],
      loading: true,
      showFilters: false,
    }
  },
  computed: {
    ...UserPreferenceComputed,
  },
  created() {
    this.requestGridResult()
  },
  methods: {
    handleFilterChange(newFilters) {
      this.showFilters = false
      this.$emit('filter-change', newFilters)
    },
    requestGridResult() {
      if (this.getFn) {
        return this.getFn().then((data) => {
          this.gridData = data
          this.loading = false
        })
      }
      return getWidgetResponseApi(this.widget, {
        checkWidgetProgress: false,
        useResultBuilder: true,
      }).then((data) => {
        this.gridData = data?.rows || []
        this.loading = false
      })
    },
    async exportGrid(type) {
      const columns = this.columns.filter((obj) => obj.key && !obj.hidden)
      let items =
        (await this.$refs?.gridRef?.getFilteredDataWithoutTake())?.data || []
      const contextData = this.$refs.gridRef.getContextData()
      this.$successNotification({
        message: 'Success',
        description: `The file will be downloaded once ready`,
      })
      exportData(columns, items, type, contextData, {
        dateTimeFormat: this.dateFormat,
        timezone: this.timezone,
      }).then((blob) => {
        downloadFile(
          blob,
          undefined,
          `${
            this.widget?.['visualization.name'] ||
            this.widget?.['name'] ||
            'monitor'
          }.${type}`
        )
      })
    },
  },
}
</script>
