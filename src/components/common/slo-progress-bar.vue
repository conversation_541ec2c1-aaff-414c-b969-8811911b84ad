<template>
  <div class="target-indicator-progress-bar">
    <!-- Progress Bar Container -->
    <div class="progress-container">
      <!-- Background Track -->
      <div class="progress-track">
        <!-- Horizontal Dotted Line in Middle -->
        <div v-if="!leftPercentageColor" class="progress-dotted-line"></div>

        <!-- Achieved Progress -->
        <div
          class="progress-fill achieved"
          :style="{
            width: `${Math.min(achievedPercentage, 100)}%`,
            backgroundColor: achievedColor,
          }"
        ></div>

        <!-- Remaining Progress -->
        <div
          class="progress-fill remaining"
          :style="{
            width: `${Math.max(0, 100 - achievedPercentage)}%`,
            left: `${Math.min(achievedPercentage, 100)}%`,
            backgroundColor: leftPercentageColor,
          }"
        ></div>
      </div>

      <!-- Target Line (Blue solid) -->
      <div
        v-if="targetPercentage > 0 && targetPercentage <= 100"
        class="target-line"
        :style="{ left: `${Math.min(targetPercentage, 100)}%` }"
      >
        <div
          class="target-line-label"
          :class="{ 'label-right': targetPercentage < 15 }"
        >
          Target: {{ targetPercentage }}%
        </div>
      </div>

      <!-- Violation Line (Yellow dotted) - optional -->
      <div
        v-if="violatedAt && violatedAt >= 0 && violatedAt <= 100"
        class="violation-line"
        :style="{ left: `${violatedAt}%` }"
      >
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TargetIndicatorProgressBar',
  props: {
    achievedPercentage: {
      type: Number,
      required: true,
    },
    targetPercentage: {
      type: Number,
      default: undefined,
    },
    leftPercentageColor: {
      type: String,
      default: undefined,
    },
    achievedColor: {
      type: String,
      default: 'var(--secondary-green)',
    },
    violatedAt: {
      type: Number,
      default: null,
    },
  },
}
</script>

<style lang="less" scoped>
.target-indicator-progress-bar {
  width: 100%;
}

.progress-container {
  position: relative;
  width: 100%;
  height: 18px;
  margin-bottom: 8px;
}

.progress-track {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: var(--code-tag-background-color);
  border-radius: 4px;
}

.progress-dotted-line {
  position: absolute;
  top: 50%;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 1px;
  pointer-events: none;
  background: repeating-linear-gradient(
    to right,
    rgba(255, 255, 255, 0.3) 0,
    rgba(255, 255, 255, 0.3) 2px,
    transparent 2px,
    transparent 4px
  );
  transform: translateY(-50%);
}

.progress-fill {
  position: absolute;
  top: 0;
  height: 100%;
  transition: all 0.3s ease;

  &.achieved {
    z-index: 3;
    border-radius: 4px 0 0 4px;
  }

  &.remaining {
    z-index: 1;
    border-radius: 0 4px 4px 0;
  }
}

.target-line {
  position: absolute;
  top: -14px;
  z-index: 3;
  width: 2px;
  height: 48.849px;
  pointer-events: none;
  background-color: var(--primary);

  .target-line-label {
    position: absolute;
    top: -15px;
    right: 0%;
    // transform: translateX(-50%);
    z-index: 4;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: 500;
    color: var(--white-regular);
    white-space: nowrap;
    background-color: var(--primary);
    border-radius: 2px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;

    &.label-right {
      right: auto;
      left: 0%;
      border-radius: 2px;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}

.violation-line {
  position: absolute;
  top: -8px;
  z-index: 11;
  width: 2px;
  height: 34.849px;
  pointer-events: none;
  border-left: 2px dotted var(--secondary-yellow);
}
</style>
