<template>
  <MCol class="slide-filters relative">
    <MRow>
      <MCol v-if="isAvailability" class="custom-col-width">
        <FlotoFormItem label="Groups">
          <div class="mt-1">
            <GroupPicker
              id="filter-group-picker"
              v-model="currentValue.groups"
              class="w-full"
              multiple
              allow-clear
            />
          </div>
        </FlotoFormItem>
      </MCol>
      <MCol v-if="isAvailability" class="custom-col-width">
        <FlotoFormItem label="Types">
          <MonitorTypePicker
            id="filter-monitor-type-picker"
            v-model="currentValue.types"
            class="w-full mt-1"
            multiple
            allow-clear
            as-input
          />
        </FlotoFormItem>
      </MCol>
      <MCol
        v-if="!isAvailability && shouldShowMetrics"
        class="custom-col-width"
      >
        <FlotoFormItem label="Metrics">
          <FlotoDropdownPicker
            v-model="currentValue.metrics"
            class="w-full mt-1"
            :options="metricOptions"
            multiple
            allow-clear
            placeholder="Select"
          />
        </FlotoFormItem>
      </MCol>
      <MCol class="custom-col-width">
        <FlotoFormItem label="Tags">
          <template v-if="isAvailability">
            <LooseTags
              v-model="currentValue.tags"
              class="w-full mt-1"
              as-dropdown
              placeholder="Select"
            />
          </template>
          <template v-else>
            <FlotoDropdownPicker
              v-model="currentValue.tags"
              class="w-full mt-1"
              :options="policyOptions"
              multiple
              allow-clear
              placeholder="Select"
            />
          </template>
        </FlotoFormItem>
      </MCol>
      <MCol
        v-if="!isAvailability && shouldShowAlertTypes"
        class="custom-col-width"
      >
        <FlotoFormItem label="Alert Types">
          <FlotoDropdownPicker
            v-model="currentValue.alertTypes"
            class="w-full mt-1"
            :options="ALERT_TYPE_OPTIONS"
            multiple
            allow-clear
            placeholder="Select"
          />
        </FlotoFormItem>
      </MCol>
      <MCol
        v-if="!isAvailability && shouldShowEventSource"
        class="custom-col-width"
      >
        <FlotoFormItem label="Event Source">
          <FlotoDropdownPicker
            v-model="currentValue.eventSources"
            class="w-full mt-1"
            :options="sourceOptions"
            multiple
            allow-clear
            placeholder="Select"
          />
        </FlotoFormItem>
      </MCol>
      <MCol
        v-if="!isAvailability && widgetCategory === 'metric'"
        class="custom-col-width"
      >
        <FlotoFormItem label="Monitors">
          <FlotoDropdownPicker
            v-model="currentValue.monitors"
            class="w-full mt-1"
            :options="monitorOptions"
            multiple
            allow-clear
            placeholder="Select"
          />
        </FlotoFormItem>
      </MCol>
      <MCol v-if="isAvailability" class="custom-col-width">
        <FlotoFormItem label="Severity">
          <SeverityPicker
            v-model="currentValue.severity"
            class="mt-1"
            multiple
            allow-clear
            :disabled-options="defaultDisabledSeverities"
          />
        </FlotoFormItem>
      </MCol>
      <MCol class="flex justiy-between items-start custom-col-width">
        <div class="flex items-center flex-1 mt-6">
          <MButton
            id="reset-btn"
            variant="default"
            @click="
              $emit('change', {
                groups: [],
                types: [],
                severity: [],
                tags: [],
                metrics: [],
                alertTypes: [],
                monitors: [],
                eventSources: [],
              })
            "
          >
            Reset
          </MButton>
          <MButton id="apply-btn" class="ml-2" @click="apply">Apply</MButton>
        </div>
      </MCol>
    </MRow>
    <MButton
      id="close-filter"
      variant="transparent"
      :shadow="false"
      shape="circle"
      style="position: absolute; top: 5px; right: 10px; z-index: 2"
      class="monitor-agent-filter-close"
      @click="$emit('hide')"
    >
      <MIcon name="times" class="text-neutral-light" />
    </MButton>
  </MCol>
</template>

<script>
import SeverityPicker from '@components/severity-picker.vue'
import MonitorTypePicker from '@components/data-picker/monitor-type-picker.vue'
import LooseTags from '@components/loose-tags.vue'
import Constants from '@constants'

const ALERT_TYPE_OPTIONS = [
  { key: 'Scheduled', text: 'Scheduled' },
  { key: 'Real Time', text: 'Real Time' },
]

export default {
  name: 'ApiSocketGridFilters',
  components: {
    MonitorTypePicker,
    SeverityPicker,
    LooseTags,
  },
  model: {
    event: 'change',
  },
  props: {
    value: { type: Object, required: true },
    widget: { type: Object, required: true },
    policyOptions: {
      type: Array,
      default: () => [],
    },
    monitorOptions: {
      type: Array,
      default: () => [],
    },
    metricOptions: {
      type: Array,
      default: () => [],
    },
    sourceOptions: {
      type: Array,
      default: () => [],
    },
    isAvailability: {
      type: Boolean,
      default: false,
    },
    widgetCategory: {
      type: String,
      default: null,
    },
  },
  data() {
    this.defaultDisabledSeverities = [
      Constants.UP,
      // Constants.UNREACHABLE,
      Constants.MAINTENANCE,
      Constants.DISABLE,
      Constants.SUSPENDED,
      Constants.UNKNOWN,
    ]
    return {
      currentValue: { ...this.value },
      ALERT_TYPE_OPTIONS,
    }
  },
  computed: {
    shouldShowMetrics() {
      return [
        Constants.METIC_PLUGIN?.toLowerCase() || '',
        Constants.LOG?.toLowerCase() || '',
        Constants.FLOW?.toLowerCase() || '',
        Constants.TRAP?.toLowerCase() || '',
      ].includes(this.widgetCategory.toLowerCase())
    },
    shouldShowAlertTypes() {
      return [
        Constants.LOG?.toLowerCase() || '',
        Constants.FLOW?.toLowerCase() || '',
      ].includes(this.widgetCategory.toLowerCase())
    },
    shouldShowEventSource() {
      return (
        this.widgetCategory?.toLowerCase() === Constants.TRAP?.toLowerCase()
      )
    },
  },
  watch: {
    value(newValue) {
      this.currentValue = { ...newValue }
    },
  },
  methods: {
    apply() {
      this.$emit('change', this.currentValue)
    },
  },
}
</script>

<style lang="less" scoped>
.custom-col-width {
  width: 13.5%;
  max-width: 300px;
}
</style>
