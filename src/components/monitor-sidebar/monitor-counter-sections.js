import Constants from '@constants'

export const MONITOR_COUNTER_SECTIONS = {
  [Constants.SERVER]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'system.os.name',
        'system.os.version',
        'system.cpu.type',
        'system.cpu.description',
        'system.model',
        'system.vendor',
        'system.memory.installed.bytes',
        'system.cpu.cores',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.NETWORK]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'system.vendor',
        'system.model',
        'system.os.name',
        'system.os.version',
        'system.serial.number',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.VCENTER]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: ['vcenter.cluster.cpu.cores'],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.VMWARE_ESXI]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'esxi.name',
        'esxi.cpu.description',
        'esxi.os.version',
        'esxi.memory.installed.bytes',
        'esxi.nic.cards',
        'esxi.hba.cards',
        'esxi.model',
        'esxi.cpu.cores',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  'esxi.vm': [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'esxi.vm~instance.name',
        'esxi.vm~ip',
        'esxi.vm~guest.os',
        'esxi.vm~host.os',
        'esxi.vm~memory.bytes',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  'hyperv.vm': [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'hyperv.vm~instance.name',
        'hyperv.vm~ip',
        'hyperv.vm~guest.os',
        'hyperv.vm~host.os',
        'hyperv.vm~memory.bytes',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  'citrix.xen.vm': [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'citrix.xen.vm~instance.name',
        'citrix.xen.vm~ip',
        'citrix.xen.vm~guest.os',
        'citrix.xen.vm~host.os',
        'citrix.xen.vm~memory.bytes',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.CITRIX_XEN]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'citrix.xen.name',
        'citrix.xen.description',
        'citrix.xen.os.version',
        'citrix.xen.memory.installed.bytes',
        'citrix.xen.model',
        'citrix.xen.cpu.cores',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.CITRIX_XEN_CLUSTER]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: ['citrix.xen.cluster.cpu.cores'],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.HYPER_V]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'hyperv.name',
        'hyperv.cpu.description',
        'hyperv.os.version',
        'hyperv.memory.installed.bytes',
        'hyperv.model',
        'hyperv.cpu.cores',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.HYPER_V_CLUSTER]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: ['hyperv.cluster.cpu.cores'],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.VIRTUALIZATION]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'system.os.name',
        'system.os.version',
        'system.cpu.type',
        'system.cpu.description',
        'system.model',
        'system.vendor',
        'system.memory.installed.bytes',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.SERVICE_CHECK]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.OTHER]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.CLOUD]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.RUCKUS_WIRELESS]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'ruckus.wireless.system.name',
        'ruckus.wireless.model',
        'ruckus.wireless.serial.no',
        'ruckus.wireless.version',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.CISCO_WIRELESS]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'cisco.wireless.controller.product.name',
        'cisco.wireless.controller.product.code',
        'cisco.wireless.controller.serial.number',
        'cisco.wireless.controller.manufacturer.name',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.ARUBA_WIRELESS]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'aruba.wireless.controller.model.name',
        'aruba.wireless.controller.host.name',
        'aruba.wireless.controller.serial.number',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.PRISM]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: ['prism.vms', 'started.time'],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.NUTANIX]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'nutanix.name',
        'nutanix.cluster',
        'nutanix.cpu.model',
        'nutanix.cpu.cores',
        'nutanix.cpu.sockets',
        'nutanix.cpu.threads',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  'nutanix.vm': [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'nutanix.vm',
        'nutanix.vm~ip',
        'nutanix.vm~hypervisor.type',
        'nutanix.vm~cpus',
        'nutanix.vm~memory.capacity.bytes',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.SDN]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.CISCO_VMANAGE]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'cisco.vmanage.cpus',
        'cisco.vmanage.control.connections',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.CISCO_VSMART]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'cisco.vsmart.cpus',
        'cisco.vsmart.control.connections',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.CISCO_VBOND]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'cisco.vbond.cpus',
        'cisco.vbond.control.connections',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.CISCO_VEDGE]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'cisco.vedge.cpus',
        'cisco.vedge.control.connections',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.CISCO_MERAKI]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'cisco.vedge.cpus',
        'cisco.vedge.control.connections',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.CISCO_MERAKI_RADIO]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'cisco.meraki.radio.mac.address',
        'cisco.meraki.radio.model',
        'cisco.meraki.radio.network.id',
        'cisco.meraki.radio.public.ip.address',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.CISCO_MERAKI_SWITCH]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'cisco.meraki.switch.mac.address',
        'cisco.meraki.switch.model',
        'cisco.meraki.switch.network.id',
        'cisco.meraki.switch.public.ip.address',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.CISCO_MERAKI_SECURITY]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'cisco.meraki.security.mac.address',
        'cisco.meraki.security.model',
        'cisco.meraki.security.network.id',
        'cisco.meraki.security.public.ip.address',
        'started.time',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.STORAGE]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'object.vendor',
        'netapp.ontap.cluster.model',
        'netapp.ontap.cluster.version',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.HPE_STOREONCE]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'hpe.storeonce.software.version',
        'hpe.storeonce.product.sku.name',
        'hpe.storeonce.product.name',
        'hpe.storeonce.serial.number',
        'hpe.storeonce.warranty.serial.number',
        'hpe.storeonce.system.location',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.HPE_PRIMERA]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'hpe.primera.system.name',
        'hpe.primera.system.version',
        'hpe.primera.system.model',
        'hpe.primera.system.serial.number',
        'hpe.primera.system.total.nodes',
        'hpe.primera.system.master.node.id',
        'hpe.primera.system.contact',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.HPE_3PAR]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'hpe.3par.system.name',
        'hpe.3par.system.version',
        'hpe.3par.system.model',
        'hpe.3par.system.serial.number',
        'hpe.3par.system.total.nodes',
        'hpe.3par.system.master.node.id',
        'hpe.3par.system.contact',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.DELL_EMC_UNITY]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'dell.emc.unity.system.name',
        'dell.emc.unity.system.model',
        'dell.emc.unity.system.software.version',
        'dell.emc.unity.system.serial.number',
        'dell.emc.unity.system.mac.address',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.CISCO_ACI]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'cisco.aci.fabric.global.health.score',
        'cisco.aci.tenant.global.health.score',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.CONTAINER_ORCHESTRATION]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: [
        'kubernetes.health.status',
        'kubernetes.liveness.probe',
        'kubernetes.readiness.probe',
        'kubernetes.client.git.version',
        'kubernetes.server.git.version',
        'kubernetes.active.master.node',
      ],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
  [Constants.NSXT]: [
    {
      title: 'Monitor Info',
      key: 'monitor-info',
    },
    {
      title: 'System Info',
      key: 'system-info',
      counters: ['nsxt.node.version', 'nsxt.product.version'],
    },
    {
      title: 'Tag Info',
      key: 'tag-info',
    },
    {
      title: 'Polling Info',
      key: 'poll-info',
    },
    {
      title: 'Triggered Policies',
      key: 'active-policies',
    },
  ],
}
