import api from '@api'
import Invert from 'lodash/invert'
import Constants from '@constants'
import { generateId } from '@utils/id'
import { provisionDiscoveryMonitorsApi } from '@modules/settings/network-discovery/network-discovery-profile-api'
import { REDISCOVER_TYPE_NAME_MAPPING } from '@modules/settings/monitoring/rediscover-setting-api'
import Capitalize from 'lodash/capitalize'

export const wanProbeOptions = [
  { key: 'ipslaicmpecho', text: 'ICMP Echo' },
  { key: 'ipslaicmpjitter', text: 'ICMP Jitter' },
  { key: 'ipslapathecho', text: 'Path Echo' },
]

export const wanProbeMap = wanProbeOptions.reduce(
  (acc, o) => ({ ...acc, [o.key]: o.text }),
  {}
)

export const containerRuntimeOptions = [
  { key: 'docker', text: 'Docker' },
  // { key: 'containerd', text: 'Containerd' },
]

export const containerRuntimeOptionMap = {
  docker: 'dockercontainer',
  // containerd: 'Containerd',
}

export const applicationTransformer = (payload) => ({
  guid: generateId(),
  eventScheduler: payload['event.scheduler'],
  metricId: payload.id,
  name: payload['metric.type'],
  objectId: payload['metric.object'],
  application: payload['metric.type'],
  message: payload.message,
  status: payload.status,
  error: payload.error,
  objects: Object.freeze(payload.objects),
  objectName: payload['object.name'],
  metricName: payload['metric.name'],
  _object: Object.freeze(payload.object),
})

function getAdditionalProps(payload) {
  const props = {}
  const type = Invert(REDISCOVER_TYPE_NAME_MAPPING)[payload['rediscover.job']]
  if (type === 'virtualization') {
    if ((payload.object || {})['object.ip']) {
      props.vmip = payload.object['object.ip']
    }
    if ((payload.object || {}).status) {
      props.interfaceStatus = payload.object.status
    }
    if ((payload.object || {})['object.type']) {
      props.type = payload.object['object.type']
    }
  }
  if (type === 'interface') {
    if ((payload.object || {})['interface']) {
      props.interfaceIndex = payload.object['interface']
    }
    if ((payload.object || {})['interface.ip.address']) {
      props.interfaceip = payload.object['interface.ip.address']
    }
    if ((payload.object || {})['interface.alias']) {
      props.alias = payload.object['interface.alias']
    }
    if ((payload.object || {})['interface.description']) {
      props.interfaceDescription = payload.object['interface.description']
    }
    if ((payload.object || {}).status) {
      props.interfaceStatus = payload.object.status
    }
    if ((payload.object || {})['interface.link.type']) {
      props.linkType = payload.object['interface.link.type']
    }
  }
  if (type === 'wireless') {
    if ((payload.object || {})['wireless.access.point.ip.address']) {
      props.ip = payload.object['wireless.access.point.ip.address']
    }
    if ((payload.object || {}).status) {
      props.interfaceStatus = payload.object.status
    }
    if ((payload.object || {})['wireless.access.point.mac.address']) {
      props.mac = payload.object['wireless.access.point.mac.address']
    }
  }
  if (type === 'service') {
    if ((payload.object || {})['system.service.description']) {
      props.systemServiceDescription =
        payload.object['system.service.description']
    }
  }
  if (type === 'cloud') {
    if ((payload.object || {})['object.ip']) {
      props.ip = payload.object['object.ip']
    }
    if ((payload.object || {})['object.type']) {
      props.type = payload.object['object.type']
    }
  }
  if (type === 'fileDirectory') {
    if ((payload.object || {})['object.type']) {
      props.fileType =
        payload.object['object.type'] === 'system.file' ? 'File' : 'Directory'
    }
  }
  if (type === 'hci') {
    if ((payload.object || {})['object.type']) {
      props.type = payload.object['object.type']
    }
  }
  if (type === 'wanLink') {
    if ((payload.object || {})['source.ip.address']) {
      props.sourceIp = payload.object['source.ip.address']
    }
    if ((payload.object || {})['destination.ip.address']) {
      props.destinationIp = payload.object['destination.ip.address']
    }
    if ((payload.object || {})['source.interface.name']) {
      props.sourceInterface = payload.object['source.interface.name']
    }
    if ((payload.object || {})['object.type']) {
      props.wanProbe = payload.object['object.type']
    }
    if ((payload.object || {})['object.credential.profile']) {
      props.credentialProfile = payload.object['object.credential.profile']
    }
    if ((payload || {})['state']) {
      props.state = payload['state']
    }
    if ((payload || {})['metric.plugin']) {
      props.metricPlugin = payload['metric.plugin']
    }
    if ((payload || {})['metric.type']) {
      props.metricType = payload['metric.type']
    }
  }
  if (type === 'container') {
    if ((payload.object || {})['object.type']) {
      props.type = payload.object['object.type']
    }
    if ((payload.object || {})['object.credential.profile']) {
      props.credentialProfile = payload.object['object.credential.profile']
    }
    if ((payload || {})['state']) {
      props.state = payload['state']
    }
    if ((payload.object || {})['status']) {
      props.containerStatus = payload.object['status']
    }
    if ((payload || {})['metric.plugin']) {
      props.metricPlugin = payload['metric.plugin']
    }
    if ((payload || {})['metric.type']) {
      props.metricType = payload['metric.type']
    }
    if ((payload.object || {})['container.state']) {
      props.containerState = payload.object['container.state']
    }
  }
  if (
    [
      'service',
      'process',
      'fileDirectory',
      'interface',
      'wireless',
      'virtualization',
      'hci',
    ].indexOf(type) >= 0
  ) {
    if (payload['object.name']) {
      props.objectName = payload['object.name']
    }
  }
  if (['cloud'].indexOf(type) >= 0) {
    if (payload['object.target']) {
      props.objectName = payload['object.target']
    }
  }
  return props
}

export const transformer = (payload) => ({
  guid: generateId(),
  id: generateId(),
  eventScheduler: payload['event.scheduler'],
  metricId: payload.id,
  name:
    (payload.object || {})['interface.name'] ||
    (payload.object || {})['object.name'],
  rediscoverJob: payload['rediscover.job'],
  message:
    payload.status === Constants.EVENT_FAIL_STATUS ? payload.message : '',
  objectId: payload['metric.object'],
  object: payload.object,
  status: payload.status,
  error: payload.error,
  metricName: payload['metric.name'],
  autoProvisioned:
    (payload?.['scheduler.context'] || {})['auto.provision.status'] === 'yes',
  ...getAdditionalProps(payload),
})

export function transformResult(payload, eventContext) {
  const type = payload['rediscover.job']
  const tabType = Invert(REDISCOVER_TYPE_NAME_MAPPING)[type]
  const fn = tabType === 'application' ? applicationTransformer : transformer
  const instance = fn(payload)
  if (tabType === 'process') {
    const instanceNamePart = instance.name.split('|')
    instance.name = instanceNamePart[0]
    instance.hint = instanceNamePart.filter((i, index) => index !== 0).join('|')
  }
  if (tabType === 'wanLink') {
    instance.objectName = eventContext?.['object.name']
  }
  if (tabType === 'container') {
    instance.objectName = eventContext?.['object.name']
    instance.tags = eventContext?.['instance.tags']
  }
  return instance
}

export function runApplicationDiscoveryApi(object, discoveryData) {
  return api.post(`/settings/discoveries/application/run`, {
    'metric.type': object.application,
    'event.scheduler': object.eventScheduler,
    'metric.name': object.metricName,
    'object.name': object.objectName,
    'discovery.target': Array.isArray(object.objectId)
      ? object.objectId
      : [object.objectId],
    'discovery.credential.profiles': discoveryData.credentials,
    'discovery.method': discoveryData.discoveryByAgent ? 'AGENT' : 'REMOTE',
    'discovery.context': {
      ...discoveryData.context,
      object: object._object,
    },
    objects: object.objects,
  })
}
export async function provisionInstanceApi(instance, type) {
  return provisionDiscoveryMonitorsApi({
    monitors: [
      {
        context: {
          id:
            instance.rediscoverJob === 'Cloud' ||
            instance.rediscoverJob === 'Container'
              ? instance.objectId
              : instance.metricId,
          'rediscover.job': instance.rediscoverJob,
          'event.scheduler': instance.eventScheduler,
          'metric.name': instance.metricName,
          'object.name': instance.objectName,
          object: instance.object,
          ...(type === 'wanLink'
            ? {
                'object.credential.profile': instance.credentialProfile,
                'metric.plugin': instance.metricPlugin,
                'metric.type': instance.metricType,
                'object.name': instance.objectName,
              }
            : {}),
          ...(type === 'container'
            ? {
                'discovery.context': {
                  'discover.available.containers': 'yes',
                  port: instance.port || 2375,
                },
                'object.credential.profile': instance.credentialProfile,
                'metric.plugin': 'dockercontainer',
                'metric.type': 'Linux',
                object: {
                  'container.id': instance.object['container.id'],
                  'object.name': instance.object['object.name'],
                  'object.type': instance.object['object.type'],
                  'instance.tags': instance.tags,
                },
              }
            : {}),
        },
      },
    ],
    guid: instance.guid,
  })
}

export function getRediscoverScheduleResultsApi(id) {
  return api
    .get(`/settings/schedulers/${id}`, {
      params: { filter: { 'scheduler.job.type': 'Rediscover' } },
    })
    .then(({ result }) => {
      const context = result['scheduler.context']
      return {
        id: result.id,
        rediscoverJob: context['rediscover.job'],
        result: (result.result || []).map((i) => transformResult(i)),
      }
    })
}

export function transformWANLinkForServer(item) {
  let config = {
    // 'wan.link.type': 'IPSLA',
    'rediscover.job': 'WAN Link',
    'discovery.credential.profiles': item.credentials,
    'operation.frequency': parseInt(item.frequency),
    'operation.timeout': parseInt(item.operationTimeout * 1000),

    ...(item.payload ? { 'operation.payload': item.payload } : {}),
    ...(item.serviceType ? { 'operation.service.type': item.serviceType } : {}),
    ...(item.timeout ? { timeout: parseInt(item.timeout) } : {}),
    ...(item.wanLinkConfigurationType === 'singleWANLink'
      ? {
          'discovery.object.type': 'ip.address',
          'object.type': item.wanProbe,
        }
      : {
          'discovery.object.type': 'csv',
          'discovery.target': item?.csv?.[0]?.result,
        }),
    ...(item.wanLinkConfigurationType === 'singleWANLink'
      ? {
          object: {
            'internet.service.provider': Capitalize(
              item.internetServiceProvider
            ),
            'interface.name': item?.sourceInterface?.name,
            'interface.ip.address': item?.sourceInterface?.ipAddress,
            ...(item.sourceRouterLocation
              ? { 'source.router.location': item.sourceRouterLocation }
              : {}),
            ...(item.destinationIP
              ? { 'destination.ip.address': item.destinationIP }
              : {}),
            ...(item.destinationRouterLocation
              ? {
                  'destination.router.location': item.destinationRouterLocation,
                }
              : {}),
          },
        }
      : {
          // 'rediscovery.object.target': item.csv,
        }),
  }
  return config
}

export function transformContainerForServer(item) {
  let config = {
    'rediscover.job': 'Container',
    'discovery.credential.profiles': item.credentials,
    'instance.tags': item.tags,
    'metric.plugin': containerRuntimeOptionMap[item.containerRuntime],
    'discovery.context': {
      port: parseInt(item.port) || 2375,
      'discover.available.containers': item.discoverAllContainers
        ? 'yes'
        : 'no',
    },
  }
  return config
}
