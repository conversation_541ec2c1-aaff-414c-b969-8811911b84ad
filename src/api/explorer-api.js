import api from '@api'

import {
  convertTimeLineForServer,
  convertTimeLine,
} from '@/src/components/widgets/helper'

export const EXPLORER_TYPE = {
  METRIC: 'metric',
  LOG: 'log',
  TOPOLOGY: 'topology',
}

/**
 * Generic create function for explorers
 */
export function createExplorerApi(context, explorerType) {
  const transformedContext = transformExplorerContextForServer(
    context,
    explorerType
  )
  return api
    .post('/visualization/explorers', transformedContext)
    .then((response) =>
      transformExplorerForClient({ id: response.id, ...transformedContext })
    )
}

/**
 * Generic update function for explorers
 */
export function updateExplorerApi(context, explorerType, patch) {
  const transformedContext = transformExplorerContextForServer(
    context,
    explorerType
  )
  return api
    .put(`/visualization/explorers/${context.id}`, patch || transformedContext)
    .then((response) =>
      transformExplorerForClient({ id: response.id, ...transformedContext })
    )
}

/**
 * Generic get function for explorers
 */
export function getExplorerSavedViewApi(monitor, explorerType) {
  return api
    .get('/visualization/explorers', {
      params: {
        ...(explorerType === 'metric'
          ? {
              filter: monitor
                ? {
                    'explorer.object.id': monitor.id,
                    'explorer.object.type': monitor.type,
                    'explorer.type': explorerType,
                  }
                : {
                    'explorer.global.view.enabled': 'yes',
                    'explorer.type': explorerType,
                  },
            }
          : {
              filter: {
                'explorer.type': explorerType,
              },
            }),
      },
    })
    .then((response) =>
      explorerType === EXPLORER_TYPE.TOPOLOGY
        ? response.result || []
        : (response.result || []).map((item) =>
            transformExplorerForClient(item, explorerType)
          )
    )
}

/**
 * Generic delete function for explorers
 */
export function deleteExplorerApi(id, explorerType) {
  return api.delete('/visualization/explorers/' + id, {
    params: {
      filter: {
        'explorer.type': explorerType,
      },
    },
  })
}

/**
 * Generic transformation for explorer context for server
 * Handles 'metric', 'log', and 'topology' explorer types
 */
export function transformExplorerContextForServer(
  context,
  explorerType = EXPLORER_TYPE.METRIC
) {
  return {
    'explorer.type': explorerType || 'metric',
    'explorer.name': context.name,
    'explorer.description': context.description,
    'explorer.access.type': context.security,

    ...(context.users ? { users: context.users } : {}),
    'explorer.global.view.enabled': !context.forDeviceTemplate ? 'yes' : 'no',

    'explorer.object.id':
      context.forDeviceTemplate &&
      context.objectId &&
      context.applyTo === 'object'
        ? context.objectId
        : -1,

    'explorer.object.type':
      context.forDeviceTemplate && context.objectType
        ? context.objectType
        : undefined,

    'explorer.context': {
      ...(explorerType === 'metric'
        ? {
            'explorer.charts': (context.selectedCharts || []).map((c) => {
              return {
                metric: c.key,
                'object.id': c.objectId,
                ...(c.overlayOptions
                  ? {
                      overlayOptions: {
                        type: c.overlayOptions.type,
                        algorithm: c.overlayOptions.algorithm,
                        ...(c.overlayOptions.deviation
                          ? { deviation: c.overlayOptions.deviation }
                          : {}),
                        'overlay.alert': c.overlayOptions.overlayAlert,
                      },
                    }
                  : {}),
                'statistical.func': c.selectedArithmeticOperation,
                'instance.type': c.instanceType,
                instance: c.instance,
              }
            }),

            'explorer.preference': {
              ...(context.timeline
                ? {
                    'visualization.timeline': convertTimeLineForServer(
                      context.timeline
                    ),
                  }
                : {}),
              ...(context.chartType
                ? {
                    'visualization.type': context.chartType,
                  }
                : {}),
              ...(context.granularity
                ? {
                    'visualization.granularity': {
                      value: context.granularity.value,
                      unit: context.granularity.unit,
                    },
                  }
                : {}),
              ...(context.granularity && context.granularity.queryType
                ? {
                    'query.type': context.granularity.queryType,
                  }
                : {}),
            },

            'explorer.merged.counter.map':
              context.metricExplorerMergedCounterContext,
          }
        : {}),

      ...(explorerType === 'log'
        ? {
            ...(context.timeline
              ? {
                  'visualization.timeline': convertTimeLineForServer(
                    context.timeline
                  ),
                }
              : {}),

            'explorer.filters': context.filters,
          }
        : {}),

      ...(explorerType === 'topology'
        ? {
            // Add topology-specific transformation here as needed

            ...transformExcludeIPTypeOptionForServer(context),
          }
        : {}),
    },

    ...(explorerType === 'topology'
      ? {
          // Add topology-specific transformation here as needed
          // Example placeholder:
          ...(context
            ? {
                'explorer.group.name': context.viewGroup,
                'explorer.access.type': context.security,
                'explorer.name': context.name,
                'explorer.users': context.users,
                'entity.id': context?.selectedTarget?.id,
              }
            : {}),
        }
      : {}),

    // 'explorer.entity': context.applyTo,
  }
}

/**
 * Generic transformation for explorer server response to client format
 * Handles 'metric', 'log', and 'topology' explorer types
 */
export function transformExplorerForClient(
  serverData = {},
  explorerType = EXPLORER_TYPE.METRIC
) {
  const preferences = serverData['explorer.context']?.['explorer.preference']

  // For 'metric' type, use the existing logic
  if (explorerType === EXPLORER_TYPE.METRIC) {
    const metricExplorerCharts =
      serverData['explorer.context']?.['explorer.charts']
    const transformedData = {
      name: serverData['explorer.name'],
      description: serverData['explorer.description'],
      security: serverData['explorer.access.type'],
      id: serverData.id || serverData['id'],
      globalView: serverData['explorer.global.view.enabled'],
      forDeviceTemplate: serverData['explorer.global.view.enabled'] === 'no',
      metricExplorerMergedCounterContext:
        serverData['explorer.context']?.['explorer.merged.counter.map'],
    }

    if (serverData['explorer.context']?.['explorer.filters']) {
      transformedData.filters =
        serverData['explorer.context']['explorer.filters']
    }

    if (serverData['explorer.object.type']) {
      transformedData.objectType = serverData['explorer.object.type']
    }

    transformedData.applyTo =
      serverData['explorer.global.view.enabled'] === 'no' &&
      serverData['explorer.object.id'] === -1
        ? 'type'
        : 'object'

    if (serverData.users) {
      transformedData.users = serverData.users
    }

    if (preferences) {
      if (preferences['visualization.timeline']) {
        transformedData.timeline = convertTimeLine(
          preferences['visualization.timeline']
        )
      }
      if (preferences['visualization.type']) {
        transformedData.chartType = preferences['visualization.type']
      }
      if (preferences['visualization.granularity']) {
        transformedData.granularity = {
          value: preferences['visualization.granularity'].value,
          unit: preferences['visualization.granularity'].unit,
        }
      }
      transformedData.granularity = transformedData.granularity || {}
      transformedData.granularity.queryType =
        preferences['query.type'] || 'aggregation'
    }

    const selectedCharts = Array.isArray(metricExplorerCharts)
      ? metricExplorerCharts.map((context) => {
          const chart = {
            counterRawName: context.metric,
            objectId: context['object.id'],
            selectedArithmeticOperation: context['statistical.func'],
            instanceType: context['instance.type'],
            instance: context.instance,
          }
          if (context.overlayOptions) {
            chart.overlayOptions = {
              type: context.overlayOptions.type,
              algorithm: context.overlayOptions.algorithm,
              overlayAlert: context.overlayOptions['overlay.alert'],
            }
            if (context.overlayOptions.deviation !== undefined) {
              chart.overlayOptions.deviation = context.overlayOptions.deviation
            }
          }
          return chart
        })
      : []
    transformedData.selectedCharts = selectedCharts
    return transformedData
  }

  // For 'log' type
  if (explorerType === EXPLORER_TYPE.LOG) {
    const transformedData = {
      name: serverData['explorer.name'],
      description: serverData['explorer.description'],
      security: serverData['explorer.access.type'],
      id: serverData.id || serverData['id'],
      globalView: serverData['explorer.global.view.enabled'],
      forDeviceTemplate: serverData['explorer.global.view.enabled'] === 'no',
      filters: serverData['explorer.context']?.['explorer.filters'],
    }
    if (preferences && preferences['visualization.timeline']) {
      transformedData.timeline = preferences['visualization.timeline']
    }
    return transformedData
  }

  // For 'topology' type (placeholder, extend as needed)
  if (explorerType === EXPLORER_TYPE.TOPOLOGY) {
    const transformedData = {
      name: serverData['explorer.name'],
      id: serverData.id || serverData['id'],
      viewGroup: serverData['explorer.group.name'],
      globalView: serverData['explorer.global.view.enabled'],
      security: serverData['explorer.access.type'],
      ...transformExcludeIPTypeOptionForClient(serverData),
    }
    if (serverData.users) {
      transformedData.users = serverData.users
    }
    return transformedData
  }

  // Default fallback
  return serverData
}

function transformExcludeIPTypeOptionForServer(option) {
  return {
    ...(option.includeExcludeTargetType
      ? {
          'topology.filter.target.type': option.includeExcludeTargetType,
          'topology.filter.targets': option.includeExcludeTargets,
        }
      : {}),
  }
}

export function transformExcludeIPTypeOptionForClient(schedule) {
  return {
    includeOrExclude:
      (schedule['topology.filter.target.type'] || '').indexOf('exclude') >= 0
        ? 'exclude'
        : 'include',
    ...(schedule['topology.filter.target.type']
      ? {
          includeExcludeTargetType: schedule['topology.filter.target.type'],
          includeExcludeTargets: schedule['topology.filter.targets'],
        }
      : {}),
  }
}
