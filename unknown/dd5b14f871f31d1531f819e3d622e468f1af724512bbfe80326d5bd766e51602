import api from '@api'
import Capitalize from 'lodash/capitalize'
import CloneDeep from 'lodash/cloneDeep'
import Omit from 'lodash/omit'
import GroupBy from 'lodash/groupBy'
import Reverse from 'lodash/reverse'
import Config from '../config'
import { objectDBWorker } from '@/src/workers'
import {
  getWidgetResponseApi,
  buildWidgetContext,
  makeCounter,
} from '@utils/socket-event-as-api'
import {
  WidgetTypeConstants,
  OPERATOR_MAP,
} from '@/src/components/widgets/constants'
import { transformWidget } from '@/src/components/widgets/translator'
import { isUnitConvertible } from '@/src/utils/unit-checker'
import applyUnit from '@/src/utils/unit-applier'
import buildWidgetResult from '@/src/components/widgets/result-builder'
import { generateId } from '@/src/utils/id'
// import { SEVERITY_MAP } from '@/src/data/monitor'
import {
  getAllFlowPoliciesApi,
  getAllLogPoliciesApi,
  getAllTrapPoliciesApi,
  getAllPoliciesApi,
  getPolicyApi,
  getAllNetroutePoliciesApi,
} from '../../settings/policy-settings/policy-api'

import { transformPolicyForList } from '../../settings/policy-settings/helpers/policy'

import PolicyConfig from '../../settings/policy-settings/config'
import { convertTimeLineOfPolicy } from '@/src/components/widgets/helper'
import duration from '@/src/filters/duration'
import Constants from '@constants'
import Datetime from '@src/filters/datetime'

export const POLICY_TYPE_COLOR = {
  threshold: `rgb(117,121,231)`,
  baseline: 'rgb(154,179,245)',
  anomaly: 'rgb(163,216,244)',
  suddenshift: 'rgb(97,85,166)',
  outliner: 'rgb(255,171,225)',
  forecast: 'rgb(166,133,226)',
  availability: 'rgba(210,109,235)',
}

const PLOAT_BAND_COLORS = {
  critical: 'rgba(240,78,62,.2)',
  major: 'rgba(245,133,24,.2)',
  warning: 'rgba(245,188,24,.2)',
}
const SEVERITY_TO_CONSIDER_IN_BANDS = ['critical', 'major', 'warning']
const SEVERITY_TO_CONSIDER = [
  Constants.DOWN,
  Constants.UNREACHABLE,
  Constants.CRITICAL,
  Constants.MAJOR,
  Constants.WARNING,
  Constants.CLEAR,
]
export const ALERT_TYEP = {
  SCHEDULED: 'Scheduled',
  REAL_TIME: 'Real Time',
}
export const COLUMNS = {
  metric: [
    {
      key: 'policy',
      name: 'Alert',
      searchable: true,
      sortable: false,
      cellRender: 'policy',
      disable: true,
      minWidth: '150px',
    },
    {
      key: 'policyType',
      name: 'Type',
      searchable: true,
      sortable: false,
      cellRender: 'type',
      disable: true,
      minWidth: '150px',
    },
    {
      key: 'monitor',
      name: 'Monitor',
      searchable: true,
      sortable: false,
      cellRender: 'monitor',
      contextKey: 'monitorContext',
      searchKey: 'monitorDisplay',
      sortKey: 'monitorDisplay',
      disable: true,
      minWidth: '100px',
    },
    {
      key: 'metric',
      name: 'Metric',
      searchable: true,
      sortable: false,
      hidden: true,
      minWidth: '100px',
    },
    {
      key: 'instance',
      name: 'Instance',
      searchable: true,
      sortable: false,
      minWidth: '100px',
    },
    {
      key: 'incidentDetails',
      name: 'Incident Details',
      searchable: true,
      sortable: false,
      minWidth: '100px',
    },

    {
      key: 'lastSeen',
      name: 'Last Seen',
      searchable: false,
      sortable: true,
      minWidth: '100px',
    },
    {
      key: 'tag',
      name: 'Tag',
      searchable: true,
      sortable: false,
      cellRender: 'tag',
      hidden: true,
      minWidth: '40px',
      searchKey: 'tagsStr',
      sortKey: 'tagsStr',
    },
    // {
    //   key: 'group',
    //   name: 'Group',
    //   searchable: true,
    //   sortable: false,
    //   hidden: true,
    // },
    {
      key: 'thresholdValue',
      name: 'Value',
      searchable: true,
      searchKey: 'value',
      sortable: false,
      cellRender: 'thresholdValue',
      exportKey: 'value',
      width: '150px',
      minWidth: '40px',
    },
    {
      key: 'duration',
      name: 'Duration',
      searchable: true,
      sortable: true,
      cellRender: 'duration',
      width: '220px',
      minWidth: '80px',
      exportType: 'duration',
      searchKey: 'durationInWords',
      sortKey: 'duration',
    },
    // {
    //   key: 'counts',
    //   name: '24 Hours Count',
    //   searchable: false,
    //   sortable: false,
    //   sortKey: 'totalCount',
    //   cellRender: 'counts',
    //   width: '100px',
    //   minWidth: '80px',
    //   hidden: true,
    // },
    {
      key: 'acknowledged',
      name: 'Acknowledged',
      searchable: false,
      sortable: false,
      width: '120px',
      align: 'center',
      exportType: 'boolean',
      minWidth: '80px',
    },
    {
      key: 'acknowledgedByUser',
      name: 'Acknowledged By',
      searchable: false,
      sortable: false,
      width: '160px',
      align: 'center',
      minWidth: '80px',
    },
    {
      key: 'note',
      name: 'Note',
      searchable: true,
      sortable: false,
      minWidth: '100px',
    },
    {
      key: 'actions',
      name: 'Actions',
      align: 'right',
      minwidth: '70px',
      cellRender: 'actions',
    },
  ],
  log: [
    {
      key: 'policy',
      name: 'Alert',
      searchable: true,
      sortable: false,
      cellRender: 'policy',
      disable: true,
      minWidth: '150px',
    },
    {
      key: 'policyType',
      name: 'Type',
      searchable: true,
      sortable: false,
      cellRender: 'type',
      disable: true,
      minWidth: '150px',
    },
    {
      key: 'alertType',
      name: 'Alert Type',
      searchable: true,
      sortable: false,
      cellRender: 'type',
      disable: true,
      minWidth: '100px',
    },
    {
      key: 'metric',
      name: 'Metric',
      searchable: true,
      sortable: false,
      minWidth: '80px',
    },
    {
      key: 'tag',
      name: 'Tag',
      searchable: true,
      sortable: false,
      cellRender: 'tag',
      hidden: true,
      minWidth: '50px',
    },
    {
      key: 'count',
      name: 'Count',
      searchable: false,
      sortable: true,
      minWidth: '100px',
    },
    {
      key: 'firstSeen',
      name: 'First Seen',
      searchable: false,
      sortable: true,
      exportType: 'datetime',
      minWidth: '100px',
    },
    {
      key: 'lastSeen',
      name: 'Last Seen',
      exportType: 'datetime',
      searchable: false,
      sortable: true,
      minWidth: '100px',
    },
    // {
    //   key: 'acknowledged',
    //   name: 'Acknowledged',
    //   searchable: false,
    //   sortable: false,
    //   width: '120px',
    //   exportType: 'boolean',
    //   align: 'center',
    // },
    // {
    //   key: 'note',
    //   name: 'Note',
    //   searchable: true,
    //   sortable: false,
    //   minWidth: '100px',
    // },
    {
      key: 'actions',
      name: 'Actions',
      align: 'right',
      width: '70px',
      cellRender: 'actions',
    },
  ],
  flow: [
    {
      key: 'policy',
      name: 'Alert',
      searchable: true,
      sortable: false,
      cellRender: 'policy',
      disable: true,
      minWidth: '150px',
    },
    {
      key: 'policyType',
      name: 'Type',
      searchable: true,
      sortable: false,
      cellRender: 'type',
      disable: true,
      minWidth: '150px',
    },
    {
      key: 'alertType',
      name: 'Alert Type',
      searchable: true,
      sortable: false,
      cellRender: 'type',
      disable: true,
      minWidth: '150px',
    },
    {
      key: 'metric',
      name: 'Metric',
      searchable: true,
      sortable: false,
      minWidth: '100px',
    },
    {
      key: 'tag',
      name: 'Tag',
      searchable: true,
      sortable: false,
      cellRender: 'tag',
      hidden: true,
      minWidth: '100px',
    },
    {
      key: 'count',
      name: 'Count',
      searchable: false,
      sortable: true,
      minWidth: '100px',
    },
    {
      key: 'firstSeen',
      name: 'First Seen',
      searchable: false,
      exportType: 'datetime',
      sortable: true,
      minWidth: '80px',
    },
    {
      key: 'lastSeen',
      name: 'Last Seen',
      searchable: false,
      exportType: 'datetime',
      sortable: true,
      minWidth: '80px',
    },
    // {
    //   key: 'acknowledged',
    //   name: 'Acknowledged',
    //   searchable: false,
    //   sortable: false,
    //   width: '120px',
    //   align: 'center',
    //   exportType: 'boolean',
    //   minWidth: '80px',
    // },
    // {
    //   key: 'note',
    //   name: 'Note',
    //   searchable: true,
    //   sortable: false,
    //   minWidth: '100px',
    // },
    {
      key: 'actions',
      name: 'Actions',
      align: 'right',
      width: '70px',
      cellRender: 'actions',
    },
  ],
  trap: [
    {
      key: 'policy',
      name: 'Alert',
      searchable: true,
      sortable: false,
      cellRender: 'policy',
      disable: true,
      minWidth: '150px',
    },

    {
      key: 'eventSource',
      name: 'Event Source',
      searchable: true,
      sortable: false,
      disable: true,
      minWidth: '150px',
    },
    {
      key: 'policyType',
      name: 'Type',
      searchable: true,
      sortable: false,
      cellRender: 'type',
      disable: true,
      minWidth: '150px',
    },
    {
      key: 'metric',
      name: 'Counter',
      searchable: true,
      sortable: false,
      cellRender: 'metric',
      disable: false,
      minWidth: '100px',
      exportKey: 'metric',
    },
    {
      key: 'count',
      name: 'Count',
      searchable: false,
      sortable: true,
      minWidth: '100px',
    },
    {
      key: 'tag',
      name: 'Tag',
      searchable: true,
      sortable: false,
      cellRender: 'tag',
      hidden: true,
      minWidth: '40px',
      searchKey: 'tagsStr',
      sortKey: 'tagsStr',
    },
    {
      key: 'firstSeen',
      name: 'First Seen',
      searchable: false,
      exportType: 'datetime',
      sortable: true,
      minWidth: '80px',
    },
    {
      key: 'lastSeen',
      name: 'Last Seen',
      searchable: false,
      exportType: 'datetime',
      sortable: true,
      minWidth: '80px',
    },
    // {
    //   key: 'acknowledged',
    //   name: 'Acknowledged',
    //   searchable: false,
    //   sortable: false,
    //   width: '120px',
    //   align: 'center',
    //   exportType: 'boolean',
    //   minWidth: '80px',
    // },
    // {
    //   key: 'note',
    //   name: 'Note',
    //   searchable: true,
    //   sortable: false,
    //   minWidth: '100px',
    // },
    {
      key: 'actions',
      name: 'Actions',
      align: 'right',
      width: '70px',
      cellRender: 'actions',
    },
  ],

  'netroute-hop-to-hop': [
    {
      key: 'policy',
      name: 'Alert',
      searchable: true,
      sortable: false,
      cellRender: 'policy',
      disable: true,
      minWidth: '150px',
    },
    {
      key: 'policyType',
      name: 'Type',
      searchable: true,
      sortable: false,
      cellRender: 'type',
      disable: true,
      minWidth: '150px',
    },
    {
      key: 'netroute_id',
      name: 'NetRoute',
      contextKey: 'netRouteServerContext',
      searchable: true,
      sortable: false,
      disable: true,
      minWidth: '100px',
      cellRender: 'netroute-path',
    },
    {
      key: 'count',
      name: 'Count',
      searchable: false,
      sortable: true,
      minWidth: '100px',
    },
    {
      key: 'firstSeen',
      name: 'First Seen',
      searchable: false,
      sortable: true,
      exportType: 'datetime',
      cellRender: 'netRouteFirstSeen',
      minWidth: '100px',
    },
    {
      key: 'netRouteLastSeen',
      name: 'Last Seen',
      exportType: 'datetime',
      searchable: false,
      sortable: true,
      cellRender: 'netRouteLastSeen',
      minWidth: '100px',
    },
    {
      key: 'actions',
      name: 'Actions',
      align: 'right',
      minwidth: '70px',
      cellRender: 'actions',
    },
  ],
  'netroute-source-to-destination': [
    {
      key: 'policy',
      name: 'Alert',
      searchable: true,
      sortable: false,
      cellRender: 'policy',
      disable: true,
      minWidth: '150px',
    },
    {
      key: 'policyType',
      name: 'Type',
      searchable: true,
      sortable: false,
      cellRender: 'type',
      disable: true,
      minWidth: '150px',
    },
    {
      key: 'netroute_id',
      name: 'NetRoute',
      contextKey: 'netRouteServerContext',
      searchable: true,
      sortable: false,
      disable: true,
      minWidth: '100px',
      cellRender: 'netroute-path',
    },
    {
      key: 'thresholdValue',
      name: 'Value',
      searchable: true,
      searchKey: 'value',
      sortable: false,
      cellRender: 'thresholdValue',
      exportKey: 'value',
      width: '150px',
      minWidth: '40px',
    },
    {
      key: 'duration',
      name: 'Duration',
      searchable: true,
      sortable: true,
      cellRender: 'duration',
      width: '220px',
      minWidth: '80px',
      exportType: 'duration',
      searchKey: 'durationInWords',
      sortKey: 'duration',
    },
    {
      key: 'acknowledged',
      name: 'Acknowledged',
      searchable: false,
      sortable: false,
      width: '120px',
      align: 'center',
      exportType: 'boolean',
      minWidth: '80px',
    },
    {
      key: 'acknowledgedByUser',
      name: 'Acknowledged By',
      searchable: false,
      sortable: false,
      width: '160px',
      align: 'center',
      minWidth: '80px',
    },
    {
      key: 'note',
      name: 'Note',
      searchable: true,
      sortable: false,
      minWidth: '100px',
    },
    {
      key: 'actions',
      name: 'Actions',
      align: 'right',
      minwidth: '70px',
      cellRender: 'actions',
    },
  ],
}

export function getColumns(tab, viewType, category) {
  if (tab.toLowerCase().indexOf('netroute') >= 0) {
    // tab = 'netroute'
  }
  let columns = COLUMNS[tab]

  if (viewType !== 'live' && tab === 'metric') {
    columns = columns.filter((c) => !['counts', 'actions'].includes(c.key))
  }

  if (tab === 'metric' && category === Config.CORRELATED_POLICIES) {
    columns = [
      ...columns.slice(0, 8),
      {
        key: 'correlatedKeys',
        name: 'Correlated Alerts',
        searchable: true,
        sortable: false,
        hidden: true,
        minWidth: '120px',
      },
      ...columns.slice(9),
    ]
  }

  return CloneDeep(columns)
}

export function getAlertCountData(timeline, groupCategory, view, options = {}) {
  return getWidgetResponseApi(
    buildWidgetContext({
      groupCategory:
        groupCategory === Constants.NETROUTE
          ? options.netrouteType === Constants.SOURCE_TO_DESTINATION
            ? 'netroute.metric'
            : 'netroute.event'
          : groupCategory,
      groupType:
        groupCategory === 'metric'
          ? view === 'live'
            ? 'policy.stream'
            : 'policy.flap'
          : groupCategory === Constants.NETROUTE &&
            options.netrouteType === Constants.SOURCE_TO_DESTINATION
          ? 'policy.stream'
          : 'policy',
      timeline: timeline,
      counters: [makeCounter('severity', 'count')],
      category: WidgetTypeConstants.GRID,
      widgetType: WidgetTypeConstants.GRID,
      resultBy: [
        'severity',
        ...(groupCategory === 'metric' ? ['object.category'] : []),
      ],
    }).generateWidgetDefinition()
  )
}

export function getHistoricChart(
  timeline,
  groupCategory,
  severities,
  resultBy = ['severity'],
  options = {}
) {
  return getWidgetResponseApi(
    buildWidgetContext({
      groupType:
        groupCategory === 'metric' ||
        options.netrouteType === Constants.SOURCE_TO_DESTINATION
          ? 'policy.flap'
          : 'policy',
      category: WidgetTypeConstants.CHART,
      widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,
      groupCategory:
        groupCategory === Constants.NETROUTE
          ? options.netrouteType === Constants.SOURCE_TO_DESTINATION
            ? 'netroute.metric'
            : 'netroute.event'
          : groupCategory,
      counters: [makeCounter('severity', 'count')],
      resultBy,
      timeline: timeline,
      granularity: {
        value: 1,
        unit: 'h',
      },
    })
      .appendToGroup(groupCategory ? 'policy.flap' : 'policy', {
        severity: severities,
        excludeSeverityResultBy: true,
      })
      .appendToGroup('policy', {
        ...(groupCategory === 'trap' && severities
          ? { severity: severities }
          : {}),
      })
      .generateWidgetDefinition(),
    { fullResponse: true }
  ).then(async (response) => {
    const widgetContext = transformWidget(Omit(response, ['result']))
    return buildWidgetResult(widgetContext, response, {
      timeRange: timeline,
    })
  })
}

export function getAlertBySeverityChart(timeline, groupCategory, severities) {
  return getWidgetResponseApi(
    buildWidgetContext({
      groupType: groupCategory === 'metric' ? 'policy.flap' : 'policy',
      category: WidgetTypeConstants.TOPN,
      widgetType: WidgetTypeConstants.HORIZONTAL_BAR,
      groupCategory: groupCategory,
      counters: [makeCounter('severity', 'count')],
      resultBy: ['severity'],
      timeline: timeline,
    })
      .appendToGroup(groupCategory ? 'policy.flap' : 'policy', {
        severity: severities,
      })
      .generateWidgetDefinition(),
    { fullResponse: true }
  ).then(async (response) => {
    const widgetContext = transformWidget(Omit(response, ['result']))
    return buildWidgetResult(widgetContext, response, {
      timeRange: timeline,
    })
  })
}

export function getColumnChartByType(
  timeline,
  groupCategory,
  severities,
  view,
  options = {}
) {
  return getWidgetResponseApi(
    buildWidgetContext({
      timeline,
      groupType:
        groupCategory === 'metric'
          ? view === 'live'
            ? 'policy.stream'
            : 'policy.flap'
          : groupCategory === Constants.NETROUTE &&
            options.netrouteType === Constants.SOURCE_TO_DESTINATION
          ? 'policy.stream'
          : 'policy',
      category: WidgetTypeConstants.GRID,
      widgetType: WidgetTypeConstants.GRID,
      groupCategory:
        groupCategory === Constants.NETROUTE
          ? options.netrouteType === Constants.SOURCE_TO_DESTINATION
            ? 'netroute.metric'
            : 'netroute.event'
          : groupCategory,
      counters: [
        ...(groupCategory === Constants.NETROUTE &&
        options.netrouteType === Constants.SOURCE_TO_DESTINATION
          ? [
              makeCounter('policy.first.trigger.tick', 'count'),
              makeCounter('policy.note', 'count'),
              makeCounter('policy.acknowledge', 'count'),
              makeCounter('policy.last.trigger.tick', 'count'),
              makeCounter('netroute.id', 'count'),
              makeCounter('policy.id', 'count'),
              makeCounter('event.source', 'count'),
            ]
          : [makeCounter('severity', 'count')]),
      ],
      resultBy:
        groupCategory === 'metric' ? ['policy.type', 'severity'] : ['severity'],
    })
      .appendToGroup(groupCategory ? 'policy.stream' : 'policy', {
        severity: severities,
      })
      .appendToGroup('policy', {
        ...(groupCategory === 'trap' && severities
          ? { severity: severities }
          : {}),
      })
      .generateWidgetDefinition()
  ).then((data) => {
    const groups = GroupBy(
      data,
      groupCategory === 'metric' ? 'policy.type' : 'severity'
    )
    return {
      series: severities.map((severity) => {
        return {
          counter: severity.toUpperCase(),
          data: Object.keys(groups).map((policyType) =>
            groups[policyType].reduce(
              (prev, item) =>
                item.severity === severity.toUpperCase()
                  ? prev + (item.count || item['severity.count'] || 0)
                  : prev,
              0
            )
          ),
          stack: 'severity',
          aggr: 'count',
          name: severity.toUpperCase(),
        }
      }),
      groupByColumns: ['policy.type', 'severity'],
      categories: Object.keys(groups),
      columns: [],
    }
  })
}

export function getPieChartByType(
  timeline,
  groupCategory,
  severities,
  view,
  options = {}
) {
  let groupType =
    groupCategory === 'metric'
      ? view === 'live'
        ? 'policy.stream'
        : 'policy.flap'
      : groupCategory === Constants.NETROUTE &&
        options.netrouteType === Constants.SOURCE_TO_DESTINATION
      ? 'policy.stream'
      : 'policy'
  return getWidgetResponseApi(
    buildWidgetContext({
      groupType,
      category: WidgetTypeConstants.GRID,
      widgetType: WidgetTypeConstants.GRID,
      groupCategory:
        groupCategory === Constants.NETROUTE
          ? options.netrouteType === Constants.SOURCE_TO_DESTINATION
            ? 'netroute.metric'
            : 'netroute.event'
          : groupCategory,
      counters: [makeCounter('severity', 'count')],
      resultBy: groupCategory === 'metric' ? ['policy.type'] : ['severity'],
      timeline: timeline,
    })
      .appendToGroup(groupType, {
        ...(groupCategory === 'metric' ||
        groupCategory === 'trap' ||
        groupCategory === Constants.NETROUTE
          ? { severity: severities }
          : {}),
      })
      .generateWidgetDefinition()
  ).then((data) => {
    const groups = GroupBy(
      data,
      groupCategory === 'metric' ? 'policy.type' : 'severity'
    )
    return {
      series: [
        {
          displayTextInCenter: true,
          flipCounterAndSeriesName: true,
          name: '',
          data: Object.keys(groups).map((group) => {
            const seriesColor =
              POLICY_TYPE_COLOR[
                Object.keys(POLICY_TYPE_COLOR).filter((type) =>
                  group.toLocaleLowerCase().includes(type)
                )?.[0]
              ]
            return {
              name: `${group}`,
              y:
                (groups[group][0] || {}).count ||
                (groups[group][0] || {})['severity.count'],

              ...(groupCategory === 'metric' && seriesColor
                ? {
                    color: seriesColor,
                  }
                : {}),
            }
          }),
        },
      ],
      categories: Object.keys(groups),
    }
  })
}

export function getMetricAlertStreamGrid(
  timeline,
  groupCategory,
  view,
  data,
  options
) {
  if (groupCategory.indexOf('netroute') >= 0) {
    if (groupCategory === 'netroute-source-to-destination') {
      return getNetRouteSourceToDestinationGridData(
        timeline,
        'netroute.metric',
        options
      )
    } else {
      return getNetRouteHopToHopGridData(timeline, 'netroute.event', options)
    }
  }
  if (view === 'live') {
    return getLiveStreamGridData(timeline, groupCategory, options)
  } else {
    return getFlapStreamGridData(timeline, groupCategory, data, options)
  }
}

function getNetRouteHopToHopGridData(timeline, groupCategory, options = {}) {
  const context = buildWidgetContext({
    category: WidgetTypeConstants.GRID,
    widgetType: WidgetTypeConstants.GRID,
    groupCategory,
    groupType: 'policy.stream',
    timeline: timeline,
    counters: [
      ...[
        'policy.first.trigger.tick',
        'policy.note',
        'severity',
        'policy.acknowledge',
        'policy.last.trigger.tick',
        'netroute.id',
        'policy.id',
        'policy.type',
        'policy.metric',
        'event.source',
        'policy.tags',
        'netroute.tags',
        'policy.threshold',
      ].map((i) => makeCounter(i, 'last')),
    ],
  })
    .appendToGroup('policy.stream', {
      severity: SEVERITY_TO_CONSIDER,

      ...(options?.ids ? { alertIds: options?.ids } : {}),
    })
    .addGroup('policy')

  context.addCounterToGroup({
    type: 'policy',
    counter: 'severity',
    aggrigateFn: 'count',
  })
  context.addResultBy(['policy.id', 'severity', 'netroute.id'], 'policy')
  context.appendToGroup('policy', {
    join: 'any',
    category: 'netroute.event',
  })
  return getWidgetResponseApi(
    context.generateWidgetDefinition({
      'join.type': 'any',
      'join.columns': ['policy.id', 'netroute.id', 'severity'],
    }),
    {
      fullResponse: true,
      getFinalResultOnly: false,
    }
  ).then((data) => transformLiveStreamData(groupCategory, data))
}

function getNetRouteSourceToDestinationGridData(
  timeline,
  groupCategory,
  options = {}
) {
  const context = buildWidgetContext({
    category: WidgetTypeConstants.GRID,
    widgetType: WidgetTypeConstants.GRID,
    groupCategory,
    groupType: 'policy.stream',
    timeline: timeline,
    counters: [
      makeCounter('severity', 'last'),
      makeCounter('policy.name', 'last'),
      makeCounter('instance', 'last'),
      makeCounter('policy.type', 'last'),
      makeCounter('policy.id', 'last'),
      makeCounter('policy.note', 'last'),
      makeCounter('policy.acknowledge', 'last'),
      makeCounter('policy.first.trigger.tick', 'last'),
      makeCounter('metric', 'last'),
      makeCounter('value', 'last'),
      makeCounter('policy.tags', 'last'),
      makeCounter('duration', 'last'),
      makeCounter('policy.threshold', 'last'),
      makeCounter('ack.id', 'last'),
      makeCounter('event.timestamp', 'last'),
      makeCounter('netroute.id', 'last'),
    ],
  }).appendToGroup('policy.stream', {
    severity: SEVERITY_TO_CONSIDER,

    ...(options?.ids ? { alertIds: options?.ids } : {}),
  })
  return getWidgetResponseApi(context.generateWidgetDefinition(), {
    fullResponse: true,
    getFinalResultOnly: false,
  }).then((data) => transformLiveStreamData(groupCategory, data))
}

function getLiveStreamGridData(timeline, groupCategory, options = {}) {
  const context = buildWidgetContext({
    category: WidgetTypeConstants.GRID,
    widgetType: WidgetTypeConstants.GRID,
    groupCategory,
    groupType: 'policy.stream',
    timeline: timeline,
    ...(groupCategory === 'netroute.event'
      ? { resultBy: ['policy.id', 'severity', 'netroute.id'] }
      : {}),
    counters: [
      makeCounter('severity', 'last'),
      makeCounter('policy.name', 'last'),
      makeCounter('instance', 'last'),
      makeCounter('policy.type', 'last'),
      makeCounter('policy.id', 'last'),
      makeCounter('policy.note', 'last'),
      makeCounter('policy.acknowledge', 'last'),
      makeCounter('policy.first.trigger.tick', 'last'),
      makeCounter('metric', 'last'),
      makeCounter('value', 'last'),
      makeCounter('policy.tags', 'last'),
      makeCounter('duration', 'last'),
      ...(groupCategory === 'netroute.metric' ||
      groupCategory === 'netroute.event'
        ? []
        : [
            makeCounter('correlation.probe'),
            makeCounter('correlated.unreachable.objects'),
          ]),
      makeCounter('policy.threshold', 'last'),
      makeCounter('ack.id', 'last'),
      makeCounter('event.timestamp', 'last'),
      ...(groupCategory === 'netroute.metric' ||
      groupCategory === 'netroute.event'
        ? [makeCounter('netroute.id', 'last')]
        : [
            makeCounter('object.id', 'last'),
            makeCounter('object.category', 'last'),
          ]),
    ],
    ...(options.instances
      ? {
          preFilters: {
            condition: 'and',
            inclusion: 'include',
            conditions: [
              {
                operand: 'interface~instance.name',
                operator: 'in',
                value: options.instances,
              },
            ],
          },
        }
      : {}),
  }).appendToGroup('policy.stream', {
    severity: SEVERITY_TO_CONSIDER,

    ...(options?.ids ? { alertIds: options?.ids } : {}),
  })
  return getWidgetResponseApi(context.generateWidgetDefinition(), {
    fullResponse: true,
    getFinalResultOnly: false,
  }).then((data) => transformLiveStreamData(groupCategory, data))
}

function getFlapStreamGridData(timeline, groupCategory, previousData, options) {
  const groupType = options.nextRequestGroup

  const counters =
    groupType === 'policy.flap'
      ? [
          makeCounter('duration', '__NONE__'),
          makeCounter('object.id', '__NONE__'),
          makeCounter('instance', '__NONE__'),
          makeCounter('severity', '__NONE__'),
          makeCounter('policy.id', '__NONE__'),
          makeCounter('policy.type', '__NONE__'),
          makeCounter('value', '__NONE__'),
          makeCounter('policy.threshold', '__NONE__'),
        ]
      : groupType === 'policy.stream'
      ? [
          makeCounter('instance', 'last'),
          makeCounter('policy.id', 'last'),
          makeCounter('policy.first.trigger.tick', 'last'),
          makeCounter('object.id', 'last'),
        ]
      : [makeCounter('*', 'last')]
  const context = buildWidgetContext({
    category: WidgetTypeConstants.CHART,
    widgetType: WidgetTypeConstants.AREA,
    groupCategory,
    groupType: groupType === 'policy.flap' ? 'event.history' : groupType,
    timeline: timeline,
    counters: counters,
  })
  return getWidgetResponseApi(
    {
      ...context.generateWidgetDefinition(),
      ...(options.queryId ? { 'query.id': options.queryId } : {}),
    },
    {
      fullResponse: true,
      getFinalResultOnly: false,
      ...(groupType === 'policy.flap' ? { checkWidgetProgress: false } : {}),
    }
  ).then((data) => {
    return transformFlapStreamDataGroupVise(
      groupCategory,
      data,
      previousData,
      options
    )
  })
}

export async function transformAlert(row) {
  const value =
    isUnitConvertible(row['metric']) && /^\d+(.\d+)?$/.test(row['value'])
      ? applyUnit(row['metric'], +row['value'])
      : row['value']

  let policyData = await getPolicyApi(row['policy.id'], 'metric', null)

  const policySeverity = policyData.severity
  return {
    id: generateId(),
    message: `${
      row['instance']
        ? `<span title='${row['instance']}'>${row['instance'].substr(0, 20)}${
            row['instance'].length > 20 ? '... ' : ''
          }</span>`
        : ''
    } <span class="font-semibold">${row['metric'].replace(
      /[~^]/g,
      '.'
    )}</span> has entered into <span class="text ${row[
      'severity'
    ].toLowerCase()}">${Capitalize(
      row['severity']
    )}</span> state with value [${value}]`,
    policySeverity,
    triggerCondition: triggerCondition(
      row['policy.type'],
      policySeverity,
      row['value'],
      row['policy.threshold'],
      row['severity'],
      (row['metric'] || '').replace(/[~^]/g, '.')
    ),
    policy: row['policy.name'],
    policyType: row['policy.type'],
    policyId: row['policy.id'],
    groupCategory: 'metric',
    monitor: row['entity.id'],
    counterRawName: row['metric'],
    category: row['object.category'],
    metric: (row['metric'] || '').replace(/[~^]/g, '.'),
    instance: row['instance'] || '',
    severity: row['severity'],
    value,
    duration: row['duration'],
    durationInWords: duration(row['duration']),
    policyThreshold: row['policy.threshold'],
    firstSeen: row['policy.first.trigger.tick'],
    lastSeen: Datetime(row['event.timestamp']),
    netRouteLastSeen: row['policy.last.trigger.tick'],
  }
}

function transformFlapStreamDataGroupVise(
  groupCategory,
  response,
  previousData,
  options
) {
  if (options.nextRequestGroup === 'policy.flap') {
    return transformPolicyFlapData(groupCategory, response, options)
  } else if (options.nextRequestGroup === 'policy.stream') {
    return transformPolicyStreamData(response, previousData, options)
  } else {
    return transformPolicyAcknowledgedData(response, previousData, options)
  }
}

async function transformPolicyFlapData(groupCategory, response, options) {
  let policyData = await getAllPoliciesApi(true)
  const untouchedPolicyData = policyData
  policyData = policyData.reduce(
    (prev, item) => ({ ...prev, [item['id']]: item }),
    {}
  )
  const objectMap = await objectDBWorker.getObjectsAsMap(
    {},
    ['id', 'objectId', 'category'],
    'objectId'
  )
  const data = response.result[WidgetTypeConstants.GRID].data || []
  const queryId = response.result.queryMeta.parentQueryId
  const progress = response.result.queryMeta.progress

  return {
    meta: { queryId, progress, requestGroup: options.nextRequestGroup },
    data: data.map((row) => {
      const policySeverity = untouchedPolicyData.find(
        (policy) => policy.id === row['policy.id.value']
      )?.severity
      const entityId = objectMap[row['object.id.value']]
        ? objectMap[row['object.id.value']].id
        : null
      const category = objectMap[row['object.id.value']]
        ? objectMap[row['object.id.value']].category
        : null
      const policy = policyData[row['policy.id.value']] || {}
      const value =
        isUnitConvertible(row['metric.value']) &&
        /^\d+(.\d+)?$/.test(row['value.value'])
          ? applyUnit(row['metric.value'], +row['value.value'])
          : row['value.value']

      return {
        id: generateId(),
        message: `${
          row['instance.value']
            ? `<span title='${row['instance.value']}'>${row[
                'instance.value'
              ].substr(0, 20)}${
                row['instance.value'].length > 20 ? '... ' : ''
              }</span>`
            : ''
        } <span class="font-semibold">${row['metric.value'].replace(
          /[~^]/g,
          '.'
        )}</span> has entered into <span class="text ${row[
          'severity.value'
        ].toLowerCase()}">${Capitalize(
          row['severity.value']
        )}</span> state with value [${value}]`,
        policySeverity,
        triggerCondition: triggerCondition(
          row['policy.type.value'],
          policySeverity,
          row['value.value'],
          row['policy.threshold.value'],
          row['severity.value'],
          row['metric.value'].replace(/[~^]/g, '.')
        ),
        policy: policy.name,
        policyType: row['policy.type.value'],
        policyId: row['policy.id.value'],
        groupCategory,
        monitor: +entityId,
        counterRawName: row['metric.value'],
        category,
        metric: row['metric.value'].replace(/[~^]/g, '.'),
        instance: row['instance.value'],
        tag: policy.tag || [],
        severity: row['severity.value'],
        value,
        duration: row['duration.value'],
        durationInWords: duration(row['duration.value']),
        policyThreshold: row['policy.threshold.value'],
      }
    }),
  }
}
function transformPolicyStreamData(response, previousData, options) {
  const firstSeenData = GroupBy(
    response.result[WidgetTypeConstants.GRID].data || [],
    (item) =>
      `${item['policy.id']}-${item['entity.id']}${
        item.instance ? '-' + item.instance : ''
      }`
  )

  return {
    meta: {
      queryId: options.queryId,
      requestGroup: options.nextRequestGroup,
    },
    data: previousData.map((row) => {
      const firstSeenItem =
        (firstSeenData[
          `${row['policyId']}-${row.monitor}${
            row['instance'] ? '-' + row['instance'] : ''
          }`
        ] || [])[0] || {}
      return {
        ...row,
        firstSeen: firstSeenItem['policy.first.trigger.tick'],
      }
    }),
  }
}
function transformPolicyAcknowledgedData(response, previousData, options) {
  const alertAckData = GroupBy(
    response.result[WidgetTypeConstants.GRID].data || [],
    (item) =>
      `${item['policy.id']}-${item['entity.id']}${
        item.instance ? '-' + item.instance : ''
      }`
  )

  return {
    meta: {
      queryId: options.queryId,
      requestGroup: options.nextRequestGroup,
    },
    data: previousData.map((row) => {
      const ackItem =
        (alertAckData[
          `${row['policyId']}-${row['monitor']}${
            row['instance'] ? '-' + row['instance'] : ''
          }`
        ] || [])[0] || {}
      return {
        ...row,
        acknowledged:
          Boolean(ackItem['alert.acknowledged.last']) ||
          Boolean(ackItem['policy.acknowledged.last']),

        acknowledgedBy: ackItem['user.id.last'],
      }
    }),
  }
}

export async function transformLiveStreamData(groupCategory, data) {
  let policyFn =
    groupCategory.indexOf('netroute') >= 0
      ? getAllNetroutePoliciesApi
      : getAllPoliciesApi
  const policyData = await policyFn(true)
  const alertStreamData = data
  // let alertData = data.find(
  //   (item) => item !== alertStreamData && item !== alertAckData
  // )
  if (alertStreamData) {
    const rows = alertStreamData.result[WidgetTypeConstants.GRID].data || []
    // alertData = GroupBy(
    //   alertData.result[WidgetTypeConstants.GRID].data || [],
    //   (item) =>
    //     `${item['policy.id']}-${item['entity.id']}${
    //       item.instance ? '-' + item.instance : ''
    //     }`
    // )
    return rows.map((row) => {
      const policy = policyData.find((policy) => policy.id === row['policy.id'])

      const value =
        isUnitConvertible(row['metric']) && /^\d+(.\d+)?$/.test(row['value'])
          ? applyUnit(row.metric, +row['value'])
          : row['value']
      // const severities = GroupBy(
      //   alertData[
      //     `${row['policy.id']}-${row['entity.id']}${
      //       row.instance ? '-' + row.instance : ''
      //     }`
      //   ],
      //   'severity'
      // )
      const ack = row['policy.acknowledge']?.length
        ? JSON.parse(row['policy.acknowledge'])
        : {}
      if (groupCategory.indexOf('netroute') >= 0 && !row['metric']) {
        row['metric'] = row['policy.metric'] || ''
      }
      return {
        id: `${row['entity.id'] || row['netroute.id']}-${row['policy.id']}${
          row.instance ? '-' + row.instance : ''
        }`,
        policy: row['policy.name'],
        policyType: row['policy.type'],
        policyId: row['policy.id'],
        message: `${
          row.instance
            ? `<span title='${row.instance}'>${row.instance.substr(0, 20)}${
                row.instance.length > 20 ? '... ' : ''
              }</span>`
            : ''
        } <span class="font-semibold">${(row['metric'] || '').replace(
          /[~^]/g,
          '.'
        )}</span> has entered into <span class="text ${(
          row['severity'] || ''
        ).toLowerCase()}">${Capitalize(
          row['severity'] || ''
        )}</span> state with value [${value}]`,
        policySeverity: policy?.severity,
        policyBreachTime: policy?.breachTime,
        triggerCondition: triggerCondition(
          row['policy.type'],
          policy?.severity,
          row['value'],
          row['policy.threshold'],
          row['severity'],
          (row['metric'] || '').replace(/[~^]/g, '.')
        ),
        monitor: +row['entity.id'],
        netroute_id: +row['netroute.id'],
        category: row['object.category'],
        counterRawName: row['metric'],
        metric: row['metric'].replace(/[~^]/g, '.'),
        acknowledged: Boolean(ack.acknowledge),
        acknowledgedBy: ack['user.id'],
        acknowledgedByUser: ack['user.name'],
        note: row['policy.note'],
        instance: row['instance'],
        incidentDetails: row['ack.id'],
        displayIncidentDetails: row['ack.id']
          ? row['ack.id'].split('#')[0]
          : undefined,
        tag: row['policy.tags'] ? row['policy.tags'].split(',') : [],
        tagsStr: row['policy.tags'] || '',
        netRouteTags: policy['tag'] ? policy['tag'] : [],
        severity: row['severity'],
        groupCategory,
        firstSeen: row['policy.first.trigger.tick'],
        value,
        duration: row['duration'],
        durationInWords: duration(row['duration']),
        isCorrelatedAlert: row['correlation.probe'] === 'yes',
        correlatedKeys: (row['correlated.unreachable.objects'] || '').split(',')
          .length,
        // counts: Object.keys(SEVERITY_MAP)
        //   .filter((key) => severities[key])
        //   .reduce(
        //     (prev, item) => ({
        //       ...prev,
        //       [item]: severities[item].reduce(
        //         (prev, i) => prev + i['severity.count'],
        //         0
        //       ),
        //     }),
        //     {}
        //   ),

        policyThreshold: row['policy.threshold'],
        lastSeen: Datetime(row['event.timestamp']),
        netRouteLastSeen: row['policy.last.trigger.tick'],
        ...(groupCategory === 'netroute.event'
          ? { count: row['severity.count'], metric: policy['metric'] }
          : {}),

        routeEvaluationType: policy.routeEvaluationType,

        objectType: row['object.type'],
      }
    })
  }
  return []
}
export function getLogFlowAlertStreamGrid(
  timeline,
  groupCategory,
  options = {}
) {
  const context = buildWidgetContext({
    groupCategory,
    groupType: 'policy',
    timeline: timeline,
    counters: [makeCounter('policy.id', 'count')],
    category: WidgetTypeConstants.GRID,
    widgetType: WidgetTypeConstants.GRID,
    resultBy:
      groupCategory === 'metric'
        ? ['policy.id', 'severity', 'metric']
        : [
            'policy.id',
            // ...(groupCategory === 'trap' ? [] : ['severity']),
            ...(groupCategory === 'trap' ? ['event.source'] : ['severity']),
          ],
  }).appendToGroup('policy', {
    join: 'any',
    ...(options?.target ? { target: options.target } : {}),
    ...(options?.severity ? { severity: options.severity } : {}),
    ...(options?.alertIds ? { alertIds: options.alertIds } : {}),
    ...(options?.tags ? { tags: options.tags } : {}),
  })

  context
    // .addGroup('policy.acknowledgement')
    // .addResultBy(['policy.id'], 'policy.acknowledgement')
    // .addCounterToGroup({
    //   counter: '*',
    //   aggrigateFn: 'last',
    //   type: 'policy.acknowledgement',
    // })
    // .appendToGroup('policy.acknowledgement', {
    //   category: groupCategory,
    // })
    .addGroup('policy.stream')
    .addCounterToGroup({
      counter: 'policy.first.trigger.tick',
      aggrigateFn: 'last',
      type: 'policy.stream',
    })
    .addCounterToGroup({
      counter: 'policy.note',
      aggrigateFn: 'last',
      type: 'policy.stream',
    })
    .addCounterToGroup({
      counter: 'policy.acknowledge',
      aggrigateFn: 'last',
      type: 'policy.stream',
    })
    .addCounterToGroup({
      counter: 'policy.last.trigger.tick',
      aggrigateFn: 'last',
      type: 'policy.stream',
    })
    .addCounterToGroup({
      counter: 'policy.id',
      aggrigateFn: 'last',
      type: 'policy.stream',
    })

    .appendToGroup('policy.stream', {
      category: groupCategory,
      ...(options.target ? { target: options.target } : {}),
      ...(options.severity ? { severity: options.severity } : {}),
      ...(options?.alertIds ? { alertIds: options.alertIds } : {}),
      ...(options?.tags ? { tags: options.tags } : {}),
    })

  if (groupCategory !== 'trap') {
    context.addCounterToGroup({
      counter: 'event.source',
      aggrigateFn: 'last',
      type: 'policy.stream',
    })
  }

  return getWidgetResponseApi(
    context.generateWidgetDefinition({
      'join.type': 'any',
      'join.columns': ['policy.id'],
    }),
    {
      fullResponse: true,
      getFinalResultOnly: false,
    }
  ).then((data) => transformLogFlowStreamData(groupCategory, data))
}

export async function transformLogFlowStreamData(groupCategory, response) {
  let policyData = await (groupCategory === 'log'
    ? getAllLogPoliciesApi(true)
    : groupCategory === 'flow'
    ? getAllFlowPoliciesApi(true)
    : getAllTrapPoliciesApi(true))
  policyData = policyData.reduce(
    (prev, item) => ({ ...prev, [item.id]: item }),
    {}
  )
  const data = response.result[WidgetTypeConstants.GRID].data || []
  return data.map((row) => {
    const policy = policyData[row['policy.id']] || {}
    const ack = row['policy.acknowledge']?.length
      ? JSON.parse(row['policy.acknowledge'])
      : {}
    const appliedSeverity = (
      groupCategory === 'trap' ? policy.severity : row['severity']
    ).toUpperCase()
    return {
      id: generateId(),
      message: `<span class="font-semibold">${(groupCategory === 'metric'
        ? row['metric']
        : policy.metric
      )?.replace(
        /[~^]/g,
        '.'
      )}</span> has entered into <span class="text ${appliedSeverity}">${Capitalize(
        appliedSeverity
      )}</span> state`,
      triggerCondition: triggerCondition(
        policy.policyType,
        policy.triggerCondition,
        null,
        null,
        null,
        groupCategory === 'metric' ? row['metric'] : policy.metric
      ),

      policy: policy.name,
      policyType: policy.policyType,
      policyId: +row['policy.id'],
      groupCategory,
      counterRawName:
        groupCategory === 'metric' ? row['metric'] : policy.metric,
      acknowledged: Boolean(ack.acknowledge),
      acknowledgedBy: ack['user.id'],
      acknowledgedByUser: ack['user.name'],
      note: row['policy.note'],
      firstSeen: row['policy.first.trigger.tick']
        ? row['policy.first.trigger.tick'] / 1000
        : null,
      metric: (groupCategory === 'metric'
        ? row['metric']
        : policy.metric
      ).replace(/[~^]/g, '.'),
      tag: policy.tag ? policy.tag : [],
      severity: appliedSeverity,
      duration: row['duration.value'],
      count: row['severity.count'],
      lastSeen: row['policy.last.trigger.tick']
        ? row['policy.last.trigger.tick'] / 1000
        : null,
      alertType: policy.isScheduledPolicy
        ? ALERT_TYEP.SCHEDULED
        : ALERT_TYEP.REAL_TIME,
      ...(groupCategory === 'trap'
        ? { entities: policy.entities, eventSource: row['event.source'] }
        : {}),
    }
  })
}

// export function getSingleAlertCount(alert, timeline, view, monitor) {
//   return getWidgetResponseApi(
//     buildWidgetContext({
//       groupCategory: alert.groupCategory,
//       category: WidgetTypeConstants.GRID,
//       widgetType: WidgetTypeConstants.GRID,
//       groupType: view === 'live' ? 'policy' : 'policy.flap',
//       timeline,
//       resultBy: ['object.id'],
//       counters: [makeCounter('severity', 'count', 'Monitor', [alert.monitor])],
//       ...(alert.instance
//         ? {
//             preFilters: {
//               condition: 'and',
//               inclusion: 'include',
//               conditions: [
//                 {
//                   operand: 'instance',
//                   operator: '=',
//                   value: alert.instance,
//                 },
//               ],
//             },
//           }
//         : {}),
//     })
//       .appendToGroup(view === 'live' ? 'policy' : 'policy.flap', {
//         alertIds: [alert.policyId],
//         target: {
//           entityType: 'Monitor',
//           entities: [alert.monitor],
//         },
//       })
//       .generateWidgetDefinition()
//   )
// }

export function getNetRouteHistoryData(
  alert,
  timeline,
  forSegmentedHistoryLine,
  onlyForwardWidgetDefinition
) {
  let widgetDefinition = buildWidgetContext({
    groupCategory: alert.groupCategory,
    category: WidgetTypeConstants.CHART,
    groupType: !forSegmentedHistoryLine ? 'policy.flap' : 'policy',
    timeline: timeline,
    counters: forSegmentedHistoryLine
      ? [
          makeCounter('severity', '__NONE__', 'NetRoute', alert.netroute_id),
          makeCounter('value', '__NONE__', 'NetRoute', alert.netroute_id),
          makeCounter(
            'policy.threshold',
            '__NONE__',
            'NetRoute',
            alert.netroute_id
          ),
          makeCounter('netroute.id', '__NONE__', 'NetRoute', alert.netroute_id),
          makeCounter('metric', '__NONE__', 'NetRoute', alert.netroute_id),
          makeCounter('policy.type', '__NONE__', 'NetRoute', alert.netroute_id),
          makeCounter('policy.id', '__NONE__', 'NetRoute', alert.netroute_id),
          makeCounter('duration', '__NONE__', 'NetRoute', alert.netroute_id),
        ]
      : [
          makeCounter('severity', '__NONE__', 'NetRoute', alert.netroute_id),
          makeCounter('value', '__NONE__', 'NetRoute', alert.netroute_id),
          makeCounter(
            'policy.threshold',
            '__NONE__',
            'NetRoute',
            alert.netroute_id
          ),
          makeCounter('netroute.id', '__NONE__', 'NetRoute', alert.netroute_id),
          makeCounter('metric', '__NONE__', 'NetRoute', alert.netroute_id),
          makeCounter('policy.type', '__NONE__', 'NetRoute', alert.netroute_id),
          makeCounter('policy.id', '__NONE__', 'NetRoute', alert.netroute_id),
          makeCounter('duration', '__NONE__', 'NetRoute', alert.netroute_id),
        ],
    preFilters: {
      condition: 'and',
      inclusion: 'include',
      conditions: [
        { operand: 'policy.id', operator: '=', value: alert.policyId },
        { operand: 'netroute.id', operator: '=', value: alert.netroute_id },
      ],
    },
  })
    .appendToGroup('policy.flap', {
      target: {
        entityType: 'NetRoute',
        entities: [alert.netroute_id],
      },
      additionalUntouchedRequestChunk: {
        'join.type': 'custom',
        type: 'policy.flap',
      },
    })
    .appendToGroup('policy', {
      excludeSeverityResultBy: true,
      useExternalCounters: true,

      ...(forSegmentedHistoryLine
        ? {
            target: {
              entityType: 'NetRoute',
              entities: [alert.netroute_id],
            },
          }
        : {}),
    })
    .generateWidgetDefinition()

  widgetDefinition = {
    ...widgetDefinition,
  }

  if (onlyForwardWidgetDefinition) {
    return widgetDefinition
  }
  return getWidgetResponseApi(widgetDefinition).then((data) => {
    return (data || []).map((row) =>
      transformAlertHistoryDataForClient(row, alert)
    )
  })
}

export function getNetRouteHopToHopHistoryData(
  alert,
  timeline,
  forSegmentedHistoryLine,
  onlyForwardWidgetDefinition
) {
  let widgetDefinition = buildWidgetContext({
    groupCategory: alert.groupCategory,
    category: WidgetTypeConstants.CHART,
    groupType: 'policy',
    timeline: timeline,
    counters: forSegmentedHistoryLine
      ? [
          makeCounter('severity', '__NONE__'),
          makeCounter('value', '__NONE__'),
          makeCounter(
            'policy.threshold',
            '__NONE__',
            'NetRoute',
            alert.netroute_id
          ),
          makeCounter('netroute.id', '__NONE__'),
          makeCounter('metric', '__NONE__'),
          makeCounter('policy.type', '__NONE__'),
          makeCounter('policy.id', '__NONE__'),
          makeCounter('duration', '__NONE__'),
        ]
      : [
          makeCounter('message', '__NONE__'),
          makeCounter('id', '__NONE__'),
          makeCounter('severity', '__NONE__'),
        ],
    preFilters: {
      condition: 'and',
      inclusion: 'include',
      conditions: [
        { operand: 'policy.id', operator: '=', value: alert.policyId },
        { operand: 'netroute.id', operator: '=', value: alert.netroute_id },
      ],
    },
  })
    .appendToGroup('policy.flap', {
      target: {
        entityType: 'NetRoute',
        entities: [alert.netroute_id],
      },
      additionalUntouchedRequestChunk: {
        'join.type': 'custom',
        type: 'policy.flap',
      },
    })
    .appendToGroup('policy', {
      excludeSeverityResultBy: true,
      useExternalCounters: true,

      ...(forSegmentedHistoryLine
        ? {
            target: {
              entityType: 'NetRoute',
              entities: [alert.netroute_id],
            },
          }
        : {}),
    })
    .generateWidgetDefinition()

  widgetDefinition = {
    ...widgetDefinition,
  }

  if (onlyForwardWidgetDefinition) {
    return widgetDefinition
  }
  return getWidgetResponseApi(widgetDefinition).then((data) => {
    return (data || []).map((row) =>
      transformAlertHistoryDataForClient(row, alert)
    )
  })
}

export function getAlertHistoryData(
  alert,
  timeline,
  forSegmentedHistoryLine,
  onlyForwardWidgetDefinition
) {
  let widgetDefinition = buildWidgetContext({
    groupCategory: alert.groupCategory,
    category: WidgetTypeConstants.CHART,
    groupType:
      alert.groupCategory === 'metric' && !forSegmentedHistoryLine
        ? 'policy.flap'
        : 'policy',
    timeline: timeline,
    counters:
      alert.groupCategory === 'metric'
        ? [
            makeCounter('severity', '__NONE__', 'Monitor', alert.monitor),
            makeCounter('value', '__NONE__', 'Monitor', alert.monitor),
            makeCounter(
              'policy.threshold',
              '__NONE__',
              'Monitor',
              alert.monitor
            ),
            makeCounter('object.id', '__NONE__', 'Monitor', alert.monitor),
            makeCounter('instance', '__NONE__', 'Monitor', alert.monitor),
            makeCounter('metric', '__NONE__', 'Monitor', alert.monitor),
            makeCounter('policy.type', '__NONE__', 'Monitor', alert.monitor),
            makeCounter('policy.id', '__NONE__', 'Monitor', alert.monitor),
            ...(forSegmentedHistoryLine
              ? [makeCounter('duration', '__NONE__', 'Monitor', alert.monitor)]
              : []),
          ]
        : [
            makeCounter('message', '__NONE__'),
            makeCounter('id', '__NONE__'),
            makeCounter('severity', '__NONE__'),
          ],
    preFilters: {
      condition: 'and',
      inclusion: 'include',
      conditions: [
        { operand: 'policy.id', operator: '=', value: alert.policyId },
        ...(alert.instance
          ? [
              {
                operand: 'instance',
                operator: '=',
                value: alert.instance,
              },
            ]
          : []),

        ...(alert.groupCategory === 'trap'
          ? [
              {
                operand: 'event.source',
                operator: '=',
                value: alert.eventSource,
              },
            ]
          : []),
      ],
    },
  })
    .appendToGroup('policy.flap', {
      target: {
        entityType: 'Monitor',
        entities: [alert.monitor],
      },
      additionalUntouchedRequestChunk: {
        'join.type': 'custom',
        type: 'policy.flap',
      },
    })
    .appendToGroup('policy', {
      excludeSeverityResultBy: true,
      useExternalCounters: true,

      ...(alert.groupCategory === 'metric' && forSegmentedHistoryLine
        ? {
            target: {
              entityType: 'Monitor',
              entities: [alert.monitor],
            },
          }
        : {}),

      ...(alert.groupCategory === 'trap'
        ? {
            additionalUntouchedRequestChunk: {
              'drill.down': 'yes',
            },
          }
        : {}),
    })
    .generateWidgetDefinition()

  widgetDefinition = {
    ...widgetDefinition,
    ...(alert.groupCategory === 'metric' && !forSegmentedHistoryLine
      ? {
          'join.result': 'integration.ticket',
          'join.type': 'custom',
        }
      : {}),
  }

  if (onlyForwardWidgetDefinition) {
    return widgetDefinition
  }
  return getWidgetResponseApi(widgetDefinition).then((data) => {
    return (data || []).map((row) =>
      transformAlertHistoryDataForClient(row, alert)
    )
  })
}

export function transformAlertHistoryDataForClient(row, alert, policyContext) {
  const instanceNameLimit = 25

  const text =
    alert.policyType && alert.policyType.toLowerCase().includes('forecast')
      ? buildForecastText(row, alert, policyContext, instanceNameLimit)
      : row['message.value'] ||
        `${(alert.instance || '').substring(
          0,
          Math.min((alert.instance || '').length, instanceNameLimit)
        )}${(alert.instance || '').length > instanceNameLimit ? '...' : ''} ${(
          alert['metric'] || ''
        ).replace(/[~^]/g, '.')} has entered into ${Capitalize(
          row['severity.value']
        )} state, ${postMessage(row, alert, policyContext)}`

  return {
    id: row['id.value'] || generateId(),
    text,
    severity: row['severity.value'],
    date: row.timestamp / 1000,
    time: row.timestamp / 1000,
    incidentDetails: row['ack.id'],
    displayIncidentDetails: row['ack.id']
      ? row['ack.id'].split('#')[0]
      : undefined,

    duration: +row['duration.value'],
    lastSeen: Datetime(row['event.timestamp']),

    guid: generateId(),
  }
}

function buildForecastText(row, alert, policyContext = {}, instanceNameLimit) {
  const policySeverity = policyContext?.['policy.context']?.['policy.severity']

  const severityKeys = Object.keys(policySeverity || {})

  const selectedPolicySeverity = policySeverity[severityKeys[0]]

  return (
    row['message.value'] ||
    `${(alert.instance || '').substring(
      0,
      Math.min((alert.instance || '').length, instanceNameLimit)
    )}${alert.instance.length > instanceNameLimit ? '...' : ''} ${(
      alert['metric'] || ''
    ).replace(/[~^]/g, '.')} ${
      row['severity.value']?.toLowerCase() === 'clear'
        ? row['value.value'] === 'Auto Clear'
          ? `has enter into ${row['severity.value']} state, with value ${row['value.value']}`
          : `is no longer expected to breach the ${(
              severityKeys[0] || ''
            ).toLowerCase()} threshold of ${
              selectedPolicySeverity['policy.threshold']
            }% within the forecast period`
        : `is trending towards a ${(
            severityKeys[0] || ''
          ).toLowerCase()} state, expected to surpass ${
            selectedPolicySeverity['policy.threshold']
          }% within ${row['value.value']}`
    }`
  )
}

export function postMessage(row, alert, policyContext = {}) {
  const policySeverity =
    alert.policySeverity ||
    policyContext?.['policy.context']?.['policy.severity']
  const alertType = alert.policyType
  const isAbsoluteThreshold =
    policyContext?.['policy.context']?.['threshold.type'] === 'absolute'

  const postMsg =
    alertType && alertType.toLowerCase().includes('anomaly')
      ? `as ${row['value.value']}% of samples where ${
          row['severity.value']?.toLowerCase() === 'clear'
            ? 'within the range of predicted value.'
            : `${
                policySeverity?.[row['severity.value']]?.['policy.condition'] ||
                ''
              } predicted value.`
        }`
      : alertType &&
        (alertType.toLowerCase().includes('forecast') ||
          alertType.toLowerCase().includes('baseline'))
      ? `as current metric value ${unitConvertedValues(
          alert['metric'],
          row['value.value']
        )}   ${
          row['severity.value']?.toLowerCase() === 'clear'
            ? `falls below the ${
                alertType.toLowerCase().includes('forecast')
                  ? 'forecasted value'
                  : 'auto-detected baseline'
              }   ${unitConvertedValues(
                alert['metric'],
                row['policy.threshold.value']
              )} ${
                isFinite(
                  percentage(row['value.value'], row['policy.threshold.value'])
                ) && !isAbsoluteThreshold
                  ? `by ${percentage(
                      row['value.value'],
                      row['policy.threshold.value']
                    )}%`
                  : isAbsoluteThreshold
                  ? `by ${unitConvertedValues(
                      alert['metric'],
                      Math.abs(
                        row['value.value'] - row['policy.threshold.value']
                      )
                    )}`
                  : ''
              }`
            : `${alertType.toLowerCase().includes('baseline') ? 'is ' : ''}${
                policySeverity?.[row['severity.value']]?.['policy.condition'] ||
                ''
              } the ${
                alertType.toLowerCase().includes('forecast')
                  ? 'forecasted value'
                  : 'auto-detected baseline'
              }  ${unitConvertedValues(
                alert['metric'],
                row['policy.threshold.value']
              )} ${
                isFinite(
                  percentage(row['value.value'], row['policy.threshold.value'])
                ) && !isAbsoluteThreshold
                  ? `by ${percentage(
                      row['value.value'],
                      row['policy.threshold.value']
                    )}%`
                  : isAbsoluteThreshold
                  ? `by ${unitConvertedValues(
                      alert['metric'],
                      Math.abs(
                        row['value.value'] - row['policy.threshold.value']
                      )
                    )}`
                  : ''
              }`
        }`
      : `with value ${unitConvertedValues(alert['metric'], row['value.value'])}`
  return postMsg
}

export function triggerCondition(
  type,
  policySeverity,
  value,
  threshold,
  severity,
  metric,
  policy = {}
) {
  const isAbsoluteThreshold =
    policy?.['policy.context']?.['threshold.type'] === 'absolute'

  return type && type.toLowerCase().includes('anomaly')
    ? `metric value ${policySeverity?.[severity]?.['policy.condition']} ${value}% of samples`
    : type.toLowerCase().includes('forecast')
    ? `metric value ${policySeverity?.[severity]?.['policy.condition']} ${policySeverity?.[severity]?.['policy.threshold']}% of Forecasted value`
    : type.toLowerCase().includes('baseline')
    ? `metric value ${policySeverity?.[severity]?.['policy.condition']} ${
        isAbsoluteThreshold
          ? `${unitConvertedValues(
              metric,
              policySeverity?.[severity]?.['policy.threshold']
            )}`
          : `${policySeverity?.[severity]?.['policy.threshold']}%`
      } of baseline`
    : type.toLowerCase().includes('log') ||
      type.toLowerCase().includes('flow') ||
      type.toLowerCase().includes('trap')
    ? `metric value ${
        OPERATOR_MAP[policySeverity?.condition]?.toLowerCase() ||
        policySeverity?.condition
      } ${
        policySeverity?.condition === 'range'
          ? unitConvertedValuesForRange(metric, policySeverity.threshold)
          : unitConvertedValues(metric, policySeverity.threshold)
      }`
    : type.toLowerCase().includes('netroute') && metric === 'status'
    ? unitConvertedValues(metric, threshold)
    : `metric value ${
        OPERATOR_MAP[
          policySeverity?.[severity]?.['policy.condition']
        ]?.toLowerCase() || policySeverity?.[severity]?.['policy.condition']
      }  ${unitConvertedValues(metric, threshold)}`
}

function percentage(value, policyThreshold) {
  return (
    Math.abs(((value - policyThreshold) * 100) / policyThreshold)?.toFixed(2) ||
    0
  )
}

function unitConvertedValues(metric, value) {
  return isUnitConvertible(metric) && /^\d+(.\d+)?$/.test(value)
    ? applyUnit(metric, +value)
    : value || ''
}

function unitConvertedValuesForRange(metric, value) {
  const startValue = value.split('#')[0]
  const endValue = value.split('#')[1]

  return `${
    isUnitConvertible(metric) && /^\d+(.\d+)?$/.test(startValue)
      ? applyUnit(metric, +startValue)
      : startValue || ''
  } and ${
    isUnitConvertible(metric) && /^\d+(.\d+)?$/.test(endValue)
      ? applyUnit(metric, +endValue)
      : endValue || ''
  }`
}

// export function getAlertOverlayCount(alert, timeline) {
//   return getWidgetResponseApi(
//     buildWidgetContext({
//       category: WidgetTypeConstants.CHART,
//       widgetType: WidgetTypeConstants.LINE,
//       groupCategory: alert.groupCategory,
//       groupType: 'policy',
//       timeline,
//       resultBy: ['severity'],
//       counters: [makeCounter('severity', 'count')],
//       ...(alert.instance
//         ? {
//             preFilters: {
//               condition: 'and',
//               inclusion: 'include',
//               conditions: [
//                 {
//                   operand: 'instance',
//                   operator: '=',
//                   value: alert.instance,
//                 },
//               ],
//             },
//           }
//         : {}),
//     })
//       .appendToGroup('policy', {
//         alertIds: [alert.policyId],
//         ...(alert.groupCategory === 'metric'
//           ? {
//               target: {
//                 entityType: 'Monitor',
//                 entities: [alert.monitor],
//               },
//             }
//           : {}),
//       })
//       .generateWidgetDefinition(),
//     { fullResponse: true }
//   ).then((data) => {
//     const series = (data.result[WidgetTypeConstants.CHART] || {}).series || []
//     const alertMap = {}
//     series.forEach((series) => {
//       const severity = series.entity.toUpperCase()
//       series.data.forEach((item) => {
//         if (alertMap[item[0]]) {
//           alertMap[item[0]][severity] = {
//             count: (alertMap[item[0]][severity] || 0) + item[1],
//           }
//         } else {
//           alertMap[item[0]] = {
//             [severity]: {
//               count: item[1],
//             },
//           }
//         }
//       })
//     })
//     return alertMap
//   })
// }

export function getUserNameFromId(userId) {
  return api.get('/settings/users').then((response) => {
    let user = response.result.filter((user) => userId === user.id).shift()
    return user ? user['user.name'] : '-'
  })
}

export function getLogFlowAlertHistoryDetail(item, timeline, groupCategory) {
  return getWidgetResponseApi(
    buildWidgetContext({
      category: WidgetTypeConstants.CHART,
      widgetType: WidgetTypeConstants.LINE,
      groupCategory,
      groupType: 'policy.result',
      timeline,
      counters: [
        makeCounter('policy.trigger.evaluation.window', '__NONE__'),
        makeCounter('policy.trigger.value', '__NONE__'),
      ],
      preFilters: {
        condition: 'and',
        inclusion: 'include',
        conditions: [
          {
            operand: 'policy.trigger.id',
            operator: '=',
            value: item.id,
          },
        ],
      },
    }).generateWidgetDefinition()
  ).then((data) => {
    if (data.length > 0) {
      return {
        value: JSON.parse(data[0]['policy.trigger.value.value']),
        evaluationWindow: convertTimeLineOfPolicy({
          ...JSON.parse(data[0]['policy.trigger.evaluation.window.value']),
          'relative.timeline': 'custom',
        }),
        timestamp: data[0].timestamp / 1000,
      }
    }
    return {
      value: [],
      evaluationWindow: {
        result: [],
      },
    }
  })
}

export function getLastTriggeredData(
  type,
  alert,
  timeline,
  onlyForwardWidgetDefinition
) {
  const isMetricPolicy = alert.groupCategory === 'metric'

  const widgetDefinition = buildWidgetContext({
    category: WidgetTypeConstants.CHART,
    widgetType: WidgetTypeConstants.LINE,
    groupType: 'runbook.worklog',
    timeline,
    counters: [
      makeCounter('runbook.worklog.status', '__NONE__'),
      makeCounter('runbook.worklog.result', '__NONE__'),
      makeCounter('runbook.worklog.type', '__NONE__'),
      makeCounter('runbook.worklog.id', '__NONE__'),
      makeCounter('runbook.worklog.error', '__NONE__'),
    ],
    preFilters: {
      groups: [
        {
          condition: 'and',
          inclusion: 'include',
          conditions: [
            ...(isMetricPolicy && alert.instance
              ? [
                  {
                    operand: 'instance',
                    operator: '=',
                    value: alert.instance,
                  },
                ]
              : []),
            {
              operand: 'runbook.worklog.type',
              operator: type === 'notifications' ? 'in' : 'in',
              value: type === 'notifications' ? [0, 1] : [2, 3],
            },
            // ...(isMetricPolicy
            //   ? [
            //       {
            //         operand: 'policy.metric',
            //         operator: '=',
            //         value: alert.counterRawName,
            //       },
            //     ]
            //   : []),

            {
              operand: 'policy.id',
              operator: '=',
              value: alert.policyId,
            },

            ...(alert.groupCategory === 'trap'
              ? [
                  {
                    operand: 'event.source',
                    operator: '=',
                    value: alert.eventSource,
                  },
                ]
              : []),

            // {
            //   operand: isMetricPolicy ? 'policy.metric' : 'policy.event.field',
            //   operator: '=',
            //   value: alert.counterRawName,
            // },
          ],
        },

        ...(isMetricPolicy
          ? [
              {
                condition: 'and',
                inclusion: 'include',
                conditions: [
                  {
                    operand: 'object.id',
                    operator: '=',
                    value: alert.monitor,
                  },
                ],
              },
            ]
          : []),
      ],
    },
  }).generateWidgetDefinition()

  if (onlyForwardWidgetDefinition) {
    return widgetDefinition
  }
  return getWidgetResponseApi(widgetDefinition).then((data) => {
    return data.map((i) => transformLastTriggeredData(i))
  })
}

export function transformLastTriggeredData(i) {
  return {
    id: generateId(),
    actionId: i['runbook.worklog.id.value'],
    type: i['runbook.worklog.type.value'],
    status: i['runbook.worklog.status.value'],
    datetime: i.timestamp / 1000,
    value:
      i['runbook.worklog.status.value'] &&
      i['runbook.worklog.status.value'] !== Constants.EVENT_SUCCESS_STATUS
        ? i['runbook.worklog.error.value'] ?? ''
        : i['runbook.worklog.result.value']
        ? [2].includes(i['runbook.worklog.type.value'])
          ? i['runbook.worklog.result.value']
          : JSON.parse(i['runbook.worklog.result.value'])
        : null,
  }
}

export function correlatedTopologyWidget() {
  return buildWidgetContext({
    groupType: 'correlation.worklog',
    category: WidgetTypeConstants.GRID,
    widgetType: WidgetTypeConstants.GRID,

    counters: [
      makeCounter('correlation.map', 'last'),
      makeCounter('correlation.objects', 'last'),
    ],
    timeline: {
      selectedKey: '-24h',
    },
  })
}

// export function anomayWidget(item, timeline, groupCategory) {
//   return getWidgetResponseApi(
//     buildWidgetContext({
//       category: WidgetTypeConstants.CHART,
//       widgetType: WidgetTypeConstants.LINE,
//       groupCategory,
//       groupType: 'policy.result',
//       timeline,
//       counters: [makeCounter('policy.trigger.value', '__NONE__')],
//       preFilters: {
//         condition: 'and',
//         inclusion: 'include',
//         conditions: [
//           {
//             operand: 'policy.trigger.policy.id',
//             operator: '=',
//             value: item.id,
//           },
//           {
//             operand: 'policy.event.object.id',
//             operator: '=',
//             value: item.smallObjectId,
//           },
//         ],
//       },
//     }).generateWidgetDefinition()
//   )
// }

export function makePlotBands(policyContext) {
  const context = Object.freeze(policyContext)

  return Object.keys(context)
    .filter((s) => !['CLEAR'].includes(s))
    .map((severity) => {
      const policy = context[severity]
      const policyCondition = policy['policy.condition']
      const policyThreshold = policy['policy.threshold']

      const allthreshould = (
        severity.toLowerCase() !== 'warning'
          ? CloneDeep(SEVERITY_TO_CONSIDER_IN_BANDS)
          : Reverse(CloneDeep(SEVERITY_TO_CONSIDER_IN_BANDS))
      ).map((s) => context?.[s.toUpperCase()]?.['policy.threshold'])

      function calculateRangeForPloatBand() {
        if (policyCondition === '=') {
          return {
            to: policyThreshold,
            from: policyThreshold,
          }
        } else if (['above', '>', '>='].includes(policyCondition)) {
          let to =
            allthreshould.find((t) => {
              const threshold = Number(t)
              return threshold > Number(policyThreshold)
            }) || Infinity

          return {
            to,
            from: policyThreshold,
          }
        } else if (['below', '<', '<='].includes(policyCondition)) {
          let to =
            allthreshould.find((t) => {
              const threshold = Number(t)

              return threshold < Number(policyThreshold)
            }) || 0
          return {
            to,
            from: policyThreshold,
          }
        }

        return {}
      }

      return {
        color: PLOAT_BAND_COLORS[severity.toLowerCase()],
        label: {
          text:
            policyCondition && policyThreshold
              ? `${policyCondition} ${policyThreshold}`
              : '',
          style: {
            color: `var(--severity-${severity.toLowerCase()})`,
          },
        },

        ...calculateRangeForPloatBand(),

        key: severity.toLowerCase(),
      }
    })
}

export function getAlertBySeverityChartAlertTrendData(timeline, severities) {
  return getWidgetResponseApi(
    buildWidgetContext({
      groupType: 'policy.flap',
      category: WidgetTypeConstants.CHART,
      widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,
      groupCategory: 'netroute.metric',
      counters: [makeCounter('severity', 'count')],
      resultBy: ['severity'],
      timeline: timeline,
    })
      .appendToGroup('policy.flap', {
        severity: severities,
      })
      .setWidgetProperties({
        styleSetting: {
          chartOptions: {
            yAxis: {
              allowDecimal: false,
            },
          },
        },
      })
      .setWidgetProperties({
        styleSettings: {
          chartOptions: {
            yAxis: {
              allowDecimals: false,
            },
          },
        },
      })

      .generateWidgetDefinition(),
    { useResultBuilder: true }
  ).then((data) => {
    return data
  })
}

export function getAlertBySeverityChartAlertTrendDataHopToHop(
  timeline,
  severities
) {
  return getWidgetResponseApi(
    buildWidgetContext({
      groupType: 'policy',
      category: WidgetTypeConstants.CHART,
      widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,
      groupCategory: 'netroute.event',
      counters: [makeCounter('severity', 'count')],
      resultBy: ['severity'],
      timeline: timeline,
    })
      .appendToGroup('policy', {
        severity: severities,
      })
      .setWidgetProperties({
        styleSetting: {
          chartOptions: {
            yAxis: {
              allowDecimal: false,
            },
          },
        },
      })
      .setWidgetProperties({
        styleSettings: {
          chartOptions: {
            yAxis: {
              allowDecimals: false,
            },
          },
        },
      })
      .generateWidgetDefinition(),
    { useResultBuilder: true }
  ).then((data) => {
    return data
  })
}
export function transformAlertDataForPolicyDetailsDrillDown(row) {
  const value =
    isUnitConvertible(row['metric']) && /^\d+(.\d+)?$/.test(row['value'])
      ? applyUnit(row.metric, +row['value'])
      : row['value']
  const ack = row['policy.acknowledge']?.length
    ? JSON.parse(row['policy.acknowledge'])
    : {}

  const policyContext = transformPolicyForList(row) || {}

  const groupCategory = [
    PolicyConfig.METRIC_BASELINE,
    PolicyConfig.METRIC_THRESHOLD,
    PolicyConfig.ANOMALY,
    PolicyConfig.FORECAST,
  ].includes(row['policy.type'])
    ? 'metric'
    : [PolicyConfig.LOG].includes(row['policy.type'])
    ? 'log'
    : [PolicyConfig.FLOW].includes(row['policy.type'])
    ? 'flow'
    : [PolicyConfig.TRAP].includes(row['policy.type'])
    ? 'trap'
    : [PolicyConfig.NETROUTE].includes(row['policy.type'])
    ? policyContext.routeEvaluationType === Constants.HOP_TO_HOP
      ? 'netroute.event'
      : 'netroute.metric'
    : undefined

  if (groupCategory.indexOf('netroute') >= 0 && !row['metric']) {
    row['metric'] = row['policy.metric'] || ''
  }

  const appliedSeverity = (
    ['trap', 'log', 'flow'].includes(groupCategory)
      ? policyContext.severity
      : row['severity']
  ).toUpperCase()

  return {
    id: `${row['entity.id'] || row['netroute.id']}-${row['policy.id']}${
      row.instance ? '-' + row.instance : ''
    }`,
    policy: row['policy.name'],
    policyType: row['policy.type'] || policyContext.policyType,
    policyId: row['policy.id'],
    message: `${
      row.instance
        ? `<span title='${row.instance}'>${row?.instance.substr(0, 20)}${
            row?.instance?.length > 20 ? '... ' : ''
          }</span>`
        : ''
    } <span class="font-semibold">${(row['metric'] || '').replace(
      /[~^]/g,
      '.'
    )}</span> has entered into <span class="text ${(
      row['severity'] || ''
    ).toLowerCase()}">${Capitalize(
      row['severity'] || ''
    )}</span> state with value [${value}]`,

    monitor: +row['entity.id'],
    category: row['object.category'],
    counterRawName:
      groupCategory === 'metric' ? row['metric'] : policyContext.metric,
    metric: (groupCategory === 'metric'
      ? row['metric']
      : policyContext.metric
    ).replace(/[~^]/g, '.'),
    acknowledged: Boolean(ack.acknowledge),
    acknowledgedBy: ack['user.id'],
    acknowledgedByUser: ack['user.name'],
    note: row['policy.note'],
    instance: row['instance'],
    incidentDetails: row['ack.id'],
    displayIncidentDetails: row['ack.id']
      ? row['ack.id']?.split('#')?.[0]
      : undefined,
    tag: ['log', 'flow', 'trap'].includes(groupCategory)
      ? policyContext.tag
        ? policyContext.tag
        : []
      : row['policy.tags']
      ? row['policy.tags']
      : [],
    tagsStr: row['policy.tags'] || '',
    severity: ['log', 'flow', 'trap'].includes(groupCategory)
      ? appliedSeverity
      : row.severity,
    firstSeen: ['log', 'flow', 'trap'].includes(groupCategory)
      ? row['policy.first.trigger.tick'] / 1000
      : row['policy.first.trigger.tick'],
    value,
    duration: row['duration'] || row['duration.value'],
    durationInWords: duration(row['duration']),
    isCorrelatedAlert: row['correlation.probe'] === 'yes',
    correlatedKeys: (row['correlated.unreachable.objects'] || '').split(',')
      .length,

    policyThreshold: row['policy.threshold'],
    lastSeen: ['log', 'flow', 'trap'].includes(groupCategory)
      ? row['policy.last.trigger.tick']
        ? row['policy.last.trigger.tick'] / 1000
        : null
      : Datetime(row['event.timestamp']),
    groupCategory,
    isScheduledPolicy: policyContext.isScheduledPolicy,
    netroute_id: +row['netroute.id'],
    netRouteLastSeen: row['policy.last.trigger.tick'],
    ...(groupCategory === 'netroute.event'
      ? { count: row['severity.count'], metric: policyContext['metric'] }
      : {}),

    routeEvaluationType: policyContext.routeEvaluationType,
    count: row['severity.count'],
    ...(groupCategory === 'trap'
      ? { entities: policyContext.entities, eventSource: row['event.source'] }
      : {}),
    policySeverity: policyContext?.severity,
    policyBreachTime: policyContext?.breachTime,
    triggerCondition: triggerCondition(
      policyContext.policyType,
      policyContext.triggerCondition,
      null,
      null,
      null,
      groupCategory === 'metric' ? row['metric'] : policyContext.metric
    ),
    alertType: policyContext.isScheduledPolicy
      ? ALERT_TYEP.SCHEDULED
      : ALERT_TYEP.REAL_TIME,
  }
}
