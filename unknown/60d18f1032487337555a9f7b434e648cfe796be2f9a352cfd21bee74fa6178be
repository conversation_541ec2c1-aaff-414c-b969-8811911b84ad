import Constants from '@constants'

export const INVENTORY_COLUMNS = {
  [Constants.SERVER]: [
    {
      name: 'ID',
      key: 'monitorId',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      width: '100px',
    },
    {
      name: 'Host',
      key: 'host',
      searchable: true,
      reorderable: true,
      hidden: true,
      resizable: true,
      width: '100px',
      // width: '120px',
    },
    {
      name: 'Monitor',
      key: 'name',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'monitor',
      disable: true,
      width: '220px',
    },
    {
      name: 'status',
      key: 'status',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '200px',
    },
    {
      name: 'IP',
      key: 'ip',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      disable: true,
      width: '100px',
      minWidth: '100px',
    },
    {
      name: 'Type',
      key: 'type',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'type',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'CPU',
      searchable: true,
      key: 'cpu',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '220px',
      minWidth: '100px',
      cellRender: 'progress',
    },
    {
      name: 'Memory',
      searchable: true,
      key: 'memory',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '220px',
      minWidth: '100px',
      cellRender: 'progress',
    },
    {
      name: 'Disk',
      searchable: true,
      key: 'disk',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '220px',
      minWidth: '100px',
      cellRender: 'progress',
    },
    {
      key: 'groups',
      name: 'Group',
      contextKey: 'groupContext',
      searchable: true,
      sortable: true,
      searchKey: 'groupsDisplay',
      sortKey: 'groupsDisplay',
      cellRender: 'groups',
      disable: true,
      width: '250px',
      minWidth: '100px',
    },
    {
      key: 'tags',
      name: 'Tags',
      searchable: true,
      sortable: true,
      hidden: true,
      searchKey: 'tagsStr',
      sortKey: 'tagsStr',
      width: '100px',
      minWidth: '100px',
    },
    {
      name: 'Active Alerts',
      searchable: true,
      key: 'alerts',
      // sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'alerts',
      minWidth: '100px',
      width: '120px',
    },
    {
      name: 'Apps',
      searchable: true,
      key: 'apps',
      sortable: true,
      reorderable: true,
      resizable: true,
      searchKey: 'appsSearchKey',
      cellRender: 'apps',
      width: '100px',
      minWidth: '100px',
    },
    {
      name: 'OS Name',
      key: 'osName',
      // sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
    {
      name: 'OS Version',
      key: 'osVersion',
      // sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
    {
      name: 'Model',
      key: 'model',
      // sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
    {
      name: 'Vendor',
      key: 'vendor',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      width: '50px',
    },
    {
      name: 'Creation Timestamp',
      key: 'creationTime',
      sortable: true,
      searchable: true,
      // reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
    {
      name: 'Collector IP',
      key: 'collectorIp',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
  ],
  [Constants.NETWORK]: [
    {
      name: 'ID',
      key: 'monitorId',
      sortable: true,
      // width: '120px',
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      width: '100px',
    },
    {
      name: 'Monitor',
      key: 'name',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'monitor',
      disable: true,
      width: '400px',
    },
    {
      name: 'status',
      key: 'status',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '200px',
    },
    {
      name: 'IP',
      key: 'ip',
      sortable: true,
      // width: '120px',
      searchable: true,
      reorderable: true,
      resizable: true,
      disable: true,
      width: '180px',
    },
    {
      name: 'Host',
      key: 'host',
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
    },
    {
      name: 'Type',
      key: 'type',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      // width: '120px',
      cellRender: 'type',
      disable: true,
      width: '100px',
    },
    {
      name: 'Response Time',
      searchable: true,
      key: 'responseTime',
      // sortable: true,
      reorderable: true,
      resizable: true,
      // width: '180px',
      cellRender: 'label',
      width: '180px',
    },
    {
      name: 'Packet Lost',
      searchable: true,
      key: 'packetLost',
      // sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'progress',
      width: '220px',
      minWidth: '100px',
    },
    {
      name: 'CPU',
      searchable: true,
      key: 'cpu',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'progress',
      hidden: true,
      minWidth: '100px',
    },
    {
      name: 'Memory',
      searchable: true,
      key: 'memory',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'progress',
      hidden: true,
      minWidth: '100px',
    },
    {
      name: 'Disk',
      searchable: true,
      key: 'disk',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'progress',
      hidden: true,
      minWidth: '100px',
    },
    {
      key: 'groups',
      name: 'Group',
      contextKey: 'groupContext',
      searchable: true,
      sortable: true,
      searchKey: 'groupsDisplay',
      sortKey: 'groupsDisplay',
      cellRender: 'groups',
      disable: true,
      minWidth: '100px',
      width: '250px',
    },
    {
      key: 'tags',
      name: 'Tags',
      searchable: true,
      sortable: true,
      hidden: true,
      searchKey: 'tagsStr',
      sortKey: 'tagsStr',
      minWidth: '100px',
      width: '140px',
    },
    {
      name: 'Active Alerts',
      searchable: true,
      key: 'alerts',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'alerts',
      minWidth: '100px',
    },
    {
      name: 'Interface',
      searchable: false,
      key: 'interfaces',
      sortable: false,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'monitorInterfaces',
      minWidth: '100px',
    },
    {
      name: 'OS Version',
      key: 'osVersion',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      minWidth: '220px',
      width: '220px',
    },
    {
      name: 'System Description',
      key: 'systemDescription',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      minWidth: '220px',
      width: '220px',
    },
    {
      name: 'Model',
      key: 'model',
      // sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      minWidth: '220px',
      width: '220px',
    },
    {
      name: 'Vendor',
      key: 'cloudType',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      minWidth: '220px',
      width: '220px',
    },
    {
      name: 'Serial Number',
      key: 'serialNumber',
      // sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      minWidth: '220px',
      width: '220px',
    },
    {
      name: 'Creation Timestamp',
      key: 'creationTime',
      sortable: true,
      searchable: true,
      // reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
    {
      name: 'Collector IP',
      key: 'collectorIp',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
  ],
  [Constants.CLOUD]: [
    {
      name: 'ID',
      key: 'id',
      sortable: true,
      width: '120px',
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      minWidth: '100px',
    },
    {
      name: 'Monitor',
      key: 'name',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'monitor',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'status',
      key: 'status',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '200px',
    },
    {
      name: 'Type',
      key: 'type',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'type',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'Resource/Region',
      key: 'region',
      sortable: true,
      width: '200px',
      searchable: true,
      reorderable: true,
      resizable: true,
      minWidth: '100px',
    },
    {
      name: 'Account ID',
      key: 'accountId',
      searchable: true,
      reorderable: true,
      resizable: true,
      width: '150px',
      minWidth: '100px',
    },
    {
      key: 'groups',
      name: 'Group',
      contextKey: 'groupContext',
      searchable: true,
      sortable: true,
      searchKey: 'groupsDisplay',
      sortKey: 'groupsDisplay',
      cellRender: 'groups',
      disable: true,
      minWidth: '100px',
      width: '120px',
    },
    {
      key: 'tags',
      name: 'Tags',
      searchable: true,
      sortable: true,
      hidden: true,
      searchKey: 'tagsStr',
      sortKey: 'tagsStr',
      minWidth: '100px',
      width: '120x',
    },
    {
      name: 'Active Alerts',
      searchable: true,
      key: 'alerts',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'alerts',
      minWidth: '100px',
    },
    {
      name: 'Creation Timestamp',
      key: 'creationTime',
      sortable: true,
      searchable: true,
      // reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
  ],
  [Constants.SERVICE_CHECK]: [
    {
      name: 'Service',
      key: 'monitorName',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      sortKey: 'monitorName',
      searchKey: 'monitorName',
      cellRender: 'monitor',
      disable: true,
      minWidth: '100px',
      width: '420px',
    },
    {
      name: 'Host',
      key: 'host',
      searchable: true,
      reorderable: true,
      hidden: true,
      resizable: true,
      width: '100px',
      // width: '120px',
    },
    {
      name: 'status',
      key: 'status',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      minWidth: '200px',
      width: '340px',
    },
    {
      name: 'Type',
      key: 'type',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '300px',
      cellRender: 'type',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'Response Time',
      key: 'responseTime',
      searchable: true,
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '400px',
      cellRender: 'label',
      minWidth: '100px',
    },
    {
      key: 'groups',
      name: 'Group',
      contextKey: 'groupContext',
      searchable: true,
      sortable: true,
      searchKey: 'groupsDisplay',
      sortKey: 'groupsDisplay',
      cellRender: 'groups',
      disable: true,
      minWidth: '100px',
      width: '300px',
    },
    {
      key: 'tags',
      name: 'Tags',
      searchable: true,
      sortable: true,
      hidden: true,
      searchKey: 'tagsStr',
      sortKey: 'tagsStr',
      minWidth: '100px',
      width: '120px',
    },
    // {
    //   key: 'action',
    //   name: 'Action',
    //   width: '150px',
    //   align: 'right',
    //   reorderable: false,
    //   cellRender: 'action',
    // },
    {
      name: 'Creation Timestamp',
      key: 'creationTime',
      sortable: true,
      searchable: true,
      // reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
    {
      name: 'Collector IP',
      key: 'collectorIp',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
  ],
  [Constants.VIRTUALIZATION]: [
    {
      name: 'ID',
      key: 'monitorId',
      sortable: true,
      width: '120px',
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      minWidth: '100px',
    },

    {
      name: 'Monitor',
      key: 'name',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'monitor',
      disable: true,
      minWidth: '100px',
      width: '240px',
    },
    {
      name: 'status',
      key: 'status',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '200px',
    },
    {
      name: 'IP',
      key: 'ip',
      sortable: true,
      width: '120px',
      searchable: true,
      reorderable: true,
      resizable: true,
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'Host',
      key: 'host',
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      minWidth: '100px',
      width: '120px',
    },
    {
      name: 'Type',
      key: 'type',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'type',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'CPU',
      searchable: true,
      key: 'cpu',
      // sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'progress',
      minWidth: '100px',
      width: '220px',
    },
    {
      name: 'Memory',
      searchable: true,
      key: 'memory',
      // sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'progress',
      minWidth: '100px',
      width: '220px',
    },
    {
      name: 'Disk',
      searchable: true,
      key: 'disk',
      // sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'progress',
      minWidth: '100px',
      width: '220px',
    },
    {
      key: 'groups',
      name: 'Group',
      contextKey: 'groupContext',
      searchable: true,
      sortable: true,
      searchKey: 'groupsDisplay',
      sortKey: 'groupsDisplay',
      cellRender: 'groups',
      disable: true,
      minWidth: '100px',
      width: '180px',
    },
    {
      key: 'tags',
      name: 'Tags',
      searchable: true,
      sortable: true,
      hidden: true,
      searchKey: 'tagsStr',
      sortKey: 'tagsStr',
      minWidth: '100px',
      width: '120px',
    },
    {
      name: 'VM',
      searchable: true,
      key: 'vms',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'vms',
      minWidth: '100px',
    },
    {
      name: 'Active Alerts',
      searchable: true,
      key: 'alerts',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'alerts',
      minWidth: '100px',
    },
    // {
    //   name: 'DataCenter',
    //   key: 'datacenter',
    //   searchable: true,
    //   reorderable: true,
    //   resizable: true,
    //   hidden: true,
    //   minWidth: '100px',
    //   width: '120px',
    // },
    // {
    //   name: 'Cluster',
    //   key: 'cluster',
    //   searchable: true,
    //   reorderable: true,
    //   resizable: true,
    //   hidden: true,
    //   minWidth: '100px',
    //   width: '120px',
    // },
    // {
    //   name: 'Resource Pool',
    //   key: 'resource',
    //   searchable: true,
    //   reorderable: true,
    //   resizable: true,
    //   hidden: true,
    //   width: '120px',
    // },
    // {
    //   name: 'DataStore',
    //   key: 'dataStore',
    //   searchable: true,
    //   reorderable: true,
    //   resizable: true,
    //   hidden: true,
    // },
    {
      name: 'OS Version',
      key: 'osVersion',
      // sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      minWidth: '220px',
      width: '220px',
    },
    {
      name: 'Creation Timestamp',
      key: 'creationTime',
      sortable: true,
      searchable: true,
      // reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
    {
      name: 'Collector IP',
      key: 'collectorIp',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
  ],
  [Constants.SERVICE]: [
    {
      name: 'Services',
      key: 'name',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'serviceName',
      disable: true,
      minWidth: '100px',
      width: '600px',
    },
    {
      name: 'Monitor',
      key: 'monitorName',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '250px',
      cellRender: 'monitorName',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'IP',
      key: 'ip',
      sortable: true,
      width: '220px',
      searchable: true,
      reorderable: true,
      resizable: true,
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'Host',
      key: 'host',
      width: '200px',
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      minWidth: '100px',
    },
    {
      name: 'Type',
      key: 'type',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '220px',
      cellRender: 'type',
      disable: true,
      minWidth: '100px',
    },

    {
      name: 'Group',
      searchable: true,
      key: 'groups',
      contextKey: 'groupContext',
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '250px',
      cellRender: 'groups',
      disable: true,
      minWidth: '100px',
    },
    // {
    //   key: 'instanceTags',
    //   name: 'Tags',
    //   searchable: true,
    //   sortable: true,
    //   hidden: true,
    //   searchKey: 'tagsStr',
    //   sortKey: 'tagsStr',
    //   width: '100px',
    //   minWidth: '100px',
    // },
    {
      key: 'status',
      name: 'status',
      sortable: true,
      searchable: true,
      width: '170px',
      cellRender: 'status',
      searchKey: 'statusFormatted',
      minWidth: '100px',
    },
    {
      name: 'Creation Timestamp',
      key: 'creationTime',
      sortable: true,
      searchable: true,
      // reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
  ],
  [Constants.PROCESS]: [
    {
      name: 'Process',
      key: 'name',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'processName',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'Monitor',
      key: 'monitorName',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      // width: '250px',
      cellRender: 'monitorName',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'IP',
      key: 'ip',
      sortable: true,
      width: '120px',
      searchable: true,
      reorderable: true,
      resizable: true,
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'Type',
      key: 'type',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'type',
      disable: true,
      minWidth: '100px',
    },
    {
      key: 'instanceTags',
      name: 'Tags',
      searchable: true,
      sortable: true,
      hidden: true,
      searchKey: 'instanceTagsStr',
      sortKey: 'instanceTagsStr',
      width: '100px',
      minWidth: '100px',
    },
    {
      key: 'status',
      name: 'status',
      sortable: true,
      searchable: true,
      width: '150px',
      cellRender: 'status',
      searchKey: 'statusFormatted',
      minWidth: '100px',
    },

    {
      name: 'CPU',
      searchable: true,
      key: 'cpu',
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'progress',
      width: '220px',
      minWidth: '100px',
    },
    {
      name: 'Memory',
      searchable: true,
      key: 'memoryBytes',
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'label',
      width: '220px',
      minWidth: '100px',
    },
    {
      name: 'Creation Timestamp',
      key: 'creationTime',
      sortable: true,
      searchable: true,
      // reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
  ],
  [Constants.INTERFACE]: [
    {
      name: 'Interface',
      key: 'interface',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'interfaceName',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'Interface Name',
      key: 'interfaceName',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'interfaceNameValue',
      disable: false,
      minWidth: '100px',
    },
    {
      name: 'Monitor',
      key: 'monitorName',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      // width: '200px',
      cellRender: 'monitorName',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'IP',
      key: 'ip',
      sortable: true,
      width: '120px',
      searchable: true,
      reorderable: true,
      resizable: true,
      disable: true,
      minWidth: '100px',
    },

    {
      name: 'Type',
      key: 'type',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '80px',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'Alias',
      key: 'alias',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      minWidth: '100px',
    },
    {
      key: 'instanceTags',
      name: 'Tags',
      searchable: true,
      sortable: true,
      hidden: true,
      searchKey: 'instanceTagsStr',
      sortKey: 'instanceTagsStr',
      width: '100px',
      minWidth: '100px',
    },
    {
      key: 'status',
      name: 'status',
      sortable: true,
      searchable: true,
      width: '150px',
      searchKey: 'statusFormatted',
      cellRender: 'status',
      minWidth: '100px',
    },
    // {
    //   name: 'Flow Status',
    //   searchable: true,
    //   key: 'flowStatus',
    //   sortable: true,
    //   reorderable: true,
    //   resizable: true,
    //   width: '120px',
    // },
    {
      name: 'Traffic',
      searchable: true,
      key: 'interfaceTraffic',
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'label',
      minWidth: '100px',
    },
    // {
    //   name: 'IN Traffic',
    //   searchable: true,
    //   key: 'inTraffic',
    //   sortable: true,
    //   reorderable: true,
    //   resizable: true,
    // },
    // {
    //   name: 'OUT Traffic',
    //   searchable: true,
    //   key: 'outTraffic',
    //   sortable: true,
    //   reorderable: true,
    //   resizable: true,
    // },
    {
      key: 'errorPackets',
      name: 'Error Packets',
      searchable: true,
      sortable: true,
      align: 'center',
      cellRender: 'label',
      minWidth: '100px',
    },
    // {
    //   key: 'receivedErrorPackets',
    //   name: 'Received Error Packets',
    //   searchable: true,
    //   sortable: true,
    //   align: 'center',
    //   cellRender: 'label',
    // },
    // {
    //   key: 'sendErrorPackets',
    //   name: 'Sent Error Packets',
    //   searchable: true,
    //   sortable: true,
    //   align: 'center',
    //   cellRender: 'label',
    // },
    {
      key: 'trafficPercent',
      name: 'Interface Utilization',
      cellRender: 'progress',
      searchable: true,
      sortable: true,
      width: '220px',
      minWidth: '100px',
    },
    {
      key: 'interfaceIP',
      name: 'Interface IP',
      searchable: true,
      sortable: true,
      width: '220px',
      minWidth: '100px',
    },
    {
      name: 'Creation Timestamp',
      key: 'creationTime',
      sortable: true,
      searchable: true,
      // reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
  ],
  [Constants.WAN_LINK]: [
    {
      name: 'WAN Link Name',
      key: 'name',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'wanLinkName',
      // width: '250px',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'Monitor',
      key: 'monitorName',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'monitorName',
      // width: '200px',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'Type',
      key: 'type',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '80px',
      disable: true,
      minWidth: '100px',
    },

    {
      name: 'WAN Probe Type',
      key: 'wanProbe',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '150px',
      minWidth: '100px',
      // disable: true,
    },
    {
      name: 'Source IP',
      key: 'sourceIP',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      minWidth: '100px',
      // disable: true,
    },
    {
      name: 'Destination IP',
      key: 'destinationIP',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      minWidth: '100px',
      // disable: true,
    },
    {
      name: 'Source Interface',
      key: 'sourceInterface',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '180px',
      minWidth: '100px',
      // disable: true,
    },
    {
      key: 'instanceTags',
      name: 'Tags',
      searchable: true,
      sortable: true,
      hidden: true,
      searchKey: 'instanceTagsStr',
      sortKey: 'instanceTagsStr',
      width: '100px',
      minWidth: '100px',
    },
    {
      key: 'status',
      name: 'status',
      sortable: true,
      searchable: true,
      searchKey: 'statusFormatted',
      cellRender: 'status',
      width: '150px',
      minWidth: '80px',
      // disable: true,
    },
    {
      key: 'rtt',
      name: 'RTT',
      sortable: true,
      searchable: true,
      width: '100px',
      minWidth: '80px',
      cellRender: 'label',
      // disable: true,
    },
  ],
  [Constants.OTHER]: [
    {
      name: 'ID',
      key: 'monitorId',
      sortable: true,
      // width: '120px',
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      minWidth: '100px',
    },
    {
      name: 'Monitor',
      key: 'name',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'monitor',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'status',
      key: 'status',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '200px',
    },
    {
      name: 'IP',
      key: 'ip',
      sortable: true,
      // width: '120px',
      searchable: true,
      reorderable: true,
      resizable: true,
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'Host',
      key: 'host',
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      minWidth: '100px',
    },
    {
      name: 'Type',
      key: 'type',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      // width: '120px',
      cellRender: 'type',
      disable: true,
      minWidth: '100px',
    },
    {
      key: 'tags',
      name: 'Tags',
      searchable: true,
      sortable: true,
      hidden: true,
      searchKey: 'tagsStr',
      sortKey: 'tagsStr',
      width: '100px',
      minWidth: '100px',
    },
    {
      name: 'Response Time',
      searchable: true,
      key: 'responseTime',
      // sortable: true,
      reorderable: true,
      resizable: true,
      // width: '180px',
      cellRender: 'label',
      minWidth: '100px',
    },
    {
      name: 'Packet Lost',
      searchable: true,
      key: 'packetLost',
      // sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'progress',
      width: '220px',
      minWidth: '100px',
    },
    {
      key: 'groups',
      name: 'Group',
      contextKey: 'groupContext',
      searchable: true,
      sortable: true,
      searchKey: 'groupsDisplay',
      sortKey: 'groupsDisplay',
      cellRender: 'groups',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'Active Alerts',
      searchable: true,
      key: 'alerts',
      // sortable: true,
      reorderable: true,
      resizable: true,
      // width: '120px',
      cellRender: 'alerts',
      minWidth: '100px',
    },
    {
      name: 'Creation Timestamp',
      key: 'creationTime',
      sortable: true,
      searchable: true,
      // reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
    {
      name: 'Collector IP',
      key: 'collectorIp',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
  ],
  [Constants.HYPERCONVERGED_INFRASTRUCTURE]: [
    {
      name: 'ID',
      key: 'monitorId',
      sortable: true,
      width: '120px',
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      minWidth: '100px',
    },
    {
      name: 'Monitor',
      key: 'name',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'monitor',
      disable: true,
      minWidth: '100px',
      width: '320px',
    },
    {
      key: 'status',
      name: 'status',
      sortable: true,
      searchable: true,
      searchKey: 'statusFormatted',
      cellRender: 'status',
      width: '200px',
    },
    {
      name: 'IP',
      key: 'ip',
      sortable: true,
      width: '200px',
      searchable: true,
      reorderable: true,
      resizable: true,
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'Type',
      key: 'type',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'type',
      disable: true,
      minWidth: '100px',
    },
    {
      key: 'tags',
      name: 'Tags',
      searchable: true,
      sortable: true,
      hidden: true,
      searchKey: 'tagsStr',
      sortKey: 'tagsStr',
      width: '100px',
      minWidth: '100px',
    },
    {
      name: 'CPU',
      searchable: true,
      key: 'cpu',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '220px',
      minWidth: '100px',
      cellRender: 'progress',
    },
    {
      name: 'Memory',
      searchable: true,
      key: 'memory',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '220px',
      minWidth: '100px',
      cellRender: 'progress',
    },
    {
      key: 'groups',
      name: 'Group',
      contextKey: 'groupContext',
      searchable: true,
      sortable: true,
      searchKey: 'groupsDisplay',
      sortKey: 'groupsDisplay',
      cellRender: 'groups',
      disable: true,
      width: '120px',
      minWidth: '100px',
    },
    {
      name: 'VM',
      searchable: true,
      key: 'vms',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'vms',
      minWidth: '100px',
    },
    {
      name: 'Cluster',
      key: 'cluster',
      searchable: true,
      reorderable: true,
      resizable: true,
      minWidth: '100px',
      width: '120px',
    },
    {
      name: 'Active Alerts',
      searchable: true,
      key: 'alerts',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'alerts',
      minWidth: '100px',
    },
  ],
  [Constants.SDN]: [
    {
      name: 'ID',
      key: 'monitorId',
      sortable: true,
      width: '120px',
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      minWidth: '100px',
    },
    {
      name: 'Monitor',
      key: 'name',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'monitor',
      disable: true,
      minWidth: '100px',
      width: '300px',
    },
    {
      key: 'status',
      name: 'status',
      sortable: true,
      searchable: true,
      searchKey: 'statusFormatted',
      cellRender: 'status',
      width: '200px',
    },
    {
      name: 'IP',
      key: 'ip',
      sortable: true,
      width: '200px',
      searchable: true,
      reorderable: true,
      resizable: true,
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'Type',
      key: 'type',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'type',
      disable: true,
      minWidth: '100px',
    },
    {
      key: 'tags',
      name: 'Tags',
      searchable: true,
      sortable: true,
      hidden: true,
      searchKey: 'tagsStr',
      sortKey: 'tagsStr',
      width: '100px',
      minWidth: '100px',
    },
    {
      name: 'CPU',
      searchable: true,
      key: 'cpu',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '250px',
      minWidth: '100px',
      cellRender: 'progress',
    },
    {
      name: 'Memory',
      searchable: true,
      key: 'memory',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '250px',
      minWidth: '100px',
      cellRender: 'progress',
    },
    {
      name: 'Fabric Health',
      searchable: true,
      key: 'fabricHealth',
      reorderable: true,
      resizable: true,
      width: '250px',
      minWidth: '100px',
      hidden: true,
    },
    {
      name: 'Tenant Health',
      searchable: true,
      key: 'tenantHealth',
      reorderable: true,
      resizable: true,
      width: '250px',
      minWidth: '100px',
      hidden: true,
    },
    {
      key: 'groups',
      name: 'Group',
      contextKey: 'groupContext',
      searchable: true,
      sortable: true,
      searchKey: 'groupsDisplay',
      sortKey: 'groupsDisplay',
      cellRender: 'groups',
      disable: true,
      width: '300px',
      minWidth: '100px',
    },
    {
      name: 'Active Alerts',
      searchable: true,
      key: 'alerts',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '180px',
      cellRender: 'alerts',
      minWidth: '100px',
    },
  ],
  [Constants.STORAGE]: [
    {
      name: 'ID',
      key: 'id',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      width: '100px',
    },
    {
      name: 'Host',
      key: 'host',
      searchable: true,
      reorderable: true,
      hidden: true,
      resizable: true,
      width: '100px',
      // width: '120px',
    },
    {
      name: 'Monitor',
      key: 'name',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'monitor',
      disable: true,
      width: '370px',
    },
    {
      name: 'status',
      key: 'status',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      width: '200px',
    },
    {
      name: 'IP',
      key: 'ip',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      disable: true,
      width: '200px',
      minWidth: '100px',
    },
    {
      name: 'Type',
      key: 'type',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '220px',
      cellRender: 'type',
      disable: true,
      minWidth: '100px',
    },

    {
      name: 'Vendor',
      key: 'cloudType',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      width: '250px',
    },

    {
      key: 'groups',
      name: 'Group',
      contextKey: 'groupContext',
      searchable: true,
      sortable: true,
      searchKey: 'groupsDisplay',
      sortKey: 'groupsDisplay',
      cellRender: 'groups',
      disable: true,
      width: '250px',
      minWidth: '100px',
    },
    {
      key: 'tags',
      name: 'Tags',
      searchable: true,
      sortable: true,
      hidden: true,
      searchKey: 'tagsStr',
      sortKey: 'tagsStr',
      width: '100px',
      minWidth: '100px',
    },
    {
      name: 'Active Alerts',
      searchable: true,
      key: 'alerts',
      // sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'alerts',
      minWidth: '100px',
      width: '320px',
    },
  ],
  [Constants.CONTAINER]: [
    {
      name: 'Container',
      key: 'container',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'containerName',
      width: '250px',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'Monitor',
      key: 'monitorName',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'monitorName',
      // width: '200px',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'Monitor IP',
      key: 'ip',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      // hidden: true,
      width: '180px',
      minWidth: '100px',
    },
    {
      name: 'Type',
      key: 'type',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '80px',
      disable: true,
      minWidth: '100px',
    },
    {
      key: 'status',
      name: 'status',
      sortable: true,
      searchable: true,
      searchKey: 'statusFormatted',
      cellRender: 'status',
      width: '150px',
      minWidth: '80px',
      // disable: true,
    },
    {
      key: 'image',
      name: 'Image',
      sortable: true,
      searchable: true,
      cellRender: 'image',
      width: '150px',
      minWidth: '80px',
      // disable: true,
    },
    {
      name: 'Process',
      key: 'process',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'dockerProcesses',
      // disable: true,
      minWidth: '100px',
    },
    {
      name: 'CPU(%)',
      searchable: true,
      key: 'cpu',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '220px',
      minWidth: '100px',
      cellRender: 'progress',
    },
    {
      name: 'Memory',
      searchable: true,
      key: 'memoryBytes',
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'label',
      width: '220px',
      minWidth: '100px',
    },
    {
      name: 'Restart',
      searchable: true,
      key: 'restarts',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '220px',
      minWidth: '100px',
    },
  ],
  [Constants.CONTAINER_ORCHESTRATION]: [
    {
      name: 'ID',
      key: 'monitorId',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      width: '100px',
    },
    {
      name: 'Host',
      key: 'host',
      searchable: true,
      reorderable: true,
      hidden: false,
      resizable: true,
      width: '100px',
      // width: '120px',
    },
    {
      name: 'Monitor',
      key: 'name',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'monitor',
      disable: true,
      width: '220px',
    },
    {
      name: 'status',
      key: 'status',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '200px',
    },
    {
      name: 'IP',
      key: 'ip',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      disable: true,
      width: '100px',
      minWidth: '100px',
    },
    {
      name: 'Type',
      key: 'type',
      searchable: true,
      sortable: true,
      reorderable: true,
      resizable: true,
      width: '120px',
      cellRender: 'type',
      disable: true,
      minWidth: '100px',
    },
    {
      name: 'CPU',
      searchable: true,
      key: 'cpu',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '220px',
      minWidth: '100px',
      cellRender: 'progress',
    },
    {
      name: 'Memory',
      searchable: true,
      key: 'memory',
      // sortable: true,
      reorderable: true,
      resizable: true,
      width: '220px',
      minWidth: '100px',
      cellRender: 'progress',
    },
    {
      key: 'tags',
      name: 'Tag',
      searchable: true,
      sortable: true,
      hidden: false,
      searchKey: 'tagsStr',
      sortKey: 'tagsStr',
      width: '100px',
      minWidth: '100px',
    },
    {
      key: 'groups',
      name: 'Group',
      contextKey: 'groupContext',
      searchable: true,
      sortable: true,
      searchKey: 'groupsDisplay',
      sortKey: 'groupsDisplay',
      cellRender: 'groups',
      disable: true,
      width: '250px',
      minWidth: '100px',
    },
    {
      name: 'Active Alerts',
      searchable: true,
      key: 'alerts',
      // sortable: true,
      reorderable: true,
      resizable: true,
      cellRender: 'alerts',
      minWidth: '100px',
      width: '120px',
    },
    {
      name: 'Collector IP',
      key: 'collectorIp',
      sortable: true,
      searchable: true,
      reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
    {
      name: 'Creation Timestamp',
      key: 'creationTime',
      sortable: true,
      searchable: true,
      // reorderable: true,
      resizable: true,
      hidden: true,
      width: '180px',
      minWidth: '100px',
    },
  ],
}
export const COLUMN_MAP = {
  'object.type': 'type',
  'entity.id': 'monitorId',
  'system.vendor.last': 'vendor',
  'system.model.last': 'model',
  'system.os.name.last': 'osName',
  'system.os.version.last': 'osVersion',
  'system.uptime.last': 'uptime',
  'system.cpu.type.last': 'cpuType',
  'system.cpu.description.last': 'cpuDescription',
  'system.memory.installed.bytes.last': 'memoryBytes',
  'system.cpu.percent.last': 'cpu',
  'esxi.cpu.percent.last': 'cpu',
  'citrix.xen.cpu.percent.last': 'cpu',
  'hyperv.cpu.percent.last': 'cpu',
  'esxi.vm': 'name',
  'esxi.vm.ip.last': 'ip',
  'citrix.xen.vm': 'name',
  'hyperv.vm': 'name',
  'hyperv.vm.ip.last': 'ip',
  'hyperv.vm.status.last': 'status',
  'citrix.xen.vm.ip.last': 'ip',
  'citrix.xen.vm.power.state.last': 'status',
  'citrix.xen.vm.status.last': 'status',
  'esxi.vm.power.state.last': 'status',
  'esxi.vm.status.last': 'status',
  'system.memory.used.percent.last': 'memory',
  'esxi.memory.used.percent.last': 'memory',
  'citrix.xen.memory.used.percent.last': 'memory',
  'hyperv.memory.used.percent.last': 'memory',
  'system.disk.used.percent.last': 'disk',
  'esxi.disk.used.percent.last': 'disk',
  'citrix.xen.disk.used.percent.last': 'disk',
  'hyperv.disk.used.percent.last': 'disk',
  'system.name.last': 'systemName',
  'system.description.last': 'systemDescription',
  'system.location.last': 'location',
  'system.oid.last': 'oid',
  'interface.bit.type.last': 'interfaceBitType',
  'ping.packet.lost.percent.last': 'packetLost',
  'availability.packet.lost.percent.last': 'packetLost',
  'ping.latency.ms.last': 'responseTime',
  'service.check.latency.ms.last': 'responseTime',
  'status.last': 'status',
  'availability.latency.ms.last': 'responseTime',
  'port.latency.ms.last': 'responseTime',
  'email.latency.ms.last': 'responseTime',
  'ntp.latency.ms.last': 'responseTime',
  'radius.latency.ms.last': 'responseTime',
  'url.latency.ms.last': 'responseTime',
  'dns.latency.ms.last': 'responseTime',
  'ftp.latency.ms.last': 'responseTime',
  'system.process.cpu.percent.last': 'cpu',
  'system.process.memory.used.percent.last': 'memory',
  'system.process.memory.used.bytes.last': 'memoryBytes',
  'system.process.threads.last': 'processThreads',
  'system.process.io.bytes.per.sec.last': 'processBytes',
  'interface.in.traffic.bits.per.sec.last': 'inTraffic',
  'interface.out.traffic.bits.per.sec.last': 'outTraffic',
  'interface.traffic.bits.per.sec.last': 'interfaceTraffic',
  'interface.traffic.utilization.percent.last': 'trafficPercent',
  'interface.ip.address.last': 'interfaceIP',
  'interface.received.error.packets.last': 'receivedErrorPackets',
  'interface.sent.error.packets.last': 'sendErrorPackets',
  'interface.error.packets.last': 'errorPackets',
  interface: 'interface',
  'interface.name.last': 'interfaceName',
  'interface.alias.last': 'alias',
  'interface.flow.status.last': 'flowStatus',
  'system.service.status.last': 'status',
  'system.service.display.name.last': 'name',
  status: 'status',
  'interface.status.last': 'status',
  'system.process.status.last': 'status',
  'total.interfaces.last': 'interfaces',
  'total.vms.last': 'vms',
  'system.process': 'name',
  'system.service': 'name',
  ipsla: 'name',
  'ipsla.latency.ms.last': 'rtt',
  // 'ipsla.operation.type.last': 'wanProbe',
  'ipsla.status.last': 'status',
  'source.ip.address': 'sourceIP',
  'destination.ip.address': 'destinationIP',
  'source.interface.name': 'sourceInterface',
  'nutanix.vm': 'name',
  'nutanix.cpu.percent.last': 'cpu',
  'nutanix.memory.used.percent.last': 'memory',
  'nutanix.vm.ip.last': 'ip',
  'nutanix.vm.status.last': 'status',
  'nutanix.cluster.last': 'cluster',
  'cisco.vmanage.cpu.percent.last': 'cpu',
  'cisco.vbond.cpu.percent.last': 'cpu',
  'cisco.vsmart.cpu.percent.last': 'cpu',
  'cisco.vedge.cpu.percent.last': 'cpu',
  'cisco.vmanage.memory.used.percent.last': 'memory',
  'cisco.vbond.memory.used.percent.last': 'memory',
  'cisco.vsmart.memory.used.percent.last': 'memory',
  'cisco.vedge.memory.used.percent.last': 'memory',
  critical: 'criticalAlerts',
  down: 'downAlerts',
  major: 'majorAlerts',
  unreachable: 'unreachableAlerts',
  warning: 'warningAlerts',
  clear: 'clearAlerts',
  'system.serial.number.last': 'serialNumber',
  'esxi.os.version.last': 'osVersion',
  'citrix.xen.os.version.last': 'osVersion',
  'hyperv.os.version.last': 'osVersion',
  'system.model.number.last': 'model',
  'object.creation.time': 'creationTime',
  'object.event.processors': 'collectorIp',
  'object.target': 'target',
  'object.ip': 'ip',
  'object.host': 'host',
  monitor: 'name',
  'object.port': 'port',
  'object.discovery.method': 'objectDiscoveryMethod',
  // 'object.groups': 'groups',
  // 'object.tags': 'tags',
  // 'object.tags': 'systemTags',
  'object.vendor': 'cloudType',
  // 'app.process': 'apps',
  'object.name': 'monitorName',
  'critical.count': 'criticalAlerts',
  'down.count': 'downAlerts',
  'major.count': 'majorAlerts',
  'unreachable.count': 'unreachableAlerts',
  'warning.count': 'warningAlerts',
  'clear.count': 'clearAlerts',
  severity: 'severity',
  'cisco.meraki.cpu.percent.last': 'cpu',
  'cisco.meraki.memory.used.percent.last': 'memory',
  'cisco.meraki.switch.cpu.percent.last': 'cpu',
  'cisco.meraki.switch.memory.used.percent.last': 'memory',
  'cisco.meraki.security.cpu.percent.last': 'cpu',
  'cisco.meraki.security.memory.used.percent.last': 'memory',
  'cisco.meraki.radio.cpu.percent.last': 'cpu',
  'cisco.meraki.radio.memory.used.percent.last': 'memory',
  'cisco.meraki.security.public.ip.address.last': 'ip',
  'system.process.name.last': 'processName',
  'cisco.aci.fabric.global.health.score.last': 'fabricHealth',
  'cisco.aci.tenant.global.health.score.last': 'tenantHealth',
  'docker.container.image.last': 'image',
  'docker.container.status.last': 'status',
  'docker.container.memory.used.bytes.last': 'memoryBytes',
  // 'docker.containers.last': 'container',
  'docker.container.cpu.percent.last': 'cpu',
  'docker.container.restarts.last': 'restarts',
  'docker.container.processes.last': 'process',
  'docker.container': 'container',
  'kubernetes.cpu.percent.last': 'cpu',
  'kubernetes.memory.percent.last': 'memory',
  'nsxt.cpu.used.percent.last': 'cpu',
  'nsxt.memory.used.percent.last': 'memory',
}

export const IGNORE_CLOUD_TYPE_TO_DRILLDOWN = [
  'azure cloud',
  'aws cloud',
  'office 365',
]

export const instanceTypeMap = {
  [Constants.HYPER_V]: 'hyperv.vm',
  [Constants.CITRIX_XEN]: 'citrix.xen.vm',
  [Constants.VMWARE_ESXI]: 'esxi.vm',
  [Constants.NUTANIX]: 'nutanix.vm',
}
