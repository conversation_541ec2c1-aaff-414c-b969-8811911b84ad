<script>
import FindIndex from 'lodash/findIndex'
import Bus from '@utils/emitter'
import { authComputed } from '@state/modules/auth'
import { getExplorerSavedViewApi } from '@api/explorer-api'

export default {
  name: 'MetricExplorerSavedViewProvider',
  provide() {
    const metricExplorerSavedView = {
      deleteView: this.deleteView,
    }
    Object.defineProperty(metricExplorerSavedView, 'savedView', {
      enumerable: true,
      get: () => {
        return this.savedView
      },
    })
    Object.defineProperty(metricExplorerSavedView, 'loading', {
      enumerable: true,
      get: () => {
        return this.loading
      },
    })
    return { MetricExplorerSavedViewProvider: metricExplorerSavedView }
  },
  props: {
    monitor: {
      type: Object,
      default: undefined,
    },
  },

  data() {
    return {
      savedView: [],
      loading: true,
    }
  },
  computed: {
    ...authComputed,
  },
  created() {
    Bus.$on('metric-explorer:view-saved', this.addMetricExplorerView)
    Bus.$on('metric-explorer:view-update', this.updateMetricExplorerView)

    this.$once('hook:beforeDestroy', () => {
      Bus.$off('metric-explorer:view-saved', this.addMetricExplorerView)
      Bus.$on('metric-explorer:view-update', this.updateMetricExplorerView)
    })
    this.fetchMetricExplorerView()
  },
  methods: {
    fetchMetricExplorerView() {
      if (this.hasPermission(this.$constants.METRIC_EXPLORER_READ_PERMISSION)) {
        this.loading = true
        return getExplorerSavedViewApi(this.monitor, 'metric').then((data) => {
          this.loading = false
          this.savedView = Object.freeze(data)

          return data
        })
      }
    },
    addMetricExplorerView(view) {
      if (view) {
        return this.fetchMetricExplorerView().then((data) => {
          const viewContext = data.find((item) => item.id === view.id)

          if (viewContext) {
            if (viewContext?.security === 'Private') {
              if (
                viewContext?.users?.length &&
                viewContext?.users?.includes(this.user.id)
              ) {
                this.$emit('select-view', viewContext)
              }
            } else {
              this.$emit('select-view', viewContext)
            }
          }
        })
        // this.savedView = [...(this.savedView || []), view].filter(Boolean)
      }
    },
    deleteView(id) {
      this.savedView = this.savedView.filter((item) => item.id !== id)
    },
    updateMetricExplorerView(view) {
      if (view) {
        const index = FindIndex(this.savedView, { id: view.id })

        this.savedView = [
          ...this.savedView.slice(0, index),
          { ...this.savedView[index], ...view },
          ...this.savedView.slice(index + 1),
        ]
      }
    },
  },

  render() {
    if (
      this.$scopedSlots.default &&
      typeof this.$scopedSlots.default === 'function'
    ) {
      return this.$scopedSlots.default({
        savedView: this.savedView,
        loading: this.loading,
      })
    }
    return this.$createElement('div', this.$slots.default)
  },
}
</script>
