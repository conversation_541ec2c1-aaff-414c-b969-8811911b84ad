<template>
  <MonitorProvider :search-params="searchParams">
    <div class="flex flex-col flex-1 min-h-0 px-4 content-inner-panel">
      <FlotoPageHeader main-header title="Start Live Trail">
        <template v-slot:back-button>
          <a class="text-neutral-light mr-3" @click.stop="$router.back">
            <MIcon id="back-btn-id" name="chevron-left" size="lg" />
          </a>
        </template>
        <template v-slot:title>
          <div class="flex-1 flex items-center justify-end">
            <MButton
              :title="screenCounts === 1 ? 'Split Screen' : 'Full Screen'"
              variant="neutral-lightest"
              class="squared-button ml-2"
              @click="screenCounts = screenCounts === 1 ? 2 : 1"
            >
              <MIcon
                :name="screenCounts === 1 ? 'splited-screen' : 'screen'"
                class="excluded-header-icon"
              />
            </MButton>

            <MButton
              title="Settings"
              variant="neutral-lightest"
              class="squared-button ml-2"
              @click="isPreferenceModalVisible = true"
            >
              <MIcon name="cog" class="excluded-header-icon" />
            </MButton>

            <MButton
              id="create-log-parser"
              class="ml-2"
              @click="navigateToLogParser"
            >
              Create Log Parser Plugin
            </MButton>
          </div>
        </template>
      </FlotoPageHeader>
      <div class="flex flex-1 flex-col min-h-0 mt-4">
        <FlotoContentLoader :loading="loading" class="min-h-0">
          <div class="flex flex-1 min-h-0">
            <LogTail
              v-for="screen in tailScreens"
              :key="screen"
              :can-split="screenCounts <= 1"
              :source-options="sourceOptions"
              :formatter="appliedFormatter"
              @screens="screenCounts = $event"
            />
          </div>
        </FlotoContentLoader>
      </div>
      <FlotoConfirmModal
        v-if="isPreferenceModalVisible"
        :open="isPreferenceModalVisible"
        variant="primary"
        hide-icon
        @hide="isPreferenceModalVisible = false"
      >
        <template v-slot:message>
          <FlotoForm @submit="handleUpdateUiPreference">
            <FlotoFormItem label="Line Spacing" rules="required">
              <FlotoDropdownPicker
                v-model="formatter.spacing"
                :options="lineSpacingOptions"
                :searchable="false"
              />
            </FlotoFormItem>
            <FlotoFormItem label="Test Size" rules="required">
              <FlotoDropdownPicker
                v-model="formatter.size"
                :options="textSizeOptions"
                :searchable="false"
              />
            </FlotoFormItem>
            <template v-slot:submit="{ submit }">
              <div class="text-right">
                <MButton id="test-btn-id" class="mr-2" @click="submit">
                  Apply
                </MButton>
                <MButton
                  variant="default"
                  @click="isPreferenceModalVisible = false"
                >
                  Cancel
                </MButton>
              </div>
            </template>
          </FlotoForm>
        </template>
        <template v-slot:action-container>
          <span />
        </template>
      </FlotoConfirmModal>
    </div>
  </MonitorProvider>
</template>

<script>
import Range from 'lodash/range'
import CloneDeep from 'lodash/cloneDeep'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
// import { getSourceApi } from '@components/widgets/widgets-api'

import LogTail from '../components/live-tail/log-tail.vue'
import {
  LINE_SPACING_OPTIONS,
  TEXT_SIZE_OPTIONS,
  getSourceFromEvent,
} from '../helpers/log.helper'

export default {
  name: 'LiveTail',
  components: { LogTail, MonitorProvider },
  data() {
    return {
      screenCounts: 1,
      sourceOptions: [],
      loading: true,
      isPreferenceModalVisible: false,
      appliedFormatter: {
        size: '12px',
        spacing: `regular`,
      },
      formatter: {
        size: '12px',
        spacing: `regular`,
      },
    }
  },
  computed: {
    tailScreens() {
      return Range(0, this.screenCounts)
    },
    searchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.OTHER,
          this.$constants.CLOUD,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.SDN,
          this.$constants.STORAGE,
          this.$constants.CONTAINER_ORCHESTRATION,
        ],
      }
    },
    lineSpacingOptions() {
      return LINE_SPACING_OPTIONS
    },
    textSizeOptions() {
      return TEXT_SIZE_OPTIONS
    },
  },
  created() {
    this.fetchSources()
  },
  methods: {
    fetchSources() {
      getSourceFromEvent().then((data) => {
        this.sourceOptions = Object.freeze(data)
        this.loading = false
      })
    },
    handleUpdateUiPreference() {
      this.appliedFormatter = CloneDeep(this.formatter)
      this.isPreferenceModalVisible = false
    },
    navigateToLogParser() {
      this.$router.push(
        this.$modules.getModuleRoute('plugin-library', 'create-log-parser')
      )
    },
  },
}
</script>
