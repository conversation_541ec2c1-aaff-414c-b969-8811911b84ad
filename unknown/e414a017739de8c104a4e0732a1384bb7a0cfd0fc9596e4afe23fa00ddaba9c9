<template>
  <FlotoDrawer
    wrap-class-name="ant-drawer-close-btn-center"
    :open="open"
    :scrolled-content="false"
    width="55%"
    @hide="$emit('hide')"
  >
    <template v-slot:title>
      <div class="w-full flex justify-between items-center">
        <div class="flex items-center">
          <MButton
            shape="circle"
            class="squared-button mr-2"
            variant="neutral-lightest"
          >
            <MonitorType
              :type="viewItem['deviceType']"
              disable-tooltip
              :center="false"
              class="p-1"
            />
          </MButton>

          <div class="flex flex-col w-11/12">
            <div class="flex items-center">
              <h5
                class="text-ellipsis mb-0 mr-2 page-text-color"
                :title="viewItem.device"
                >{{ viewItem.device }}
              </h5>
              <span class="text-neutral-light font-500 text-xs mt-1">
                {{ ` | ${viewItem.ip} | ${viewItem.vendor}` }}
              </span>
            </div>

            <div class="inline-flex flex-grow-0 min-w-0 flex-shrink-0">
              <GroupPicker
                :value="viewItem.groups"
                disabled
                :wrap="false"
                style="flex: none"
              />
              <LooseTags :value="viewItem.tags" disabled />
            </div>
          </div>
        </div>
        <MPermissionChecker :permission="$constants.NCM_UPDATE_PERMISSION">
          <MButton
            title="Terminal"
            variant="neutral-lightest"
            class="mx-2 squared-button"
            @click="$emit('terminal')"
          >
            <MIcon name="terminal" />
          </MButton>
        </MPermissionChecker>
        <!-- <MButton
          variant="transparent"
          :shadow="false"
          :rounded="false"
          class="p-0"
        >
          <MIcon name="backup-now" class="mb-3" />
        </MButton> -->
      </div>
    </template>

    <MRow :gutter="0" class="mx-3">
      <MTab v-model="activeMainTab" class="w-full" @change="handleTabChange">
        <MTabPane v-for="tab in tabs" :key="tab.key" :tab="tab.name" />
        <MTabPane v-if="hasComplianceReadPermission" key="rules" tab="Rules" />
      </MTab>
    </MRow>

    <div
      class="flex flex-col min-h-0 flex-1 w-full mt-2 px-2 pl-3 overflow-scroll"
    >
      <template v-if="activeMainTab === 'device_detail'">
        <MRow class="wiget-background-color rounded p-3" :gutter="0">
          <MCol
            v-for="(detail, index) in deviceDetails"
            :key="detail.key"
            :size="(index + 1) % 2 !== 0 ? 5 : 7"
            :class="{
              'mb-4':
                index !== deviceDetails.length - 1 &&
                index !== deviceDetails.length - 2,
            }"
          >
            <MRow :gutter="0">
              <MCol
                :size="4"
                class="text-neutral-light font-500"
                style="flex-shrink: 0; color: var(--neutral-regular)"
                >{{ detail.label }}</MCol
              >
              <MCol
                :size="8"
                class="text-ellipsis"
                :class="{
                  flex: detail.key === 'deviceType',
                }"
                :title="
                  detail.key === 'lastBackupTime'
                    ? undefined
                    : updatedViewItem[detail.key] || ''
                "
              >
                <MonitorType
                  v-if="detail.key === 'deviceType'"
                  width="15px"
                  :type="viewItem[detail.key]"
                  disable-tooltip
                  :center="false"
                  class="mr-1"
                />

                {{
                  detail.key === 'lastBackupTime'
                    ? ''
                    : updatedViewItem[detail.key] || ''
                }}
                <MRow v-if="detail.key === 'lastBackupTime'" :gutter="0">
                  <MCol
                    :size="11"
                    class="text-ellipsis"
                    :title="lastBackupTime"
                  >
                    {{ lastBackupTime }}
                  </MCol>
                  <MCol v-if="hasCreateDeletePermission" :size="1">
                    <MTooltip
                      style="display: flex; flex: unset; align-items: center"
                    >
                      <template v-slot:trigger>
                        <MButton
                          variant="transparent"
                          :shadow="false"
                          :rounded="false"
                          class="p-0"
                          @click="configBackup"
                        >
                          <MIcon name="backup-now" class="mb-3" />
                        </MButton>
                      </template>

                      Click to Backup
                    </MTooltip>
                  </MCol>
                </MRow>
              </MCol>
            </MRow>
          </MCol>
        </MRow>

        <MRow :gutter="0" class="w-full">
          <MCol>
            <MDivider class="" />

            <h5 class="text-primary">Config Conflict</h5>

            <div class="flex mb-1 items-center">
              <span class="metric-details mr-4">
                Baseline-Running-Conflict</span
              >
              <span>
                <MButton
                  v-if="
                    viewItem.baselineRunningConflictStatus ===
                    'Conflict Detected'
                  "
                  variant="transparent"
                  :shadow="false"
                  :rounded="false"
                  class="p-0"
                  @click="drillDown(2)"
                >
                  <span class="text-secondary-red">Conflict Detected</span>
                </MButton>
                <span
                  v-else
                  :class="`${
                    viewItem.baselineRunningConflictStatus === 'In Sync'
                      ? 'text-secondary-green'
                      : 'text-neutral-light'
                  }`"
                >
                  {{ viewItem.baselineRunningConflictStatus }}</span
                ></span
              >
            </div>

            <div class="flex items-center">
              <span class="metric-details mr-4"> Startup-Running-Conflict</span>
              <span>
                <MButton
                  v-if="
                    viewItem.startupRunningConflictStatus ===
                    'Conflict Detected'
                  "
                  variant="transparent"
                  :shadow="false"
                  :rounded="false"
                  class="p-0"
                  @click="drillDown(0)"
                >
                  <span class="text-secondary-red">Conflict Detected</span>
                </MButton>
                <span
                  v-else
                  :class="`${
                    viewItem.startupRunningConflictStatus === 'In Sync'
                      ? 'text-secondary-green'
                      : 'text-neutral-light'
                  }`"
                >
                  {{ viewItem.startupRunningConflictStatus }}</span
                ></span
              >
            </div>

            <MDivider class="mb-0" />
          </MCol>
        </MRow>

        <MRow :gutter="0" class="w-full">
          <MTab v-model="activeTab" class="w-full" @change="handleTabChange">
            <MTabPane :key="FILE_TYPE.RUNNING" tab="Running Config" />
            <MTabPane :key="FILE_TYPE.STARTUP" tab="Startup Config" />
          </MTab>
        </MRow>

        <MRow class="mt-2 justify-between" :gutter="0">
          <MCol :size="4" class="flex items-center">
            <label
              class="label-strip text-neutral-light flex justify-between mr-3"
              >Version</label
            >
            <FlotoDropdownPicker
              v-model="version"
              :options="versionOptions"
              @change="requestNetworkBackup"
            >
              <template v-slot:after-menu-text="{ item }">
                <span class="ml-2 mb-1">
                  <MIcon
                    v-if="item.isBaselineVersion"
                    class="relative cursor-pointer text-secondary-yellow"
                    name="star"
                    size="sm"
                  />
                </span>
              </template>
            </FlotoDropdownPicker>
          </MCol>

          <MCol :size="7" class="flex items-center text-right justify-end">
            <MInput
              v-if="shouldShowBackupInHtml"
              v-model="searchTerm"
              class="search-box mr-2"
              placeholder="Search"
              name="search"
            >
              <template v-slot:prefix>
                <MIcon name="search" />
              </template>
              <template v-if="searchTerm" v-slot:suffix>
                <MIcon
                  name="times-circle"
                  class="text-neutral-light cursor-pointer"
                  @click="searchTerm = undefined"
                />
              </template>
            </MInput>

            <MPermissionChecker :permission="$constants.NCM_CREATE_PERMISSION">
              <MTooltip
                v-if="activeTab === FILE_TYPE.RUNNING"
                style="display: flex; flex: unset; align-items: center"
              >
                <template v-slot:trigger>
                  <MButton
                    variant="neutral-lightest"
                    :shadow="false"
                    :rounded="false"
                    class="mr-2"
                    :disabled="!backupData || isBaseLineProcessing"
                    @click="assignBaseline"
                  >
                    <MIcon
                      name="star"
                      :class="{
                        'text-secondary-yellow': isSelectedVersionIsBaseline,
                      }"
                    />
                  </MButton>
                </template>
                Click to
                {{
                  isSelectedVersionIsBaseline
                    ? 'Remove Baseline'
                    : 'Set as Baseline'
                }}
              </MTooltip>
            </MPermissionChecker>

            <MTooltip style="display: flex; flex: unset; align-items: center">
              <template v-slot:trigger>
                <MButton
                  :shadow="false"
                  :rounded="false"
                  :disabled="!version || loading"
                  variant="neutral-lightest"
                  @click="exportNetworkBackup"
                >
                  <MIcon name="download" />
                </MButton>
              </template>

              Click to Download
            </MTooltip>
          </MCol>
        </MRow>

        <div
          v-if="backupData && shouldShowBackupInHtml"
          class="backup-shower"
          v-html="filteredData"
        />

        <pre
          v-if="backupData && !shouldShowBackupInHtml"
          class="backup-shower"
          v-text="convertedBackupData"
        />

        <div
          v-if="loading"
          class="flex flex-col flex-1 items-center justify-center"
        >
          <MLoader />
        </div>
      </template>

      <NcmDeviceDetails
        v-else-if="
          activeMainTab === 'action_history' ||
          activeMainTab === 'change_summary'
        "
        :tab="activeMainTab"
        :data-item="viewItem"
      />

      <HardwareDetails
        v-else-if="activeMainTab === 'hardware_detail'"
        :device-id="viewItem.id"
      />
      <DeviceRulesList
        v-else-if="activeMainTab === 'rules'"
        :data-item="viewItem"
      >
        rules
      </DeviceRulesList>
    </div>
  </FlotoDrawer>
</template>

<script>
import Trim from 'lodash/trim'
import Constants from '@constants'
import { generateId } from '@utils/id'
import Bus from '@utils/emitter'
import datetime from '@src/filters/datetime'
import MonitorType from '@components/monitor-type.vue'
import {
  calculateVersion,
  fetchMonitorInfoWidgetDefinition,
  FILE_TYPE,
} from '../helpers/explorer'

import { exportBackup, fetchNetworkBackup, changeBaselineApi } from '../ncm-api'
import { WidgetTypeConstants } from '@components/widgets/constants'
import CloneDeep from 'lodash/cloneDeep'
import { authComputed } from '@state/modules/auth'
import LooseTags from '@components/loose-tags.vue'
import NcmDeviceDetails from './ncm-device-details.vue'

import HardwareDetails from './hardware-details.vue'

import DeviceRulesList from '../components/compliance/device-rules-list.vue'

export default {
  name: 'ViewDetailDrawer',
  components: {
    MonitorType,
    LooseTags,
    NcmDeviceDetails,
    HardwareDetails,
    DeviceRulesList,
  },
  inject: { SocketContext: { default: {} } },
  props: {
    viewItem: {
      type: Object,
      required: true,
    },
    fetchFn: {
      type: Function,
      required: true,
    },
  },
  data() {
    this.FILE_TYPE = FILE_TYPE
    this.deviceDetails = [
      {
        key: 'device',
        label: 'Device',
      },
      {
        key: 'serialNumber',
        label: 'Serial Number',
      },
      {
        key: 'ip',
        label: 'IP',
      },
      {
        key: 'modelNumber',
        label: 'Model Number',
      },
      {
        key: 'deviceType',
        label: 'Type',
      },
      {
        key: 'template',
        label: 'Template',
      },
      {
        key: 'vendor',
        label: 'Vendor',
      },
      {
        key: 'oid',
        label: 'OID',
      },
      {
        key: 'os',
        label: 'OS Type',
      },
      {
        key: 'lastBackupTime',
        label: 'Last Backup',
      },
    ]
    this.tabs = [
      {
        key: 'device_detail',
        name: 'Device Detail',
      },
      {
        key: 'hardware_detail',
        name: 'Hardware Detail',
      },
      {
        key: 'action_history',
        name: 'Action History',
      },

      {
        key: 'change_summary',
        name: 'Change Summary',
      },
    ]
    return {
      open: true,
      activeTab: FILE_TYPE.RUNNING,
      startupVersion: [],
      runningVersion: [],
      version: undefined,
      backupData: undefined,
      searchTerm: undefined,
      uuid: generateId(),
      monitorInfo: {},
      updatedViewItem: CloneDeep(this.viewItem),
      loading: false,
      isBaseLineProcessing: false,
      activeMainTab: 'device_detail',
    }
  },
  computed: {
    ...authComputed,

    versionOptions() {
      return this.activeTab === FILE_TYPE.STARTUP
        ? this.startupVersion
        : this.runningVersion
    },
    convertedBackupData() {
      return `${this.backupData || ''}`
    },

    filteredData() {
      if (this.searchTerm && Trim(this.searchTerm) !== '') {
        this.scrollIntoView()
        return this.convertedBackupData
          .replaceAll(
            this.searchTerm,
            `<span class="tag-yellow">${this.searchTerm}</span>`
          )
          .replaceAll('\n', '<br/> ')
      } else {
        return this.convertedBackupData.replaceAll('\n', '<br/> ')
      }
    },
    lastBackupTime() {
      return this.formatDateTime(this.viewItem.lastBackupTime)
    },
    type() {
      return this.monitorInfo['object.type'] || this.viewItem.deviceType
    },
    vendor() {
      return this.monitorInfo['object.vendor'] || this.viewItem.vendor
    },
    hasCreateDeletePermission() {
      return this.hasPermission([
        this.$constants.NCM_UPDATE_PERMISSION,
        this.$constants.NCM_DELETE_PERMISSION,
      ])
    },
    hasComplianceReadPermission() {
      return (
        this.hasPermission([this.$constants.COMPLIANCE_READ_PERMISSION]) &&
        this.hasLicensePermission(this.$constants.COMPLIANCE_LICENSE_PERMISSION)
      )
    },

    shouldShowBackupInHtml() {
      return !/^<\?xml\b|^<[^>]+>/.test(this.backupData || '')
    },
    isSelectedVersionIsBaseline() {
      return this.version === this.viewItem.baselineVersion
    },
  },
  watch: {
    activeTab: {
      immediate: true,
      handler(newValue) {
        this.backupData = undefined
        this.version =
          newValue === FILE_TYPE.RUNNING
            ? this.viewItem.runningFileCurrentVersion
            : this.viewItem.startupFileCurrentVersion

        this.requestNetworkBackup()
      },
    },

    'viewItem.baselineVersion'(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.calculateFileVersion()
      }
    },
  },
  created() {
    Bus.$on(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleDataReceived)
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleDataReceived)
    })
    this.calculateFileVersion()
    this.askForMonitorInfo()
  },

  methods: {
    handleTabChange() {},
    calculateFileVersion() {
      this.startupVersion = calculateVersion(
        this.viewItem['startupFileCurrentVersion'],
        this.viewItem['startupFileMinVersion']
      )
      this.runningVersion = calculateVersion(
        this.viewItem['runningFileCurrentVersion'],
        this.viewItem['runningFileMinVersion'],
        this.viewItem.baselineVersion
      )
    },
    requestNetworkBackup() {
      this.backupData = undefined
      if (this.viewItem.id && this.version && this.activeTab) {
        this.loading = true
        return fetchNetworkBackup(
          this.viewItem.id,
          this.version,
          this.activeTab
        ).then((data) => {
          this.loading = false
          this.backupData = data.decodedNetworkBackup
        })
      }
    },
    exportNetworkBackup() {
      this.SocketContext.addGuidForEvent(
        Constants.UI_EVENT_CSV_EXPORT,
        this.uuid
      )

      return exportBackup(
        {
          id: this.viewItem.id,
          fileType: this.activeTab,
          version: this.version,
          uuid: this.uuid,
        },
        true
      )
    },
    scrollIntoView() {
      setTimeout(() => {
        if (document.querySelectorAll('.tag-yellow')[0]) {
          document.querySelectorAll('.tag-yellow')[0].scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest',
          })
        }
      }, 1000)
    },
    formatDateTime(value) {
      return datetime(Math.round(value))
    },

    askForMonitorInfo() {
      const widget = fetchMonitorInfoWidgetDefinition(this.viewItem.objectId)
      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_WIDGET_RESULT_EVENT,
        'event.context': {
          ...widget,
          id: -1,
          [this.$constants.UI_EVENT_UUID]: this.uuid,
        },
      })
    },
    handleDataReceived(data) {
      if (!data) {
        return
      }
      if (data[this.$constants.UI_EVENT_UUID] !== this.uuid) {
        return
      }
      if (((data.result || {})[WidgetTypeConstants.GRID] || {}).data) {
        this.addCounters(
          ((data.result || {})[WidgetTypeConstants.GRID] || {}).data[0]
        )
      }
    },
    addCounters(data) {
      this.monitorInfo = Object.freeze(
        Object.keys(data || {}).reduce((acc, counter) => {
          return {
            ...acc,
            [counter]: data[counter],
          }
        }, {})
      )
      this.updatedViewItem = {
        ...this.updatedViewItem,
        deviceType: this.monitorInfo['object.type'] || this.viewItem.deviceType,
        vendor: this.monitorInfo['object.vendor'] || this.viewItem.vendor,
        serialNumber: this.monitorInfo['system.serial.number.last'],
      }
    },

    drillDown(type) {
      this.$emit('drillDown', { ...this.viewItem, drillDownType: type })
    },
    configBackup() {
      this.$emit('config-backup', this.viewItem)
    },
    assignBaseline() {
      this.isBaseLineProcessing = true
      const actionToTake = this.isSelectedVersionIsBaseline
        ? 'unassign_baseline'
        : 'setup_as_baseline'
      return changeBaselineApi(
        this.viewItem,
        actionToTake,

        this.version
      ).then(() => {
        this.getNcmObject(this.viewItem.id)

        if (actionToTake === 'unassign_baseline') {
          this.version = this.viewItem.runningFileCurrentVersion
          this.requestNetworkBackup()
        }
      })
    },

    getNcmObject(id) {
      return this.fetchFn(id).then((item) => {
        this.$emit('update-row', { ...this.viewItem, ...item })
        this.isBaseLineProcessing = false
      })
    },
  },
}
</script>

<style lang="less" scoped>
.metric-details {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--neutral-regular);
}

.backup-shower {
  @apply my-2;

  min-height: 500px;
  padding: 10px;
  overflow: scroll;
  background: var(--widget-background-color);
  border-radius: 5px;
}
</style>
