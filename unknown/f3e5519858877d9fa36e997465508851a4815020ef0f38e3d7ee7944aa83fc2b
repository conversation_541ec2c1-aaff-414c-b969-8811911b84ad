import Moment from 'moment'
import Capitalize from 'lodash/capitalize'
import Omit from 'lodash/omit'
import Constants from '@constants'

export function transformScheduleForServer(schedule) {
  if (
    (schedule.context || {}).objects !== undefined &&
    (schedule.context || {}).objects.length === 0
  ) {
    schedule.context = Omit(schedule.context, ['objects'])
  }
  const scheduleInfo = schedule.scheduleInfo
  return {
    'scheduler.job.type': schedule.type,
    ...('enabled' in schedule
      ? { 'scheduler.state': schedule.enabled ? 'yes' : 'no' }
      : {}),
    'scheduler.start.date': Moment.unix(
      schedule['scheduleInfo']['startDate'] / 1000
    ).format('DD-MM-YYYY'),
    'scheduler.times': Array.isArray(scheduleInfo.times)
      ? scheduleInfo.times
      : [scheduleInfo.times],
    'scheduler.timeline': schedule.scheduleType,
    ...(schedule.scheduleType === 'Monthly'
      ? {
          'scheduler.months': scheduleInfo.months.map(
            (m) => `${m.toLowerCase()}`
          ),
          'scheduler.dates': scheduleInfo.dates.map((m) => `${m}`),
        }
      : {}),
    ...(schedule.scheduleType === 'Weekly'
      ? {
          'scheduler.days': scheduleInfo.days.map((m) => `${m.toLowerCase()}`),
        }
      : {}),
    ...(schedule.context
      ? {
          'scheduler.context': schedule.context,
        }
      : {}),
  }
}

export function transformScheduleForClient(schedule) {
  let scheduleDateTime = []
  if (schedule['scheduler.timeline'] === 'Monthly') {
    scheduleDateTime = (schedule['scheduler.date.time'] || []).map((d) =>
      d.replace(d.split(' ')[1], Capitalize(d.split(' ')[1]))
    )
  } else {
    scheduleDateTime = (schedule['scheduler.date.time'] || []).map((d) =>
      Capitalize(d)
    )
  }
  return {
    id: schedule.id,
    scheduleType: schedule['scheduler.timeline'],
    enabled: schedule['scheduler.state'] === 'yes',
    startDate: schedule['scheduler.start.date'],
    runSchedules: scheduleDateTime,
    objects: schedule['scheduler.context']['objects'] || [],
    schedule: {
      scheduleType: schedule['scheduler.timeline'],
      scheduleInfo: {
        scheduleType: schedule['scheduler.timeline'],
        startDate: Moment(
          schedule['scheduler.start.date'],
          'DD-MM-YYYY'
        ).valueOf(),
        times: schedule['scheduler.times'],
        days: (schedule['scheduler.days'] || []).map((d) => Capitalize(d)),
        dates: (schedule['scheduler.dates'] || []).map((d) => parseInt(d)),
        months: (schedule['scheduler.months'] || []).map((d) => Capitalize(d)),
      },
      scheduleContext: schedule['scheduler.context'],
    },
  }
}

export function transformBulkMonitorScheduleForServer(
  selectedMonitorIds,
  schedule
) {
  let data = transformMonitorScheduleForServer(
    selectedMonitorIds,
    schedule,
    true
  )
  data['ids'] = selectedMonitorIds
  return data
}

export function transformMonitorScheduleLogForClient(schedule) {
  let data = {}
  data['id'] = schedule[Constants.ID_PROPERTY]
  data.enabled = schedule['scheduler.state']
  data['scheduleType'] = schedule['scheduler.timeline']
  data['objects'] = schedule['scheduler.context']?.['objects'] || []

  data['onSchedules'] = (
    schedule['scheduler.context']['event.context'][0]['scheduler.date.time'] ||
    []
  ).length
    ? (
        schedule['scheduler.context']['event.context'][0][
          'scheduler.date.time'
        ] || []
      ).map((d) => Capitalize(d))
    : ''
  data['offSchedules'] = (
    schedule['scheduler.context']['event.context'][1]['scheduler.date.time'] ||
    []
  ).length
    ? (
        schedule['scheduler.context']['event.context'][1][
          'scheduler.date.time'
        ] || []
      ).map((d) => Capitalize(d))
    : ''
  data['scheduleFormData'] = {
    offSchedule: {},
    schedule: {},
    scheduleDetails: {
      scheduleType: schedule['scheduler.timeline'],
      scheduleInfo: {
        days:
          schedule['scheduler.days'] !== undefined
            ? schedule['scheduler.days'].map(
                (v) => v.charAt(0).toUpperCase() + v.slice(1)
              )
            : [],
        dates:
          schedule['scheduler.dates'] !== undefined
            ? schedule['scheduler.dates'].map((v) => parseInt(v))
            : [],
        months:
          schedule['scheduler.months'] !== undefined
            ? schedule['scheduler.months'].map(
                (v) => v.charAt(0).toUpperCase() + v.slice(1)
              )
            : [],
      },
    },
  }
  if (schedule['scheduler.context']['event.context'] !== undefined) {
    if (
      schedule['scheduler.context']['event.context'][0] !== undefined &&
      schedule['scheduler.context']['event.context'][0]['object.state'] ===
        'MAINTENANCE'
    ) {
      data['scheduleFormData']['schedule'] = {
        startDate: Moment(
          schedule['scheduler.context']['event.context'][0][
            'scheduler.start.date'
          ],
          'DD-MM-YYYY'
        ).valueOf(),
        times:
          schedule['scheduler.context']['event.context'][0]['scheduler.times'],
      }
    }
    if (
      schedule['scheduler.context']['event.context'][1] !== undefined &&
      schedule['scheduler.context']['event.context'][1]['object.state'] ===
        Constants.STATE_ENABLE
    ) {
      data['scheduleFormData']['offSchedule'] = {
        startDate: Moment(
          schedule['scheduler.context']['event.context'][1][
            'scheduler.start.date'
          ],
          'DD-MM-YYYY'
        ).valueOf(),
        times:
          schedule['scheduler.context']['event.context'][1]['scheduler.times'],
      }
    }
    if (
      schedule['scheduler.context']['event.context'][0] !== undefined &&
      schedule['scheduler.context']['event.context'][0][
        'scheduler.request.type'
      ] === 'off'
    ) {
      data['scheduleFormData']['offSchedule'] = {
        startDate: Moment(
          schedule['scheduler.context']['event.context'][0][
            'scheduler.start.date'
          ],
          'DD-MM-YYYY'
        ).valueOf(),
        times:
          schedule['scheduler.context']['event.context'][0]['scheduler.times'],
      }
    }
  }
  return data
}

export function transformMonitorScheduleLogForList(schedule) {
  let data = {}
  data['id'] = schedule[Constants.ID_PROPERTY]
  data.enabled = schedule['scheduler.state'] === 'yes'
  data['scheduleType'] = schedule['scheduler.timeline']
  data['onStartDate'] =
    schedule['scheduler.context']['event.context'][0]['scheduler.start.date']
  data['offStartDate'] =
    schedule['scheduler.context']['event.context'][1]['scheduler.start.date']
  data['onSchedules'] = (
    schedule['scheduler.context']['event.context'][0]['scheduler.date.time'] ||
    []
  ).length
    ? (
        schedule['scheduler.context']['event.context'][0][
          'scheduler.date.time'
        ] || []
      ).map((d) => Capitalize(d))
    : ''
  data['offSchedules'] = (
    schedule['scheduler.context']['event.context'][1]['scheduler.date.time'] ||
    []
  ).length
    ? (
        schedule['scheduler.context']['event.context'][1][
          'scheduler.date.time'
        ] || []
      ).map((d) => Capitalize(d))
    : ''
  return data
}

export function transformMonitorScheduleForServer(id, schedule, bulkAction) {
  let data = {}

  data['scheduler.job.type'] = 'Maintenance'

  let schedulerContextData = {}
  let monitorId = []
  monitorId.push(id)
  schedulerContextData['event.context'] = transformONOFFMaintainance(schedule)
  if (bulkAction) {
    schedulerContextData['objects'] = id
  } else {
    schedulerContextData['objects'] = monitorId
  }
  data['scheduler.context'] = schedulerContextData

  data['scheduler.timeline'] = schedule['scheduleDetails']['scheduleType']
  if (schedule['scheduleDetails']['scheduleType'] === 'Monthly') {
    data['scheduler.months'] = schedule['scheduleDetails']['scheduleInfo'][
      'months'
    ].map((v) => v.toLowerCase())
    data['scheduler.dates'] = schedule['scheduleDetails']['scheduleInfo'][
      'dates'
    ].map((v) => v + '')
  } else if (schedule['scheduleDetails']['scheduleType'] === 'Weekly') {
    data['scheduler.days'] = schedule['scheduleDetails']['scheduleInfo'][
      'days'
    ].map((v) => v.toLowerCase())
  }
  return data
}

export function transformMonitorScheduleUpdateForServer(id, schedule) {
  let data = {}
  data['scheduler.state'] = schedule.enabled
  data['scheduler.job.type'] = 'Maintenance'

  let schedulerContextData = {}
  // Use the preserved objects array if available, otherwise fall back to single monitor ID
  let monitorIds =
    schedule.objects && schedule.objects.length > 0 ? schedule.objects : [id]
  schedulerContextData['event.context'] =
    transformONOFFMaintainanceUpdate(schedule)
  schedulerContextData['objects'] = monitorIds
  data['scheduler.context'] = schedulerContextData

  data['scheduler.timeline'] =
    schedule['scheduleFormData']['scheduleDetails']['scheduleType']
  if (
    schedule['scheduleFormData']['scheduleDetails']['scheduleType'] ===
    'Monthly'
  ) {
    data['scheduler.months'] = schedule['scheduleFormData']['scheduleDetails'][
      'scheduleInfo'
    ]['months'].map((v) => v.toLowerCase())
    data['scheduler.dates'] = schedule['scheduleFormData']['scheduleDetails'][
      'scheduleInfo'
    ]['dates'].map((v) => v + '')
  } else if (
    schedule['scheduleFormData']['scheduleDetails']['scheduleType'] === 'Weekly'
  ) {
    data['scheduler.days'] = schedule['scheduleFormData']['scheduleDetails'][
      'scheduleInfo'
    ]['days'].map((v) => v.toLowerCase())
  }
  return data
}

export function transformONOFFMaintainance(schedule) {
  let result = []
  let data = {}
  if (Object.keys(schedule.schedule).length > 0) {
    if (schedule.schedule.startDate) {
      data = {}
      data['object.state'] = 'MAINTENANCE'
      data['scheduler.start.date'] = Moment.unix(
        schedule['schedule']['startDate'] / 1000
      ).format('DD-MM-YYYY')
      data['scheduler.times'] = schedule['schedule']['times']
      result.push(data)
    }
  }
  if (Object.keys(schedule.offSchedule).length > 0) {
    if (schedule.offSchedule.startDate) {
      data = {}
      data['object.state'] = Constants.STATE_ENABLE
      data['scheduler.start.date'] = Moment.unix(
        schedule['offSchedule']['startDate'] / 1000
      ).format('DD-MM-YYYY')
      data['scheduler.times'] = schedule['offSchedule']['times']
      result.push(data)
    }
  }
  return result
}

export function transformONOFFMaintainanceUpdate(schedule) {
  let result = []
  let data = {}
  if (Object.keys(schedule['scheduleFormData']['schedule']).length > 0) {
    data = {}
    data['object.state'] = 'MAINTENANCE'
    data['scheduler.start.date'] = Moment.unix(
      schedule['scheduleFormData']['schedule']['startDate'] / 1000
    ).format('DD-MM-YYYY')
    data['scheduler.times'] = schedule['scheduleFormData']['schedule']['times']
    result.push(data)
  }
  if (Object.keys(schedule['scheduleFormData']['offSchedule']).length > 0) {
    data = {}
    data['object.state'] = Constants.STATE_ENABLE
    data['scheduler.start.date'] = Moment.unix(
      schedule['scheduleFormData']['offSchedule']['startDate'] / 1000
    ).format('DD-MM-YYYY')
    data['scheduler.times'] =
      schedule['scheduleFormData']['offSchedule']['times']
    result.push(data)
  }
  return result
}
