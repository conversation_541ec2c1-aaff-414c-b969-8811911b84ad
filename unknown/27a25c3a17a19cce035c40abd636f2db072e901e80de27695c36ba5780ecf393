<template>
  <FlotoFixedView>
    <div class="flex flex-1 min-w-0 min-h-0 flex-col no-data-container p-2">
      <MRow class="h-full" :gutter="0">
        <MCol
          :size="3"
          class="h-full flex flex-col left-side min-h-0 overflow-auto"
        >
          <div class="flex flex-col flex-1 justify-between px-6 py-4">
            <topPattern />
            <div class="flex-1 py-4 flex flex-col min-h-0">
              <div class="flex flex-col flex-1 justify-center">
                <h2 class="text-white font-600">{{ leftTitle }}</h2>
              </div>

              <div class="flex flex-1 items-center">
                <dotPattern />
              </div>

              <div class="flex flex-col flex-1">
                <component :is="leftSvg" class="self-center" />
              </div>

              <div class="flex flex-col flex-1 justify-center">
                <h5 class="text-white">{{ leftInfo }}</h5>
              </div>
            </div>
            <div
              v-if="
                module !== 'alert-trap' &&
                (routeToModuleDocumentation || '').length > 0
              "
              class="flex items-center"
            >
              <h6 class="text-white">
                For more information:
                <a :href="routeToModuleDocumentation" target="_blank">
                  {{ moduleInformationTitle }}
                  <MIcon name="external-link"></MIcon>
                </a>
              </h6>
            </div>
          </div>
        </MCol>
        <MCol :size="9" class="h-full relative" style="z-index: 11">
          <div class="overflow-y-auto w-full h-full px-4">
            <h1 class="text-primary mb-0 font-extrabold">
              {{ moduleTitle }}
            </h1>

            <h5 class="mb-2">
              {{ moduleInfo }}
            </h5>

            <img
              :src="`${imgPath}`"
              :alt="module"
              class="w-full"
              style="max-height: 85%"
            />
          </div>
          <!-- <MRow :gutter="0" class="mx-6">
            <MCol :size="12" class="z-10">
            </MCol>
            <MCol :size="12" style="height: 80px">
            </MCol>
          </MRow> -->

          <img
            id="logo"
            :src="appliedLogo"
            height="60"
            width="160"
            class="logo"
          />
        </MCol>
        <component :is="dotPattern" class="large-dot" />
      </MRow>
    </div>
  </FlotoFixedView>
</template>

<script>
import { UserPreferenceComputed } from '@state/modules/user-preference'
import { BrandingComputed } from '@state/modules/branding'
import topPattern from '@assets/images/no-data-image/side-image/top-pattern.svg'
import dotPattern from '@assets/images/no-data-image/side-image/dot-pattern.svg'
import dotPatternLargeBlack from '@assets/images/no-data-image/dot-pattern-large-black.svg'
import dotPatternLargeWhite from '@assets/images/no-data-image/dot-pattern-large-white.svg'

import dashboard from '@assets/images/no-data-image/side-image/dashboard.svg'
import FlowExplorer from '@assets/images/no-data-image/side-image/flow-explorer.svg'
import LogExplorer from '@assets/images/no-data-image/side-image/log-explorer.svg'
import MetricExplorer from '@assets/images/no-data-image/side-image/metric-explorer.svg'
import ncm from '@assets/images/no-data-image/side-image/ncm.svg'
import Topology from '@assets/images/no-data-image/side-image/topology.svg'
import TrapExplorer from '@assets/images/no-data-image/side-image/trap-explorer.svg'
import Alert from '@assets/images/no-data-image/side-image/alert.svg'
import Inventory from '@assets/images/no-data-image/side-image/inventory.svg'
import netroute from '@assets/images/no-data-image/side-image/netroute.svg'

export default {
  name: 'FlotoModuleNoData',
  components: {
    topPattern,
    dotPattern,
    dashboard,
    dotPatternLargeBlack,
    FlowExplorer,
    LogExplorer,
    MetricExplorer,
    ncm,
    Topology,
    TrapExplorer,
    netroute,
  },
  props: {
    module: {
      type: String,
      required: true,
    },
  },
  computed: {
    ...BrandingComputed,
    ...UserPreferenceComputed,

    imgPath() {
      return require(`../../src/assets/images/no-data-image/${this.module.toLowerCase()}-${
        this.theme
      }.png`)
    },

    appliedLogo() {
      if (this.theme === 'black') {
        return this.darkLogo
      }
      return this.logo
    },

    dotPattern() {
      if (this.theme === 'black') {
        return dotPatternLargeBlack
      }

      return dotPatternLargeWhite
    },

    leftSvg() {
      if (this.module.toLowerCase() === 'dashboard') {
        return dashboard
      } else if (this.module.toLowerCase() === 'flow-explorer') {
        return FlowExplorer
      } else if (this.module.toLowerCase() === 'log-explorer') {
        return LogExplorer
      } else if (this.module.toLowerCase() === 'metric-explorer') {
        return MetricExplorer
      } else if (this.module.toLowerCase() === 'ncm') {
        return ncm
      } else if (this.module.toLowerCase() === 'topology') {
        return Topology
      } else if (this.module.toLowerCase() === 'trap-explorer') {
        return TrapExplorer
      } else if (this.module.toLowerCase() === 'netroute') {
        return netroute
      } else if (
        [
          'alert-flow',
          'alert-log',
          'alert-metric',
          'alert-trap',
          'alert-netroute',
        ].includes(this.module.toLowerCase())
      ) {
        return Alert
      } else if (this.module.toLowerCase().indexOf('inventory-') >= 0) {
        return Inventory
      }

      return ''
    },

    leftTitle() {
      if (this.module.toLowerCase() === 'dashboard') {
        return 'Get full visibility across your Hybrid Infrastructure Stack'
      } else if (this.module.toLowerCase() === 'flow-explorer') {
        return 'Start your Network Observability Journey with Motadata AIOps'
      } else if (this.module.toLowerCase() === 'log-explorer') {
        return 'Empower Your Operations with Motadata AIOps Log Management'
      } else if (this.module.toLowerCase() === 'metric-explorer') {
        return 'Understand patterns, identify trends, and derive meaningful insights'
      } else if (this.module.toLowerCase() === 'ncm') {
        return ' Detect configuration conflicts, oversee version tracking, and extract critical insights'
      } else if (this.module.toLowerCase() === 'netroute') {
        return 'Detect configuration conflicts, oversee version tracking, and extract critical insights'
      } else if (this.module.toLowerCase() === 'topology') {
        return 'Visualize Network Dynamics with Motadata AIOps Topology Maps'
      } else if (this.module.toLowerCase() === 'trap-explorer') {
        return 'Elevate Network Awareness with Motadata AIOps Trap Explorer'
      } else if (
        [
          'alert-flow',
          'alert-log',
          'alert-metric',
          'alert-trap',
          'alert-netroute',
        ].includes(this.module.toLowerCase())
      ) {
        return 'Alert Dashboard, equipped with smart widgets, offers instant insights into system health by intelligently classifying alerts.'
      } else if (this.module.toLowerCase().indexOf('inventory-') >= 0) {
        return 'Enhance your infrastructure monitoring with dynamic, real-time insights into the health and performance of your IT systems using Motadata AIOps Monitors.'
      }

      return ''
    },
    leftInfo() {
      if (this.module.toLowerCase() === 'dashboard') {
        return 'Discover devices or route log and flow data to Motadata AIOps to enrich dashboards with insightful information.'
      } else if (this.module.toLowerCase() === 'flow-explorer') {
        return 'Start Analyzing your Network Flow by sending sFlow to port 6343 and Netflow to port 2055'
      } else if (this.module.toLowerCase() === 'log-explorer') {
        return 'Start Analysing your Infrastructure Logs by forwarding them through Motadata Agent or forward device syslog to port 514'
      } else if (this.module.toLowerCase() === 'metric-explorer') {
        return 'To view, analyze and compare different metrics, discover your hybrid infrastructure stack'
      } else if (this.module.toLowerCase() === 'ncm') {
        return 'To view, analyze and compare different configurations, discover your network infrastructure stack.'
      } else if (this.module.toLowerCase() === 'topology') {
        return 'Visualize dependencies, including L2 & L3 network devices, cloud, virtual infrastructure resources, and Applications by discovering these devices in Motadata AIOps.'
      } else if (this.module.toLowerCase() === 'trap-explorer') {
        return 'Start Analyzing SNMP Traps by forwarding them to port 1620'
      } else if (this.module.toLowerCase() === 'netroute') {
        return 'To view, analyze and compare different configurations, discover your network infrastructure stack.'
      } else if (
        [
          'alert-flow',
          'alert-log',
          'alert-metric',
          'alert-trap',
          'alert-netroute',
        ].includes(this.module.toLowerCase())
      ) {
        return 'Dive into actionable insights by creating policies and effortlessly visualize and manage the triggered alerts on the Alert Dashboard.'
      } else if (this.module.toLowerCase().indexOf('inventory-') >= 0) {
        return 'Proactively monitor, manage and optimize your infrastructure for peak performance and reliability.'
      }

      return ''
    },

    moduleInformationTitle() {
      if (this.module.toLowerCase().indexOf('inventory-') >= 0) {
        return 'Monitors'
      } else {
        return this.moduleTitle
      }
    },

    moduleTitle() {
      if (this.module.toLowerCase() === 'dashboard') {
        return 'Dashboard'
      } else if (this.module.toLowerCase() === 'flow-explorer') {
        return 'Flow Explorer'
      } else if (this.module.toLowerCase() === 'log-explorer') {
        return 'Log Explorer'
      } else if (this.module.toLowerCase() === 'metric-explorer') {
        return 'Metric Explorer'
      } else if (this.module.toLowerCase() === 'ncm') {
        return 'Network Configuration Management'
      } else if (this.module.toLowerCase() === 'topology') {
        return 'Topology'
      } else if (this.module.toLowerCase() === 'trap-explorer') {
        return 'Trap Explorer'
      } else if (this.module.toLowerCase() === 'netroute') {
        return 'NetRoute'
      } else if (this.module.toLowerCase() === 'alert-metric') {
        return 'Metric Alert Dashboard'
      } else if (this.module.toLowerCase() === 'alert-log') {
        return 'Log Alert Dashboard'
      } else if (this.module.toLowerCase() === 'alert-flow') {
        return 'Flow Alert Dashboard'
      } else if (this.module.toLowerCase() === 'alert-trap') {
        return 'Trap Alert Dashboard'
      } else if (this.module.toLowerCase() === 'alert-netroute') {
        return 'NetRoute Alert Dashboard'
      } else if (this.module.toLowerCase() === 'inventory-server') {
        return 'Server & Apps'
      } else if (this.module.toLowerCase() === 'inventory-network') {
        return 'Network'
      } else if (this.module.toLowerCase() === 'inventory-sdn') {
        return 'SDN'
      } else if (this.module.toLowerCase() === 'inventory-cloud') {
        return `Cloud`
      } else if (this.module.toLowerCase() === 'inventory-service check') {
        return `Service Check`
      } else if (this.module.toLowerCase() === 'inventory-virtualization') {
        return `Virtualization`
      } else if (this.module.toLowerCase() === `inventory-hci`) {
        return `HCI`
      } else if (this.module.toLowerCase() === 'inventory-service') {
        return `Service`
      } else if (this.module.toLowerCase() === 'inventory-process') {
        return `Process`
      } else if (this.module.toLowerCase() === 'inventory-interface') {
        return `Interface`
      } else if (this.module.toLowerCase() === `inventory-wan link`) {
        return `WAN Link`
      } else if (this.module.toLowerCase() === 'inventory-other') {
        return `Other`
      } else if (this.module.toLowerCase() === 'inventory-storage') {
        return `Storage`
      } else if (this.module.toLowerCase() === 'inventory-container') {
        return `Container`
      } else if (
        this.module.toLowerCase() === 'inventory-container orchestration'
      ) {
        return `Container Orchestration`
      }
      // else if (this.module.toLowerCase() === 'alert-trap') {
      //   return 'Trap Alert Dashboard'
      // }

      return ''
    },
    moduleInfo() {
      if (this.module.toLowerCase() === 'dashboard') {
        return 'Empower your decision-making using out-of-the-box dashboards and build custom dashboards as per your needs.'
      } else if (this.module.toLowerCase() === 'flow-explorer') {
        return 'Visualize and Analyze network traffic flows in real-time. Gain insights traffic patterns and identity bottlenecks in your network'
      } else if (this.module.toLowerCase() === 'log-explorer') {
        return 'Monitor, Analyze, and Interpret logs in real-time to easily track down errors and troubleshoot issues with precision.'
      } else if (this.module.toLowerCase() === 'metric-explorer') {
        return 'Analyze performance trends, uncover abnormal behavior, and optimize infrastructure using data-driven insights.'
      } else if (this.module.toLowerCase() === 'ncm') {
        return 'Improve network efficiency, strengthen security, and ensure reliability through insightful configuration management.'
      } else if (this.module.toLowerCase() === 'topology') {
        return 'Understand relationships within your Hybrid infrastructure stack, uncover dependencies to empower informed decision making for your infrastructure'
      } else if (this.module.toLowerCase() === 'trap-explorer') {
        return "Explore SNMP Traps indicating potential irregularities in your network, address them, and elevate your network's stability."
      } else if (this.module.toLowerCase() === 'netroute') {
        return 'Improve network efficiency, strengthen security, and ensure reliability through insightful configuration management.'
      } else if (
        [
          'alert-flow',
          'alert-log',
          'alert-metric',
          'alert-trap',
          'alert-netroute',
        ].includes(this.module.toLowerCase())
      ) {
        return `Minimize MTTR, boost visibility, and eradicate alert fatigue using AlOps's user-friendly alert dashboard.`
      } else if (this.module.toLowerCase() === 'inventory-server') {
        return `Monitor critical servers and applications to minimize downtime and maximize efficiency.`
      } else if (this.module.toLowerCase() === 'inventory-network') {
        return `Maintain optimal network performance and ensure network connectivity for your business operations by keeping a close eye on your network devices.`
      } else if (this.module.toLowerCase() === 'inventory-cloud') {
        return `Stay in control of your cloud infrastructure with comprehensive monitoring, optimizing your resource utilization, and ensuring availability.`
      } else if (this.module.toLowerCase() === 'inventory-service check') {
        return `Monitor essential services to detect and address issues promptly, ensuring uninterrupted service delivery.`
      } else if (this.module.toLowerCase() === 'inventory-virtualization') {
        return `Keep a close eye on your comprehensive virtualized stack, ensuring maximum operational efficiency and resource utilization.`
      } else if (this.module.toLowerCase() === 'inventory-service') {
        return `Monitor and optimize service performance and availability with precision using Motadata AIOps.`
      } else if (this.module.toLowerCase() === 'inventory-process') {
        return `Monitor and optimize process performance and availability with precision using Motadata AIOps.`
      } else if (this.module.toLowerCase() === 'inventory-interface') {
        return `Monitor interface activity and performance to ensure smooth data transmission and communication across your network.`
      } else if (this.module.toLowerCase() === 'inventory-wan link') {
        return `Gain insights into your WAN Links and prevent performance bottlenecks and network downtime before they even happen.`
      } else if (this.module.toLowerCase() === 'inventory-other') {
        return `Gain visibility into network connectivity and responsiveness with detailed ping monitoring.`
      } else if (this.module.toLowerCase() === 'inventory-hci') {
        return `Track your hyper-converged infrastructure effortlessly, enhancing operational resilience and ensuring continuous system optimization.`
      } else if (this.module.toLowerCase() === 'inventory-sdn') {
        return `Enhance the performance of your software-defined network by monitoring key SDN components, gaining actionable insights, and ensuring service availability.`
      } else if (this.module.toLowerCase() === 'inventory-storage') {
        return `Monitor your storage infrastructure seamlessly, ensuring optimal performance, maximizing capacity utilization, and enhancing data availability for uninterrupted operations.`
      } else if (this.module.toLowerCase() === 'inventory-container') {
        return `Gain insights into your Container and prevent performance bottlenecks and network downtime before they even happen.`
      } else if (
        this.module.toLowerCase() === 'inventory-container orchestration'
      ) {
        return `Enhance the performance of your containerized environments by monitoring Kubernetes and other orchestration platforms, gaining insights, and ensuring availability.`
      }

      return ''
    },
    routeToModuleDocumentation() {
      if (this.module.toLowerCase() === 'dashboard') {
        return 'https://docs.motadata.com/motadata-aiops-docs/dashboards/Overview'
      } else if (this.module.toLowerCase() === 'flow-explorer') {
        return 'https://docs.motadata.com/motadata-aiops-docs/flow-analysis/flow-explorer'
      } else if (this.module.toLowerCase() === 'log-explorer') {
        return 'https://docs.motadata.com/motadata-aiops-docs/log-management/how-to-view-and-analyze-the-logs'
      } else if (this.module.toLowerCase() === 'metric-explorer') {
        return 'https://docs.motadata.com/motadata-aiops-docs/Metric%20Analysis/Metric%20Explorer'
      } else if (this.module.toLowerCase() === 'topology') {
        return 'https://docs.motadata.com/motadata-aiops-docs/topology/topology-overview'
      } else if (this.module.toLowerCase() === 'trap-explorer') {
        return 'https://docs.motadata.com/motadata-aiops-docs/trap-management/SNMP-Trap-Explorer'
      } else if (this.module.toLowerCase() === 'alert-metric') {
        return 'https://docs.motadata.com/motadata-aiops-docs/alerts-and-policies/view-and-manage-alerts#metric-alert-details'
      } else if (this.module.toLowerCase() === 'alert-log') {
        return 'https://docs.motadata.com/motadata-aiops-docs/alerts-and-policies/view-and-manage-alerts#log-alert-details'
      } else if (this.module.toLowerCase() === 'alert-flow') {
        return 'https://docs.motadata.com/motadata-aiops-docs/alerts-and-policies/view-and-manage-alerts#flow-alert-details'
      } else if (this.module.toLowerCase().indexOf('inventory-') >= 0) {
        return 'https://docs.motadata.com/motadata-aiops-docs/monitors/monitor-screen'
      }
      // else if (this.module.toLowerCase() === 'alert-trap') {
      //   return 'https://docs.motadata.com/motadata-aiops-docs/trap-management/SNMP-Trap-Explorer'
      // }
      return ''
    },

    // shouldShowLink() {
    //   return ![
    //     'alert-flow',
    //     'alert-log',
    //     'alert-metric',
    //     'alert-trap',
    //   ].includes(this.module.toLowerCase())
    // },
  },
  methods: {
    // handleLinkClicked() {},
  },
}
</script>

<style lang="less" scoped>
.no-data-container {
  // background: var(--neutral-lightest);
  .left-side {
    z-index: 11;
    background: linear-gradient(188deg, #003c61 21.13%, #115a92 77.96%);
  }

  .large-dot {
    position: absolute;
    right: 20px;
    bottom: 0;
  }

  .logo {
    position: absolute;
    right: 20px;
    bottom: 0;
  }
}
</style>
