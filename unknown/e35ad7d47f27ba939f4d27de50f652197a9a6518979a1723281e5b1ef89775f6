<template>
  <MRow class="flex flex-col min-h-0 overflow-auto" :gutter="0">
    <Widgets
      disabled
      :allow-create="false"
      :dashboard-id="dashboard.id"
      :widgets="dashboard.widgets"
      :max-widgets="25"
      :time-range="timeRange"
      :dashboard-style="dashboard.style"
    >
      <template v-slot:widget="{ item, queue }">
        <WidgetContainer
          :widget="widgets[item.id]"
          :queue="queue"
          use-initial-request
          disable-refresh-interval
          disabled
          font-size="medium"
          hide-timeline
          is-preview
        />
      </template>
    </Widgets>
  </MRow>
</template>

<script>
import Widgets from '@components/widgets/widgets.vue'
import WidgetContainer from '@components/widgets/views/container.vue'
import { WIDGETS, DASHBOARD } from '../helpers/ncm-overview-dashboard'
export default {
  name: 'Overview',
  components: {
    Widgets,
    WidgetContainer,
  },
  computed: {
    dashboard() {
      return DASHBOARD
    },
    widgets() {
      return WIDGETS
    },
    timeRange() {
      return {
        selectedKey: 'today',
      }
    },
  },
}
</script>
