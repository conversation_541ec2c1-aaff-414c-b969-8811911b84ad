<template>
  <MPopover
    ref="popoverRef"
    placement="bottomLeft"
    :overlay-class-name="`${overlayClassName} timerange-dropdown-overlay ${
      rangeOptions.length === 0 ? 'no-options' : ''
    }`"
    :disabled="disabled"
    :get-popup-container="getPopupContainer"
    @show="handlePopoverOpen"
    @hide="handlePopoverHide"
  >
    <template v-slot:trigger="{ toggle }">
      <div
        class="inline-flex items-center time-range-picker-input"
        @click.stop="toggle"
      >
        <div
          class="bg-neutral-lightest flex items-center rounded"
          :class="{
            'input-error': inputError,
            'px-2 py-2': !onlyLabel,
            'bordered rounded': bordered,
          }"
        >
          <MIcon
            v-if="!selectedTimeRangeTag"
            name="calendar-alt"
            class="mr-1 excluded-header-icon text-neutral-light"
          />
          <MTooltip
            v-else
            :disabled="!onlyLabel"
            :get-popup-container="getPopupContainer"
          >
            <template v-slot:trigger>
              <div class="timerange-pill" :style="pillStyle">
                {{ selectedTimeRangeTag }}
              </div>
            </template>
            <span v-html="dateTimeTooltip" />
          </MTooltip>

          <template v-if="!onlyLabel">
            <div class="seperator"></div>
            <TextInput
              v-if="showInput"
              :initial-value="selectedOptionName"
              :autocomplete-options="autocompleteOptions"
              @input-value-changed="rangeInputTextValue = $event"
              @change="handleTextBoxValueChanged"
            />
            <MTooltip
              v-else
              :disabled="disableHintTooltip"
              :get-popup-container="getPopupContainer"
            >
              <template v-slot:trigger>
                <div style="min-width: 100px" :style="pillStyle">
                  <span
                    v-if="selectedTimeRangeAsReadableText"
                    :style="disabled ? { opacity: '0.5' } : {}"
                  >
                    {{ selectedTimeRangeAsReadableText }}
                  </span>
                  <span v-else class="placeholder-text"> Select Time </span>
                </div>
              </template>
              <span v-html="dateTimeTooltip" />
            </MTooltip>
            <a
              v-if="allowClear && selectedKey"
              class="ml-auto"
              @click.prevent.stop="handleClear"
            >
              <MIcon
                name="times-circle"
                class="text-neutral-light excluded-header-icon"
              />
            </a>
          </template>
        </div>
        <div
          v-if="!hideSelectedTime"
          class="flex flex-col text-neutral text-xs"
          :class="{ 'ml-2': !hideSelectedTime && defaultValue.startDate }"
        >
          <div>
            {{
              Math.ceil(defaultValue.startDate / 1000) | datetime(dateFormat)
            }}
            <!-- {{
              (defaultValue.startTime || '').replace(
                /^(\d{1,2}:\d{1,2}):\d{1,2}$/,
                '$1'
              )
            }} -->
          </div>
          <div>
            {{ Math.ceil(defaultValue.endDate / 1000) | datetime(dateFormat) }}
            <!-- {{
              (defaultValue.endTime || '').replace(
                /^(\d{1,2}:\d{1,2}):\d{1,2}$/,
                '$1'
              )
            }} -->
          </div>
        </div>
      </div>
    </template>
    <div
      ref="containerRef"
      class="flex flex-1 popupcontainer"
      :class="{ calendar: shouldShowCalendar }"
    >
      <div v-if="!shouldShowCalendar" class="flex flex-col range-list flex-1">
        <TransitionGroup name="placeholder" mode="out-in">
          <div
            v-for="range in rangeOptions"
            :key="range.key"
            class="range-item"
            :class="{ active: range.key === selectedKey }"
          >
            <a @click="selectPrefedefinedRange(range.key)">
              <span>{{ range.text }}</span>
              <div v-if="range.shortcut" class="timerange-pill">
                {{
                  typeof range.shortcut === 'function'
                    ? range.shortcut()
                    : range.shortcut
                }}
              </div>
            </a>
          </div>
        </TransitionGroup>
      </div>
      <ARangePicker
        v-else
        open
        :disabled-date="disabledDate"
        input-read-only
        :value="dateRangePickerValue"
        :get-calendar-container="getCalendarPopupContainer"
        @change="handleCustomRangeApplied"
      >
        <template slot="renderExtraFooter">
          <div class="flex flex-1 flex-col">
            <div class="flex items-center">
              <div class="flex-1 pr-2">
                <FlotoFormItem label="From Time">
                  <Timepicker
                    v-model="startTime"
                    use-seconds
                    :multiple="false"
                    use-popover
                  />
                </FlotoFormItem>
              </div>
              <div class="flex-1 pl-2">
                <FlotoFormItem label="To Time">
                  <Timepicker
                    v-model="endTime"
                    use-seconds
                    :multiple="false"
                    use-popover
                  />
                </FlotoFormItem>
              </div>
            </div>
            <div v-if="error" class="text-secondary-red" v-text="error"></div>
            <MDivider />
            <div class="flex justify-end">
              <MButton variant="default" @click="shouldShowCalendar = false">
                Cancel
              </MButton>
              <MButton class="ml-2" @click="handleApply"> Apply </MButton>
            </div>
          </div>
        </template>
      </ARangePicker>
    </div>
  </MPopover>
</template>

<script>
import Moment from 'moment'
import ADatePicker from 'ant-design-vue/es/date-picker'
import { getAdjustedTime } from '@components/widgets/helper'
import Datetime from '@src/filters/datetime'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import { getRange } from './helper'
import { AVAILABLE_RANGE_OPTIONS, DATE_FORMAT, TIME_FORMAT } from './constants'
import TextInput from './time-range-picker/text-input.vue'
import {
  parseRangeAsText,
  parseRangeAsReadableText,
  convertDurationToText,
} from './time-range-picker/range-parser'
import Timepicker from '../time-picker.vue'

export default {
  name: 'TimeRangePicker',
  components: {
    TextInput,
    Timepicker,
    ARangePicker: ADatePicker.RangePicker,
  },
  model: { event: 'change' },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    bordered: {
      type: Boolean,
      default: false,
    },
    onlyLabel: {
      type: Boolean,
      default: false,
    },
    allowClear: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Object,
      default: undefined,
    },
    hideCustomTimeRange: {
      type: Boolean,
      default: false,
    },
    hideIcon: {
      type: Boolean,
      default: false,
    },
    excludedOptions: {
      type: Array,
      default: undefined,
    },
    hideSelectedTime: {
      type: Boolean,
      default: false,
    },
    getPopupContainer: {
      type: Function,
      default: undefined,
    },
    pillStyle: {
      type: Object,
      default: undefined,
    },
    overlayClassName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      error: null,
      selectedKey: (this.value || {}).selectedKey,
      startDate: (this.value || {}).startDate,
      startTime: (this.value || {}).startTime,
      endDate: (this.value || {}).endDate,
      endTime: (this.value || {}).endTime,
      dailyRollingData: (this.value || {}).dailyRollingData || false,
      inputError: false,
      showInput: false,
      shouldShowCalendar: false,
      rangeInputTextValue: undefined,
    }
  },
  computed: {
    ...UserPreferenceComputed,
    autocompleteOptions() {
      return AVAILABLE_RANGE_OPTIONS.filter((item) => item.shortcut).map(
        (i) => i.shortcut
      )
    },
    disableHintTooltip() {
      if (this.hideSelectedTime) {
        return !this.selectedKey
      }
      return true
    },
    startDateAndTime() {
      const value = this.defaultValue || {}
      return Moment(
        `${Moment(value.startDate).startOf('day').format(DATE_FORMAT)} ${Moment(
          value.startTime,
          TIME_FORMAT
        )
          .set('second', 0)
          .format(TIME_FORMAT)}`,
        `${DATE_FORMAT} ${TIME_FORMAT}`
      ).unix()
    },
    endDateAndTime() {
      const value = this.defaultValue || {}
      return Moment(
        `${Moment(value.endDate).startOf('day').format(DATE_FORMAT)} ${Moment(
          value.endTime,
          TIME_FORMAT
        )
          .set('second', 0)
          .format(TIME_FORMAT)}`,
        `${DATE_FORMAT} ${TIME_FORMAT}`
      ).unix()
    },
    dateTimeTooltip() {
      if ((this.value || {}).selectedKey) {
        return `<div style="font-family: var(--chart-font-family); font-size:12px">From: ${Datetime(
          this.startDateAndTime,
          this.dateFormat
        )} <br /> To: ${Datetime(this.endDateAndTime, this.dateFormat)}</div>`
      }
      return undefined
    },
    dateRangePickerValue() {
      return [
        Moment.unix(this.startDate / 1000),
        Moment.unix(this.endDate / 1000),
      ]
    },
    defaultValue() {
      const value = this.value || {}
      const data = {}
      if (value && value.selectedKey !== 'custom') {
        const range = getRange(value.selectedKey)
        data.startDate = value.startDate || range.startDate
        data.startTime = value.startTime || range.startTime
        data.endDate = value.endDate || range.endDate
        data.endTime = value.endTime || range.endTime
      } else {
        data.startDate = value.startDate
        data.startTime = value.startTime
        data.endDate = value.endDate
        data.endTime = value.endTime
      }
      return data
    },
    selectedTimeRangeTag() {
      const value = this.value || {}
      if (value.selectedKey) {
        if (value.selectedKey !== 'custom') {
          const item = this.rangeOptions.find(
            (i) => i.key === value.selectedKey
          )
          if (item) {
            return item.shortcut
          }
        }
        return convertDurationToText(
          this.endDateAndTime - this.startDateAndTime
        )
      }
      return undefined
    },
    selectedTimeRangeAsReadableText() {
      const value = this.value || {}
      if (value.selectedKey !== 'custom') {
        return parseRangeAsReadableText(
          this.selectedTimeRangeTag,
          this.selectedKey
        )
      }
      return 'Custom'
    },
    selectedOptionName() {
      const selectedKey = (this.value || {}).selectedKey
      if (selectedKey === 'custom') {
        return 'Custom'
      }
      return (
        (AVAILABLE_RANGE_OPTIONS.find((o) => o.key === selectedKey) || {})
          .text ||
        selectedKey ||
        ''
      )
    },
    rangeOptions() {
      const excludedOptions = this.excludedOptions || []
      let options = AVAILABLE_RANGE_OPTIONS
      if (this.hideCustomTimeRange) {
        excludedOptions.push('custom')
      }
      if (excludedOptions.length > 0) {
        options = options.filter((o) => !excludedOptions.includes(o.key))
      }
      if (this.rangeInputTextValue) {
        options = options.filter(
          (o) =>
            o.shortcut.indexOf(this.rangeInputTextValue.toLowerCase()) === 0 ||
            o.key === 'custom'
        )
      }
      return options
    },
  },
  watch: {
    shouldShowCalendar(newValue) {
      if (newValue) {
        this.error = null
      }
    },
  },
  methods: {
    disabledDate(current) {
      // Can not select days before today and today
      return current && current > Moment().endOf('day')
    },
    handleCustomRangeApplied(data) {
      this.startDate = data[0].unix() * 1000
      this.endDate = data[1].unix() * 1000
    },
    handleTextBoxValueChanged(value) {
      const parsedValue = parseRangeAsText(value)
      if (parsedValue) {
        this.selectedKey = parsedValue.selectedKey
        this.startDate = parsedValue.startDate
        this.startTime = parsedValue.startTime
        this.endDate = parsedValue.endDate
        this.endTime = parsedValue.endTime
        this.inputError = false
        this.handleApply()
      } else {
        this.inputError = true
      }
    },
    getCalendarPopupContainer() {
      return this.$refs.containerRef
    },
    handlePopoverOpen() {
      this.showInput = true
      this.rangeInputTextValue = undefined
      const value = this.value || {}
      this.shouldShowCalendar = value.selectedKey === 'custom'
      this.selectedKey = value.selectedKey
      if (value && value.selectedKey !== 'custom') {
        const range = getRange(value.selectedKey)
        this.startDate = value.startDate || range.startDate
        this.startTime = value.startTime || range.startTime
        this.endDate = value.endDate || range.endDate
        this.endTime = value.endTime || range.endTime
      } else {
        this.startDate = value.startDate
        this.startTime = value.startTime
        this.endDate = value.endDate
        this.endTime = value.endTime
      }
      this.dailyRollingData = value.dailyRollingData || false
    },
    selectPrefedefinedRange(rangeName) {
      const range = getRange(rangeName)
      this.selectedKey = rangeName
      this.startDate = range.startDate
      this.endDate = range.endDate
      this.endTime = range.endTime
      this.startTime = range.startTime
      if (rangeName === 'custom') {
        this.shouldShowCalendar = true
        this.startTime = Moment().startOf('day').format(TIME_FORMAT)
        this.endTime = getAdjustedTime(Moment())
      } else {
        this.handleApply()
      }
    },
    handleApply() {
      setTimeout(() => {
        const endDate =
          Moment(
            `${Moment.unix(this.endDate / 1000).format('YYYY-MM-DD')} ${
              this.endTime
            }`
          ).unix() * 1000
        const startDate =
          Moment(
            `${Moment.unix(this.startDate / 1000).format('YYYY-MM-DD')} ${
              this.startTime
            }`
          ).unix() * 1000
        if (startDate >= endDate) {
          this.error = 'End Date/Time should be greater than Start Date/Time'
          return
        } else {
          this.error = null
        }
        this.shouldShowCalendar = false
        this.hidePopover()
        this.$emit('change', {
          selectedKey: this.selectedKey,
          startDate: startDate,
          endDate: endDate,
          startTime: this.startTime,
          endTime: this.endTime,
          dailyRollingData: this.dailyRollingData,
        })
      }, 400)
    },
    handleClear() {
      this.hidePopover()
      setTimeout(() => {
        this.$emit('change', undefined)
      }, 400)
    },
    handlePopoverHide() {
      this.showInput = false
      this.inputError = false
    },
    hidePopover() {
      this.$refs.popoverRef.hide()
    },
  },
}
</script>

<style lang="less">
.time-range-picker-input {
  font-size: 0.8rem;
  font-weight: normal;

  .seperator {
    height: 20px;

    @apply mx-2;

    border-left: 1px solid var(--border-color);
  }

  .input-error {
    border: 1px solid var(--secondary-red);
  }
}

.timerange-pill {
  height: 18px;
  padding: 0 4px;
  margin: 0;
  font-size: 0.7rem;
  font-weight: 600;
  line-height: initial !important;
  color: var(--timerange-text-color);
  cursor: auto;
  background: var(--timerange-background-color) !important;
  border-radius: 4px;
}

.timerange-dropdown-overlay {
  z-index: 1055;
  padding: 0;

  &.searchbar-dropdown {
    z-index: 1056;
  }

  border-radius: 0 0 4px 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  .@{ant-prefix}-popover-content {
    border-radius: 4px;
  }
  .@{ant-prefix}-popover-inner {
    background: transparent;
    box-shadow: none;
  }
  .@{ant-prefix}-popover-inner-content {
    display: flex;
    min-width: 200px;
    padding: 0;
    background: transparent;
  }
  .@{ant-prefix}-popover-arrow {
    display: none;
  }

  &.no-options {
    .@{ant-prefix}-popover-inner-content {
      display: none;
    }
  }

  .popupcontainer {
    padding: 0.5rem;
    background: var(--dropdown-background);

    &.calendar {
      background: transparent;
    }

    .@{ant-prefix}-calendar-picker {
      visibility: hidden;
      opacity: 0;
    }

    .@{ant-prefix}-calendar-input-wrap,
    .@{ant-prefix}-calendar-range-middle {
      display: none;
    }

    .@{ant-prefix}-calendar-picker-container {
      background: var(--dropdown-background);

      &-content {
        background: var(--dropdown-background);
      }

      .@{ant-prefix}-calendar-year-panel,
      .@{ant-prefix}-calendar-month-panel {
        background: var(--dropdown-background);
      }

      .@{ant-prefix}-calendar-month-panel-cell-disabled
        .@{ant-prefix}-calendar-month-panel-month,
      .@{ant-prefix}-calendar-month-panel-cell-disabled
        .@{ant-prefix}-calendar-month-panel-month:hover,
      .@{ant-prefix}-calendar-disabled-cell .@{ant-prefix}-calendar-date {
        color: var(--page-text-color);
        background: transparent;
        opacity: 0.5;
      }

      .@{ant-prefix}-calendar-date:hover {
        color: white;
        background: var(--primary);
      }

      .@{ant-prefix}-calendar-range {
        .@{ant-prefix}-calendar-in-range-cell::before {
          background: fade(#099dd9, 20);
        }
      }

      .@{ant-prefix}-calendar-last-month-cell .@{ant-prefix}-calendar-date,
      .@{ant-prefix}-calendar-next-month-btn-day .@{ant-prefix}-calendar-date {
        color: var(--neutral-light);
      }

      .@{ant-prefix}-calendar-footer-btn {
        display: flex;

        @apply py-2;
      }

      .@{ant-prefix}-calendar-footer-extra {
        flex: 1;
      }
    }
  }

  .range-list {
    @apply flex flex-col;

    div.range-item {
      @apply p-1 my-1 rounded flex;

      a {
        @apply flex justify-between flex-1;

        width: 100%;
        color: var(--page-text-color);

        .default {
          margin: 0;
        }
      }

      &:hover,
      &.active {
        color: var(--left-menu-text-color-hover);
        background: var(--left-menu-hover-bg);

        a {
          color: var(--left-menu-text-color-hover);
        }
      }
    }
  }
}
</style>
