export default {
  SERVICE: 'Service',
  PROCESS: 'Process',
  INTERFACE: 'Interface',
  WINDOWS: 'Windows',
  IBM_AS_400: 'IBM AS/400',
  LINUX: 'Linux',
  IBM_AIX: 'IBM AIX',
  HP_UX: 'HP-UX',
  WINDOWS_CLUSTER: 'Windows Cluster',
  ACTIVE_DIRECTORY: 'Active Directory',
  APACHE_HTTP: 'Apache HTTP',
  AWS_CLOUD: 'AWS Cloud',
  AMAZON_DYNAMO_DB: 'Amazon DynamoDB',
  AMAZON_EBS: 'Amazon EBS',
  AMAZON_EC2: 'Amazon EC2',
  AWS_ELB: 'AWS ELB',
  AMAZON_RDS: 'Amazon RDS',
  AMAZON_S3: 'Amazon S3',
  AMAZON_SNS: 'Amazon SNS',
  AMAZON_CLOUD_FRONT: 'Amazon CloudFront',
  AWS_AUTO_SCALING: 'AWS Auto Scaling',
  AWS_LAMBDA: 'AWS Lambda',
  AMAZON_SQS: 'Amazon SQS',
  AWS_ELASTIC_BEANSTALK: 'AWS Elastic Beanstalk',
  AMAZON_DOCUMENTDB: 'Amazon DocumentDB',
  AZURE_CLOUD: 'Azure Cloud',
  AZURE_COSMOS_DB: 'Azure Cosmos DB',
  AZURE_SQL_DATABASE: 'Azure SQL Database',
  AZURE_STORAGE: 'Azure Storage',
  AZURE_VM: 'Azure VM',
  AZURE_WEB_APP: 'Azure WebApp',
  AZURE_SERVICE_BUS: 'Azure Service Bus',
  AZURE_APPLICATION_GATEWAY: 'Azure Application Gateway',
  AZURE_FUNCTION: 'Azure Function',
  AZURE_LOAD_BALANCER: 'Azure Load Balancer',
  AZURE_MYSQL_SERVER: 'Azure MySQL Server',
  AZURE_POSTGRESQL_SERVER: 'Azure PostgreSQL Server',
  AZURE_VM_SCALE_SET: 'Azure VM Scale Set',
  AZURE_CDN: 'Azure CDN',
  BIND9: 'Bind9',
  MICROSOFT_IIS: 'Microsoft IIS',
  NGINX: 'Nginx',
  DOTNET: 'Dotnet',
  IBM_DB2: 'IBM Db2',
  ORACLE_DATABASE: 'Oracle Database',
  SQL_SERVER: 'SQL Server',
  MYSQL: 'MySQL',
  MARIADB: 'MariaDB',
  POSTGRESQL: 'PostgreSQL',
  MONGODB: 'MongoDB',
  WINDOWS_DNS: 'Windows DNS',
  WINDOWS_DHCP: 'Windows DHCP',
  EXCHANGE_MAILBOX: 'Exchange Mailbox',
  EXCHANGE_CLIENT_ACCESS_ROLE: 'Exchange Client Access Role',
  EXCHANGE_EDGE_TRANSPORT_ROLE: 'Exchange Edge Transport Role',
  EXCHANGE_MAILBOX_ROLE: 'Exchange Mailbox Role',
  HA_PROXY: 'HAProxy',
  IBM_MQ: 'IBM MQ',
  MSMQ: 'MSMQ',
  RABBITMQ: 'RabbitMQ',
  APACHE_MQ: 'Apache MQ',
  ORACLE_WEBLOGIC: 'Oracle WebLogic',
  GLASS_FISH_SERVER: 'GlassFish Server',
  LIGHTTPD: 'Light Httpd',
  LINUX_DHCP: 'Linux DHCP',
  APACHE_TOMCAT: 'Apache Tomcat',
  IBM_WEBSPHERE: 'IBM WebSphere',
  WILDFLY: 'WildFly',
  WINDOWS_RDP: 'Windows RDP',
  SYBASE: 'Sybase',
  SAP_HANA: 'SAP HANA',
  SAP_MAXDB: 'SAP MaxDB',
  MICROSOFT_DYNAMICS_CRM: 'Microsoft Dynamics CRM',
  SSL_CERTIFICATE: 'SSL Certificate',
  CERTIFICATE: 'Certificate',
  CISCO_UCS: 'Cisco UCS',
  CISCO_WIRELESS: 'Cisco Wireless',
  CITRIX_XEN: 'Citrix Xen',
  CITRIX_XEN_CLUSTER: 'Citrix Xen Cluster',
  NUTANIX: 'Nutanix',
  NUTANIX_HOST: 'Nutanix Host',
  PRISM: 'Prism',
  CISCO_VMANAGE: 'Cisco vManage',
  CISCO_VSMART: 'Cisco vSmart',
  CISCO_VBOND: 'Cisco vBond',
  CISCO_VEDGE: 'Cisco vEdge',
  CISCO_MERAKI: 'Cisco Meraki',
  CISCO_MERAKI_CONTROLLER: 'Cisco Meraki Controller',
  CISCO_MERAKI_RADIO: 'Cisco Meraki Radio',
  CISCO_MERAKI_SECURITY: 'Cisco Meraki Security',
  CISCO_MERAKI_SWITCH: 'Cisco Meraki Switch',
  HPE_STOREONCE: 'HPE StoreOnce',
  HPE_PRIMERA: 'HPE Primera',
  HPE_3PAR: 'HPE 3PAR',
  NETAPP: 'NetApp ONTAP Cluster',
  DELL_EMC_UNITY: 'Dell EMC Unity',
  CISCO_ACI: 'Cisco ACI',
  NETAPP_ONTAP_CLUSTER: 'NetApp ONTAP Cluster',
  CISCO_ACI_CONTROLLER: 'Cisco ACI Controller',
  CISCO_ACI_SPINE: 'Cisco ACI Spine',
  CISCO_ACI_LEAF: 'Cisco ACI Leaf',
  CISCO_ACI_ENDPOINT: 'Cisco ACI Endpoint',
  NSXT: 'VMware NSX-T',

  DNS: 'DNS',
  DOMAIN: 'Domain',
  EMAIL: 'Email',
  ZIMBRA: 'Zimbra',
  VMWARE: 'VMWare',
  VMWARE_ESXI: 'VMware ESXi',
  FTP: 'FTP',
  SFTP: 'SFTP',
  TFTP: 'TFTP',
  LOCAL: 'LOCAL',

  HYPER_V: 'Hyper-V',
  HYPER_V_CLUSTER: 'Hyper-V Cluster',
  NTP: 'NTP',
  OFFICE_365: 'Office 365',
  ONEDRIVE: 'OneDrive',
  PING: 'Ping',
  PORT: 'Port',
  RADIUS: 'RADIUS',
  RUCKUS_WIRELESS: 'Ruckus Wireless',
  SHAREPOINT_ONLINE: 'SharePoint Online',
  EXCHANGE_ONLINE: 'Exchange Online',
  SOLARIS: 'Solaris',
  MICROSOFT_TEAMS: 'Microsoft Teams',
  URL: 'URL',
  VCENTER: 'vCenter',
  SWITCH: 'Switch',
  ROUTER: 'Router',
  FIREWALL: 'Firewall',
  PRINTER: 'Printer',
  LOAD_BALANCER: 'Load Balancer',
  UPS: 'UPS',
  SYMANTEC_MESSAGING_GATEWAY: 'Symantec Messaging Gateway',
  IBM_TAPE_LIBRARY: 'IBM Tape Library',
  SNMP_DEVICE: 'SNMP Device',
  WIRELESS_CONTROLLER: 'Wireless Controller',
  ARUBA_WIRELESS: 'Aruba Wireless',
  VIRTUAL_MACHINE: 'vm',
  VM: 'Vm',
  JBOSS: 'JBoss',
  CUSTOM: 'Custom',
  SOLARIS_PROCESS: 'Solaris Process',
  WINDOWS_SERVICE: 'Windows Service',
  WINDOWS_FILE: 'Windows File',
  WINDOWS_DIRECTORY: 'Windows Directory',
  WINDOWS_PROCESS: 'Windows Process',
  LINUX_PROCESS: 'Linux Process',
  IBM_AIX_PROCESS: 'IBM AIX Process',
  HP_UX_PROCESS: 'HP-UX Process',
  LINUX_FILE: 'Linux File',
  LINUX_DIRECTORY: 'Linux Directory',
  AVAILABILITY: 'Availability',
  NETWORK_SERVICE: 'Network Service',
  SYSTEM_PROCESS: 'system.process',
  SYSTEM_SERVICE: 'system.service',
  SYSTEM_NETWORK_INTERFACE: 'system.network.interface',
  SYSTEM_DISK_VOLUME: 'system.disk.volume',
  SYSTEM_CPU_CORE: 'system.cpu.core',
  NETWORK_INTERFACE: 'interface',
  IPSLA: 'IPSLA',
  SMTP: 'SMTP',
  MAC_OS: 'Mac OS',
  CHROME: 'Chrome',
  MICROSOFT_EDGE: 'Microsoft Edge',
  FIREFOX: 'Firefox',
  SAFARI: 'Safari',
  MIDDLEWARE: 'Middleware',
  DATABASE: 'Database',
  WEB_SERVER: 'Web Server',
  HARDWARE_SENSOR: 'Hardware Sensor',
  EMAIL_GATEWAY: 'Email Gateway',
  ANDROID: 'Android',
  IOS: 'iOS',
  CUSTOM_MONITOR_TYPE: 'Custom Monitor Type',
  OPERA: 'Opera',
  DOCKER_CONTAINER: 'Docker Container',
  DOCKERCONTAINER: 'dockercontainer',
  WINDOWS_SNMP: 'Windows (SNMP)',
  LINUX_SNMP: 'Linux (SNMP)',
  DNS_SECURITY: 'DNS Security',
  ECM_SYSTEM: 'ECM System',
  SEARCH_ENGINE: 'Search Engine',
  API_GATEWAY: 'API Gateway',
  ARRAY_NETWORKS_ADC: 'Array Networks ADC',
  IDENTITY_ACCESS_MGMT: 'Identity & Access Mgmt',
  ENDPOINT_SECURITY: 'Endpoint Security',
  FILE_INTEGRITY_MONITORING: 'File Integrity Monitoring',
  EMAIL_SECURITY: 'Email Security',
  NOSQL_DATABASE: 'NoSQL Database',
  OCR_ENGINE: 'OCR Engine',
  IT_ASSET_MANAGEMENT: 'IT Asset Management',
  AUTHENTICATION_SERVER: 'Authentication Server',
  WEB_FRAMEWORK: 'Web Framework',
  PRIVILEGED_ACCESS_MGMT: 'Privileged Access Mgmt',
  GOVERNMENT_AUDIT: 'Government Audit',
  LOG_COLLECTOR: 'Log Collector',
  PLM_SYSTEM: 'PLM System',
  ERP: 'ERP',
  VMWARE_TANZU_KUBERNETES: 'Tanzu Kubernetes',
  KUBERNETES: 'Kubernetes',
}
