<template>
  <MRow class="slide-filters items-center pr-2">
    <!-- <MCol :size="2">
      <FlotoFormItem label="Severity">
          <FlotoDropdownPicker
            id="filter-monitor-picker"
            v-model="currentValue.severities"
            multiple
            :options="severityOptions"
          >
            <template v-slot:before-menu-text="{ item }">
              <Severity
                :severity="item.key"
                :shadow="true"
                :disable-tooltip="true"
                class="mx-1"
              />
            </template>
          </FlotoDropdownPicker>
      </FlotoFormItem>
    </MCol> -->
    <MCol v-if="category === 'metric'" :size="2">
      <FlotoFormItem label="Monitors">
        <MonitorPicker
          id="filter-monitor-picker"
          v-model="currentValue.monitors"
          class="mt-1"
          placeholder="Monitor"
          multiple
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="2" class="alert-multi-spacer">
      <FlotoFormItem label="Alert">
        <PolicyPicker
          id="alert-picker"
          v-model="currentValue.policies"
          class="mt-1"
          placeholder="Alerts"
          multiple
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="2">
      <FlotoFormItem :label="category === 'trap' ? 'Counter' : 'Metric'">
        <FlotoDropdownPicker
          id="metric-picker"
          v-model="currentValue.metrics"
          class="mt-1"
          :options="metricOptions"
          :placeholder="category === 'trap' ? 'Counter' : 'Metric'"
          multiple
        />
      </FlotoFormItem>
    </MCol>
    <MCol :size="2">
      <FlotoFormItem label="Tag">
        <FlotoDropdownPicker
          id="tag"
          v-model="currentValue.tags"
          class="mt-1"
          :options="tagsOptions"
          placeholder="Tag"
          multiple
        />
      </FlotoFormItem>
    </MCol>
    <MCol v-if="category === 'metric'" :size="2">
      <FlotoFormItem label="Type">
        <FlotoDropdownPicker
          id="type"
          v-model="currentValue.types"
          class="mt-1"
          :options="typeOptions"
          placeholder="Type"
          multiple
        >
        </FlotoDropdownPicker>
      </FlotoFormItem>
    </MCol>
    <MCol v-if="['log', 'flow'].includes(category)" :size="2">
      <FlotoFormItem label="Alert Type">
        <FlotoDropdownPicker
          v-model="currentValue.alertType"
          class="mt-1"
          :options="alertTypeOptions"
          placeholder="Alert Type"
        >
        </FlotoDropdownPicker>
      </FlotoFormItem>
    </MCol>
    <MCol v-if="category === 'trap'" :size="2">
      <FlotoFormItem label="Event Source">
        <FlotoDropdownPicker
          v-model="currentValue.eventSources"
          class="mt-1"
          :options="sourceOptions"
          multiple
          placeholder="Event Source"
        />
      </FlotoFormItem>
    </MCol>
    <MCol class="flex justify-between ml-auto" :size="2">
      <div class="flex-1 items-center mt-2 flex justify-center">
        <MButton
          id="reset-btn"
          variant="default"
          @click="
            $emit('change', {
              groups: [],
              monitors: [],
              policies: [],
              metrics: [],
              tags: [],
              severities: [],
              types: [],
              alertType: '',
              eventSources: [],
            })
          "
        >
          Reset
        </MButton>
        <MButton id="apply-btn" class="ml-2" @click="apply">Apply</MButton>
      </div>
    </MCol>
    <!-- </div> -->
    <MButton
      id="close-filter"
      variant="transparent"
      :shadow="false"
      shape="circle"
      style="position: absolute; top: 0; right: 0"
      class="monitor-agent-filter-close"
      @click="$emit('hide')"
    >
      <MIcon name="times" class="text-neutral-light" />
    </MButton>
  </MRow>
</template>

<script>
import Uniq from 'lodash/uniq'
import UniqBy from 'lodash/uniqBy'
import Constants from '@constants'
import MonitorPicker from '@components/data-picker/monitor-picker.vue'
import PolicyPicker from '@components/data-picker/policy-picker.vue'
// import Severity from '@/src/components/severity.vue'
import Types from '@/src/modules/settings/policy-settings/config'
import { ALERT_TYEP } from '@/src/modules/alert/helpers/alert-helper'

const SEVERITY_TO_CONSIDER = [
  Constants.DOWN,
  Constants.UNREACHABLE,
  Constants.CRITICAL,
  Constants.MAJOR,
  Constants.WARNING,
  Constants.CLEAR,
]

const TYPES_TO_CONSIDER = [
  Types.AVAILABILITY,
  Types.METRIC_THRESHOLD,
  Types.FORECAST,
  Types.ANOMALY,
  Types.BASELINE,
]

export default {
  name: 'Filters',
  components: {
    MonitorPicker,
    PolicyPicker,
    // Severity,
  },
  inject: { policyContext: { default: { options: new Map() } } },
  model: {
    event: 'change',
  },
  props: {
    value: { type: [Object, Array], required: true },
    category: { type: String, required: true },
    sourceOptions: { type: Array },
  },
  data() {
    this.severityOptions = SEVERITY_TO_CONSIDER.map((i) => ({
      text: i,
      key: i,
    }))
    this.alertTypeOptions = Object.entries(ALERT_TYEP).map(([key, value]) => ({
      key: value,
      text: value,
    }))
    this.acknowledgedOptions = [
      { key: 'yes', text: 'Yes' },
      { key: 'no', text: 'No' },
    ]
    this.typeOptions = TYPES_TO_CONSIDER.map((i) => ({
      text: i,
      key: i,
    }))
    return {
      currentValue: { ...this.value },
    }
  },
  computed: {
    metricOptions() {
      return UniqBy(
        Array.from(this.policyContext.options.values())
          .filter(({ metric }) => metric)
          .map(({ metric }) => ({
            key: metric,
            text: metric.replace(/[~^]/g, '.'),
          })),
        'key'
      )
    },
    tagsOptions() {
      const tagsMap = {}
      Array.from(this.policyContext.options.values()).forEach(
        ({ tags, key }) => {
          tags.forEach((tag) => {
            tagsMap[tag] = [...(tagsMap[tag] || []), key]
          })
        }
      )
      return Uniq(Object.keys(tagsMap)).map((tag) => ({
        text: tag,
        key: tag,
      }))
    },
  },
  watch: {
    value(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.currentValue = { ...newValue }
      }
    },
  },
  methods: {
    apply() {
      this.$emit('change', this.currentValue)
    },
  },
}
</script>
