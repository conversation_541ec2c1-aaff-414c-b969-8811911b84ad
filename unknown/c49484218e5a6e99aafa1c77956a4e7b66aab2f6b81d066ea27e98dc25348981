<template>
  <div
    v-if="monitor.id && monitor.type"
    id="monitor-detail-element"
    class="border-bot flex py-1 page-background items-center"
  >
    <div class="flex items-center mr-4">
      <MButton variant="transparent" @click="handleGoToHome">
        <MIcon name="home-lg-alt" class="text-neutral-light" size="lg" />
      </MButton>
      <MIcon name="chevron-right" class="text-neutral-light mr-4" />
      <div class="inline-flex items-center">
        <div class="cursor-auto p-2 bg-neutral-lightest rounded">
          <MonitorType
            :type="
              application
                ? application
                : vm
                ? $constants.VIRTUAL_MACHINE
                : monitor.type
            "
            :center="false"
            width="20px"
            height="20px"
            disable-tooltip
          />
        </div>
      </div>
      <div v-if="shouldShowAgentIcon" class="inline-flex items-center pl-2">
        <div class="cursor-auto p-2 bg-neutral-lightest rounded">
          <MonitorType
            :type="$constants.AGENT"
            :center="false"
            width="20px"
            height="20px"
            disable-tooltip
          />
        </div>
      </div>
    </div>
    <div class="flex-1 items-start flex flex-col min-w-0">
      <div class="flex-none min-w-0 flex items-center">
        <h5 class="mb-0 text-ellipsis">
          {{
            application
              ? `${application} (${
                  monitor.name || monitor.host || monitor.ip || monitor.target
                })`
              : vm
              ? `${vm} (${
                  monitor.name || monitor.host || monitor.ip || monitor.target
                })`
              : ap
              ? `${ap} (${
                  monitor.name || monitor.host || monitor.ip || monitor.target
                })`
              : monitor.name ||
                monitor.host ||
                monitor.ip ||
                monitor.target ||
                monitor.serviceCheckTarget
          }}
        </h5>
        <div class="text-neutral-light mx-2">
          {{
            monitor.host
              ? ` | ${
                  monitor.category === $constants.SERVICE_CHECK
                    ? monitor.target
                    : instanceIp ||
                      monitor.ip ||
                      monitor.host ||
                      monitor.target ||
                      monitor.serviceCheckTarget
                } | `
              : ''
          }}
          {{
            `${monitor.host ? '' : '|'} ${monitor.vendor || monitor.type} ${
              monitor.id ? '|' : ''
            } `
          }}
        </div>
        <MonitorStatus
          v-if="monitor.id"
          :key="`${monitor.id}${application || ''}${vm || ''}`"
          :monitor="monitor"
          :application="application"
          :vm="vm"
          :ap="ap"
        />
        <CredentialSelectionDrawer
          v-if="metricPluginProtocol"
          :device-type="monitor.type"
          :protocol="metricPluginProtocol"
          :vendor="monitor.vendor"
        />
      </div>
      <div class="inline-flex flex-grow-0 min-w-0 flex-shrink-0 w-full">
        <div class="group-picker-container">
          <GroupPicker :value="monitor.groups" disabled :wrap="false" />
        </div>
        <div class="loose-tags-container">
          <LooseTags :value="monitor.tags" disabled :wrap="false" />
        </div>
      </div>
    </div>
    <div class="flex-1 items-center flex justify-end min-w-0 relative">
      <div class="flex-1 min-w-0 text-right">
        <div class="flex flex-1 justify-end">
          <div
            v-if="
              convertGridResultToGauge.series[0].data.length &&
              haseAlertPermission
            "
            class="inline-flex flex-grow-0 min-w-0 flex-shrink-0 text-neutral-light justify-center items-center"
          >
            <RadialView
              :key="`${monitor.id}${application || ''}${vm || ''}`"
              :data="convertGridResultToGauge"
              for-monitor-details
              @filter-by-severity="filterBySeverity"
            />
          </div>
        </div>
      </div>
      <WANLinkForm :monitor="monitor" />

      <ContainerRuntimeForm :monitor="monitor" />

      <MPermissionChecker
        :permission="$constants.MONITOR_SETTINGS_UPDATE_PERMISSION"
      >
        <MButton
          v-if="shouldShowTerminal"
          title="Terminal"
          variant="neutral-lightest"
          class="ml-2 squared-button"
          @click="isTerminalOpen = true"
        >
          <MIcon name="terminal" />
        </MButton>
      </MPermissionChecker>
      <MButton
        v-if="shouldShowPollNow"
        title="Poll Now"
        variant="neutral-lightest"
        class="ml-2 squared-button"
        @click="handleRefreshPoll"
      >
        <MIcon name="sync" />
      </MButton>

      <MButton
        title="Execute Runbook"
        variant="neutral-lightest"
        class="ml-2 squared-button"
        @click="onClickExecuteRunbbok"
      >
        <MIcon name="runbook" />
      </MButton>
      <MButton
        variant="neutral-lightest"
        title="Export"
        class="mx-2 squared-button"
        @click="triggerTemplateExport"
      >
        <MIcon name="image" />
      </MButton>
      <MButton
        class="squared-button mr-2"
        title="Fullscreen"
        variant="neutral-lightest"
        @click="triggerTemplateFullScreen"
      >
        <MIcon name="fullscreen" />
      </MButton>
      <MButton
        variant="neutral-lightest"
        title="View More"
        class="mr-2 squared-button"
        @click="handleOpenDrawer"
      >
        <MIcon name="view-more" />
      </MButton>
    </div>
    <FlotoDrawer
      :open="isDrawerOpen"
      :scrolled-content="false"
      width="50%"
      @hide="isDrawerOpen = false"
    >
      <template v-if="monitor" v-slot:title>
        <MRow :gutter="0" class="w-full">
          <MCol>
            <MRow :gutter="0" class="w-full flex items-center">
              <MButton
                shape="circle"
                class="squared-button mr-2"
                variant="neutral-lightest"
              >
                <MonitorType
                  :type="
                    application
                      ? application
                      : vm
                      ? $constants.VIRTUAL_MACHINE
                      : monitor.type
                  "
                  disable-tooltip
                  :center="false"
                  class="p-1"
                />
              </MButton>

              <div class="flex flex-col w-11/12">
                <div class="flex items-center">
                  <Severity
                    :object-id="monitor.id"
                    :instance="application || vm || ap"
                    class="mr-2"
                  />
                  <h5
                    class="text-ellipsis mb-0 mr-2 page-text-color"
                    style="
                      max-width: 500px;
                      color: var(--page-text-color) !important;
                    "
                    :title="
                      application
                        ? `${application} (${monitor.name || monitor.host})`
                        : vm
                        ? `${vm} (${monitor.name || monitor.host})`
                        : ap
                        ? `${ap} (${monitor.name || monitor.host})`
                        : monitor.name ||
                          monitor.target ||
                          monitor.host ||
                          monitor.serviceCheckTarget
                    "
                  >
                    {{
                      application
                        ? `${application} (${
                            monitor.name ||
                            monitor.host ||
                            monitor.ip ||
                            monitor.target
                          })`
                        : vm
                        ? `${vm} (${
                            monitor.name ||
                            monitor.host ||
                            monitor.ip ||
                            monitor.target
                          })`
                        : ap
                        ? `${ap} (${
                            monitor.name ||
                            monitor.host ||
                            monitor.ip ||
                            monitor.target
                          })`
                        : monitor.name ||
                          monitor.host ||
                          monitor.ip ||
                          monitor.target ||
                          monitor.serviceCheckTarget
                    }}
                  </h5>
                  <span class="text-neutral-light font-500 text-xs mt-1">
                    {{
                      monitor.host
                        ? ` | ${
                            monitor.category === $constants.SERVICE_CHECK
                              ? monitor.target
                              : instanceIp ||
                                monitor.ip ||
                                monitor.host ||
                                monitor.target ||
                                monitor.serviceCheckTarget
                          } | `
                        : ''
                    }}
                    {{
                      `${monitor.host ? '' : '|'} ${
                        monitor.vendor || monitor.type
                      } `
                    }}
                  </span>
                </div>

                <div
                  class="inline-flex flex-grow-0 min-w-0 flex-shrink-0 w-full"
                >
                  <div class="group-picker-container">
                    <GroupPicker
                      :value="monitor.groups"
                      disabled
                      :wrap="false"
                      style="flex: none"
                    />
                  </div>
                  <div class="loose-tags-container">
                    <LooseTags :value="monitor.tags" disabled />
                  </div>
                </div>
              </div>
            </MRow>
          </MCol>
        </MRow>

        <!-- <div class="flex items-center">
          <Severity
            :object-id="monitor.id"
            :instance="application || vm || ap"
          />
          <span class="text-ellipsis mx-2">
            {{
              application
                ? `${application} (${monitor.host})`
                : vm
                ? `${vm} (${monitor.host})`
                : ap
                ? `${ap} (${monitor.host})`
                : monitor.category === $constants.CLOUD ||
                  monitor.category === $constants.SERVICE_CHECK
                ? monitor.serviceCheckTarget || monitor.target
                : monitor.host || monitor.name
            }}
          </span>
        </div> -->
      </template>
      <MonitorStringCounters
        v-if="monitor.id"
        :key="`${monitor.id}${application || ''}${vm || ''}`"
        :monitor="monitor"
        :application="application"
        class="mb-4"
        :vm="vm"
        hide-metrics
        :is-network-device="isNetworkDevice"
        @show-action-result="showActionResultForItem = $event"
      />
    </FlotoDrawer>
    <Terminal
      :id="monitorId"
      :open="shouldShowTerminal && isTerminalOpen"
      ask-credentials
      @close="isTerminalOpen = false"
    />
    <RunbookList
      v-if="monitor.id"
      :monitor="monitor"
      :open="openRunbookListDrawer"
      @show-action-result="showActionResultForItem = $event"
      @close="openRunbookListDrawer = false"
    />
    <ActionResult
      v-if="showActionResultForItem !== null"
      :item="showActionResultForItem"
      :open="showActionResultForItem !== null"
      @close="showActionResultForItem = null"
    />
  </div>
</template>

<script>
import { authComputed } from '@state/modules/auth'
import GroupBy from 'lodash/groupBy'
import Bus from '@utils/emitter'
import Constants from '@constants'
import { generateId } from '@utils/id'
import { objectDBWorker } from '@/src/workers'
import Severity from '@components/severity.vue'
import MonitorType from '@components/monitor-type.vue'
import MonitorStringCounters from '@components/monitor-sidebar/monitor-string-counters.vue'
import MonitorStatus from '@components/monitor-sidebar/monitor-status.vue'
import LooseTags from '@/src/components/loose-tags.vue'
import CredentialSelectionDrawer from './credential-selection-drawer.vue'
import RadialView from '@/src/components/widgets/views/radial-view.vue'
import WANLinkForm from './wan-link-form.vue'
import ContainerRuntimeForm from './container-runtime-form.vue'
import Terminal from '@/src/components/terminal/terminal.vue'
import RunbookList from './runbook-list.vue'
import ActionResult from '@modules/settings/plugin-library/components/action-result.vue'

export default {
  name: 'MonitorDetails',
  components: {
    MonitorType,
    MonitorStringCounters,
    MonitorStatus,
    Severity,
    LooseTags,
    CredentialSelectionDrawer,
    RadialView,
    WANLinkForm,
    ContainerRuntimeForm,
    Terminal,
    RunbookList,
    ActionResult,
  },
  inject: { policyGridContext: { default: { data: undefined } } },
  props: {
    monitorId: {
      type: Number,
      required: true,
    },
    application: {
      type: String,
      default: undefined,
    },
    vm: {
      type: String,
      default: undefined,
    },
    ap: {
      type: String,
      default: undefined,
    },
    metricPluginProtocol: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      isDrawerOpen: false,
      monitor: {},
      guid: generateId(),
      loading: false,
      data: undefined,
      isTerminalOpen: false,
      openRunbookListDrawer: false,
      showActionResultForItem: null,
    }
  },
  computed: {
    ...authComputed,
    shouldShowTerminal() {
      return (
        this.monitor.category === this.$constants.NETWORK ||
        (this.monitor.category === this.$constants.SERVER &&
          this.monitor.type === this.$constants.LINUX)
      )
    },
    shouldShowPollNow() {
      if (this.application) {
        return true
      }
      // if (this.monitor.status === this.$constants.STATE_ENABLE) {
      if (this.monitor.isAgent) {
        return false
      }
      return true
      // }
      // return false
    },
    convertGridResultToGauge() {
      const groupBySeverity = GroupBy(
        (this.policyGridContext.data || {}).rows || [],
        'severity'
      )

      return {
        categories: Object.keys(groupBySeverity),
        series: [
          {
            data: Object.keys(groupBySeverity).map((key) => {
              return {
                name: key,
                y: (groupBySeverity[key] || []).length,
              }
            }),
          },
        ],
      }
    },
    getPolicyData() {
      return this.policyGridContext.policyWidget
    },
    isNetworkDevice() {
      return (
        [Constants.NETWORK].includes(
          this.monitor.category || this.monitor.type
        ) &&
        ![
          Constants.RUCKUS_WIRELESS,
          Constants.CISCO_WIRELESS,
          Constants.ARUBA_WIRELESS,
        ].includes(this.monitor.type || this.monitor.category)
      )
    },
    appsDiscoveredByAgent() {
      return this.monitor.appsDiscoveredByAgent
    },
    shouldShowAgentIcon() {
      if (this.application) {
        return (this.appsDiscoveredByAgent || []).includes(this.application)
      } else {
        return this.monitor.isAgent
      }
    },
    haseAlertPermission() {
      return this.hasPermission(this.$constants.ALERT_READ_PERMISSION)
    },
    instanceIp() {
      const ip = this.monitor?.instanceIpMap?.find(
        (i) => i.instance === (this.vm || this.ap)
      )?.['instance.ip']

      if (ip && ip !== '') {
        return ip
      }
      return undefined
    },
  },
  watch: {
    monitorId: {
      handler(newValue, oldValue) {
        if (newValue !== oldValue && newValue) {
          this.setDevice()
        }
      },
    },
  },
  mounted() {
    this.setDevice()
    Bus.$on(this.$constants.EVENT_MONITOR_DB_CHANGED, this.setDevice)
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(this.$constants.EVENT_MONITOR_DB_CHANGED, this.setDevice)
    })
  },
  beforeDestroy() {
    this.isDrawerOpen = false
  },
  methods: {
    triggerTemplateFullScreen() {
      Bus.$emit('ui:template:fullscreen')
    },
    triggerTemplateExport() {
      Bus.$emit('ui:template:export')
    },
    handleOpenDrawer() {
      this.isDrawerOpen = true
    },
    handleGoToHome() {
      this.$router.push(
        this.$currentModule.getRoute('group-template', {
          params: {
            category: this.$route.params.category,
          },
        })
      )
    },
    handleRefreshPoll() {
      Bus.$once(this.$constants.POLL_NOW_EVENT, (e) => {
        if (e.status === this.$constants.EVENT_SUCCESS_STATUS) {
          this.$successToast(e.message)
        } else {
          this.$errorToast(e.message)
        }
      })
      Bus.$emit('server:event', {
        'event.type': this.$constants.POLL_NOW_EVENT,
        'event.context': {
          id: this.monitor.id,
        },
      })
    },
    async setDevice() {
      const monitorId = this.monitorId
      const monitor = await objectDBWorker.getObjectById(monitorId)
      if (monitor) {
        this.monitor = Object.freeze(monitor)
      }
    },
    filterBySeverity(severity) {
      Bus.$emit('filter:policy_template', severity)
    },
    onClickExecuteRunbbok() {
      this.openRunbookListDrawer = true
    },
  },
}
</script>

<style lang="less" scoped>
.group-picker-container {
  min-width: 0; /* Prevents overflow issues */
  max-width: 50%; /* Limits the width to 50% when content overflows */
}

.loose-tags-container {
  min-width: 0; /* Prevents overflow issues */
  max-width: 50%; /* Limits the width to 50% when content overflows */
}
</style>
