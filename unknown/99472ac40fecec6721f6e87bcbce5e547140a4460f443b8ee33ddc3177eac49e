<template>
  <MCol
    class="left-side-list-view-panel h-full flex flex-col min-h-0 transition-width page-background-color"
    :class="{
      'border-right': menuVisible,
      absolute: affixedSidebar,
    }"
    :style="sidebarStyle"
  >
    <div class="flex-1 flex min-h-0 flex-col page-background-color">
      <div
        v-if="!forDeviceTemplate"
        class="dropdown-header p-3 pl-2 flex justify-between items-center"
      >
        <h4 class="mb-0 text-lg">Save View</h4>
      </div>
      <div
        class="page-background-color mb-2"
        :class="{ 'my-2 px-2': !forDeviceTemplate }"
      >
        <MInput
          v-model="searchTerm"
          class="search-box"
          placeholder="Search"
          name="search"
          style="max-width: unset"
        >
          <template v-slot:prefix>
            <MIcon name="search" />
          </template>
        </MInput>
      </div>
      <FlotoContentLoader :loading="shouldLoading" class="flex flex-col flex-1">
        <div class="flex-1 min-h-0 overflow-auto">
          <div class="flex flex-col" :class="{ 'px-2': !forDeviceTemplate }">
            <div class="w-full flex border-bot mb-2">
              <MButton
                variant="transparent"
                :shadow="false"
                shape="circle"
                class="w-full flex flex-start"
                @click.prevent.stop="$emit('create')"
              >
                <div class="flex w-full flex-end items-center">
                  <MIcon
                    name="plus"
                    class="cursor-pointer text-primary mr-1 mb-0"
                  />

                  <span class="text-primary text-xs"> Create New</span>
                </div>
              </MButton>
            </div>
            <div
              v-for="item in currentUserSavedView"
              :key="item.id"
              class="page-background dashboard-list-item flex items-center rounded cursor-pointer mb-1 font-400"
              :style="
                (selectedMetricExplorerView || {}).id === item.id
                  ? { background: 'var(--group-list-hover-bg)' }
                  : {}
              "
              @click.stop="$emit('select-view', item)"
            >
              <div class="flex-1 w-full flex items-center justify-between">
                <div class="text-ellipsis w-10/12 pl-2 py-2" :title="item.name">
                  {{ item.name }}
                </div>

                <MPermissionChecker
                  :permission="$constants.METRIC_EXPLORER_DELETE_PERMISSION"
                >
                  <MButton
                    variant="transparent"
                    :shadow="false"
                    shape="circle"
                    class="ml-2"
                    @click.prevent.stop="handleDeleteSavedView(item.id)"
                  >
                    <MIcon
                      name="trash-alt"
                      class="cursor-pointer text-secondary-red"
                    />
                  </MButton>
                </MPermissionChecker>
              </div>
            </div>
          </div>
        </div>
      </FlotoContentLoader>
    </div>
  </MCol>
</template>

<script>
import { authComputed } from '@state/modules/auth'

import { deleteExplorerApi, EXPLORER_TYPE } from '@api/explorer-api'

export default {
  name: 'SavedMetricExplorersList',

  inject: {
    MetricExplorerSavedViewProvider: {
      default: { savedView: [], loading: false },
    },
  },

  props: {
    selectedMetricExplorerView: {
      type: Object,
      default: undefined,
    },
    menuVisible: {
      type: Boolean,
      default: false,
    },
    forDeviceTemplate: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      searchTerm: undefined,
    }
  },
  computed: {
    ...authComputed,
    sidebarStyle() {
      if (this.affixedSidebar) {
        return {
          position: 'absolute',
          top: 0,
          bottom: 0,
          zIndex: 6,
          width: '25%',
          ...(!this.menuVisible ? { backgroundColor: 'transparent' } : {}),
          marginTop: 0,
        }
      }
      return {}
    },
    filteredItems() {
      const data = this.MetricExplorerSavedViewProvider?.savedView

      const searchTerm = this.searchTerm
      if (searchTerm && (searchTerm || '').length) {
        return data?.filter(
          (n) => n?.name?.toLowerCase()?.indexOf(searchTerm?.toLowerCase()) >= 0
        )
      }
      return data
    },
    currentUserSavedView() {
      return this.filteredItems?.filter((viewItem) => {
        if (viewItem?.security === 'Private' && this.user.id) {
          if (viewItem?.users?.length) {
            return viewItem?.users?.includes(this.user.id)
          }
        } else {
          return true
        }
      })
    },

    shouldLoading() {
      return this.MetricExplorerSavedViewProvider?.loading
    },
    affixedSidebar() {
      if (this.forDeviceTemplate) {
        return false
      }

      return true
    },
  },
  methods: {
    handleDeleteSavedView(id) {
      if (this.selectedMetricExplorerView?.id === id) {
        this.$emit('create')
      }
      return deleteExplorerApi(id, EXPLORER_TYPE.METRIC).then(() => {
        this.MetricExplorerSavedViewProvider.deleteView(id)
      })
    },
  },
}
</script>

<style lang="less" scoped>
.dashboard-list-item {
  &:hover {
    background: var(--group-list-hover-bg);
  }
}
</style>
