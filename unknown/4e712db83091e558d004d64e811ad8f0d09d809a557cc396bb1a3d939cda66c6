<template>
  <div>
    <MRow :gutter="0" class="items-center justify-between w-full">
      <MCol v-if="shouldShowWidgetType">
        <FlotoFormItem label="Widget Type">
          <MRadioGroup
            v-model="category"
            :disabled="disabled"
            :options="widgetCategoriesOptions"
            as-button
          />
        </FlotoFormItem>
      </MCol>
      <MCol v-if="shoudShowArchivedMonitors"> </MCol>
      <MCol
        v-if="shoudShowArchivedMonitors"
        class="flex items-center justify-end m-2"
      >
        <div class="flex items-center" @click.stop>
          <span class="mr-2 text-neutral"> Show Archived Monitors only</span>
          <MSwitch
            v-model="showArchivedMonitors"
            :disabled="disabled"
            checked-children="ON"
            un-checked-children="OFF"
          />
        </div>
      </MCol>

      <!-- <MCol>
        <MRow class="justify-end items-center">
          <h6 class="mr-3 mb-3 text-neutral-light text-sm">Time Range</h6>
          <FlotoFormItem rules="required">
            <TimeRangePicker
              :value="timeline"
              :disabled="disabled"
              hide-icon
              :hide-custom-time-range="false"
              @change="$emit('timelineChange', $event)"
            />
          </FlotoFormItem>
        </MRow>
      </MCol> -->
    </MRow>
    <MRow :gutter="0">
      <MCol :size="12">
        <WidgetGroupForm
          v-if="shouldShowWidgetGroupForm"
          :key="value.category"
          :disabled="disabled"
          :value="group"
          :category="value.category"
          :widget-type="value.widgetType"
          :widget="value"
          :type="group.type"
          :counter-data-type="counterDataType"
          :timeline="timeline"
          hide-ribbon
          disable-padding
          disable-bg
          disable-remove-group
          :report-category="reportCategory"
          :hide-aggrigation-options="hideAggrigationOptions"
          :show-archived-monitors="showArchivedMonitors"
          :disable-type-selector="disableTypeSelector"
          @widget:update="$emit('change', $event)"
          @change="group = $event"
        />

        <div v-else class="pr-3">
          <MRow :gutter="0" :size="12">
            <MCol class="mt-3" :size="8">
              <MonitorProvider :search-params="searchParams">
                <GroupProvider>
                  <MonitorGroupSelection
                    v-model="sourceSelection"
                    :row-gutter="16"
                    source-type="metric"
                    show-label
                  />
                </GroupProvider>
              </MonitorProvider>
            </MCol>
            <MCol :size="3" class="ml-3 mt-3">
              <FlotoFormItem label="Result By">
                <FlotoDropdownPicker
                  v-model="customResultby"
                  placeholder=" "
                  :options="resultByOptions"
                />
              </FlotoFormItem>
            </MCol>
            <FlotoFormItem label="Script Language" class="my-3">
              <MRadioGroup
                v-model="reportScriptType"
                :disabled="disabled"
                as-button
                :options="reportScriptOptions"
              />
            </FlotoFormItem>
          </MRow>
          <FlotoFormItem label="Reporting Script">
            <CodeEditor
              v-model="reportScript"
              :disabled="disabled"
              :mode="languageMode"
            />
          </FlotoFormItem>
          <MRow :gutter="0" class="flex justify-end w-full">
            <MButton :disabled="reportScript === ''" @click="executeScript">
              Execute
            </MButton>
          </MRow>
        </div>
      </MCol>
      <!-- <MCol :size="1" class="flex justify-end mt-8">
        <MButton @click="isSelectingWidget = true">
          <MIcon name="monitor-import" size="sm" /> Widget
        </MButton>
      </MCol> -->
    </MRow>
    <!-- <WidgetSelector
      ref="widgetSelectorRef"
      for-report-form
      :open="isSelectingWidget"
      @add="addWidget"
      @cancel="isSelectingWidget = false"
    /> -->
  </div>
</template>

<script>
import {
  AvailableWidgetCategories,
  WidgetTypeConstants,
} from '@components/widgets/constants'
import {
  getWidgetProperties,
  getDefaultDataForGroup,
} from '@components/widgets/helper'
import { AvailableReportCategories } from '../helpers/report'
import WidgetGroupForm from '@components/widgets/widget-group-form/widget-group-form.vue'
import CodeEditor from '@components/code-editor.vue'
import MonitorGroupSelection from '@components/widgets/monitor-or-group-selection.vue'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import GroupProvider from '@components/data-provider/group-provider.vue'

// import TimeRangePicker from '@components/widgets/time-range-picker.vue'
// import WidgetSelector from '@components/widgets/widget-selector.vue'
import Bus from '@utils/emitter'

export default {
  name: 'ReportGroupForm',
  components: {
    WidgetGroupForm,
    CodeEditor,
    // TimeRangePicker,
    // WidgetSelector,
    MonitorGroupSelection,
    MonitorProvider,
    GroupProvider,
  },
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default: undefined,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    reportCategory: {
      type: String,
      required: true,
    },
    timeline: {
      type: Object,
      required: true,
    },
    report: {
      type: Object,
      default: undefined,
    },
    schedule: {
      type: Object,
      default: undefined,
    },

    shoudShowArchivedMonitors: {
      type: Boolean,
      default: false,
    },
    defaultWidget: {
      type: Object,
      default: undefined,
    },
  },
  data() {
    this.reportScriptOptions = [
      { value: 'go', text: 'GO' },
      { value: 'python', text: 'Python' },
      { value: 'node', text: 'Node.js' },
    ]
    this.resultByOptions = [
      {
        text: 'Monitor',
        key: 'monitor',
      },
      {
        text: 'Group',
        key: 'group',
      },
      {
        text: 'Tag',
        key: 'tag',
      },
    ]
    return {
      // isSelectingWidget: false,
      // reportScript: '',
    }
  },
  computed: {
    searchParams() {
      return {
        category: [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.SDN,
          this.$constants.OTHER,
          this.$constants.CLOUD,
          this.$constants.VIRTUALIZATION,
          this.$constants.SERVICE_CHECK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.STORAGE,
          this.$constants.CONTAINER_ORCHESTRATION,
        ],
      }
    },
    counterDataType() {
      if (
        this.category === WidgetTypeConstants.GRID ||
        this.value.widgetType === WidgetTypeConstants.GRID
      ) {
        return ['string', 'numeric']
      }

      if (
        [
          AvailableReportCategories.INVENTORY,
          AvailableReportCategories.AVAILABILITY,
          AvailableReportCategories.ACTIVE_ALERTS,
          AvailableReportCategories.AVAILABILITY_ALERT,
          AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,

          AvailableReportCategories.METRIC_ALERT,
        ].includes(this.reportCategory)
      ) {
        return ['string']
      }
      return ['numeric']
    },
    widgetCategoriesOptions() {
      let excluded = []
      const category = this.reportCategory
      if (
        [
          AvailableReportCategories.PERFORMANCE,
          AvailableReportCategories.LOG_ANALYTICS,
          AvailableReportCategories.FLOW_ANALYTICS,
        ].includes(category)
      ) {
        excluded = [
          WidgetTypeConstants.SANKEY,
          WidgetTypeConstants.HEATMAP,
          WidgetTypeConstants.GAUGE,
          WidgetTypeConstants.STREAM,
          WidgetTypeConstants.MAP_VIEW,
          WidgetTypeConstants.ANOMALY,
          WidgetTypeConstants.FORECAST,
          WidgetTypeConstants.ACTIVE_ALERT,
          WidgetTypeConstants.EVENT_HISTORY,
        ]
      }
      if (category === 'alert') {
        excluded = [
          WidgetTypeConstants.SANKEY,
          WidgetTypeConstants.GAUGE,
          WidgetTypeConstants.STREAM,
          WidgetTypeConstants.MAP_VIEW,
          WidgetTypeConstants.ANOMALY,
          WidgetTypeConstants.FORECAST,
          WidgetTypeConstants.ACTIVE_ALERT,
          WidgetTypeConstants.EVENT_HISTORY,
        ]
      }
      if (category === 'availability') {
        excluded = [
          WidgetTypeConstants.TOPN,
          WidgetTypeConstants.GAUGE,
          WidgetTypeConstants.HEATMAP,
          WidgetTypeConstants.SANKEY,
          WidgetTypeConstants.STREAM,
          WidgetTypeConstants.MAP_VIEW,
          WidgetTypeConstants.ANOMALY,
          WidgetTypeConstants.FORECAST,
          WidgetTypeConstants.ACTIVE_ALERT,
          WidgetTypeConstants.EVENT_HISTORY,
        ]
      }
      return Object.keys(AvailableWidgetCategories)
        .filter((key) => excluded.includes(key) === false)
        .map((key) => ({
          value: key,
          text: AvailableWidgetCategories[key],
        }))
    },
    category: {
      get() {
        return (this.value || {}).category
      },
      set(category) {
        const value = {
          ...(this.value || {}),
          category,
          widgetType:
            category === WidgetTypeConstants.CHART
              ? WidgetTypeConstants.AREA
              : category === WidgetTypeConstants.TOPN
              ? WidgetTypeConstants.AREA
              : category === WidgetTypeConstants.GAUGE
              ? WidgetTypeConstants.METRO_TILE
              : undefined,
          groups: [
            getDefaultDataForGroup(this.reportCategory.toLowerCase(), {
              ...this.value,
              category,
            }),
          ],
          widgetProperties: getWidgetProperties(
            category,
            [WidgetTypeConstants.TOPN, WidgetTypeConstants.CHART].includes(
              category
            )
              ? {
                  styleSetting: {
                    legendEnabled: true,
                  },
                }
              : undefined
          ),
          ...(this.value?.category !== category
            ? { visualizationTags: undefined }
            : {}),
        }

        this.$emit('change', value)
      },
    },
    group: {
      get() {
        if (!this.value) {
          return {}
        }
        return (this.value.groups || [])[0] || {}
      },
      set(data) {
        this.$emit('change', {
          ...(this.value || {}),
          groups: [
            { ...(((this.value || {}).groups || [])[0] || {}), ...data },
          ],
          // visualizationTags: undefined,

          ...(this.value?.groups?.[0]?.counters?.[0]?.counter?.key !==
          data?.counters?.[0]?.counter?.key
            ? { visualizationTags: undefined }
            : {}),
        })
      },
    },

    reportScriptType: {
      get() {
        return this.value.reportScriptType
      },
      set(reportScriptType) {
        this.$emit('change', {
          ...this.value,
          reportScriptType,
          reportScript: '',
          executeScript: false,
          customResultby: '',
          sourceSelection: {
            entityType: undefined,
            entities: undefined,
          },
        })
      },
    },

    reportScript: {
      get() {
        return this.value.reportScript
      },
      set(reportScript) {
        this.$emit('change', { ...this.value, reportScript })
      },
    },
    sourceSelection: {
      get() {
        return this.value.sourceSelection
      },
      set(sourceSelection) {
        this.$emit('change', { ...this.value, sourceSelection })
        this.$nextTick(() => {
          this.executeScript()
        })
      },
    },
    customResultby: {
      get() {
        return this.value.customResultby
      },
      set(customResultby) {
        this.$emit('change', { ...this.value, customResultby })
        this.$nextTick(() => {
          this.executeScript()
        })
      },
    },

    showArchivedMonitors: {
      get() {
        return this.value.showArchivedMonitors
      },
      set(showArchivedMonitors) {
        this.$emit('change', {
          ...this.value,
          showArchivedMonitors,
        })
      },
    },

    shouldShowWidgetType() {
      return [
        AvailableReportCategories.PERFORMANCE,
        AvailableReportCategories.LOG_ANALYTICS,
        AvailableReportCategories.FLOW_ANALYTICS,
      ].includes(this.reportCategory)
    },
    hideAggrigationOptions() {
      return [
        AvailableReportCategories.INVENTORY,
        AvailableReportCategories.AVAILABILITY,
        AvailableReportCategories.ACTIVE_ALERTS,
        AvailableReportCategories.AVAILABILITY_ALERT,
        AvailableReportCategories.METRIC_ALERT,
        AvailableReportCategories.POLLING_REPORT,
        AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
      ].includes(this.reportCategory)
    },
    shouldShowWidgetGroupForm() {
      return ![AvailableReportCategories.CUSTOM_SCRIPT].includes(
        this.reportCategory
      )
    },
    languageMode() {
      if (this.value.reportScriptType === 'python') {
        return 'text/x-python'
      } else if (this.value.reportScriptType === 'go') {
        return 'text/x-go'
      } else {
        return 'text/javascript'
      }
    },
    disableTypeSelector() {
      return [AvailableReportCategories.POLLING_REPORT].includes(
        this.reportCategory
      )
    },
  },
  methods: {
    // addWidget(widget) {
    //   Bus.$emit('widget-selector:add-widget', {
    //     widget: widget,
    //     report: this.report,
    //     schedule: this.schedule,
    //   })
    //   this.isSelectingWidget = false
    // },

    executeScript() {
      // const inputString = this.reportScript

      // // Define the regular expression pattern
      // const pattern = /"visualization\.category"\s*:\s*"([^"]*)",/

      // // Use the match method to find matches in the input string
      // const match = inputString.match(pattern)

      this.$emit('change', {
        ...(this.value || {}),
        executeScript: true,
        isStaticPreview: true,

        // category: match && match[1] ? match[1] : 'Grid',
      })

      Bus.$emit('widget.generate.preview')
    },
  },
}
</script>
