<template>
  <FlotoScrollView>
    <BenchmarkProvider>
      <div class="flex flex-col flex-1 w-full px-2 dashboard-container">
        <MRow
          class="flex mt-2 alert-dashboard"
          :gutter="8"
          style="flex-shrink: 0; height: 23%"
        >
          <MCol :size="4" class="h-full">
            <OverallCompliance />
          </MCol>

          <MCol :size="8" class="h-full">
            <div class="vue-grid-item h-full flex flex-col">
              <small
                class="font-500 mx-2 my-2 m-0 inline-block text-size-medium"
                >Compliance Trend - Last 7 Days</small
              >
              <div class="flex flex-1 min-h-0 overflow-auto">
                <WidgetContainer
                  :widget="columnChartWidget"
                  use-initial-request
                  disable-refresh-interval
                  disabled
                  font-size="large"
                  hide-timeline
                  is-preview
                  tooltip-out-side
                  :series-color="config.COMPLIANCE_TRAND_COLOR"
                />
              </div>
            </div>
          </MCol>
        </MRow>

        <MRow
          class="flex mt-2 alert-dashboard"
          :gutter="8"
          style="flex-shrink: 0; height: 23%"
        >
          <MCol :size="4" class="h-full">
            <AssessmentResult @drill-down="onDrillDown" />
          </MCol>
          <MCol :size="8" class="h-full">
            <FailureBySeverity @drill-down="onDrillDown" />
          </MCol>
        </MRow>

        <MRow class="flex mt-2 flex-1 pb-4" :gutter="0">
          <AuditPolicyExplorerList />
        </MRow>
      </div>
    </BenchmarkProvider>

    <ComplianceDrillDownDrawer
      v-if="showDrillDownFor"
      :open="showDrillDownFor !== null"
      :drill-down-item="showDrillDownFor"
      @hide="showDrillDownFor = null"
    />
  </FlotoScrollView>
</template>

<script>
import WidgetContainer from '@components/widgets/views/container.vue'
import { buildWidgetContext, makeCounter } from '@utils/socket-event-as-api'
import { WidgetTypeConstants } from '@components/widgets/constants'
import OverallCompliance from '../components/compliance/overall-compliance.vue'
import AssessmentResult from '../components/compliance/assessment-result.vue'
import FailureBySeverity from '../components/compliance/failure-by-sevetiry.vue'
import AuditPolicyExplorerList from '../components/compliance/audit-policy-explorer-list.vue'
import Config from '../config'
import { calculateLastTimeline } from '@components/widgets/helper'
import ComplianceDrillDownDrawer from '../components/compliance/compliance-drilldown-drawer.vue'
import BenchmarkProvider from '@components/data-provider/benchmark-provider.vue'

export default {
  name: 'Compliance',
  components: {
    WidgetContainer,
    OverallCompliance,
    AssessmentResult,
    FailureBySeverity,
    AuditPolicyExplorerList,
    ComplianceDrillDownDrawer,
    BenchmarkProvider,
  },
  data() {
    this.config = Config

    return {
      showDrillDownFor: null,
    }
  },
  computed: {
    columnChartWidget() {
      const timeline = calculateLastTimeline(7, 'day')
      return buildWidgetContext({
        groupType: 'compliance',
        timeline,
        category: WidgetTypeConstants.CHART,
        widgetType: WidgetTypeConstants.STACKED_VERTICAL_BAR,
        counters: [makeCounter('compliance.percentage', 'avg')],
        granularity: { value: '1', unit: 'd' },
      })
        .setWidgetProperties({
          styleSetting: {
            chartOptions: {
              yAxis: {
                allowDecimal: false,
              },
            },
          },
        })
        .setWidgetProperties({
          styleSettings: {
            chartOptions: {
              yAxis: {
                allowDecimals: false,
              },
            },
          },
        })
        .getContext()
    },
  },
  methods: {
    onDrillDown(context) {
      this.showDrillDownFor = context
    },
  },
}
</script>
