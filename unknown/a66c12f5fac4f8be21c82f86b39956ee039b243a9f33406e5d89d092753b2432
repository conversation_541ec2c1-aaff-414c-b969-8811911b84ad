import Template from 'lodash/template'

const message = {
  // general confirm messages
  confirm: 'Are you sure, <%= message %>',
  delete_resource: 'you want to delete <%= resource %>',
  group_level_error: 'Group can have a maximum of five hierarchy levels.',

  // general crud messages
  created: '<%= resource %> has been created successfully.',
  updated: '<%= resource %> has been updated successfully.',
  updated_bulk: '<%= resource %> have been updated successfully.',
  deleted: '<%= resource %> has been deleted successfully.',
  deleted_bulk: '<%= resource %> have been deleted successfully.',

  // general action completed messages
  start: 'Request to start <%= resource %> has been sent successfully.',
  stop: 'Request to stop <%= resource %> has been sent successfully.',
  restart: 'Request to restart <%= resource %> has been sent successfully.',
  reset: 'Request to reset <%= resource %> has been sent successfully.',
  download: 'Download <%= resource %> log request has been sent successfully.',
  requested: 'Request to <%= resource %> has been sent successfully.',
  enabled: '<%= resource %> has been enabled successfully.',
  enabled_bulk: '<%= resource %> have been enabled successfully.',
  disabled: '<%= resource %> has been disabled successfully.',
  disabled_bulk: '<%= resource %> have been disabled successfully.',

  // page specific messages
  password_reset_confirm:
    'Are you sure, you want to reset password policy settings?',
  ldap_help:
    'Ensure that the "motadata users" group is present on the target LDAP server.',
  ldap_host_help: 'Ensure Motadata should be able to resolve the host',
  ldap_sync_confirm: 'Are you sure you want to sync  <%= ip %> ?',
  ldap_testing: 'Testing LDAP Server Connection...',
  ldap_syncing: 'Syncing LDAP Server...',
  ldap_sync_sent: 'Sync request sent to the Motadata server.',

  mail_test: 'Testing Email Configurations...',
  url_test: 'Testing URL....',
  dns_test: 'Testing DNS Settings...',
  sms_test: 'Testing SMS Server...',
  metric_plugin_test: 'Testing Metric Plugin...',
  topology_plugin_test: 'Testing Topology Plugin...',
  runbook_plugin_test: 'Testing Runbook Plugin...',
  config_plugin_test: 'Testing Config Plugin...',
  motadata_serviceops_test: 'Testing Motadata Serviceops Configurations...',
  servicenow_test: 'Testing ServiceNow Configurations...',

  url_time_out:
    'Specify the time limit (in seconds) for establishing AIOPs server connection to ServiceOps server URL.',
  motadata_serviceops_urgency:
    'Map AIOPs alert severity to the ServiceOps incident urgency.',
  auto_close_ticket:
    'Auto-close tickets when the alert severity changes to clear.',
  business_hour:
    'ServiceNow incidents will only be generated for specified Business Hours.',

  servicenow_url_time_out:
    'Specify the time limit (in seconds) for establishing AIOPs server connection to ServiceNow server URL.',
  servicenow_auto_sync:
    'Sync ServiceNow metadata, including Impact, Urgency, Category, Sub Category, Technician, and more, with your Motadata AIOps integration profile.',
  topology_monitor_help:
    'Leave this field blank to include all monitors that match the Vendor, Make, and Model selection.',

  discovery_run: 'Run discovery request sent to the Motadata server.',
  discovery_abort: 'Abort discovery request sent to the Motadata server.',
  discovery_provision_started: 'Object provisioning started...',
  discovery_credential_help: `We recommend you to select maximum 2 or 3 credential profiles for better performance.`,
  discovery_wan_link_credential_help: `You must provide write community for IPSLA rediscovery`,
  agent_availability_status_help: `Determine an agent availability if the agent is not able to connect to Motadata server.`,
  service_url_discovery_help: 'Content that should be present in web pages',
  service_url_discovery_method_help:
    'Enter request parameters using key/value pairs.',
  service_url_discovery_method_headers_help:
    'Enter header parameters using key/value pairs.',

  monitor_onmaintenance: 'Monitor has been put on maintenance successfully.',
  monitors_onmaintenance: 'Monitors have been put on maintenance successfully.',
  monitor_offmaintenance:
    'Monitor has been removed from maintenance successfully.',
  monitors_offmaintenance:
    'Monitors have been removed from maintenance successfully.',
  monitor_action: `Are you sure, you want to <%= action %>?`,

  agent_configuration_restart:
    'To Apply the new configurations, agents need to be restarted, Are you sure you want to restart selected agents?',
  agent_action: `Are you sure, you want to <%= action %>?`,
  agent_config_restriction:
    'Agent is not running so unable to configure agent <%= agentIP %>',
  monitor_schedule_error: 'Both schedules are required',
  snmp_trap_profile_auto_clear: `if you choose 'Yes' then the Motadata will clear the severity of the current trap profile.`,
  snmp_trap_profile_filter: `Selecting 'Yes' will discard this trap and 'No' will ingest the trap for analysis.`,
  collector_tooltip: `Choose a collector to initiate the polling, discovery, and rediscovery process from a designated collector or you can leave this field empty to enable load balancing throughout the process.`,
  aws_regions_tooltip: `Select all relevant AWS Regions to discover the corresponding AWS Services.`,
  polling_fail_notification:
    'The sysem will send a notification if polling fails due to timeout, an invalid port, incorrect credentials, or a failed connection.',
  rediscover_warning:
    'Note: By adding this <%= type %>, your license will be consumed.',
  rediscover_confirmation: 'Are you sure, you want to add <%= type %>?',
  unsupported_file_type: 'Unsupported file type',
  url_tooltip: `google.com`,
  snmp_trap_translator_helper:
    'Provide the message you want to format a trap message using variable bindings. Variable binding index starts from 1 ($1 and so forth).',

  anomaly_algorithm:
    'Possible values are Basic and Agile where Basic contains no seasonality while Agile contains seasonality',
  anomaly_deviation:
    'It modifies the width of upper and lower anomalies band. Possible values are 1 to 5',
  forecast_algorithm:
    ' Possible values are Linear and Seasonal where Linear contains no seasonality while Seasonal contains seasonality',
  outlier_tolerance:
    'To make Tolerance more sensitive reduce value to 2.5 or 2.0 To make less sensitive, increase the value to 4.0 or 4.5. Ideal value 3.0',
  alert_mode:
    'Combined alert mode triggers on aggregated alert for all the hosts. \n Individual alert mode trigger a separate alert for each host.',
  evaluation_window:
    'Specifies the time period for policy evaluation, from the start of the policy evaluation, going back in time for a period equal to the value mentioned in this field. <br/><br/> The value should be more than 1 minute and less than 7 days',
  evaluation_frequency:
    'Determines the frequency at which policy evaluation occurs. <br/><br/> The minimum value should be 30 seconds',

  select_monitor_group: 'Select Monitors or Groups',
  select_source_group: 'Select Source or Groups',

  poll_success: 'Requset for polling has been sent.',
  downloading_report: 'Exporting <%= reportName %> report',
  save_mail_server_setting: 'Mail server setting saved successfully!',
  save_sso_setting: 'SSO configurations saved successfully!',
  integration_profile_setting: 'Integration saved successfully!',
  servicenow_setting: 'ServieNow saved successfully!',
  microsoft_teams_setting: 'Microsoft Teams Integration Successful',
  microsoft_teams_setting_error: 'Microsoft Teams Integration Failed',

  atlassian_setting: 'Atlassian Jira Integration Successful',
  atlassian_setting_error: 'Atlassian Jira Integration Failed',

  data_retention_setting: 'Data Retention saved successfully!',
  log_level_change: 'System log level changed successfully',
  collector_log_level_change: 'Logging settings updated successfully!',
  clear_alert: 'Clear Alert',
  choose_minimum_entity:
    'Kindly choose a minimum of  <%= count %> entity listed under the " <%= name %> " section.',
  config_device_delete_note:
    'Note: Deleting a device from here will also delete it from the network config management.',
  config_device_disable_note:
    'Note: Disabling the device will also disable it from the network config management.',
  config_device_on_maintainance_note:
    'Note: This will prevent any network configuration activities for the monitor.',
  config_file_restore:
    "This will restore to running-config of your device - selected version will be restored to device's running-config",
  storage_profile_attach: 'Storage profile attached successfully!',
  collector_upgrade:
    'while upgrading Motadata Collector, all data collection activity will be stopped. <br/>Do you want to proceed ?',
  master_upgrade:
    'While upgrading master applicatin, you will not be able to access Motadata UI and all monitoring activity will be stopped.<br/>Do you want to proceed ?',
  agent_upgrade:
    'While upgrading MotadataAgent, all data collection and monitoring activity will be stopped. <br/>Do you want to proceed ?',
  motadata_restore_confirmation:
    'While restoring ConfigDB backup, you will not be able to access. Motadata Ul and all monitoring activity will be stopped. Restoration time will depend on your ConfigDB size. You may have data loss if you will restore older backup file. \n  \n  Do you want to proceed?',
  upgrade_message:
    "The  <%= type %> version doesn't match the Master. kindly perform an upgrade",
  compatibility_test_message: 'Running compatibility check ...',

  notify_team_renotification:
    'Users will receive a follow-up notification after a designated time period if the alert remains at the choosen severity level. ',
  renotify_acknowledged:
    'The system will stop sending additional notifications once the alert has been acknowledged.',
  alert_message:
    '$$$counter$$$ has entered into $$$severity$$$ state with value $$$value$$$ at $$$policy.trigger.time$$$',
  alert_message_macros:
    'For a list of default supported Macros, Please visit: ',
  sparkline_limit:
    'Sparkline will be available for timelines shorter than 48 hours.',
  ldap_server_setting_delete_note:
    'Note: Deleting this LDAP server will remove all users synced from it, and they will lose access to Motadata AIOps.',
  mail_server_email:
    'Configure the email address to be used for notifying the users.',

  revoke_token:
    'Note: Any applications or integration using this token will no longer be able to access APIs',
  log_parsing_filters:
    'Specify how log should be filtered for parsing based on the keywords and condition specified in this field.',

  single_signon_name_id:
    'Select the format of the NameID that will be used in the SAML assertion',
  identity_provider_metadata_file:
    'Upload the XML file containing Certificate details',
  identity_provider_entity_id:
    'Specify a unique identifier for the identity provider',
  identity_provider_login_url:
    'Specify URL for Single Sign-On services where authentication requests are sent',
  identity_provider_logout_url:
    'Specify URL for Single Logout services where logout requests are sent',
  identity_provider_fingerprint:
    'Specify the hexadecimal string that uniquely identifies the certificate',
  client_certificate:
    'Specify the Base64-encoded string that uniquely identifies the certificate.',
  client_certificate_file:
    'Specify the Base64-encoded file with extension .crt that uniquely identifies the certificate.',
  client_key:
    'Specify the private key associated with the client certificate in Base64-encoded format.',
  client_key_file:
    'Specify the private key file with extension .key associated with the client certificate in Base64-encoded format.',
  certificate_authority:
    'Specify the Base64-encoded string of the certificate authority that issued the client certificate.',
  certificate_authority_file:
    'Specify the Base64-encoded file with extension .crt of the certificate authority that issued the client certificate.',

  import_users_mapped_fields:
    'Map these fields with the corresponding field names from your identity provider',
  logging_duration:
    'Value should not be greater than 15 minutes for datastore and for other types it should not be greater than 30 minutes and for all types value should not be less than 10 minutes',

  health_diagnostics:
    'Diagnostics data collection has started successfully. The download will start shortly.',
  hyperconverged_ip_host: 'Enter Prism Element or Prism Central IP',
  sdn_ip_host: 'Enter Manager IP',
  container_orchestration_ip_host: `Enter API Server's IP`,
  log_forwarder_test: 'Testing Log Forwarder...',
  single_sign_on_import_user: `User import started. You'll be notified when it's done.`,
  log_collection_runbook:
    'Enable this to use the runbook for remote log collection',
  backup_profile_database_type:
    'Determine which database to back up based on your needs: ConfigDB for configuration-related data or ReportDB for either the original or summarized visual representation data.',
  backup_profile_datastore_type:
    'Here, data type refers to the default data types available in the database, whether raw or aggregated.',
  invalid_SSO_configuration: 'Invalid SSO Configuration',
  login_failed:
    'Login failed. Reason: Invalid SAML response received from the IDP',

  serviceops_auto_sync:
    'Sync Motadata ServiceOps metadata, including Impact, Urgency, Category, Assignee, Technician Group, and more, with your Motadata AIOps integration profile.',
  microsoft_teams_time_out:
    'Time required to connect with the provider (in seconds).',
  microsoft_teams_sync:
    'Sync Microsoft Teams metadata, including teams, channels and more with your Motadata AIOps Integration profile.',

  handle_name:
    'The Handle name is same as Profile name with forward slash (/) in the beginning which will be used in Policy Settings to Notify Team.',
  proxy_server:
    'Enable this option to use the proxy server for this integration. Ensure proxy settings are correctly configured in the system',
  malicious_html_content_error_message: 'Invalid Input Detected.',
  save_Multi_factor_authentication_setting:
    'Two Factor Authentication updated successfully!',
  disabled_tow_factor_authentication:
    'You are about to disable Two-Factor Authentication (2FA). This will reduce security and may expose your account to unauthorized access',
  assign_tag_key_tooltip:
    'Specify the key that you wish to assign. You can also specify a simple-value tag here (key-value tags are recommended)',

  assign_tag_value_tooltip:
    "Specify the value that you wish to assign or enter '{' to derive values from the existing counters.",

  remove_tag_tooltip: 'Specify the tag that you wish to remove.',
  auto_sync_atlassian_jira:
    'Sync Jira metadata, including project, issue type, status, and more, with your Motadata AIOps integration profile.',
  atlassian_jira_time_out:
    'Specify the time limit (in seconds) for establishing AIOps server connection to Jira server URL.',

  use_proxy_atlassian_jira:
    'Enable this option to use the proxy server for this integration. Ensure proxy settings are correctly configured in the system.',
  atlassian_jira_test: 'Testing Atlassian Jira Configurations...',
  metric_explorer_update: 'Metric Explorer updated successfully',
  discover_available_containers:
    'When enabled, this option discovers containers in all states - including running, exited, paused and created. When disabled, only running containers will be discovered.',
  use_custom_sticky_timeline:
    'When enabled, this widget will display data based on its own selected timeline, overriding the global dashboard timeline.',
  topology_view_group:
    'Use tag to organize this topology view under a defined standalone or key-value label for better classification and access.',
}

export default function getMessage(key, args) {
  if (message[key]) {
    const fn = Template(message[key])
    return fn(args)
  }
  return `No Message found for key ${key}`
}
