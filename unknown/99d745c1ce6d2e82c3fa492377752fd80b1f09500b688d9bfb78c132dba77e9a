<template>
  <FlotoDrawer :open="open" @hide="handleHide">
    <template v-slot:title>
      <h5 class="text-primary text-ellipsis" :title="responseData['name']"
        >{{ responseData['name'] }}
        <MIcon name="external-link"></MIcon>
      </h5>
    </template>

    <FlotoContentLoader :loading="loading" class="flex flex-col flex-1">
      <MRow :gutter="18">
        <MCol v-for="detail in deviceDetails" :key="detail.key" :size="12">
          <MRow :gutter="0">
            <MCol
              :size="4"
              class="text-neutral-light mt-4"
              style="color: var(--neutral-regular)"
              >{{ detail.label }}</MCol
            >

            <template v-if="detail.useDisplayValue">
              <MCol v-if="detail.key === 'target'" :size="8" class="mt-4">
                <a
                  :href="`${(responseData[detail.key] || {}).display_value}`"
                  target="_blank"
                  class="text-primary"
                >
                  {{ (responseData[detail.key] || {}).display_value }}
                </a>
              </MCol>

              <MCol
                v-else-if="detail.key === 'assigned_to'"
                :size="8"
                class="mt-4"
              >
                <MIcon
                  v-if="responseData[detail.key]"
                  name="user"
                  class="text-primary"
                ></MIcon>
                {{ (responseData[detail.key] || {}).display_value }}
              </MCol>

              <MCol v-else-if="detail.key === 'labels'" :size="8" class="mt-4">
                <SelectedItemPills
                  :value="responseData[detail.key].display_value"
                />
              </MCol>

              <MCol v-else-if="detail.key === 'impact'" :size="8" class="mt-4">
                <MTag
                  v-if="responseData[detail.key]"
                  :closable="false"
                  rounded
                  class="used-count-pill"
                >
                  {{ (responseData[detail.key] || {}).display_value }}
                </MTag>
              </MCol>
              <MCol v-else-if="detail.key === 'urgency'" :size="8" class="mt-4">
                <MTag
                  v-if="responseData[detail.key]"
                  :closable="false"
                  rounded
                  class="used-count-pill"
                >
                  {{ (responseData[detail.key] || {}).display_value }}
                </MTag>
              </MCol>
              <MCol
                v-else-if="detail.key === 'priority'"
                :size="8"
                class="mt-4"
              >
                <MTag
                  v-if="responseData[detail.key]"
                  :closable="false"
                  rounded
                  class="used-count-pill"
                >
                  {{ (responseData[detail.key] || {}).display_value }}
                </MTag>
              </MCol>
              <MCol v-else-if="detail.key === 'state'" :size="8" class="mt-4">
                <MTag
                  v-if="responseData[detail.key]"
                  :closable="false"
                  rounded
                  class="used-count-pill"
                >
                  {{ (responseData[detail.key] || {}).display_value }}
                </MTag>
              </MCol>
              <!-- <MCol
                v-else-if="detail.key === 'sys_tags'"
                :size="8"
                class="mt-4"
              >
                <LooseTags
                  v-if="responseData[detail.key]"
                  :value="(responseData[detail.key] || {}).display_value"
                  disabled
                  rounded
                />
              </MCol> -->
              <MCol v-else :size="8" class="mt-4">
                {{ (responseData[detail.key] || {}).display_value }}
              </MCol>
            </template>

            <template v-else>
              <MCol v-if="detail.key === 'target'" :size="8" class="mt-4">
                <a
                  :href="`${responseData[detail.key]}`"
                  target="_blank"
                  class="text-primary"
                >
                  {{ responseData[detail.key] }}
                </a>
              </MCol>
              <MCol v-else-if="detail.key === 'labels'" :size="8" class="mt-4">
                <SelectedItemPills
                  :value="responseData[detail.key]"
                  :max-items="3"
                />
              </MCol>

              <MCol v-else-if="detail.key === 'id'" :size="8" class="mt-4">
                {{ responseData[detail.key] }}
              </MCol>

              <MCol
                v-else-if="detail.key === 'error'"
                class="text-secondary-red mt-4"
                :size="8"
              >
                {{ responseData[detail.key] }}
              </MCol>

              <MCol
                v-else-if="detail.key === 'assigneeEmail'"
                :size="8"
                class="mt-4"
              >
                <MIcon
                  v-if="responseData[detail.key]"
                  name="user"
                  class="text-primary"
                ></MIcon>
                {{ responseData[detail.key] }}
              </MCol>

              <MCol
                v-else-if="detail.key === 'createdTime'"
                :size="8"
                class="mt-4"
              >
                <span v-if="responseData[detail.key]">
                  {{
                    Math.round(responseData[detail.key] / 1000) | datetime
                  }}</span
                >
              </MCol>

              <MCol
                v-else-if="detail.key === 'priorityName'"
                :size="8"
                class="mt-4"
              >
                <MTag
                  v-if="responseData[detail.key]"
                  :closable="false"
                  rounded
                  class="used-count-pill"
                >
                  {{ responseData[detail.key] }}
                </MTag>
              </MCol>

              <MCol
                v-else-if="detail.key === 'urgencyName'"
                :size="8"
                class="mt-4"
              >
                <MTag
                  v-if="responseData[detail.key]"
                  :closable="false"
                  rounded
                  class="used-count-pill"
                >
                  {{ responseData[detail.key] }}
                </MTag>
              </MCol>

              <MCol
                v-else-if="detail.key === 'statusName'"
                :size="8"
                class="mt-4"
              >
                <MTag
                  v-if="responseData[detail.key]"
                  :closable="false"
                  rounded
                  class="used-count-pill"
                >
                  {{ responseData[detail.key] }}
                </MTag>
              </MCol>

              <MCol v-else-if="detail.key === 'tags'" :size="8" class="mt-4">
                <LooseTags
                  v-if="responseData[detail.key]"
                  :value="responseData[detail.key]"
                  disabled
                  rounded
                />
              </MCol>

              <MCol
                v-else-if="detail.key === 'description'"
                :size="8"
                class="mt-4"
                v-html="responseData[detail.key]"
              >
              </MCol>

              <MCol v-else :size="8" class="mt-4">
                {{ responseData[detail.key] }}
              </MCol>
            </template>
          </MRow>
        </MCol>
      </MRow>
    </FlotoContentLoader>
  </FlotoDrawer>
</template>

<script>
import Bus from '@utils/emitter'
import { generateId } from '@utils/id'
import LooseTags from '@components/loose-tags.vue'
import SelectedItemPills from '@components/dropdown-trigger/selected-item-pills.vue'
import config from '../../config'

export default {
  name: 'IncidentDetailsDrawer',
  components: { LooseTags, SelectedItemPills },
  props: {
    incidentItem: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      open: true,
      uuid: generateId(),
      responseData: {},
      integrationConfigurationType: undefined,
      loading: true,
    }
  },
  computed: {
    deviceDetails() {
      if (this.integrationConfigurationType === 'ServiceNow') {
        return [
          {
            key: 'name',
            label: 'Ticket ID',
          },
          {
            key: 'target',
            label: 'URL',
          },
          {
            key: 'sys_created_on',
            label: 'Incident Trigger Time',
            useDisplayValue: true,
          },
          {
            key: 'integration.type',
            label: 'Integration Type',
          },
          {
            key: 'category',
            label: 'Category',
            useDisplayValue: true,
          },
          {
            key: 'subcategory',
            label: 'Sub Category',
            useDisplayValue: true,
          },
          {
            key: 'assignment_group',
            label: 'Group',
            useDisplayValue: true,
          },
          {
            key: 'assigned_to',
            label: 'Technician',
            useDisplayValue: true,
          },
          {
            key: 'impact',
            label: 'Impact',
            useDisplayValue: true,
          },
          {
            key: 'urgency',
            label: 'Urgency',
            useDisplayValue: true,
          },
          {
            key: 'priority',
            label: 'Priority',
            useDisplayValue: true,
          },
          {
            key: 'business_service',
            label: 'Service',
            useDisplayValue: true,
          },
          {
            key: 'service_offering',
            label: 'Service Offering',
            useDisplayValue: true,
          },
          {
            key: 'state',
            label: 'Status',
            useDisplayValue: true,
          },
          {
            key: 'short_description',
            label: 'Subject',
            useDisplayValue: true,
          },
          {
            key: 'description',
            label: 'Description',
            useDisplayValue: true,
          },
          {
            key: 'sys_tags',
            label: 'Tags',
            useDisplayValue: true,
          },
        ]
      } else if (this.integrationConfigurationType === 'Atlassian Jira') {
        return [
          {
            key: 'id',
            label: 'Issue ID',
          },
          {
            key: 'target',
            label: 'URL',
          },
          {
            key: 'created',
            label: 'Incident Trigger Time',
          },
          // {
          //   key: 'profileName',
          //   label: 'Profile Name',
          // },
          {
            key: 'integration.type',
            label: 'Integration Type',
          },
          {
            key: 'project',
            label: 'Project',
          },
          {
            key: 'issueType',
            label: 'Issue Type',
          },
          {
            key: 'status',
            label: 'Status',
          },
          {
            key: 'components',
            label: 'Component(s)',
          },
          {
            key: 'reporter',
            label: 'Reporter',
          },
          {
            key: 'assignee',
            label: 'Assignee',
          },
          {
            key: 'priority',
            label: 'Priority',
          },
          {
            key: 'labels',
            label: 'Labels',
          },
          // {
          //   key: 'requestParticipants',
          //   label: 'Request Participants',
          // },
          // {
          //   key: 'approvers',
          //   label: 'Approvers',
          // },
          // {
          //   key: 'organizations',
          //   label: 'Organizations',
          // },
          // {
          //   key: 'impact',
          //   label: 'Impact',
          // },
          // {
          //   key: 'urgency',
          //   label: 'Urgency',
          // },
          // {
          //   key: 'source',
          //   label: 'Source',
          // },
          // {
          //   key: 'productCategorization',
          //   label: 'Product Categorization',
          // },
          // {
          //   key: 'productSubCategorization',
          //   label: 'Product Sub-Categorization',
          // },
          // {
          //   key: 'operationalCategorization',
          //   label: 'Operational Categorization',
          // },
          // {
          //   key: 'operationalSubCategorization',
          //   label: 'Operational Sub-Categorization',
          // },
          // {
          //   key: 'subject',
          //   label: 'Subject',
          // },
        ]
      }
      return [
        {
          key: 'id',
          label: 'Ticket ID',
        },
        {
          key: 'target',
          label: 'URL',
        },
        {
          key: 'createdTime',
          label: 'Incident Trigger Time',
        },
        {
          key: 'integration.type',
          label: 'Integration Type',
        },
        {
          key: 'urgencyName',
          label: 'Urgency',
        },
        {
          key: 'impactName',
          label: 'Impact',
        },
        {
          key: 'priorityName',
          label: 'Priority',
        },
        {
          key: 'statusName',
          label: 'Status',
        },
        {
          key: 'assigneeEmail',
          label: 'Technician',
        },
        // {
        //   key: 'source',
        //   label: 'Source',
        // },

        {
          key: 'subject',
          label: 'Subject',
        },
        {
          key: 'description',
          label: 'Description',
        },
        {
          key: 'tags',
          label: 'Tags',
        },
        // {
        //   key: 'error',
        //   label: 'Message',
        // },
      ]
    },
  },
  created() {
    this.loading = true
    Bus.$on(
      this.$currentModule.getConfig().ALERT_INTEGRATION_TICKET ||
        config.ALERT_INTEGRATION_TICKET,
      this.handleDataReceived
    )
    this.$once('hook:beforeDestroy', () => {
      Bus.$off(
        this.$currentModule.getConfig().ALERT_INTEGRATION_TICKET ||
          config.ALERT_INTEGRATION_TICKET,
        this.handleDataReceived
      )
    })
    this.askForIncidentDetails()
  },

  methods: {
    handleHide() {
      this.open = false
      setTimeout(() => this.$emit('hide'), 400)
    },
    askForIncidentDetails() {
      Bus.$emit('server:event', {
        'event.type':
          this.$currentModule.getConfig().ALERT_INTEGRATION_TICKET ||
          config.ALERT_INTEGRATION_TICKET,
        'event.context': {
          id: -1,
          [this.$currentModule.getConfig().ALERT_INTEGRATION_TICKET ||
          config.ALERT_INTEGRATION_TICKET]: this.uuid,
          'ack.id': this.incidentItem.incidentDetails,
        },
      })
    },
    handleDataReceived(data) {
      this.loading = false
      this.integrationConfigurationType = data['integration.type']
      this.responseData = {
        ...data,
        ...(this.integrationConfigurationType === 'ServiceNow'
          ? { name: data['number']?.display_value }
          : {}),
        ...(this.integrationConfigurationType === 'Atlassian Jira'
          ? {
              name: data['key'],
              project: data?.fields?.['project']?.name,
              assignee: data?.fields?.['assignee']?.name,
              reporter: data?.fields?.['reporter']?.name,
              priority: data?.fields?.['priority']?.name,
              components: data?.fields?.['components']?.[0]?.name,
              issueType: data?.fields?.['issuetype']?.name,
              labels: data?.fields?.['labels'],
              created: new Date(data?.fields?.['created'])
                .toString()
                .replace(/ GMT.*$/, ''),
              status: data?.fields?.['status']?.name,
            }
          : {}),
      }
    },
    // hide() {
    //   this.$emit('hide')
    // },
  },
}
</script>
