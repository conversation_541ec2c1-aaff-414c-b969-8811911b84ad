<template>
  <div v-if="visible" class="px-4 mt-2" style="margin-bottom: -7px">
    <MRow class="slide-filters items-center pr-2">
      <MCol :size="3">
        <FlotoFormItem label="Severity">
          <FlotoDropdownPicker
            id="severity-picker"
            v-model="currentValue.severity"
            class="mt-1"
            :options="severityOptions"
            placeholder="Severity"
            multiple
          >
            <template v-slot:before-menu-text="{ item }">
              <Severity
                :severity="getSeverityForCompliance(item.key)"
                class="mx-1"
                disable-tooltip
              />
            </template>
          </FlotoDropdownPicker>
        </FlotoFormItem>
      </MCol>
      <MCol :size="3">
        <FlotoFormItem label="Last Scan Status">
          <FlotoDropdownPicker
            id="scan-status-picker"
            v-model="currentValue.lastScanStatus"
            class="mt-1"
            :options="scanStatusOptions"
            placeholder="Last Scan Status"
          />
        </FlotoFormItem>
      </MCol>
      <MCol class="flex justify-between ml-auto" :size="2">
        <div class="flex-1 items-center mt-2 flex justify-center">
          <MButton
            id="reset-btn"
            variant="default"
            @click="
              $emit('change', {
                severity: [],
                lastScanStatus: undefined,
              })
            "
          >
            Reset
          </MButton>
          <MButton id="apply-btn" class="ml-2" @click="apply">Apply</MButton>
        </div>
      </MCol>
      <MButton
        id="close-filter"
        variant="transparent"
        :shadow="false"
        shape="circle"
        style="position: absolute; top: 0; right: 0"
        class="monitor-agent-filter-close"
        @click="$emit('hide')"
      >
        <MIcon name="times" class="text-neutral-light" />
      </MButton>
    </MRow>
  </div>
</template>

<script>
import Severity from '@components/severity'
import {
  COMPLIANCE_SEVERITY,
  COMPLIANCE_SEVERITY_TO_SVERITY_MAP,
} from '../../helpers/compliance'

export default {
  name: 'ComplianceDrilldownFilter',
  components: {
    Severity,
  },
  model: {
    event: 'change',
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    // Define severity options based on compliance severity
    this.severityOptions = [
      { key: COMPLIANCE_SEVERITY.SECURE, text: 'Secure' },
      { key: COMPLIANCE_SEVERITY.MODERATE, text: 'Moderate' },
      { key: COMPLIANCE_SEVERITY.POOR, text: 'Poor' },
      { key: COMPLIANCE_SEVERITY.VULNERABLE, text: 'Vulnerable' },
    ]

    // Define scan status options
    this.scanStatusOptions = [
      { key: this.$constants.EVENT_SUCCEDED_STATUS, text: 'Success' },
      { key: this.$constants.EVENT_FAILED_STATUS, text: 'Failed' },
    ]

    return {
      currentValue: { ...this.value },
      severityOptions: this.severityOptions,
      scanStatusOptions: this.scanStatusOptions,
    }
  },
  watch: {
    value(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.currentValue = { ...newValue }
      }
    },
  },
  methods: {
    getSeverityForCompliance(complianceSeverity) {
      return COMPLIANCE_SEVERITY_TO_SVERITY_MAP[complianceSeverity]
    },
    apply() {
      this.$emit('change', this.currentValue)
    },
  },
}
</script>
