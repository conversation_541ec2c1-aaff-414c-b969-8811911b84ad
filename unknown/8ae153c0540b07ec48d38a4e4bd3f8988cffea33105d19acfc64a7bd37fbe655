<template>
  <div class="flex flex-col flex-1">
    <FlotoContentLoader :loading="loadingWidgets">
      <Widgets
        :allow-create="false"
        disable-overflow
        :dashboard-id="dashboard.id"
        :widgets="dashboard.widgets"
        :time-range="timeRange"
        :params="widgetParams"
        :disabled="true"
        :max-widgets="25"
        :hide-widget-actions="true"
        ignore-widget-update
        enable-active-session-debounce
        :dashboard-style="dashboardStyle"
        @drilldown="$emit('drilldown', $event)"
      />
    </FlotoContentLoader>
  </div>
</template>

<script>
import { getDashboardApi } from '@modules/dashboard/dashboard-api'
import { FILTER_CONDITION_DEFAULT_DATA } from '@components/widgets/constants'
import { transformConditionsForServer } from '@components/widgets/helper'
import Widgets from '@components/widgets/widgets.vue'

const FLOW_DASHBOARD_ID = 10000000000033

export default {
  name: 'DashboardTab',
  page() {
    return {
      title: 'Flow Dashboard',
    }
  },
  components: {
    Widgets,
  },
  inject: {
    FlowRouteContext: {
      default: {
        dashboardWidgetParams: { sources: [] },
        dashboardInterfaceParams: { interfaces: [] },
      },
    },
  },
  data() {
    return {
      dashboardStyle: {
        fontSize: 'medium',
        horizontalGap: 8,
        verticalGap: 8,
      },
      loadingWidgets: true,
      dashboard: {},
    }
  },
  computed: {
    timeRange() {
      return this.FlowRouteContext.dashboardWidgetParams.timeline
    },
    widgetParams() {
      if (
        this.FlowRouteContext.dashboardWidgetParams.sources.length > 0 ||
        this.FlowRouteContext?.dashboardWidgetParams?.interfaces?.length > 0
      ) {
        const defaultFilter = { ...FILTER_CONDITION_DEFAULT_DATA }
        const interfaces = (
          this.FlowRouteContext.dashboardWidgetParams.interfaces || []
        ).map((i) => i)
        return {
          entities: this.FlowRouteContext.dashboardWidgetParams.sources,
          ...((interfaces || []).length > 0
            ? {
                filters: {
                  'data.filter': transformConditionsForServer({
                    ...defaultFilter,
                    groups: [
                      {
                        ...defaultFilter.groups[0],
                        condition: 'or',
                        conditions: [
                          {
                            operand: 'source.if.index',
                            operator: 'in',
                            value: interfaces,
                          },
                          {
                            operand: 'destination.if.index',
                            operator: 'in',
                            value: interfaces,
                          },
                        ],
                      },
                    ],
                  }),
                },
              }
            : {}),
          'entity.type': 'event.source',
        }
      }
      return {}
    },
  },
  created() {
    this.loadDashboard()
  },
  methods: {
    loadDashboard() {
      getDashboardApi(FLOW_DASHBOARD_ID).then((dashboard) => {
        this.dashboard = Object.freeze(dashboard)
        this.loadingWidgets = false
      })
    },
  },
}
</script>
