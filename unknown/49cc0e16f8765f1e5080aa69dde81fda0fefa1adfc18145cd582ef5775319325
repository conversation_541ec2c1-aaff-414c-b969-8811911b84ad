<template>
  <MRow class="h-100">
    <MCol class="login-container">
      <MRow :gutter="0" class="items-center justify-center flex-1">
        <MCol class="login-box relative shadow-lg">
          <MRow :gutter="0" class="login-row">
            <MCol :size="5" class="flex flex-col py-4">
              <div class="px-8 pb-8">
                <img id="login-logo" :src="logo" height="50" />
              </div>
              <div
                class="flex-1 justify-center flex-col flex px-8 login-form-container"
              >
                <div
                  v-if="isSsoInProgress"
                  class="flex flex-1 min-h-0 flex-col pt-8 items-center justify-center"
                >
                  <div class="flex flex-col flex-1 items-center justify-center">
                    <MLoader />
                    <h5>Authenticating...</h5>
                  </div>
                </div>
                <template v-else>
                  <div
                    v-if="message"
                    class="mb-4 bg-secondary-red text-white px-2 py-1 rounded"
                    >{{ message }}</div
                  >
                  <div
                    v-if="authError"
                    class="mb-4 bg-secondary-red text-white px-2 py-1 rounded"
                    >{{ authError }} <br />
                    <span v-if="loginAttemptsError">
                      {{ loginAttemptsError }}</span
                    >
                  </div>
                  <FlotoForm v-if="!mfaInProgress" @submit="attemptLogin">
                    <FlotoFormItem
                      v-model="username"
                      auto-focus
                      name="username"
                      validation-label="Username"
                      placeholder="Username"
                      class="mb-0"
                      label="Username"
                      rules="required"
                      autocomplete="off"
                    />
                    <PasswordInput
                      v-model="password"
                      placeholder="Password"
                      validation-label="Password"
                      name="password"
                      rules="required"
                      label="Password"
                      class="mb-0"
                    />
                    <!-- <FlotoFormItem mode="passive">
                    <MCheckbox v-model="remember">
                      Remember Me
                    </MCheckbox>
                  </FlotoFormItem>-->
                    <div class="flex justify-end mb-3">
                      <span
                        class="cursor-pointer text-neutral"
                        @click="showForgotPasswordModal = true"
                        >Forgot your Password?</span
                      >
                    </div>

                    <template v-slot:submit>
                      <MButton type="submit" block :loading="inProgress"
                        >Login</MButton
                      >
                    </template>
                  </FlotoForm>

                  <TwoFactorVerification
                    v-else
                    :mfa-context="mfaContext"
                    :mfa-method="mfaMethod"
                    :in-progress="inProgress"
                    :has-sso-configured="hasSSOConfigured"
                    @submit="attemptLogin"
                    @restart-two-factor-verification="
                      onRestartTwoFactorVerification
                    "
                  />

                  <template v-if="hasSSOConfigured && !mfaInProgress">
                    <span class="flex justify-center my-2">OR</span>

                    <MRow class="w-full" :gutter="0">
                      <MCol span="12">
                        <form action="/api/v1/sso" method="get">
                          <MButton block outline type="submit"
                            >Login with Single Sign-On</MButton
                          >
                        </form>
                      </MCol>
                    </MRow>
                  </template>
                </template>
              </div>
            </MCol>
            <MCol />
            <MCol :size="6" class="illustration-container">
              <ComputerSvg class="w-full relative" style="bottom: -5px" />
            </MCol>
          </MRow>
          <div class="orange-ball">
            <OrangeBall />
          </div>
          <div class="green-ball">
            <GreenBall />
          </div>
          <div class="shape">
            <Shape />
          </div>
        </MCol>
      </MRow>
    </MCol>
    <ForgotPasswordModal
      v-if="showForgotPasswordModal"
      @hide="showForgotPasswordModal = false"
    />
    <PasswordExpireNotification
      ref="passwordExpireRef"
      for-login-page
      @reset-password="showForgotPasswordModal = true"
    />
  </MRow>
</template>

<script>
import PasswordInput from '@components/password-input.vue'
import { BrandingComputed, BrandingMethods } from '@state/modules/branding'
import { authMethods } from '@state/modules/auth/'
import ComputerSvg from '../components/computer.svg'
import OrangeBall from '../components/orange-ball.vue'
import GreenBall from '../components/green-ball.vue'
import Shape from '../components/shape.vue'
import ForgotPasswordModal from '../components/forgot-password-modal.vue'
import { getLoginMetadataApi, verifySSOTokenApi } from '../auth-api'
import PasswordExpireNotification from '@components/common/password-expire-notification.vue'
import TwoFactorVerification from '../components/two-factor-verification.vue'

export default {
  name: 'Login',
  components: {
    PasswordInput,
    ComputerSvg,
    OrangeBall,
    GreenBall,
    Shape,
    ForgotPasswordModal,
    PasswordExpireNotification,
    TwoFactorVerification,
  },
  page() {
    return {
      title: 'Login',
    }
  },
  data() {
    return {
      username: '',
      password: '',
      remember: false,
      authError: null,
      loginAttemptsError: null,
      inProgress: false,
      showForgotPasswordModal: false,
      isSsoInProgress: false,
      hasSSOConfigured: false,
      message: undefined,
      mfaInProgress: false,
      mfaConfigured: false,
      mfaMethod: undefined,
      mfaContext: undefined,
    }
  },
  computed: {
    ...BrandingComputed,
  },
  watch: {
    $route: {
      immediate: true,
      handler(newValue) {
        if (newValue?.query?.message) {
          if (
            newValue?.query?.message ===
              this.$message('invalid_SSO_configuration') ||
            newValue?.query?.message === this.$message('login_failed')
          ) {
            this.message = newValue.query.message
          }
        }
      },
    },
  },
  created() {
    this.refreshBranding()
    if (this.$route.query.id) {
      this.checkIfSSOIsEnabled()
      this.checkSSOToken()
    } else {
      this.checkIfSSOIsEnabled()
    }
  },
  methods: {
    ...authMethods,
    ...BrandingMethods,
    checkSSOToken() {
      if (this.$route.query.id) {
        let id = this.$route.query.id
        if (id && String(id).length) {
          this.isSsoInProgress = true
          verifySSOTokenApi(id)
            .then(({ data }) =>
              this.loginByAuthToken({
                ...data,
                mfaConfigured: this.mfaConfigured,
              })
            )
            .then(this.handleLoginResponse)
            .catch((error) => {
              this.authError =
                ((error.response || {}).data || {}).message ||
                'Failed to login using SSO'
              this.inProgress = false
              this.isSsoInProgress = false
            })
        }
      }
    },
    checkIfSSOIsEnabled() {
      getLoginMetadataApi().then(({ data }) => {
        if (data['saml.configured']) {
          this.hasSSOConfigured = true
        }
        if (data['2fa.configured']) {
          this.mfaConfigured = data['2fa.configured'] === 'yes'
        }
        if (data['2fa.method']) {
          this.mfaMethod = data['2fa.method']
        }
      })
    },
    attemptSingleSignon() {
      this.authError = false
      this.ssoInProgress = true
      return this.singleSignOn()
        .then(({ url }) => {
          this.ssoInProgress = false
          window.location.href = url
        })
        .catch((error) => {
          this.authError =
            ((error.response || {}).data || {}).message ||
            'Invalid credentials.'
          this.ssoInProgress = false
        })
    },
    // Try to log the user in with the username
    // and password they provided.
    attemptLogin(tfaContext = {}) {
      this.authError = null
      this.loginAttemptsError = null
      this.inProgress = true
      // Reset the authError if it existed.
      // this.authError = null
      return this.login({
        username: this.username,
        password: this.password,
        mfaConfigured: this.mfaConfigured,
        tfaContext,
      })
        .then(this.handleLoginResponse)
        .catch((error) => {
          const responseData = (error.response || {}).data || {}

          this.authError =
            ((error.response || {}).data || {}).message ||
            'Invalid credentials.'
          // Check if we have login attempts information
          if (responseData['login.attempts.left'] !== undefined) {
            const attemptsLeft = responseData['login.attempts.left']
            this.loginAttemptsError = `You have ${attemptsLeft} ${
              attemptsLeft === 1 ? 'attempt' : 'attempts'
            } left.`
          }

          this.inProgress = false

          if (
            ((error.response || {}).data || {})?.['error.code'] === 'MD121' &&
            this.$refs.passwordExpireRef
          ) {
            this.$refs.passwordExpireRef.showNotification({
              'remaining.days': -1,
            })
          } else {
            if (this.$refs.passwordExpireRef) {
              this.$refs.passwordExpireRef.closeNotification({
                'remaining.days': -1,
              })
            }
          }
        })
    },
    handleLoginResponse({ user, token, role, preference }) {
      this.inProgress = false
      this.isSsoInProgress = false

      if (token?.mfaInProgress) {
        this.mfaInProgress = true
        this.mfaContext = token

        return
      }
      if (user) {
        if (token.temperory_password) {
          this.$router.replace(this.$currentModule.getRoute('reset-password'))
          return
        }

        const isReportRender =
          this.$route?.query?.redirectFrom?.includes('/reports/export/')

        if (
          role.name === 'admin' &&
          preference.hasSetupCompleted === false &&
          !isReportRender
        ) {
          this.$router.replace(this.$modules.getModuleRoute('product-setup'))
          return
        }
        // Redirect to the originally requested page, or to the home page
        if (this.$route.query.redirectFrom) {
          this.$router.replace(this.$route.query.redirectFrom)
        } else {
          this.$modules.replace('dashboard')
        }
      } else {
        this.authError = true
      }
    },
    reset() {
      this.username = ''
      this.password = ''
    },
    onRestartTwoFactorVerification() {
      this.mfaInProgress = false
      this.mfaContext = undefined

      this.reset()
    },
  },
}
</script>

<style lang="less" scoped>
.login-container {
  @apply flex flex-col flex-1;

  background: #7b8fa5; /* Old browsers */
  background: linear-gradient(-45deg, #7b8fa5 1%, #364658 100%); /* FF3.6-15 */
  background: linear-gradient(
    -45deg,
    #7b8fa5 1%,
    #364658 100%
  ); /* Chrome10-25,Safari5.1-6 */

  background: linear-gradient(
    135deg,
    #7b8fa5 1%,
    #364658 100%
  ); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */

  filter: progid:dximagetransform.microsoft.gradient( startColorstr='#7b8fa5', endColorstr='#364658',GradientType=1 ); /* IE6-9 fallback on horizontal gradient */

  .login-box {
    min-width: 720px;
    max-width: 1020px;
    background: white;
    border-radius: 15px;

    .login-form-container {
      margin-top: -50px;
    }

    .illustration-container {
      position: relative;
      padding-top: 3rem;
    }

    .orange-ball {
      position: absolute;
      right: 60px;
      bottom: -50px;
      z-index: 3;
      width: 100px;
      height: 100px;
    }

    .green-ball {
      position: absolute;
      right: -70px;
      bottom: -50px;
      z-index: 1;
    }

    .login-row {
      position: relative;
      z-index: 2;
      background: white;
      border-radius: 15px;
    }

    .shape {
      position: absolute;
      top: -80px;
      left: -80px;
      z-index: 1;
      width: 304px;
      height: 350px;
    }
  }
}
</style>
