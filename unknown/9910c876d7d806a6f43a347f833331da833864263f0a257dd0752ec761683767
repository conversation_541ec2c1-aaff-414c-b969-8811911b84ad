<template>
  <ScrollableExport v-if="shouldShowModule" use-queue>
    <template v-slot="{ convertToImage }">
      <FlotoContentLoader :loading="loading">
        <FlotoFixedView>
          <MRow class="dashboard-selector-bar px-2" :gutter="0">
            <MCol :size="12" class="flex justify-between items-center">
              <MenuToggleButton
                :visible="menuVisible"
                @click="() => (menuVisible = !menuVisible)"
              />
              <div class="mx-2 menu-divider" />

              <h3 class="flex-1 text-primary mx-2 mb-0 flex items-center">
                <MIcon name="dashboard" class="mr-2" />
                {{ dashboard.name }}
                <MIcon
                  name="star"
                  :class="
                    isFavourite ? 'text-secondary-yellow' : 'text-neutral-light'
                  "
                  class="ml-2 cursor-pointer"
                  @click.stop="handleToggleFavorite(dashboardId)"
                />
              </h3>
              <div class="flex items-center">
                <DashboardJsonEditor
                  :dashboard="dashboard"
                  @update="handleUpdateDashboardDefinition"
                >
                  <template v-slot="{ open: triggerEditJson }">
                    <MButton
                      class="mx-2"
                      variant="error"
                      @click="triggerEditJson"
                      >Edit Dashboard Definition</MButton
                    >
                  </template>
                </DashboardJsonEditor>
                <div class="px-2">
                  <TimeRangePicker
                    v-model="timeRange"
                    :hide-custom-time-range="false"
                    allow-clear
                    @change="sliderRenderKey++"
                  />
                </div>
                <div>
                  <MButton
                    class="squared-button mr-2"
                    variant="neutral-lightest"
                    title="Full Screen"
                    @click="enterFullScreen"
                  >
                    <MIcon name="fullscreen" />
                  </MButton>
                  <MButton
                    variant="neutral-lightest"
                    class="squared-button"
                    title="Export"
                    @click="
                      convertToImage(
                        $refs.widgetsRef.getScrollContainer(),
                        dashboard.name
                      )
                    "
                  >
                    <MIcon name="image" />
                  </MButton>
                  <FlotoGridActions
                    style="padding-right: 0; padding-left: 0.5rem"
                    :resource="dashboard"
                    :actions="dashboardActions"
                    :edit-permission-name="
                      $constants.DASHBOARD_SETTINGS_UPDATE_PERMISSION
                    "
                    :delete-permission-name="
                      $constants.DASHBOARD_SETTINGS_DELETE_PERMISSION
                    "
                    :create-permission-name="
                      $constants.DASHBOARD_SETTINGS_CREATE_PERMISSION
                    "
                    :create-permission-keys="['clone']"
                    @mark-as-default="handleMarkAsDefault"
                    @clone="handleClone(data)"
                    @edit="showForm(dashboard)"
                    @delete="showConfirmDeleteModalFor = dashboard"
                  />
                </div>
              </div>
            </MCol>
          </MRow>
          <MRow
            class="w-full h-full flex flex-col relative min-h-0"
            :style="{ marginLeft: '0px', marginRight: '0px' }"
          >
            <Transition name="sidebar">
              <DashboardSelector
                v-if="menuVisible"
                v-model="dashboardId"
                :menu-visible="menuVisible"
                :options="dashboardList"
                @visible-change="menuVisible = $event"
                @toggle-favourite="handleToggleFavorite"
                @create="showForm"
              />
            </Transition>
            <div v-if="timeRange" class="mt-2 px-2 mr-2 border-bot">
              <TimeRangeSlider :key="sliderRenderKey" v-model="timeRange" />
            </div>
            <div ref="widgetsContainerRef" class="flex flex-col min-h-0 flex-1">
              <FlotoContentLoader :loading="loadingWidgets">
                <Widgets
                  ref="widgetsRef"
                  allow-create
                  :dashboard-id="dashboard.id"
                  :widgets="dashboard.widgets"
                  :blinking-items="blinkingDivs"
                  :time-range="timeRange"
                  :params="widgetParams"
                  :fullscreen="isInFullScreen"
                  :disabled="isInFullScreen || !dashboard.canEdit"
                  :is-custom-template="dashboard.canEdit"
                  :max-widgets="25"
                  :hide-widget-actions="isInFullScreen"
                  :dashboard-style="dashboard.style"
                  @change="handleWidgetsUpdated"
                  @add-widget="handleAddNewWidget"
                />
              </FlotoContentLoader>
            </div>
          </MRow>

          <DashboardForm
            v-if="editingItem !== null"
            :default-value="editingItem"
            @close="editingItem = null"
            @update="updateDashboard"
          />
          <FlotoConfirmModal
            v-if="showConfirmDeleteModalFor !== null"
            open
            no-icon-shadow
            @confirm="handleDeleteDashboard"
            @hide="showConfirmDeleteModalFor = null"
          >
            <template v-slot:icon>
              <slot name="confirm-delete-icon">
                <MIcon name="trash-alt" size="2x" class="text-secondary-red" />
              </slot>
            </template>
            <template v-slot:message>
              {{
                $message('confirm', {
                  message: $message('delete_resource', {
                    resource: 'dashboard',
                  }),
                })
              }}?
            </template>
          </FlotoConfirmModal>
        </FlotoFixedView>
      </FlotoContentLoader>
    </template>
  </ScrollableExport>

  <FlotoModuleNoData v-else module="dashboard" />
</template>

<script>
import CloneDeep from 'lodash/cloneDeep'
import FindIndex from 'lodash/findIndex'
import Bus from '@utils/emitter'
import { makeFullScreen } from '@utils/fullscreen'
import Widgets from '@components/widgets/widgets.vue'
import TimeRangePicker from '@components/widgets/time-range-picker.vue'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import { authMethods, authComputed } from '@state/modules/auth'
import ScrollableExport from '@components/scrollable-export.vue'
import TimeRangeSlider from '@components/widgets/time-range-picker/time-range-slider.vue'
import DashboardSelector from '../components/dashboard-dropdown.vue'
import DashboardForm from '../components/dashboard-form.vue'
import DashboardJsonEditor from '../components/temp-dashboard-json-editor.vue'
import {
  getAvailableDashboardsApi,
  getDashboardApi,
  updateDashboardApi,
  deleteDashboardApi,
} from '../dashboard-api'
import { UserPreferenceMethods } from '@/src/state/modules/user-preference'
import { objectDBWorker } from '@/src/workers'
import { getSourceApi } from '@components/widgets/widgets-api'
import MenuToggleButton from '@components/menu-toggle-button.vue'
import { getAllLogInventoryApi } from '@modules/settings/log-settings/log-settings-api'

const ACTIVE_INTERVAL_MS = 30000

export default {
  name: 'Dashboard',
  page() {
    return {
      title: this.dashboard.name,
    }
  },
  components: {
    MenuToggleButton,
    DashboardSelector,
    Widgets,
    TimeRangePicker,
    DashboardForm,
    DashboardJsonEditor,
    ScrollableExport,
    TimeRangeSlider,
  },
  data() {
    this.recentViewedDashboardsLimit = 10
    this.widgetParams = {}
    this.dashboardPreferenceKey = 'dashboard'
    return {
      sliderRenderKey: 1,
      loading: true,
      loadingWidgets: true,
      isInFullScreen: false,
      dashboardId: undefined,
      dashboard: {},
      dashboardList: [],
      blinkingDivs: [],
      timeRange: {
        selectedKey: 'today',
      },
      editingItem: null,
      showConfirmDeleteModalFor: null,
      hasObjects: undefined,
      flowSourceOptions: [],
      logInventories: [],
      shouldShowModule: true,
      menuVisible: false,
    }
  },
  computed: {
    ...authComputed,
    ...UserPreferenceComputed,
    dashboardActions() {
      if (this.dashboard.canEdit) {
        return [
          { key: 'clone', name: 'Clone', icon: 'clone' },
          { key: 'edit', name: 'Edit', icon: 'pencil' },
          ...(this.preference.defaultHomeScreen === this.dashboard.id
            ? []
            : [
                {
                  key: 'mark-as-default',
                  name: 'Mark As Default',
                  icon: 'locks',
                },
              ]),
          { key: 'delete', name: 'Delete', icon: 'trash-alt', isDanger: true },
        ]
      }
      return [
        { key: 'clone', name: 'Clone', icon: 'clone' },
        ...(this.preference.defaultHomeScreen === this.dashboard.id
          ? []
          : [
              {
                key: 'mark-as-default',
                name: 'Mark As Default',
                icon: 'locks',
              },
            ]),
      ]
    },
    isFavourite() {
      return this.preference.favouriteDashboards.includes(this.dashboardId)
    },

    shouldShowDashboard() {
      return (
        !!this.hasObjects ||
        !!this.flowSourceOptions.length ||
        !!this.logInventories.length
      )
    },
  },
  watch: {
    // dashboardId(newValue, oldValue) {
    //   if (newValue !== oldValue && newValue) {
    //     this.getDashboard(newValue)
    //     this.$router.push(
    //       this.$currentModule.getRoute('', { params: { id: newValue } })
    //     )
    //   }
    // },

    dashboardId(newValue, oldValue) {
      if (newValue !== oldValue && newValue) {
        setTimeout(async () => {
          await this.refreshUser()
          this.updatePartialPreference({
            recentViewedDashboards: [
              newValue,
              ...this.preference.recentViewedDashboards.filter(
                (id) => id !== newValue
              ),
            ].slice(0, this.recentViewedDashboardsLimit),
          })
        }, 1000)
      }
    },

    timeRange(newValue, oldValue) {
      const userPrefrenceTimeline =
        this.timelinePreference?.[this.dashboardPreferenceKey]?.timeline
      if (
        (newValue &&
          newValue?.selectedKey !== oldValue?.selectedKey &&
          newValue?.selectedKey !== userPrefrenceTimeline?.selectedKey) ||
        newValue?.selectedKey === 'custom'
      ) {
        if (newValue?.selectedKey === 'custom') {
          if (
            newValue?.endDate === oldValue?.endDate &&
            newValue?.startDate === oldValue?.startDate
          ) {
            return
          }
          if (
            newValue?.endDate === userPrefrenceTimeline?.endDate &&
            newValue?.startDate === userPrefrenceTimeline?.startDate
          ) {
            return
          }
        }
        this.updateTimelinePreferenceForUser(newValue)
      }
    },
  },
  async created() {
    await this.getLogFlowObjectAndUpdatePreference()

    const dashboardId = this.$route.params.id
    this.dashboardId = +this.$route.params.id
    let p = Promise.resolve()
    if (dashboardId) {
      p = this.getDashboard(dashboardId)
    }
    p.then(() => {
      this.getDashboardList()
    })

    const fullscreenHandler = (e) => {
      if (!document.fullscreen) {
        this.isInFullScreen = false
      }
    }
    document.addEventListener('fullscreenchange', fullscreenHandler)
    this.$once('hook:beforeDestroy', () => {
      document.removeEventListener('fullscreenchange', fullscreenHandler)
    })

    this.timeRange = await this.getTimelinePreferenceByModule({
      module: 'dashboard',
    })
  },
  beforeDestroy() {
    this.inactivateSession()
  },
  methods: {
    ...authMethods,
    ...UserPreferenceMethods,
    activateSession() {
      if (this.dashboard.id) {
        this.dashboardActiveSessionInterval = setInterval(() => {
          Bus.$emit('server:event', {
            'event.type':
              this.$currentModule.getConfig().UI_EVENT_DASHBOARD_ACTIVE_EVENT,
            'event.context': {
              id: this.dashboard.id,
            },
          })
        }, ACTIVE_INTERVAL_MS)
      }
    },
    inactivateSession() {
      if (this.dashboard.id) {
        Bus.$emit('server:event', {
          'event.type':
            this.$currentModule.getConfig().UI_EVENT_DASHBOARD_INACTIVE_EVENT,
          'event.context': {
            id: this.dashboard.id,
          },
        })
      }
      if (this.dashboardActiveSessionInterval) {
        clearInterval(this.dashboardActiveSessionInterval)
      }
    },
    enterFullScreen() {
      makeFullScreen(this.$refs.widgetsContainerRef)
      this.isInFullScreen = true
    },
    handleClone() {
      const data = CloneDeep(this.dashboard)
      data.name = `Copy of ${data.name}`
      data.clonning = true
      data.id = undefined
      this.showForm(data)
    },
    showForm(item = {}) {
      this.editingItem = CloneDeep(
        item.id || item.clonning
          ? {
              ...item,
              isDefault: item.id === this.preference.defaultHomeScreen,
            }
          : {
              security: 'public',
              users: [],
              style: {
                fontSize: 'medium',
                horizontalGap: 8,
                verticalGap: 8,
                rowHeight: 50,
              },
            }
      )
    },
    getDashboard(id) {
      this.loadingWidgets = true
      return getDashboardApi(id)
        .then((data) => {
          this.inactivateSession()
          if (data.timeRange) {
            this.timeRange = data.timeRange
          }
          this.dashboard = Object.freeze(data)
          this.loadingWidgets = false

          this.activateSession()
        })
        .catch(() => {
          this.$router.replace({ name: 404 })
        })
    },
    getDashboardList() {
      return getAvailableDashboardsApi().then((data) => {
        this.dashboardList = data
        this.loading = false
        if (data.length && !this.dashboardId) {
          const defaultDashboardId = this.preference.defaultHomeScreen
          this.dashboardId =
            (data.find((d) => d.id === defaultDashboardId) || {}).id ||
            data[0]?.id

          this.getDashboard(this.dashboardId)
        }

        this.$watch('dashboardId', (newValue, oldValue) => {
          if (newValue !== oldValue && newValue) {
            this.getDashboard(newValue)
            this.$router.push(
              this.$currentModule.getRoute('', { params: { id: newValue } })
            )
          }
        })
      })
    },
    handleToggleFavorite(dashboardId) {
      const index = FindIndex(this.dashboardList, { id: dashboardId })
      if (index !== -1) {
        this.updatePartialPreference({
          favouriteDashboards: this.preference.favouriteDashboards.includes(
            dashboardId
          )
            ? this.preference.favouriteDashboards.filter(
                (id) => id !== dashboardId
              )
            : [...this.preference.favouriteDashboards, dashboardId],
        }).then(() => {
          this.refreshUser()
        })
      }
    },
    updateDashboard(dashboard, setCurrentDashboard = true) {
      const index = FindIndex(this.dashboardList, { id: dashboard.id })
      const list = this.dashboardList
      if (index !== -1) {
        this.dashboardList = [
          ...list.slice(0, index),
          { ...list[index], ...dashboard },
          ...list.slice(index + 1),
        ]
      } else {
        if (
          (dashboard.security === 'private' &&
            dashboard.users.includes(this.user.id)) ||
          dashboard.security === 'public'
        ) {
          this.dashboardList = [...list, dashboard]
        }
      }

      if (
        ((dashboard.security === 'private' &&
          dashboard.users.includes(this.user.id)) ||
          dashboard.security === 'public') &&
        setCurrentDashboard
      ) {
        this.dashboardId = dashboard.id
        this.dashboard = Object.freeze(dashboard)
        if (dashboard.timeRange) {
          this.timeRange = dashboard.timeRange
        }
      }
    },
    handleDeleteDashboard() {
      this.showConfirmDeleteModalFor = null
      const dashboard = this.dashboard
      deleteDashboardApi(dashboard.id).then(() => {
        this.dashboardList = this.dashboardList.filter(
          (d) => d.id !== dashboard.id
        )
        if (this.dashboardId === dashboard.id) {
          this.dashboardId = this.dashboardList[0] && this.dashboardList[0].id
        }
      })
    },
    handleWidgetsUpdated(widgets) {
      const data = {
        ...this.dashboard,
        widgets,
      }
      updateDashboardApi(data, false).then((dashboard) => {
        this.dashboard = Object.freeze(dashboard)
        this.updateDashboard(dashboard)
      })
    },
    handleAddNewWidget(widget) {
      const data = {
        ...this.dashboard,
        widgets: [...this.dashboard.widgets, widget],
      }
      updateDashboardApi(data).then(() => {
        this.dashboard = Object.freeze(data)
        this.updateDashboard(data)
      })
      this.$nextTick(() => {
        if (this.$refs.widgetsRef) {
          this.$refs.widgetsRef.scrollToBottom()
        }
      })
    },
    handleUpdateDashboardDefinition(dashboard) {
      const shouldReloadUser = dashboard.isDefault
      updateDashboardApi(dashboard, true).then((dashboard) => {
        this.updateDashboard(dashboard)
        if (this.dashboard.id === dashboard.id) {
          this.dashboard = Object.freeze(dashboard)
        }
        if (shouldReloadUser) {
          this.updatePartialPreference({
            defaultHomeScreen: dashboard.id,
          }).then(() => {
            this.refreshUser()
          })
        }
      })
    },
    handleMarkAsDefault() {
      this.updatePartialPreference({
        defaultHomeScreen: this.dashboard.id,
      }).then(() => {
        this.refreshUser()
      })
    },
    getSources(hasFlow) {
      return getSourceApi('flow').then(async ({ result }) => {
        this.flowSourceOptions = Object.freeze(
          Object.keys(result).map((i) => ({ key: i, text: result[i] }))
        )

        const hasSource = !!this.flowSourceOptions.length

        if (hasFlow !== hasSource) {
          await this.updateStaticLendingPagePreferences({
            lendingPagePreferences: { flow: hasSource },
          })
        }
      })
    },
    getLogInventories(hasLog) {
      return getAllLogInventoryApi().then(async (data) => {
        this.logInventories = data.filter(
          (logInventory) => !logInventory.isHealthAgentLogInventory
        )

        const hasLogInventories = !!this.logInventories.length

        if (hasLog !== hasLogInventories) {
          await this.updateStaticLendingPagePreferences({
            lendingPagePreferences: { log: hasLogInventories },
          })
        }
      })
    },
    async getLogFlowObjectAndUpdatePreference() {
      let { hasObjects, hasLog, hasFlow } =
        await this.getLendingPagePreference()

      if (!hasObjects) {
        const objects = await objectDBWorker.getObjects({})
        this.hasObjects = objects.length

        if (hasObjects !== !!this.hasObjects) {
          await this.updateStaticLendingPagePreferences({
            lendingPagePreferences: { object: !!objects.length },
          })
        }

        await this.getLendingPagePreference()
      }

      if (!hasFlow) {
        if (this.hasPermission(this.$constants.FLOW_SETTINGS_READ_PERMISSION)) {
          await this.getSources(hasFlow)
          await this.getLendingPagePreference()
        }
      }

      if (!hasLog) {
        if (this.hasPermission(this.$constants.LOG_SETTINGS_READ_PERMISSION)) {
          await this.getLogInventories(hasLog)
          await this.getLendingPagePreference()
        }
      }

      await this.getLendingPagePreference()
    },

    async getLendingPagePreference() {
      const hasObjects = await this.getStaticLendingPagePreferenceByModule({
        module: 'object',
      })
      const hasLog = await this.getStaticLendingPagePreferenceByModule({
        module: 'log',
      })

      const hasFlow = await this.getStaticLendingPagePreferenceByModule({
        module: 'flow',
      })

      this.shouldShowModule = hasObjects || hasLog || hasFlow

      return {
        hasObjects,
        hasLog,
        hasFlow,
      }
    },
    updateTimelinePreferenceForUser(timeline) {
      this.updateTimelinePreference({ module: 'dashboard', timeline })
    },
  },
}
</script>

<style lang="less" scoped>
.dashboard-selector-bar {
  z-index: 2;
  border-bottom: 1px solid var(--border-color);

  @apply py-1 flex justify-between items-center;
}
</style>
