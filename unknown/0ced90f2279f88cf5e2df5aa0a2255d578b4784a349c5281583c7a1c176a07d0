<template>
  <GroupProvider>
    <div class="w-full flex flex-col flex-1 min-h-0 relative">
      <MRow class="flex-1 min-h-0" :gutter="0">
        <MCol
          :size="2"
          class="left-side-list-view-panel h-full flex flex-col min-h-0 transition-width page-background-color pl-2 border-right"
        >
          <!-- <FlotoContentLoader v-if="lodingGrid" :loading="true" /> -->

          <ExplorerGridVirticalFilter
            :loading="lodingGrid"
            @filterChange="filters = $event"
          />
          <!-- <FlotoNoData v-else /> -->
        </MCol>

        <MCol :size="10">
          <div
            class="right-side-content-view-panel relative flex flex-col min-h-0 flex-1 h-full"
          >
            <MPersistedColumns
              v-model="gridColumns"
              :default-value="gridColumns"
              module-key="ncm"
              :available-columns="availableColumns"
            >
              <template
                v-slot="{
                  columns: persistedColumns,
                  setColumns: updatePersistedColumns,
                }"
              >
                <FlotoFixedView>
                  <FlotoScrollView>
                    <FlotoPaginatedCrud
                      ref="paginatedCrudRef"
                      resource-name="ncm"
                      as-table
                      :columns="persistedColumns"
                      :fetch-fn="getNcmObjects"
                      selectable
                      :filters="filters"
                      @selection-change="selectedItems = $event"
                      @loaded-once="onGridLoaded"
                      @column-change="updatePersistedColumns"
                    >
                      <template
                        v-slot:add-controls="{
                          filter,
                          resetFilter,
                          searchTerm,
                        }"
                      >
                        <MRow :gutter="0" class="-mt-2">
                          <MCol :size="5">
                            <MInput
                              :value="searchTerm"
                              class="search-box"
                              placeholder="Search"
                              name="search"
                              @update="filter"
                            >
                              <template v-slot:prefix>
                                <MIcon name="search" />
                              </template>
                              <template v-if="searchTerm" v-slot:suffix>
                                <MIcon
                                  name="times-circle"
                                  class="text-neutral-light cursor-pointer"
                                  @click="resetFilter"
                                />
                              </template>
                            </MInput>
                          </MCol>

                          <MCol
                            :size="7"
                            class="text-right flex items-center justify-end"
                          >
                            <div class="flex items-center">
                              <ColumnSelector
                                v-model="gridColumns"
                                :columns="availableColumns"
                                @change="updatePersistedColumns"
                              />

                              <MButton
                                :shadow="false"
                                class="squared-button mr-2"
                                :rounded="false"
                                variant="neutral-lightest"
                                title="Export As PDF"
                                @click="handleExport('pdf')"
                              >
                                <MIcon name="export-pdf" />
                              </MButton>
                              <MButton
                                :shadow="false"
                                class="squared-button mr-2"
                                :rounded="false"
                                variant="neutral-lightest"
                                title="Export As CSV"
                                @click="handleExport('csv')"
                              >
                                <MIcon name="export-csv" />
                              </MButton>

                              <FlotoGridActions
                                v-if="selectedItems.length > 1"
                                id="bulk-action"
                                :resource="{}"
                                :actions="bulkActions"
                                :edit-permission-name="
                                  $constants.NCM_UPDATE_PERMISSION
                                "
                                :edit-permission-keys="[
                                  'bulk_backup',
                                  'bulk_sync',
                                  'bulk_restore',
                                  'bulk_firmware_upgrade',
                                  'bulk_get_hardware_details',
                                  'execute_runbook',
                                ]"
                                @bulk_backup="
                                  sendConfigChangeRequest(
                                    null,
                                    ACTION_MAP.BACKUP,
                                    true
                                  )
                                "
                                @bulk_sync="
                                  sendConfigChangeRequest(
                                    null,
                                    ACTION_MAP.SYNC,
                                    true
                                  )
                                "
                                @bulk_restore="
                                  () => {
                                    showRestoreActionItem = {
                                      selectedItems,
                                    }
                                    forBulkRestore = true
                                  }
                                "
                                @bulk_firmware_upgrade="
                                  () => {
                                    showFirmwareActionItem = {
                                      selectedItems,
                                    }
                                    forBulkaction = true
                                  }
                                "
                                @bulk_get_hardware_details="
                                  getHardwareDetails(
                                    null,
                                    ACTION_MAP.INFO,
                                    true
                                  )
                                "
                                @execute_runbook="
                                  () => {
                                    isExecuteRunbookDrawerOpen = true
                                    forBulkaction = true
                                  }
                                "
                              >
                                <template v-slot:trigger>
                                  <MButton
                                    id="btn-show-hide-columns"
                                    :shadow="false"
                                    :rounded="false"
                                    variant="neutral-lightest"
                                    class="squared-button mr-2"
                                  >
                                    <MIcon
                                      name="ellipsis-v"
                                      class="excluded-header-icon"
                                    />
                                  </MButton>
                                </template>
                              </FlotoGridActions>

                              <MButton
                                id="compare-btn"
                                class="mr-2"
                                @click="isCompareModelOpen = true"
                              >
                                Compare
                              </MButton>
                            </div>
                          </MCol>
                        </MRow>
                      </template>

                      <template v-slot:device="{ item }">
                        <a @click="showViewDetailItem = item">
                          {{ item.device }}
                        </a>
                      </template>

                      <template v-slot:deviceType="{ item }">
                        <MonitorType
                          :type="item.deviceType"
                          disable-tooltip
                          class="mr-1"
                        />
                      </template>
                      <template v-slot:tags="{ item }">
                        <LooseTags :value="item.tags" disabled />
                      </template>

                      <template v-slot:runningFileCurrentVersion="{ item }">
                        <div
                          v-if="item.runningFileCurrentVersion"
                          class="inline-flex items-center justify-center"
                        >
                          <SelectedItemPills
                            :value="[`${item.runningFileCurrentVersion}.0`]"
                          />
                          <MIcon
                            v-if="item.isBaselineVersion"
                            name="star"
                            class="relative cursor-pointer text-secondary-yellow"
                          />
                        </div>
                      </template>
                      <template v-slot:baselineVersion="{ item }">
                        <div
                          v-if="item.baselineVersion"
                          class="inline-flex items-center justify-center"
                        >
                          <SelectedItemPills
                            :value="[`${item.baselineVersion}.0`]"
                          />
                        </div>
                        <span v-else> --- </span>
                      </template>

                      <template v-slot:configConflict="{ item }">
                        <template v-if="!item.syncInProgress">
                          <MButton
                            v-if="item.configConflict === 'Conflict Detected'"
                            variant="transparent"
                            :shadow="false"
                            :rounded="false"
                            class="p-0"
                            @click="handelDrillDownBackupCompareForm(item, 0)"
                          >
                            <span class="text-secondary-red"
                              >Conflict Detected</span
                            >
                          </MButton>
                          <span
                            v-else
                            :class="`${
                              item.configConflict === 'In Sync'
                                ? 'text-secondary-green'
                                : 'text-neutral-light'
                            }`"
                          >
                            {{ item.configConflict }}</span
                          >
                        </template>

                        <StatusProgress v-else />
                      </template>

                      <template v-slot:lastBackupStatus="{ item }">
                        <NCMCredentialStatus
                          v-if="
                            !item.backupInProgress && !item.restoreInProgress
                          "
                          :status="item.lastBackupStatus"
                          use-status-map
                        />

                        <StatusProgress v-else />
                      </template>

                      <template v-slot:lastFirmwareUpgradeStatus="{ item }">
                        <NCMCredentialStatus
                          v-if="!item.firmwareInProgress"
                          :status="item.lastFirmwareUpgradeStatus"
                          use-status-map
                        />

                        <StatusProgress
                          v-else
                          :progress-message="
                            item.firmwareState === $constants.STATE_IN_QUEUE
                              ? 'In-queue'
                              : item.firmwareState === $constants.STATE_RUNNING
                              ? 'Running'
                              : undefined
                          "
                        />
                      </template>

                      <template v-slot:lastPerfomedAction="{ item }">
                        <MButton
                          v-if="
                            !item.backupInProgress &&
                            !item.restoreInProgress &&
                            !item.syncInProgress &&
                            !item.firmwareInProgress &&
                            !item.infoInProgress &&
                            !item.runbookInProgress
                          "
                          variant="transparent"
                          :shadow="false"
                          :rounded="false"
                          class="p-0"
                          @click="showLastPerfomedActionModelItem = item"
                        >
                          <span class="text-primary">
                            {{ item.lastPerfomedAction }}</span
                          >
                        </MButton>
                        <span v-else> --- </span>
                      </template>

                      <template v-slot:lastBackupTime="{ item }">
                        {{ formatDateTime(item.lastBackupTime) }}
                      </template>

                      <template v-slot:lastActionTime="{ item }">
                        {{ formatDateTime(item.lastActionTime) }}
                      </template>
                      <template v-slot:actions="{ item }">
                        <FlotoGridActions
                          :actions="getActions(item)"
                          :resource="item"
                          class="mr-3 action-btn-handle"
                          :edit-permission-name="
                            $constants.NCM_UPDATE_PERMISSION
                          "
                          :edit-permission-keys="[
                            'restore',
                            'sync',
                            'backup',
                            'setup_as_baseline',
                            'unassign_baseline',
                            'firmware_upgrade',
                            'get_hardware_details',
                            'terminal',
                            'execute_runbook',
                          ]"
                          @terminal="
                            handleOpenTerminal({
                              id: item.objectId,
                              ncmId: item.id,
                            })
                          "
                          @restore="showRestoreActionItem = item"
                          @sync="sendConfigChangeRequest(item, ACTION_MAP.SYNC)"
                          @backup="
                            sendConfigChangeRequest(item, ACTION_MAP.BACKUP)
                          "
                          @download_backup="exportNetworkBackup(item)"
                          @setup_as_baseline="
                            baselineActions(item, 'setup_as_baseline')
                          "
                          @unassign_baseline="
                            baselineActions(item, 'unassign_baseline')
                          "
                          @view="showViewDetailItem = item"
                          @compare="handelDrillDownBackupCompareForm(item, 1)"
                          @firmware_upgrade="showFirmwareActionItem = item"
                          @get_hardware_details="
                            getHardwareDetails(item, ACTION_MAP.INFO)
                          "
                          @execute_runbook="handleOpenRunbook(item)"
                        />
                      </template>
                    </FlotoPaginatedCrud>
                  </FlotoScrollView>
                </FlotoFixedView>
              </template>
            </MPersistedColumns>
          </div>
        </MCol>
        <BackupCompareForm
          v-if="isCompareModelOpen"
          :fetch-device="getItems"
          :default-data="defaultDataForBackupCompareForm"
          @cancel="closeBackupCompareForm"
        />
        <RestoreBackupModel
          v-if="showRestoreActionItem !== null"
          :restore-item="showRestoreActionItem"
          :for-bulk-restore="forBulkRestore"
          @hide="
            () => {
              showRestoreActionItem = null
              forBulkRestore = false
              selectedItems = []
              resetList()
            }
          "
        />

        <FirmwareModel
          v-if="showFirmwareActionItem !== null"
          :item="showFirmwareActionItem"
          :for-bulk-action="forBulkaction"
          @hide="
            () => {
              showFirmwareActionItem = null
              forBulkaction = false
              selectedItems = []
              resetList()
            }
          "
        />

        <ViewDetailDrawer
          v-if="showViewDetailItem !== null"
          :view-item="showViewDetailItem"
          :fetch-fn="getSingleNcmObjects"
          @hide="showViewDetailItem = null"
          @drillDown="
            handelDrillDownBackupCompareForm($event, $event.drillDownType)
          "
          @config-backup="sendConfigChangeRequest($event, ACTION_MAP.BACKUP)"
          @update-row="updateRow"
          @terminal="
            handleOpenTerminal({
              id: showViewDetailItem.objectId,
              ncmId: showViewDetailItem.id,
            })
          "
        />

        <FlotoConfirmModal
          v-if="showLastPerfomedActionModelItem !== null"
          :open="showLastPerfomedActionModelItem !== null"
          variant="primary-alt"
          :width="420"
          :mask-closable="false"
          overlay-class-name="no-padding-confrim-modal"
          hide-icon
          @hide="showLastPerfomedActionModelItem = null"
        >
          <template v-slot:message>
            <h5 class="my-2"
              >Last Performed Action -
              {{
                lastPerfomedActionFormate(
                  showLastPerfomedActionModelItem.lastActionPerformed
                )
              }}</h5
            >

            <MRow :gutter="0" class="flex">
              <MCol :size="1">
                <MIcon
                  :name="
                    showLastPerfomedActionModelItem.lastActionStatus ===
                    'succeed'
                      ? 'check-circle'
                      : 'times-circle'
                  "
                  :class="{
                    'text-secondary-green':
                      showLastPerfomedActionModelItem.lastActionStatus ===
                      'succeed',
                    'text-secondary-red':
                      showLastPerfomedActionModelItem.lastActionStatus !==
                      'succeed',
                  }"
                />
              </MCol>

              <MCol :size="11">
                <span>{{
                  showLastPerfomedActionModelItem.lastActionStatus === 'succeed'
                    ? 'Successful'
                    : 'Failed'
                }}</span>
                <br />

                <span
                  v-if="
                    showLastPerfomedActionModelItem.lastActionStatus !==
                    'succeed'
                  "
                >
                  {{
                    showLastPerfomedActionModelItem.lastActionErrorMessage
                  }}</span
                >
              </MCol>
              <MCol :size="12" class="ml-6"> </MCol>
            </MRow>
          </template>

          <template v-slot:action-container>
            <MRow class="flex justify-end w-full" :gutter="16">
              <MCol class="text-right">
                <MButton
                  class="mr-2"
                  variant="primary"
                  @click="showLastPerfomedActionModelItem = null"
                  >Close</MButton
                >
              </MCol>
            </MRow>
          </template>
        </FlotoConfirmModal>
      </MRow>
      <Terminal
        v-if="isTerminalOpen"
        :open="Boolean(showTerminalForId)"
        v-bind="showTerminalForId"
        @close="handleCloseTerminal"
      />
      <ExecuteRunbook
        v-if="isExecuteRunbookDrawerOpen"
        :open="Boolean(showRunbookFor) || (selectedItems || []).length"
        :selected-ids="selectedItems"
        :monitor="showRunbookFor"
        @close="handleCloseRunbook"
      />
    </div>
  </GroupProvider>
</template>

<script>
import Bus from '@utils/emitter'
import Constants from '@constants'
import { WidgetTypeConstants } from '@components/widgets/constants'
import { generateId } from '@utils/id'
import { arrayWorker } from '@/src/workers'

import {
  getNcmObjectsApi,
  exportBackup,
  changeBaselineApi,
  sendConfigRequest,
  getSingleNcmObjectsApi,
} from '../ncm-api'
import {
  buildUpdatedContext,
  FILE_TYPE,
  fetchMonitorInfoWidgetDefinition,
  ACTION_MAP,
} from '../helpers/explorer'
import Config from '../config'
import ColumnSelector from '@components/column-selector.vue'
import MonitorType from '@components/monitor-type.vue'
import SelectedItemPills from '@/src/components/dropdown-trigger/selected-item-pills.vue'
import ExplorerGridVirticalFilter from '../components/explorer-grid-virtical-filter.vue'
import NCMCredentialStatus from '@modules/settings/ncm-settings/components/ncm-credential-status.vue'
import BackupCompareForm from '../components/backup-compare-form/backup-compare-form.vue'
import RestoreBackupModel from '../components/restore-backup-model.vue'
import FirmwareModel from '../components/firmware-model.vue'

import datetime from '@src/filters/datetime'
import ViewDetailDrawer from '../components/view-detail-drawer.vue'
import StatusProgress from '../components/status-progress.vue'
import LooseTags from '@components/loose-tags.vue'
import GroupProvider from '@components/data-provider/group-provider.vue'
import Terminal from '@components/terminal/terminal.vue'
import ExecuteRunbook from '../components/execute-runbook.vue'
import exportData from '@modules/settings/monitoring/helpers/export-pdf-csv'
import { downloadFile } from '@utils/download'
import { UserPreferenceComputed } from '@state/modules/user-preference'

export default {
  name: 'Explorer',
  components: {
    ExplorerGridVirticalFilter,
    ColumnSelector,
    MonitorType,
    SelectedItemPills,
    NCMCredentialStatus,
    BackupCompareForm,
    RestoreBackupModel,
    ViewDetailDrawer,
    StatusProgress,
    LooseTags,
    GroupProvider,
    FirmwareModel,
    Terminal,
    ExecuteRunbook,
  },

  inject: { SocketContext: { default: {} } },
  data() {
    this.eventCache = {}
    this.ACTION_MAP = ACTION_MAP
    this.actions = [
      { key: 'backup', name: 'Backup Now', icon: 'backup-now' },
      { key: 'compare', name: 'Compare', icon: 'compare' },
      { key: 'download_backup', name: 'Download Backup', icon: 'download' },
      {
        key: 'setup_as_baseline',
        name: 'Set as Baseline',
        icon: 'filled-star',
      },
      { key: 'unassign_baseline', name: 'Remove Baseline', icon: 'star' },
      { key: 'terminal', name: 'SSH Terminal', icon: 'terminal' },
      { key: 'view', name: 'View', icon: 'eye' },
      { key: 'sync', name: 'Sync', icon: 'sync' },
      { key: 'restore', name: 'Restore', icon: 'restore' },
      { key: 'execute_runbook', name: 'Execute Runbook', icon: 'runbook' },
      {
        key: 'get_hardware_details',
        name: 'Get Hardware Details',
        icon: 'hardware-details',
      },

      { key: 'firmware_upgrade', name: 'Firmware Upgrade', icon: 'upgrade' },
    ]

    this.bulkActions = [
      {
        key: 'bulk_backup',
        name: 'Backup Now',
        icon: 'backup-now',
      },
      {
        key: 'bulk_sync',
        name: 'Sync',
        icon: 'sync',
      },
      { key: 'bulk_restore', name: 'Restore', icon: 'restore' },
      {
        key: 'bulk_get_hardware_details',
        name: 'Get Hardware Details',
        icon: 'hardware-details',
      },
      { key: 'execute_runbook', name: 'Execute Runbook', icon: 'runbook' },
      {
        key: 'bulk_firmware_upgrade',
        name: 'Firmware Upgrade',
        icon: 'upgrade',
      },
    ]
    this.columns = [
      {
        key: 'device',
        name: 'Device',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        disable: true,
      },
      {
        key: 'ip',
        name: 'IP',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        disable: true,
      },
      {
        key: 'deviceType',
        align: 'center',
        name: 'Device Type',
        searchable: true,
        sortable: true,
        minWidth: '100px',
      },
      {
        key: 'vendor',
        name: 'Vendor',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        hidden: true,
      },
      {
        key: 'runningFileCurrentVersion',
        name: 'Current Version',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        disable: true,
      },
      {
        key: 'configConflict',
        name: 'Config Conflict',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        disable: true,
      },
      {
        key: 'lastPerfomedAction',
        name: 'Last Performed Action',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        disable: true,
      },
      {
        key: 'lastBackupStatus',
        name: 'Last Backup Status',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        hidden: true,
      },

      {
        key: 'lastFirmwareUpgradeStatus',
        name: 'Last Firmware Upgrade Status',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        hidden: true,
      },
      {
        key: 'lastBackupTime',
        name: 'Last Backup Time',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        hidden: true,
        exportType: 'datetime',
      },
      {
        key: 'serialNumber',
        name: 'Serial Number',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        hidden: true,
      },

      {
        key: 'modelNumber',
        name: 'Model',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        hidden: true,
      },
      {
        key: 'template',
        name: 'Template',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        hidden: true,
      },
      {
        key: 'oid',
        name: 'OID',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        hidden: true,
      },
      {
        key: 'lastActionTime',
        name: 'Last Action Time',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        hidden: true,
        exportType: 'datetime',
      },
      {
        key: 'baselineVersion',
        name: 'Baseline Version',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        hidden: true,
      },
      {
        key: 'os',
        name: 'OS Type',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        hidden: true,
      },
      {
        key: 'tags',
        name: 'Tags',
        searchable: true,
        sortable: true,
        minWidth: '100px',
        hidden: true,
        searchKey: 'tagsStr',
        sortKey: 'tagsStr',
      },
      {
        key: 'actions',
        name: 'Actions',
        width: '120px',
        disable: true,
        export: false,
      },
    ]
    return {
      selectedItems: [],
      gridColumns: this.columns,
      filters: [],
      showTerminalForId: null,
      // girdData: [],
      lodingGrid: true,
      isCompareModelOpen: false,
      showLastPerfomedActionModelItem: null,
      showRestoreActionItem: null,
      forBulkRestore: false,
      showViewDetailItem: null,
      uuid: generateId(),
      defaultDataForBackupCompareForm: undefined,
      showFirmwareActionItem: null,
      showExecuteRunbookActionItem: null,
      forBulkaction: false,
      isTerminalOpen: false,
      isExecuteRunbookDrawerOpen: false,
      showRunbookFor: null,
    }
  },
  computed: {
    ...UserPreferenceComputed,
    availableColumns() {
      return this.columns
    },
  },
  created() {
    const handler = (payload) => {
      if (payload.id) {
        this.updateStatus(payload)
      }
    }

    Bus.$on(this.$currentModule.getConfig().STATE_CHANGE, handler)

    Bus.$on(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleDataReceived)

    this.$once('hook:beforeDestroy', () => {
      this.clearCacheExecutionInterval()

      Bus.$off(this.$currentModule.getConfig().STATE_CHANGE, handler)

      Bus.$off(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleDataReceived)
    })
  },

  methods: {
    handleOpenTerminal(item) {
      this.isTerminalOpen = true
      setTimeout(() => {
        this.showTerminalForId = item
      })
    },
    handleCloseTerminal() {
      this.showTerminalForId = null
      setTimeout(() => {
        this.isTerminalOpen = false
      }, 400)
    },
    handleOpenRunbook(item) {
      this.isExecuteRunbookDrawerOpen = true
      setTimeout(() => {
        this.showRunbookFor = item
      })
    },
    handleCloseRunbook() {
      this.showRunbookFor = null
      if (this.forBulkaction) {
        this.selectedItems = []
        this.resetList()
      }
      this.forBulkaction = false
      setTimeout(() => {
        this.isExecuteRunbookDrawerOpen = false
      }, 400)
    },
    getNcmObjects() {
      return getNcmObjectsApi()
    },
    getSingleNcmObjects(id) {
      return getSingleNcmObjectsApi(id)
    },

    getActions(item) {
      let actions = this.actions

      if (item.isBaselineVersion) {
        actions = actions.filter(
          (action) => !['setup_as_baseline'].includes(action.key)
        )
      } else {
        actions = actions.filter(
          (action) => !['unassign_baseline'].includes(action.key)
        )
      }

      if (item?.configConflict?.toLowerCase() === 'not applicable') {
        actions = actions.filter((action) => !['sync'].includes(action.key))
      }

      if (item.backupInProgress) {
        actions = actions.filter((action) => !['backup'].includes(action.key))
      }
      if (item.restoreInProgress) {
        actions = actions.filter((action) => !['restore'].includes(action.key))
      }
      if (item.syncInProgress) {
        actions = actions.filter((action) => !['sync'].includes(action.key))
      }
      if (item.firmwareInProgress) {
        actions = actions.filter(
          (action) => !['firmware_upgrade'].includes(action.key)
        )
      }
      if (item.infoInProgress) {
        actions = actions.filter(
          (action) => !['get_hardware_details'].includes(action.key)
        )
      }

      if (item.runbookInProgress) {
        actions = actions.filter(
          (action) => !['execute_runbook'].includes(action.key)
        )
      }

      return actions
    },

    onGridLoaded() {
      if (this.$refs.paginatedCrudRef) {
        this.lodingGrid = false
        this.startCacheExecutionInterval()

        // this.girdData = this.getItems()
        this.updateVerticalValueData()
        this.askForMonitorInfo()
      }
    },
    getItems() {
      if (this.$refs.paginatedCrudRef) {
        return this.$refs.paginatedCrudRef.getData()
      } else {
        return []
      }
    },

    formatDateTime(value) {
      return datetime(Math.round(value))
    },
    exportNetworkBackup(item) {
      this.SocketContext.addGuidForEvent(
        Constants.UI_EVENT_CSV_EXPORT,
        this.uuid
      )
      return exportBackup({
        id: item.id,
        uuid: this.uuid,
      })
    },
    baselineActions(item, action) {
      const untouchedData = item
      return changeBaselineApi(
        item,
        action,
        item.runningFileCurrentVersion
      ).then(async (res) => {
        if (res.status === this.$constants.EVENT_SUCCESS_STATUS) {
          return this.getSingleNcmObjects(item.id).then(async (item) => {
            await this.handleInventoryUpdated({ ...untouchedData, ...item })
          })
        }
      })
    },
    async updateStatus(payload) {
      // if (this.$refs.paginatedCrudRef) {
      // const item = await this.$refs.paginatedCrudRef.getItem(payload.id)
      // const itemIndex = await arrayWorker.findIndex(this.girdData, {
      //   id: payload.id,
      // })

      // if (item) {
      let plainItem =
        //  {
        // ...item,
        // ...
        buildUpdatedContext(payload)
      // }

      this.eventCache[payload.id] = {
        ...this.eventCache[payload.id],
        ...plainItem,
      }

      // this.handleInventoryUpdated(plainItem)

      // if (payload.state !== 'Running') {
      //   if (itemIndex >= 0) {
      //     this.girdData = Object.freeze([
      //       ...this.girdData.slice(0, itemIndex),
      //       plainItem,
      //       ...this.girdData.slice(itemIndex + 1),
      //     ])
      //   }

      // }
      // }
      // }
    },
    handleInventoryUpdated(inventory) {
      if (this.$refs.paginatedCrudRef) {
        this.$refs.paginatedCrudRef.handleUpdateItem(inventory)
      }
    },

    showNotification(event) {
      this.$successNotification({
        message: event.message,
      })
    },

    closeBackupCompareForm() {
      this.defaultDataForBackupCompareForm = undefined
      this.isCompareModelOpen = false
    },
    handelDrillDownBackupCompareForm(item, drillDownType) {
      const drilldownTypeMap = {
        0: {
          left: {
            fileType: FILE_TYPE.STARTUP,
            version: item.startupFileCurrentVersion,
          },
          right: {
            fileType: FILE_TYPE.RUNNING,
            version: item.runningFileCurrentVersion,
          },
        },
        1: {
          left: {
            fileType: FILE_TYPE.RUNNING,
            version: item.runningFileCurrentVersion - 1,
          },
          right: {
            fileType: FILE_TYPE.RUNNING,
            version: item.runningFileCurrentVersion,
          },
        },
        2: {
          left: {
            fileType: FILE_TYPE.RUNNING,
            version: item.baselineVersion,
          },
          right: {
            fileType: FILE_TYPE.RUNNING,
            version: item.runningFileCurrentVersion,
          },
        },
      }

      this.defaultDataForBackupCompareForm = {
        configurations: {
          fileType: drilldownTypeMap[drillDownType].left.fileType,
          device: item.id,
          version: drilldownTypeMap[drillDownType].left.version,
        },
        configurationsToCompare: {
          fileType: drilldownTypeMap[drillDownType].right.fileType,
          device: item.id,
          version: drilldownTypeMap[drillDownType].right.version,
        },
      }
      this.isCompareModelOpen = true
    },
    askForMonitorInfo() {
      const ids = this.getItems().map((item) => item.objectId)
      const widget = fetchMonitorInfoWidgetDefinition(ids)
      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_WIDGET_RESULT_EVENT,
        'event.context': {
          ...widget,
          id: -1,
          [this.$constants.UI_EVENT_UUID]: this.uuid,
        },
      })
    },

    handleDataReceived(data) {
      if (!data) {
        return
      }
      if (
        data[this.$constants.UI_EVENT_UUID] !== this.uuid &&
        ((data.result || {}).queryMeta || {}).progress !== 100
      ) {
        return
      }
      if (((data.result || {})[WidgetTypeConstants.GRID] || {}).data) {
        this.addMonitorInfo(
          ((data.result || {})[WidgetTypeConstants.GRID] || {}).data
        )
      }
    },
    addMonitorInfo(data) {
      const infoMap = (data || []).reduce(
        (acc, item) => ({
          ...acc,
          [item['entity.id']]: {
            serialNumber: item['system.serial.number.last'],
            osVersion: item['system.os.version.last'],
            modelNumber: item['system.model.last'],
          },
        }),
        {}
      )

      const updatedData = this.getItems().map((item) => ({
        ...item,
        ...(infoMap[item.objectId] || {}),
      }))

      if (this.$refs.paginatedCrudRef) {
        this.$refs.paginatedCrudRef.setItems(updatedData)
      }
    },

    sendConfigChangeRequest(item, type, forBulkAction) {
      return sendConfigRequest(
        forBulkAction ? this.selectedItems : item.id,
        type
      ).then((response) => {
        if (
          [ACTION_MAP.SYNC, ACTION_MAP.BACKUP, ACTION_MAP.RESTORE].includes(
            type
          )
        ) {
          this.showNotification(response)

          if (forBulkAction) {
            this.selectedItems = []
            this.resetList()
          }
        }
      })
    },

    getHardwareDetails(item, type, forBulkAction) {
      return sendConfigRequest(
        forBulkAction ? this.selectedItems : item.id,
        type
      ).then((response) => {
        if ([ACTION_MAP.INFO].includes(type)) {
          this.showNotification(response)

          if (forBulkAction) {
            this.selectedItems = []
            this.resetList()
          }
        }
      })
    },

    resetList() {
      if (this.$refs.paginatedCrudRef) {
        this.$refs.paginatedCrudRef.resetList()
      }
    },
    async executeCache() {
      if (this.$refs.paginatedCrudRef && Object.keys(this.eventCache).length) {
        let data = this.getItems()
        const keys = Object.keys(this.eventCache)

        for (const id of keys) {
          const itemIndex = await arrayWorker.findIndex(data, {
            id: Number(id),
          })

          if (itemIndex >= 0) {
            data = Object.freeze([
              ...data.slice(0, itemIndex),
              {
                ...data[itemIndex],
                ...(this.eventCache[id] || {}),
              },
              ...data.slice(itemIndex + 1),
            ])
          }
          delete this.eventCache[id]
        }
        await this.$refs.paginatedCrudRef.setItems(data)

        setTimeout(() => {
          this.updateVerticalValueData()
        }, 1300)
      }
    },
    updateVerticalValueData() {
      Bus.$emit('update-vertical-filter', this.getItems())
    },

    startCacheExecutionInterval() {
      this.clearCacheExecutionInterval()
      this.__cacheExecutionInterval = setInterval(
        this.executeCache,
        Config.CACHE_EXECUTION_INTERVAL
      )
    },
    clearCacheExecutionInterval() {
      if (this.__cacheExecutionInterval) {
        clearInterval(this.__cacheExecutionInterval)
        this.__cacheExecutionInterval = null
      }
    },

    updateRow(item) {
      this.handleInventoryUpdated(item)
      this.showViewDetailItem = item
    },
    lastPerfomedActionFormate(action) {
      return (action || '').replace('.', ' ')
    },
    async handleExport(type) {
      if (!this.$refs.paginatedCrudRef) return

      this.$successNotification({
        message: 'Success',
        description: `The file will be downloaded once ready`,
      })

      let items = await this.$refs.paginatedCrudRef.getFilteredData()
      const contextData = this.$refs.paginatedCrudRef.getContextData()
      items = items.map((item) => {
        item.lastBackupStatus =
          item.lastBackupStatus === this.$constants.EVENT_FAIL_STATUS
            ? 'Failed'
            : 'Successful'
        return item
      })
      const options = {
        dateTimeFormat: this.dateFormat,
        timezone: this.timezone,
      }

      exportData(
        this.gridColumns.filter((obj) => obj.key && !obj.hidden),
        items,
        type,
        contextData,
        options
      ).then((blob) => {
        downloadFile(blob, undefined, `NCM managed monitors.${type}`)
      })
    },
  },
}
</script>
