import Moment from 'moment'
import Uniq from 'lodash/uniq'
import <PERSON><PERSON><PERSON> from 'lodash/maxBy'
import Merge from 'lodash/merge'
import GroupBy from 'lodash/groupBy'
import CloneDeep from 'lodash/cloneDeep'
import SortBy from 'lodash/sortBy'
import Omit from 'lodash/omit'
import { generateId } from '@utils/id'
import Constants from '@constants'
import SnappyJs from 'snappyjs'

import WidgetContextBuilder from './widget-context-builder'
import {
  OPERATOR_MAP,
  TIME_FORMAT,
  FILTER_CONDITION_DEFAULT_DATA,
  WidgetTypeConstants,
  DATE_FORMAT,
  DATE_FORMAT_REJAX,
  TIME_FORMATE_REJAX,
  COLOR_PALETTES,
  AVAILABLE_GROUP_TYPES,
} from './constants'
import {
  buildArubaAccessPointCalculatedGrid,
  buildAvailabilityTimeSeries,
  buildInterfaceCalculatedGrid,
  buildSwitchPortMapperGrid,
  buildVlanCalculatedGrid,
  mergeAvailabilityPieResult,
  defaultMerger,
  mergeTopNHorizontalResult,
  buildStackedSwitchGrid,
} from './custom-widget-data-formatter'

import { AvailableReportCategories } from '@modules/report/helpers/report'

const SEVERITY_TO_CONSIDER = [
  Constants.DOWN,
  Constants.UNREACHABLE,
  Constants.CRITICAL,
  Constants.MAJOR,
  Constants.WARNING,
  Constants.CLEAR,
  'UP',
  Constants.DISABLE,
  Constants.MAINTENANCE,
  Constants.UNKNOWN,
]

export const FLOW_DRILLDOWN_IGNORED_COUNTERS = [
  // 'flows.per.sec',
  'flow.volume.bytes.per.sec',
  // 'flow.volume.bytes',
]

export const FLOW_DRILLDOWN_REPLACEMENT_COUNTERS_MAP = {
  'flow.volume.bytes': 'volume.bytes',
  'flows.per.sec': 'volume.bytes',
}

export const POLICY_GRID_DEFAULT_COLUMN_SETTINGS = [
  {
    name: 'object.id',
    rawName: 'object.id',
    hidden: false,
    displayName: 'Monitor',
  },
]

export const PREMIUM_WIDGET_CATEGORIES = [
  WidgetTypeConstants.MAP_VIEW,
  // WidgetTypeConstants.ANOMALY,
  // WidgetTypeConstants.FORECAST,
]

export function getOperatorOptions(allowedOptions = []) {
  if (allowedOptions.length) {
    return Object.keys(OPERATOR_MAP)
      .filter((key) => allowedOptions.indexOf(key) >= 0)
      .map((key) => ({ key, name: OPERATOR_MAP[key] }))
  }
  return Object.keys(OPERATOR_MAP).map((key) => ({
    key,
    name: OPERATOR_MAP[key],
  }))
}

export function getAdjustedTime(momentInstance) {
  const remainder = momentInstance.minute() % 5
  if (remainder === 0) {
    return momentInstance.format(TIME_FORMAT)
  }
  const adjustment = 5 - remainder
  return momentInstance.add(adjustment, 'minutes').format(TIME_FORMAT)
}

export function getRangeForMinutes(minutes) {
  return {
    startDate: Moment().subtract(minutes, 'minutes').unix() * 1000,
    startTime: Moment().subtract(minutes, 'minutes').format(TIME_FORMAT),
    endDate: Moment().unix() * 1000,
    endTime: Moment().format(TIME_FORMAT),
  }
}

export function getRange(rangeName) {
  let startDate
  let endDate
  let startTime
  let endTime
  if (rangeName === '-5m') {
    return getRangeForMinutes(5)
  } else if (rangeName === '-15m') {
    return getRangeForMinutes(15)
  } else if (rangeName === '-30m') {
    return getRangeForMinutes(30)
  } else if (rangeName === '-1h') {
    return getRangeForMinutes(60)
  } else if (rangeName === '-6h') {
    return getRangeForMinutes(6 * 60)
  } else if (rangeName === '-12h') {
    return getRangeForMinutes(12 * 60)
  } else if (rangeName === '-24h') {
    return getRangeForMinutes(24 * 60)
  } else if (rangeName === '-48h') {
    return getRangeForMinutes(48 * 60)
  } else if (rangeName === 'this.week') {
    startDate = Moment().startOf('isoWeek').unix() * 1000
    endDate = Moment().unix() * 1000
    endTime = getAdjustedTime(Moment())
    startTime = getAdjustedTime(Moment().startOf('isoWeek'))
  } else if (rangeName === 'this.month') {
    startDate = Moment().startOf('month').unix() * 1000
    endDate = Moment().unix() * 1000
    endTime = getAdjustedTime(Moment())
    startTime = getAdjustedTime(Moment().startOf('month'))
  } else if (rangeName === 'quarter') {
    startDate = Moment().startOf('quarter').unix() * 1000
    endDate = Moment().unix() * 1000
    endTime = getAdjustedTime(Moment())
    startTime = getAdjustedTime(Moment().startOf('quarter'))
  } else if (rangeName === 'year') {
    startDate = Moment().startOf('year').unix() * 1000
    endDate = Moment().unix() * 1000
    endTime = getAdjustedTime(Moment())
    startTime = getAdjustedTime(Moment().startOf('year'))
  } else if (rangeName === 'today') {
    startDate = Moment().startOf('day').unix() * 1000
    endDate = Moment().unix() * 1000
    endTime = Moment().format(TIME_FORMAT)
    startTime = getAdjustedTime(Moment().startOf('day'))
  } else if (rangeName === 'yesterday') {
    startDate = Moment().subtract(1, 'days').startOf('day').unix() * 1000
    endDate = Moment().subtract(1, 'days').endOf('day').unix() * 1000
    endTime = Moment().subtract(1, 'days').endOf('day').format(TIME_FORMAT)
    startTime = getAdjustedTime(Moment().subtract(1, 'days').startOf('day'))
  } else if (rangeName === '-7d') {
    startDate = Moment().subtract(1, 'weeks').unix() * 1000
    endDate = Moment().unix() * 1000
    endTime = getAdjustedTime(Moment())
    startTime = endTime
  } else if (rangeName === '-30d') {
    startDate = Moment().subtract(30, 'days').unix() * 1000
    endDate = Moment().unix() * 1000
    endTime = getAdjustedTime(Moment())
    startTime = endTime
  } else if (rangeName === '-90d') {
    startDate = Moment().subtract(90, 'days').unix() * 1000
    endDate = Moment().unix() * 1000
    endTime = getAdjustedTime(Moment())
    startTime = endTime
  } else if (rangeName === 'last.week') {
    startDate =
      Moment()
        .startOf('isoWeek')
        .endOf('day')
        .subtract(7, 'days')
        .startOf('day')
        .unix() * 1000
    endDate = Moment().startOf('week').endOf('day').unix() * 1000
    endTime = Moment().startOf('week').endOf('day').format(TIME_FORMAT)
    startTime = Moment()
      .startOf('isoWeek')
      .endOf('day')
      .subtract(7, 'days')
      .startOf('day')
      .format(TIME_FORMAT)
  } else if (rangeName === 'last.month') {
    startDate = Moment().subtract(1, 'month').startOf('month').unix() * 1000
    endDate = Moment().subtract(1, 'month').endOf('month').unix() * 1000
    endTime = Moment().subtract(1, 'month').endOf('month').format(TIME_FORMAT)
    startTime = Moment()
      .subtract(1, 'month')
      .startOf('month')
      .format(TIME_FORMAT)
  } else if (rangeName === 'last.quarter') {
    startDate = Moment().subtract(1, 'quarter').startOf('quarter').unix() * 1000
    endDate = Moment().subtract(1, 'quarter').endOf('quarter').unix() * 1000
    endTime = Moment()
      .subtract(1, 'quarter')
      .endOf('quarter')
      .format(TIME_FORMAT)
    startTime = Moment()
      .subtract(1, 'quarter')
      .startOf('quarter')
      .format(TIME_FORMAT)
  } else if (rangeName === 'last.year') {
    startDate = Moment().subtract(1, 'year').startOf('year').unix() * 1000
    endDate = Moment().subtract(1, 'year').endOf('year').unix() * 1000
    endTime = Moment().subtract(1, 'year').endOf('year').format(TIME_FORMAT)
    startTime = Moment().subtract(1, 'year').startOf('year').format(TIME_FORMAT)
  } else if (rangeName === 'custom') {
    return getRangeForMinutes(24 * 60)
  }
  return {
    startDate,
    endDate,
    startTime,
    endTime,
  }
}

export function canRenderWidgetPreview(widget) {
  if (widget.reportId === 0) {
    if (widget.reportScript === '' || !widget.reportScriptType) {
      return false
    }
    if (widget.executeScript) {
      return true
    } else {
      return false
    }
  }

  const groups = widget.groups
  const keys = (groups || []).map(({ type }) => type)
  if (!keys.length) {
    return false
  }
  if (widget.category === WidgetTypeConstants.SANKEY) {
    const group = groups[0]
    if ((group.counters || []).length) {
      if (group.counters[0].counter && group.counters[0].counter.key) {
        if (group.resultBy && group.resultBy.length > 1) {
          return true
        }
      }
    }
    return false
  }
  for (let key of keys) {
    const group = groups.find(({ type }) => type === key)
    const isEntitiesSelected =
      key === 'alert' ||
      key === 'policy' ||
      key === 'policy.flap' ||
      key === 'policy.stream'
        ? group?.target?.entities?.length > 0
        : group?.counters?.[0]?.target?.entities?.length > 0

    const isFirstCounterSelected =
      (group.counters || []).length &&
      group.counters[0].counter &&
      group.counters[0].counter.key
    if (
      [
        'metric',
        'availability',
        'flow',
        'log',
        'alert',
        'policy',
        'policy.flap',
        'policy.stream',
      ].includes(key)
    ) {
      if (
        [WidgetTypeConstants.FORECAST, WidgetTypeConstants.ANOMALY].includes(
          widget.category
        ) ||
        [AvailableReportCategories.POLLING_REPORT].includes(widget.reportType)
      ) {
        let canRender
        if (
          group.counters.length &&
          group.counters[0].target &&
          group.counters[0].counter &&
          group.counters[0].counter?.key?.indexOf('~') === -1 &&
          group.counters[0].target.entities
        ) {
          canRender = true
        }
        if (
          group.counters.length &&
          group.counters[0].target &&
          group.counters[0].counter &&
          group.counters[0].counter?.key?.indexOf('~') >= 0 &&
          group.counters[0].target.entities
        ) {
          if (group.instance) {
            canRender = true
          }
        }
        return canRender
      }
      if (
        ![
          WidgetTypeConstants.GAUGE,
          WidgetTypeConstants.HEATMAP,
          WidgetTypeConstants.ACTIVE_ALERT,
          WidgetTypeConstants.CHART,
          WidgetTypeConstants.STREAM,
        ].includes(widget.category)
      ) {
        if (['log', 'flow'].includes(key)) {
          if (
            widget.reportType === AvailableReportCategories.EVENT_HISTORY ||
            widget.category === WidgetTypeConstants.EVENT_HISTORY
          ) {
            return true
          }
          if (
            group.resultBy &&
            group.resultBy.length > 0 &&
            isFirstCounterSelected
          ) {
            return true
          }
        } else {
          if (group.resultBy) {
            if (
              isFirstCounterSelected ||
              ['policy', 'policy.flap', 'policy.stream'].includes(key)
            ) {
              if (
                [AvailableReportCategories.AVAILABILITY].includes(
                  widget.reportType
                )
              ) {
                if (
                  isEntitiesSelected &&
                  group?.counters?.[0]?.filterCounter?.length > 0
                ) {
                  return true
                } else {
                  return false
                }
              }
              return true
            } else {
              return false
            }
          }
          if (widget.reportType === AvailableReportCategories.AVAILABILITY) {
            if (
              isEntitiesSelected &&
              group?.counters?.[0]?.filterCounter?.length > 0
            ) {
              return true
            } else {
              return false
            }
          }
        }
        return false
      }
    }
    if ((group.counters || []).length) {
      if (group.counters[0].counter && group.counters[0].counter.key) {
        if (
          [
            AvailableReportCategories.INVENTORY,
            AvailableReportCategories.AVAILABILITY_ALERT,
            AvailableReportCategories.METRIC_ALERT,
            AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
          ].includes(widget.reportType)
        ) {
          if (isEntitiesSelected) {
            return true
          } else {
            return false
          }
        }
        return true
      }
    } else if (
      key === 'alert' ||
      key === 'policy' ||
      key === 'policy.flap' ||
      key === 'policy.stream'
    ) {
      return true
    }
  }
  return false
}

export const DEFAULT_STREAM_CATEGORY_GROUP = {
  metric: {
    type: 'policy.flap',
    category: 'metric',
    join: 'any',
    counters: [
      makeCounter('duration', '__NONE__'),
      makeCounter('object.id', '__NONE__'),

      // makeCounter('', '__NONE__'),
      makeCounter('instance', '__NONE__'),
      makeCounter('severity', '__NONE__'),
      makeCounter('policy.id', '__NONE__'),
      makeCounter('policy.type', '__NONE__'),
      makeCounter('metric', '__NONE__'),
      makeCounter('value', '__NONE__'),
    ].map((c) => {
      return {
        ...c,
        counter: {
          key: c.counter,
        },
      }
    }),
  },
  log: {
    useExternalCounters: true,
    type: 'policy',
    category: 'log',
    join: 'any',
    counters: [
      makeCounter('severity', '__NONE__'),
      makeCounter('event.field', '__NONE__'),
      makeCounter('event.source', '__NONE__'),
      makeCounter('policy.id', '__NONE__'),
    ].map((c) => {
      return {
        ...c,
        counter: {
          key: c.counter,
        },
      }
    }),
  },
  flow: {
    useExternalCounters: true,
    type: 'policy',
    category: 'flow',
    join: 'any',
    counters: [
      makeCounter('severity', '__NONE__'),
      makeCounter('event.field', '__NONE__'),
      makeCounter('event.source', '__NONE__'),
      makeCounter('policy.id', '__NONE__'),
    ].map((c) => {
      return {
        ...c,
        counter: {
          key: c.counter,
        },
      }
    }),
  },
  trap: {
    useExternalCounters: true,
    type: 'policy',
    category: 'trap',
    join: 'any',
    counters: [
      makeCounter('severity', '__NONE__'),
      makeCounter('event.field', '__NONE__'),
      makeCounter('event.source', '__NONE__'),
      makeCounter('policy.id', '__NONE__'),
    ].map((c) => {
      return {
        ...c,
        counter: {
          key: c.counter,
        },
      }
    }),
  },
  [AVAILABLE_GROUP_TYPES.NETROUTE_EVENT]: {
    type: 'policy.flap',
    category: AVAILABLE_GROUP_TYPES.NETROUTE_EVENT,
    join: 'any',
    counters: [
      makeCounter('duration', '__NONE__'),
      makeCounter('netroute.id', '__NONE__'),

      makeCounter('severity', '__NONE__'),
      makeCounter('policy.id', '__NONE__'),
      makeCounter('policy.type', '__NONE__'),
    ].map((c) => {
      return {
        ...c,
        counter: {
          key: c.counter,
        },
      }
    }),
  },
  [AVAILABLE_GROUP_TYPES.NETROUTE_METRIC]: {
    type: 'policy.flap',
    category: AVAILABLE_GROUP_TYPES.NETROUTE_METRIC,
    join: 'any',
    counters: [
      makeCounter('duration', '__NONE__'),
      makeCounter('netroute.id', '__NONE__'),
      makeCounter('object.id', '__NONE__'),

      // makeCounter('', '__NONE__'),
      makeCounter('instance', '__NONE__'),
      makeCounter('severity', '__NONE__'),
      makeCounter('policy.id', '__NONE__'),
      makeCounter('policy.type', '__NONE__'),
      makeCounter('metric', '__NONE__'),
      makeCounter('value', '__NONE__'),
    ].map((c) => {
      return {
        ...c,
        counter: {
          key: c.counter,
        },
      }
    }),
  },
}
export function getDefaultDataForGroup(groupType, formData) {
  if (['alert', 'policy', 'policy.flap'].includes(groupType)) {
    if (formData?.category === WidgetTypeConstants.STREAM) {
      return DEFAULT_STREAM_CATEGORY_GROUP.metric
    } else {
      return {
        type: groupType,
        category: 'metric',
        ...([WidgetTypeConstants.GRID, WidgetTypeConstants.TOPN].includes(
          formData?.category
        )
          ? { resultBy: 'object.id' }
          : {}),
      }
    }
  } else if (['log', 'flow'].includes(groupType)) {
    return {
      type: groupType,
      category: groupType,
      target: {},
      counters: [
        {
          key: generateId(),
          counter: {},
        },
      ],

      ...([
        WidgetTypeConstants.GRID,
        WidgetTypeConstants.TOPN,
        WidgetTypeConstants.SANKEY,
        WidgetTypeConstants.MAP_VIEW,
      ].includes(formData?.category)
        ? {
            resultBy:
              groupType === 'log'
                ? formData?.category === WidgetTypeConstants.MAP_VIEW
                  ? []
                  : ['event.source.type']
                : formData?.category === WidgetTypeConstants.MAP_VIEW
                ? ['destination.country']
                : ['source.ip', 'destination.ip'],
          }
        : {}),
      filters: {
        pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
        post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
      },
    }
  } else if (
    ['metric', 'availability', 'status.flap', 'hourly.status.flap'].indexOf(
      groupType
    ) >= 0
  ) {
    return {
      type: groupType,
      counters: [
        {
          key: generateId(),
          target: {
            ...([
              WidgetTypeConstants.FORECAST,
              WidgetTypeConstants.ANOMALY,
            ].includes(formData?.category) ||
            [AvailableReportCategories.POLLING_REPORT].includes(
              formData?.reportType
            )
              ? { entityType: 'Monitor' }
              : {}),
          },
          counter: {
            ...(formData?.reportType &&
            [
              AvailableReportCategories.AVAILABILITY,
              AvailableReportCategories.AVAILABILITY_ALERT,
              AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY,
            ].includes(formData?.reportType)
              ? { key: 'monitor' }
              : {}),
          },
          aggrigateFn:
            formData?.category === WidgetTypeConstants.HEATMAP ? 'last' : 'avg',
        },
      ],

      ...(([WidgetTypeConstants.GRID, WidgetTypeConstants.TOPN].includes(
        formData?.category
      ) &&
        ![AvailableReportCategories.POLLING_REPORT].includes(
          formData?.reportType
        )) ||
      ([WidgetTypeConstants.HEATMAP].includes(formData?.category) &&
        groupType === 'availability')
        ? { resultBy: 'monitor' }
        : {}),
      filters: {
        pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
        post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
      },
    }
  } else if (groupType === AVAILABLE_GROUP_TYPES.NETROUTE_METRIC) {
    return {
      type: groupType,
      target: {},

      counters: [
        {
          key: generateId(),
          target: {},
          counter: {},
        },
      ],
      ...([WidgetTypeConstants.GRID, WidgetTypeConstants.TOPN].includes(
        formData?.category
      )
        ? { resultBy: 'netroute.id' }
        : {}),
      filters: {
        pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
        post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
      },
    }
  }
  return {
    type: groupType,
    counters: [
      {
        key: generateId(),
      },
    ],
  }
}

export function getWidgetProperties(widgetCategory, override = {}) {
  const commonProperties = {}
  if (
    [
      WidgetTypeConstants.CHART,
      WidgetTypeConstants.ANOMALY,
      WidgetTypeConstants.FORECAST,
    ].includes(widgetCategory)
  ) {
    return Merge(
      {
        ...commonProperties,
        sortingSetting: {
          direction: 'desc',
          topCount: 10,
        },
        styleSetting: {
          rotation: 0,
          legendEnabled: false,
          ellipsisEnabled: false,
          pieDataLabelsEnabled: false,
          xAxisTitle: undefined,
          yAxisTitle: undefined,
          zAxisTitle: undefined,
          lineWidth: 2,
          markerProperty: [
            {
              key: generateId(),
            },
          ],
          ...(widgetCategory === WidgetTypeConstants.CHART
            ? {
                lineStyle: 'solid',
                dashPattern: '3, 3',
                pointsEnabled: false,
                pointSize: 4,
              }
            : {}),
        },
      },
      override
    )
  } else if (
    [
      WidgetTypeConstants.GRID,
      WidgetTypeConstants.TOPN,
      WidgetTypeConstants.STREAM,
      WidgetTypeConstants.ACTIVE_ALERT,
      WidgetTypeConstants.EVENT_HISTORY,
    ].indexOf(widgetCategory) >= 0
  ) {
    return Merge(
      {
        ...commonProperties,
        headerStyle: {
          fontSize: 'small',
        },

        rotation: 0,
        legendEnabled: false,
        ellipsisEnabled: false,
        pieDataLabelsEnabled: false,
        xAxisTitle: undefined,
        yAxisTitle: undefined,
        zAxisTitle: undefined,
        columnSettings: [],
        ...(widgetCategory === WidgetTypeConstants.TOPN
          ? {
              sortingSetting: {
                showSparklineChart: false,
                sparklineChartType: 'sparkline',
                column: undefined,
                direction: 'desc',
                topCount: 10,
              },
              styleSetting: {
                lineWidth: 2,
                lineStyle: 'solid',
                dashPattern: '3, 3',
                pointsEnabled: false,
                pointSize: 4,
              },
              dataLabelEnabled: false,
            }
          : {}),
      },
      override
    )
  } else if (widgetCategory === WidgetTypeConstants.GAUGE) {
    return Merge(
      {
        ...commonProperties,
        fontSize: 'small',
        textAlign: 'left',
        iconName: undefined,
        iconPosition: 'prefix',
        criticalColor: {
          color: '#f04e3e',
          value: 0,
        },
        majorColor: {
          color: '#f58518',
          value: 0,
        },
        warningColor: {
          color: '#f5bc18',
          value: 0,
        },
      },
      override
    )
  } else if (widgetCategory === WidgetTypeConstants.HEATMAP) {
    return Merge({
      ...commonProperties,
      selectedColorPalette: Object.keys(COLOR_PALETTES)[0],
    })
  } else if (widgetCategory === WidgetTypeConstants.FREE_TEXT) {
    return Merge(
      {
        ...commonProperties,
        fontSize: 'auto',
        textAlign: 'center',
        fontColor: 'default',
      },
      override
    )
  }
  return commonProperties
}

export function convertTimeLine(timeline, useCombineDateTime = false) {
  const combineDateTime = (date, time) => {
    const [year, month, day] = date.split('/').map(Number)
    const [hours, minutes, seconds] = time.split(':').map(Number)
    //  new Date(year, monthIndex, day, hours, minutes, seconds, milliseconds) Integer value representing the month, beginning with 0 for January to 11 for December.
    return new Date(year, month - 1, day, hours, minutes, seconds).valueOf()
  }
  return {
    selectedKey: timeline['relative.timeline'],
    ...(timeline['relative.timeline'] === 'custom'
      ? {
          startDate:
            typeof timeline['from.date'] === 'string'
              ? useCombineDateTime &&
                DATE_FORMAT_REJAX.test(timeline['from.date']) &&
                TIME_FORMATE_REJAX.test(timeline['from.time'])
                ? combineDateTime(timeline['from.date'], timeline['from.time'])
                : new Date(timeline['from.date']).valueOf()
              : timeline['from.date'],
          startTime:
            timeline['from.time'] === '23:59:59'
              ? '23:59:00'
              : timeline['from.time'],
          endDate:
            typeof timeline['to.date'] === 'string'
              ? useCombineDateTime &&
                DATE_FORMAT_REJAX.test(timeline['to.date']) &&
                TIME_FORMATE_REJAX.test(timeline['to.time'])
                ? combineDateTime(timeline['to.date'], timeline['to.time'])
                : new Date(timeline['to.date']).valueOf()
              : timeline['to.date'],
          endTime:
            timeline['to.time'] === '23:59:59'
              ? '23:59:00'
              : timeline['to.time'],
        }
      : getRange(timeline.timeline || timeline['relative.timeline'])),
  }
}

export function convertTimeLineOfPolicy(timeline) {
  return {
    selectedKey: 'custom',
    startDate: timeline['from.datetime'],
    startTime: Moment.unix(timeline['from.datetime'] / 1000).format(
      TIME_FORMAT
    ),
    endDate: timeline['to.datetime'],
    endTime: Moment.unix(timeline['to.datetime'] / 1000).format(TIME_FORMAT),
  }
}

export function convertTimeLineForServer(timeline) {
  return {
    'relative.timeline': timeline.selectedKey,
    ...(timeline.selectedKey === 'custom'
      ? {
          'from.date':
            typeof timeline.startDate === 'number'
              ? Moment.unix(timeline.startDate / 1000).format(DATE_FORMAT)
              : timeline.startDate,
          'from.time':
            timeline.startTime === '23:59:00' ? '23:59:59' : timeline.startTime,
          'to.date':
            typeof timeline.endDate === 'number'
              ? Moment.unix(timeline.endDate / 1000).format(DATE_FORMAT)
              : timeline.endDate,
          'to.time':
            timeline.endTime === '23:59:00' ? '23:59:59' : timeline.endTime,
        }
      : {}),
  }
}

function getValueOfCondition(condition) {
  let value = condition['from.value'] || condition.value
  let toValue = condition['to.value']
  // if (condition['comparison.operator'] === 'in') {
  //   value = value.join(',')
  // }
  return {
    value,
    toValue,
  }
}

export function transformConditions(
  conditions,
  isPostFilter = false,
  options = {}
) {
  if (isPostFilter) {
    return {
      condition: conditions.operator || 'and',
      inclusion: conditions.filter || 'include',
      groups:
        (conditions.conditions || []).length > 0
          ? [
              {
                key: generateId(),
                condition: conditions.operator || 'and',
                inclusion: conditions.filter || 'include',
                conditions: (conditions.conditions || []).map((g) => ({
                  operand: options.addAggrigator
                    ? `${g.operand.replace(
                        /[\^\.](last|avg|sum|min|max|count)/,
                        ''
                      )}^last`
                    : g.operand,
                  operator: g.operator,
                  ...getValueOfCondition(g),
                  key: generateId(),
                })),
              },
            ]
          : FILTER_CONDITION_DEFAULT_DATA.groups,
    }
  }

  const hasAdditionalDataInFilter = Object.keys(
    Omit(conditions, ['operator', 'filter', 'conditions', 'groups'])
  ).length
  return {
    condition: conditions.operator || 'and',
    inclusion: conditions.filter || 'include',
    groups: (conditions.groups || []).length
      ? conditions.groups.map((group) => ({
          key: generateId(),
          condition: group.operator,
          inclusion: group.filter,
          conditions: group.conditions.map((g) => ({
            operand: options.addAggrigator
              ? `${g.operand.replace(
                  /[\^\.](last|avg|sum|min|max|count)/,
                  ''
                )}^last`
              : g.operand,
            operator: g.operator,
            ...getValueOfCondition(g),
            key: generateId(),
          })),
        }))
      : FILTER_CONDITION_DEFAULT_DATA.groups,

    ...(hasAdditionalDataInFilter
      ? {
          additionalData: {
            ...Omit(conditions, ['operator', 'filter', 'conditions']),
          },
        }
      : {}),
  }
}

export function transformConditionItemsForServer(condition, options = {}) {
  let value
  let toValue
  if (['=', '>', '<', '>=', '<=', 'between'].indexOf(condition.operator) >= 0) {
    if (condition.dataType && condition.dataType.includes('string')) {
      if (/^\d+$/.test(condition.value)) {
        value = parseInt(condition.value)
      } else if (/^\d+\.\d+$/.test(condition.value)) {
        value = parseFloat(condition.value)
      } else {
        value = condition.value
      }
      if (condition.toValue) {
        if (/^\d+$/.test(condition.toValue)) {
          toValue = parseInt(condition.toValue)
        } else if (/^\d+\.\d+$/.test(condition.toValue)) {
          toValue = parseFloat(condition.toValue)
        } else {
          toValue = condition.toValue
        }
      }
    } else {
      if (/^\d+$/.test(condition.value)) {
        value = parseInt(condition.value)
      } else if (/^\d+\.\d+$/.test(condition.value)) {
        value = parseFloat(condition.value)
      } else {
        value = condition.value
      }
      if (condition.toValue) {
        if (/^\d+$/.test(condition.toValue)) {
          toValue = parseInt(condition.toValue)
        } else if (/^\d+\.\d+$/.test(condition.toValue)) {
          toValue = parseFloat(condition.toValue)
        } else {
          toValue = condition.toValue
        }
      }
    }
  } else if (condition.operator === 'in') {
    if (Array.isArray(condition.value)) {
      value = condition.value
    } else {
      value = (condition.value || '').split(',')
    }
  } else {
    value = condition.value
  }
  return {
    operand: options.removeAggrigator
      ? condition.operand.replace(/[\^\.](last|avg|sum|min|max|count)/, '')
      : condition.operand,
    operator: condition.operator,
    ...(value !== undefined && value !== ''
      ? { [condition.operator === 'between' ? 'from.value' : 'value']: value }
      : { value: 'Any' }),
    ...(toValue !== undefined ? { 'to.value': toValue } : {}),
  }
}

export function transformConditionsForServer(
  conditions,
  isPostFilter = false,
  options = {}
) {
  const groups = (conditions || {}).groups || []
  if (!groups.length) {
    return {}
  }
  const c = groups[0].conditions || []
  if (!c.length || !(c[0].operator && c[0].operand)) {
    return { ...(conditions.additionalData ? conditions.additionalData : {}) }
  }
  if (isPostFilter) {
    return {
      operator: conditions.groups[0].condition,
      filter: conditions.groups[0].inclusion,
      conditions: (conditions.groups[0].conditions || []).map((i) =>
        transformConditionItemsForServer(i, options)
      ),
    }
  }
  return {
    operator: conditions.condition,
    filter: conditions.inclusion,
    groups: conditions.groups.map((group) => ({
      filter: group.inclusion,
      operator: group.condition,
      conditions: group.conditions.map((i) =>
        transformConditionItemsForServer(i, options)
      ),
    })),

    ...(conditions.additionalData ? { ...conditions.additionalData } : {}),
  }
}

export function evaluateExpression(op1, operator, op2) {
  switch (operator) {
    case '>':
      return op1 > op2
    case '<':
      return op1 < op2
    case '<=':
      return op1 <= op2
    case '>=':
      return op1 >= op2
    case '=':
      // eslint-disable-next-line
      return op1 == op2
    case 'contain':
      return String(op1 || '').indexOf(String(op2 || '')) >= 0

    default:
      break
  }
}

export function calculateNewItemPosition(itemSize, widgets, gridColumns) {
  if (!widgets.length) {
    return {
      x: 0,
      y: 0,
    }
  }
  const maxRowItem = MaxBy(widgets, ({ y }) => y)
  const itemsInLastRow = widgets.filter(({ y }) => y === maxRowItem.y)
  const maxColumnItem = MaxBy(itemsInLastRow, ({ x }) => x)
  const availableColumns = gridColumns - (maxColumnItem.x + maxColumnItem.w)
  if (availableColumns >= itemSize.w) {
    return {
      y: maxRowItem.y,
      x: maxColumnItem.x + maxColumnItem.w,
    }
  }
  return {
    y: maxRowItem.y + 1,
    x: 0,
  }
}

export function makeCounter(
  counter,
  aggrigateFn,
  entityType,
  entities,
  entityKeys
) {
  return {
    counter,
    aggrigateFn,
    entityType,
    entities,
    entityKeys,
  }
}

export function buildWidgetContext({
  groupType,
  category = WidgetTypeConstants.GRID,
  widgetType = WidgetTypeConstants.GRID,
  groupCategory,
  counters,
  timeline = { selectedKey: 'today' },
  resultBy,
  preFilters,
  postFilters,
  drillDownFilters,
  granularity,
}) {
  const widgetContext = new WidgetContextBuilder()
  widgetContext.addGroup(groupType).setCategory(category).setTimeline(timeline)

  if (granularity) {
    widgetContext.setGranularity(granularity)
  }

  if (groupCategory) {
    widgetContext.appendToGroup(groupType, { category: groupCategory })
  }

  if (widgetType) {
    widgetContext.setWidgetType(widgetType)
  }
  if (resultBy) {
    widgetContext.addResultBy(Array.isArray(resultBy) ? resultBy : [resultBy])
  }
  if (preFilters) {
    if (preFilters.groups) {
      let preFiltersGroups = preFilters.groups || []
      // for flow-explorer builder
      preFiltersGroups.forEach((group) =>
        widgetContext.addPreFilterGroup(group, preFilters.condition)
      )
    } else {
      // for trap-explorer builder
      widgetContext.addPreFilterGroup(preFilters)
    }
  }
  if (drillDownFilters) {
    if (drillDownFilters.groups) {
      let preFiltersGroups = drillDownFilters.groups || []
      // for flow-explorer builder
      preFiltersGroups.forEach((group) =>
        widgetContext.addDrilldownFilterGroup(group, drillDownFilters.condition)
      )
    } else {
      // for trap-explorer builder
      widgetContext.addDrilldownFilterGroup(drillDownFilters)
    }
  }
  if (postFilters) {
    if (postFilters.groups) {
      let postFiltersGroups = postFilters.groups || []
      postFiltersGroups.forEach((group) =>
        widgetContext.addPostFilterGroup(group)
      )
    } else {
      widgetContext.addPostFilterGroup(postFilters)
    }
  }
  counters.forEach((counter) => widgetContext.addCounterToGroup(counter))

  return widgetContext
}

export function mergeResultsIfNeeded(widget, previousResult, newResult) {
  let result = newResult
  if (widget.widgetType === WidgetTypeConstants.HORIZONTAL_TOPN) {
    result = mergeTopNHorizontalResult(previousResult, newResult)
  }
  if (widget.category === WidgetTypeConstants.CHART) {
    if (widget.isMultiTrendReport) {
      let allResults = {
        ...previousResult,
        ...newResult,
        series: [...(previousResult.series || []), ...(newResult.series || [])],
      }
      let groups = GroupBy(allResults.series || [], 'id')
      result = {
        series: allResults.series,
        isMultiTrendReport: true,
        data: Object.keys(groups || {}).map((key) => {
          return {
            key,
            monitor: groups[key][0].monitor,
            data: {
              series: groups[key],
              groupByColumns: ['id'],
            },
          }
        }),
      }
    } else {
      const newSeriesNames = (newResult.series || []).map((s) =>
        s.name.toLowerCase()
      )
      const previousSeries = (previousResult.series || []).filter(
        (series) => !newSeriesNames.includes(series.name.toLowerCase())
      )
      result = {
        ...previousResult,
        ...newResult,
        series: [...previousSeries, ...(newResult.series || [])],
      }
    }
  } else if (widget.widgetType === WidgetTypeConstants.INTERFACE_GRID) {
    result = buildInterfaceCalculatedGrid(previousResult, newResult)
  } else if (widget.widgetType === WidgetTypeConstants.VLAN_GRID) {
    result = buildVlanCalculatedGrid(previousResult, newResult)
  } else if (widget.widgetType === WidgetTypeConstants.ACCESS_POINT) {
    result = buildArubaAccessPointCalculatedGrid(previousResult, newResult)
  } else if (
    widget.widgetType === WidgetTypeConstants.SWTICH_PORT_MAPPER_GRID
  ) {
    result = buildSwitchPortMapperGrid(previousResult, newResult)
  } else if (
    [
      WidgetTypeConstants.AVAILABILITY_TIME_SERIES,
      WidgetTypeConstants.APPLICATION_AVAILABILITY_TIME_SERIES,
    ].includes(widget.widgetType)
  ) {
    result = buildAvailabilityTimeSeries(previousResult, newResult)
  } else if (widget.widgetType === WidgetTypeConstants.HARDWARE_SENSOR_GRID) {
    result = defaultMerger(previousResult, newResult)
  }
  // else if (widget.widgetType === WidgetTypeConstants.APPLICATION_STATUS) {
  //   return defaultMerger(previousResult, newResult, (item) => item.process)
  // }
  else if (widget.category === WidgetTypeConstants.CUSTOM) {
    if (
      widget.widgetType === WidgetTypeConstants.PIE ||
      widget.widgetType === WidgetTypeConstants.APPLICATION_TODAY_AVAILABILITY
    ) {
      result = mergeAvailabilityPieResult(previousResult, newResult)
    }
  } else if (widget.widgetType === WidgetTypeConstants.STACKED_SWITCH_VIEW) {
    result = buildStackedSwitchGrid(previousResult, newResult)
  }

  previousResult = null
  setTimeout(() => {
    result = null
  }, 10)
  return result
}

export function sortedSeriesData(seriesData, sortBy = 'severityIndex') {
  return SortBy(
    seriesData.map((series) => ({
      ...series,
      ...(sortBy === 'severityIndex'
        ? {
            severityIndex: SEVERITY_TO_CONSIDER.indexOf(
              series.name.toUpperCase()
            ),
          }
        : {}),
    })),
    sortBy
  )
}

export function overrideWidgetPropertyByWidgetCategory(
  widgetCategory,
  widget = {}
) {
  let overRide = {}
  if (widgetCategory === WidgetTypeConstants.STREAM) {
    overRide = {
      columnSettings: [
        ...[
          'policy.name',
          'monitor',
          'object.type',
          'metric',
          'value',
          'policy.type',
          'start.time',
          'instance',
          'duration',
          'severity',
          'policy.id',
          'policy.first.trigger.tick',
          'end.time',
          'object.vendor',
          'object.ip',
        ].map((c, index) => ({
          hidden: [
            'object.vendor',
            'object.type',
            'object.ip',
            'severity',
            'duration',
            'instance',
            'policy.id',
            'policy.type',
            'policy.first.trigger.tick',
            'end.time',
          ].includes(c),
          name: c,
          orderIndex: index,
          orderable: true,
          rawName: c,
          resizable: true,
          searchable: true,
          sortable:
            !['start.time', 'end.time'].includes(c) && !widget.reportType,
          disable: ['event.id', 'severity'].includes(c),
          displayName: ['object.id'].includes(c) ? 'Monitor' : undefined,
        })),
      ],
    }
  } else if (
    widgetCategory === WidgetTypeConstants.ACTIVE_ALERT ||
    widget?.reportType === AvailableReportCategories.ACTIVE_ALERTS
  ) {
    overRide = {
      columnSettings: [
        ...[
          'policy.name',
          'monitor',
          'object.type',
          'metric',
          'value',
          'policy.type',
          'start.time',
          'instance',
          'duration',
          'severity',
          'policy.id',
          'previous.flap.timestamp',
          'end.time',
          'policy.note',
          'object.vendor',
          'object.ip',
          'policy.acknowledge.time',
          'policy.acknowledge.by',
          'acknowledge',
          'event.timestamp',
          'object.creation.time',
        ].map((c, index) => ({
          hidden: [
            'object.vendor',
            'object.type',
            'object.ip',
            'severity',
            'duration',
            'instance',
            'policy.id',
            'policy.type',
            'policy.first.trigger.tick',
            'end.time',
            'policy.note',
            'policy.acknowledge.time',
            'policy.acknowledge.by',
            'acknowledge',
            'event.timestamp',
            'event.id.value',
            'object.creation.time',
          ].includes(c),
          name: c,
          orderIndex: index,
          orderable: true,
          rawName: c,
          resizable: true,
          searchable: true,
          sortable:
            !['start.time', 'end.time'].includes(c) && !widget.reportType,
          disable: ['event.id', 'severity', 'event.id.value'].includes(c),
          displayName: ['timestamp'].includes(c)
            ? 'Active Since'
            : ['event.timestamp'].includes(c)
            ? 'last triggered time'
            : ['object.creation.time'].includes(c)
            ? 'Discovered Time'
            : undefined,

          ...(['policy.acknowledge.time'].includes(c)
            ? { cellRender: 'ms_datetime' }
            : {}),
          ...(['event.timestamp', 'object.creation.time'].includes(c)
            ? { cellRender: 'datetime' }
            : {}),
        })),
      ],
    }
  } else if (
    widget?.reportType === AvailableReportCategories.AVAILABILITY_ALERT ||
    widget?.reportType === AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY
  ) {
    overRide = {
      columnSettings: [
        ...[
          'instance',
          'monitor',
          'status.flap.history',
          ...(widget?.reportType ===
          AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY
            ? ['interface~status.flap.history']
            : []),
          'start.time',
          'end.time',
          ...(widget?.reportType ===
          AvailableReportCategories.AVAILABILITY_FLAP_SUMMARY
            ? ['interface~duration']
            : []),

          'duration',
          'object.type',
          'object.vendor',
          'object.ip',
        ].map((c, index) => ({
          hidden: [
            ...(widget?.reportType ===
            AvailableReportCategories.AVAILABILITY_ALERT
              ? ['status.flap.history']
              : []),
            'object.type',
            'object.vendor',
            'object.ip',
          ].includes(c),
          name: c,
          orderIndex: index,
          orderable: true,
          rawName: c,
          resizable: true,
          searchable: true,
          sortable: !['start.time', 'end.time'].includes(c),
          disable: false,
          displayName: undefined,
        })),
      ],
    }
  } else if (widget?.reportType === AvailableReportCategories.AVAILABILITY) {
    overRide = {
      columnSettings: [
        ...[
          'monitor',
          'network.service',
          'network.service.uptime.seconds.sum',
          'network.service.uptime.percent.avg',
          'network.service.downtime.percent.avg',
          'network.service.downtime.seconds.sum',
          'network.service.maintenancetime.percent.avg',
          'network.service.unreachabletime.percent.avg',
          'network.service.disabletime.percent.avg',
          'network.service.suspendtime.percent.avg',
          'network.service.unknowntime.percent.avg',
          'network.service.maintenancetime.seconds.sum',
          'network.service.unreachabletime.seconds.sum',
          'network.service.disabletime.seconds.sum',
          'network.service.suspendtime.seconds.sum',
          'network.service.unknowntime.seconds.sum',
        ].map((c, index) => ({
          hidden: [
            'network.service.maintenancetime.percent.avg',
            'network.service.unreachabletime.percent.avg',
            'network.service.disabletime.percent.avg',
            'network.service.suspendtime.percent.avg',
            'network.service.unknowntime.percent.avg',
            'network.service.maintenancetime.seconds.sum',
            'network.service.unreachabletime.seconds.sum',
            'network.service.disabletime.seconds.sum',
            'network.service.suspendtime.seconds.sum',
            'network.service.unknowntime.seconds.sum',
          ].includes(c),
          name: c,
          orderIndex: index,
          orderable: true,
          rawName: c,
          resizable: true,
          searchable: true,
          sortable: true,
          disable: false,
          displayName: undefined,
        })),
      ],
    }
  } else if (widget.layout === WidgetTypeConstants.KEY_VALUE_LAYOUT) {
    overRide = { layout: WidgetTypeConstants.KEY_VALUE_LAYOUT }
  } else if (widgetCategory === WidgetTypeConstants.GRID) {
    overRide = { layout: WidgetTypeConstants.GRID }
  }
  return overRide
}
export function additionalGroupForStreamWidget(widgetCategory, group) {
  if (widgetCategory === WidgetTypeConstants.STREAM) {
    const counters = [
      { key: 'instance', aggrigateFn: 'last' },
      { key: 'policy.id', aggrigateFn: 'last' },
      { key: 'policy.first.trigger.tick', aggrigateFn: 'last' },
      { key: 'object.id', aggrigateFn: 'last' },
    ]
    return [
      {
        type: 'policy.trigger.tick',
        category: 'metric',
        'visualization.result.by': [],
        filters: { 'data.filter': {} },

        ...(group.alertIds ? { policies: group.alertIds } : {}),
        ...(group.tags ? { tags: group.tags } : {}),
        ...(group.severity && (group.severity || []).length > 0
          ? { severity: group.severity }
          : {}),

        'data.points': counters.map((c) => ({
          'data.point': c.key,
          aggregator: c.aggrigateFn === '__NONE__' ? '' : c.aggrigateFn,
          'entity.type': (group.target || {}).entityType,
          entities: (group.target || {}).entities,
        })),
      },
    ]
  }
}

export const additionalDataForCategory = {
  [WidgetTypeConstants.STREAM]: {
    'join.alias': true,
    'join.type': 'any',
    'join.columns': ['policy.id', 'instance', 'object.id'],
    'join.result': 'policy.stream',
    'join.replacable.columns': {
      'policy.id': 'policy.id^value',
      instance: 'instance^value',
      'object.id': 'object.id^value',
    },
    'sorting.column': 'Timestamp',
  },
  tag: {
    'join.type': 'custom',
    'join.result': 'tag',
    'join.columns': ['monitor'],
  },
  [AvailableReportCategories.EVENT_HISTORY]: {
    'join.type': 'custom',
    'join.result': 'log.event',
    cache: true,
  },
  'event.stream': {
    'join.alias': true,

    'join.type': 'custom',
    'join.result': 'policy.stream',
    'drill.down': 'yes',
    'max.records': 10000,
  },
  [AvailableReportCategories.AVAILABILITY]: {
    'join.type': 'custom',
    'join.result': 'instance.ip',
    'join.columns': ['monitor'],
  },
}

export const additionalDataForGroup = {
  tag: {
    'join.type': 'custom',
  },
  [AvailableReportCategories.EVENT_HISTORY]: {
    'join.type': 'custom',
  },
}
export function getWidgetGroupByChartType(
  newCategory,
  oldCategory,
  widget = {}
) {
  const widgetNewCategory = newCategory
  const hasCounterInWidget =
    widget?.groups?.[0]?.counters?.[0]?.counter?.counterName

  const numericCounters =
    widget?.groups?.[0]?.counters?.filter((c) =>
      c?.counter?.dataType?.includes('numeric')
    ) || []
  const firstGroup = widget?.groups?.[0]
  const groupTypeWiseResultBy = {
    metric: 'monitor',
    log: ['event.source.type'],
    flow: ['source.ip'],
    availability: 'monitor',
    policy: 'object.id',
    [AVAILABLE_GROUP_TYPES.NETROUTE_METRIC]: 'netroute.id',
  }
  function shouldFillCounters() {
    if (
      firstGroup.type === 'availability' &&
      (newCategory === WidgetTypeConstants.GAUGE ||
        oldCategory === WidgetTypeConstants.GAUGE)
    ) {
      return false
    } else if (
      firstGroup.type === 'metric' &&
      oldCategory === WidgetTypeConstants.GRID &&
      !numericCounters.length
    ) {
      return false
    }
    return true
  }

  if (
    oldCategory === WidgetTypeConstants.STREAM &&
    ![
      WidgetTypeConstants.ANOMALY,
      WidgetTypeConstants.FORECAST,
      WidgetTypeConstants.SANKEY,
      WidgetTypeConstants.MAP_VIEW,
      WidgetTypeConstants.EVENT_HISTORY,
    ].includes(newCategory)
  ) {
    return [
      {
        type: 'policy',
        category: 'metric',
        ...([WidgetTypeConstants.GRID, WidgetTypeConstants.TOPN].includes(
          widgetNewCategory
        )
          ? {
              resultBy: groupTypeWiseResultBy['policy'],
            }
          : {}),
      },
    ]
  }

  let groups = []
  if (
    [
      WidgetTypeConstants.CHART,
      WidgetTypeConstants.GRID,
      WidgetTypeConstants.TOPN,
      WidgetTypeConstants.GAUGE,
    ].includes(widgetNewCategory)
  ) {
    groups = firstGroup
      ? [
          {
            ...firstGroup,
            ...(firstGroup.type !== 'policy'
              ? {
                  counters: [
                    ...(shouldFillCounters() && hasCounterInWidget
                      ? [
                          {
                            ...(firstGroup?.counters?.find((c) => {
                              if (
                                [WidgetTypeConstants.GRID].includes(
                                  widgetNewCategory
                                )
                              ) {
                                return true
                              } else {
                                return (
                                  c?.counter?.dataType?.includes('numeric') ||
                                  ['log', 'flow'].includes(firstGroup.type)
                                )
                              }
                            }) || {}),
                            target: undefined,
                            key: generateId(),
                            arithmeticOperation: undefined,
                          },
                        ]
                      : [
                          {
                            key: generateId(),
                            counter: {},
                          },
                        ]),
                  ],
                }
              : {
                  ...([WidgetTypeConstants.GAUGE].includes(widgetNewCategory)
                    ? { resultBy: undefined }
                    : {}),
                }),

            ...([WidgetTypeConstants.GRID, WidgetTypeConstants.TOPN].includes(
              widgetNewCategory
            )
              ? {
                  resultBy:
                    firstGroup?.type === 'policy' &&
                    ['log', 'flow', 'trap'].includes(
                      widget?.groups?.[0]?.category
                    )
                      ? 'severity'
                      : [
                          AVAILABLE_GROUP_TYPES.NETROUTE_EVENT,
                          AVAILABLE_GROUP_TYPES.NETROUTE_METRIC,
                        ].includes(firstGroup?.category)
                      ? groupTypeWiseResultBy[
                          AVAILABLE_GROUP_TYPES.NETROUTE_METRIC
                        ]
                      : groupTypeWiseResultBy[firstGroup.type.toLowerCase()],
                }
              : [WidgetTypeConstants.GAUGE].includes[widgetNewCategory]
              ? {
                  resultBy: undefined,
                }
              : {}),
          },
        ]
      : [
          {
            type: 'metric',
            counters: [
              {
                key: generateId(),
                target: {},
                counter: {},
                aggrigateFn: 'avg',
                arithmeticOperation: undefined,
              },
            ],
            filters: {
              pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
              post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
            },

            ...([WidgetTypeConstants.GRID, WidgetTypeConstants.TOPN].includes(
              widgetNewCategory
            )
              ? { resultBy: 'monitor' }
              : {}),
          },
        ]
  } else if (
    [WidgetTypeConstants.HEATMAP, WidgetTypeConstants.ACTIVE_ALERT].includes(
      widgetNewCategory
    )
  ) {
    groups = [{ type: 'policy', category: 'metric' }]
  } else if (
    widgetNewCategory === WidgetTypeConstants.SANKEY ||
    widgetNewCategory === WidgetTypeConstants.MAP_VIEW
  ) {
    groups = [
      {
        type: 'flow',
        category: 'flow',
        target: {},
        counters: [
          {
            key: generateId(),
            counter: {
              key: 'volume.bytes',
              counterName: 'volume.bytes',
              name: 'volume.bytes',
              isStatusCounter: false,
            },
            aggrigateFn: 'avg',
            arithmeticOperation: undefined,
          },
        ],
        filters: {
          pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
          post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
        },
        resultBy:
          widgetNewCategory === WidgetTypeConstants.SANKEY
            ? ['source.ip', 'destination.ip']
            : ['destination.country'],
      },
    ]
  } else if (widgetNewCategory === WidgetTypeConstants.STREAM) {
    // const counters = [
    //   'duration',
    //   'object.id',
    //   'instance',
    //   'severity',
    //   'policy.id',
    //   'policy.type',
    //   'metric',
    //   'value',
    // ]

    groups = [
      {
        type: 'policy.flap',
        category: 'metric',
        join: 'any',
        counters: [
          makeCounter('duration', '__NONE__'),
          makeCounter('object.id', '__NONE__'),
          makeCounter('instance', '__NONE__'),
          makeCounter('severity', '__NONE__'),
          makeCounter('policy.id', '__NONE__'),
          makeCounter('policy.type', '__NONE__'),
          makeCounter('metric', '__NONE__'),
          makeCounter('value', '__NONE__'),
        ].map((c) => {
          return {
            ...c,
            counter: {
              key: c.counter,
            },
            arithmeticOperation: undefined,
          }
        }),
      },
    ]
  } else if (
    [WidgetTypeConstants.ANOMALY, WidgetTypeConstants.FORECAST].includes(
      widgetNewCategory
    )
  ) {
    groups = [
      {
        type: 'metric',
        counters:
          hasCounterInWidget &&
          numericCounters.length &&
          firstGroup.type === 'metric'
            ? [
                {
                  ...numericCounters[0],
                  target: {
                    entityType: 'Monitor',
                  },
                  arithmeticOperation: undefined,
                },
              ]
            : [
                {
                  key: generateId(),
                  target: {
                    entityType: 'Monitor',
                  },
                  counter: {},
                  aggrigateFn: 'avg',
                  arithmeticOperation: undefined,
                },
              ],
        filters: {
          pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
          post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
        },
      },
    ]
  } else if (widgetNewCategory === WidgetTypeConstants.EVENT_HISTORY) {
    groups = [
      {
        type: 'log',
        counters: [
          {
            key: generateId(),
            target: {},
            counter: {},
            arithmeticOperation: undefined,
          },
        ],
        filters: {
          pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
          post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
        },
      },
    ]
  }
  return groups
}

export const SERIES_COLOR_MAP = {
  // firewall: `var(--secondary-green)`,
  // router: `var(--secondary-yellow)`,
  // switch: `var(--primary)`,
  fail: `var(--severity-down)`,
  'conflict detected': `var(--severity-down)`,
  succeed: `var(--secondary-green)`,
  'in sync': `var(--secondary-green)`,
  'not applicable': `var(--primary)`,
}

export const CONFIG_SERIES_NAME = {
  [Constants.EVENT_SUCCESS_STATUS]: 'Successful',
  [Constants.EVENT_FAIL_STATUS]: 'Failed',
  'conflict detected': 'Conflict',
  'in sync': 'In-sync',
}

export const WIDGET_CATEGORY_MAP = [
  {
    displayChartText: 'Graph',
    key: 'graph',

    children: [
      {
        text: 'Chart',
        key: WidgetTypeConstants.CHART,

        defaultData: {
          category: WidgetTypeConstants.CHART,
          widgetType: WidgetTypeConstants.AREA,
        },
      },
      {
        text: 'Top N',
        key: WidgetTypeConstants.TOPN,
        defaultData: {
          category: WidgetTypeConstants.TOPN,
          widgetType: WidgetTypeConstants.AREA,
        },
      },

      {
        text: 'Gauge',
        key: WidgetTypeConstants.GAUGE,
        defaultData: {
          category: WidgetTypeConstants.GAUGE,
          widgetType: WidgetTypeConstants.SOLID_GAUGE,
        },
      },
      {
        text: 'Grid',
        key: WidgetTypeConstants.GRID,
        defaultData: {
          category: WidgetTypeConstants.GRID,
          widgetType: WidgetTypeConstants.GRID,
        },
      },
      {
        text: 'Pie',
        key: WidgetTypeConstants.PIE,
        defaultData: {
          category: WidgetTypeConstants.TOPN,
          widgetType: WidgetTypeConstants.PIE,
        },
      },
      {
        text: 'Query Value',
        key: WidgetTypeConstants.METRO_TILE,
        defaultData: {
          category: WidgetTypeConstants.GAUGE,
          widgetType: WidgetTypeConstants.METRO_TILE,
          key: WidgetTypeConstants.METRO_TILE,
        },
      },
      {
        text: 'Numeric Grid',
        key: WidgetTypeConstants.KEY_VALUE_LAYOUT,
        defaultData: {
          category: WidgetTypeConstants.GRID,
          widgetType: WidgetTypeConstants.KEY_VALUE_LAYOUT,
          layout: WidgetTypeConstants.KEY_VALUE_LAYOUT,
          key: WidgetTypeConstants.KEY_VALUE_LAYOUT,
        },
      },
      {
        text: 'Sankey',
        key: WidgetTypeConstants.SANKEY,
        defaultData: {
          category: WidgetTypeConstants.SANKEY,
        },
      },
    ],
  },
  {
    displayChartText: 'Alert / Availability',
    key: 'Alert / Availability',

    children: [
      {
        text: 'Heat Map',
        key: WidgetTypeConstants.HEATMAP,
        defaultData: {
          category: WidgetTypeConstants.HEATMAP,
          widgetType: WidgetTypeConstants.HEATMAP_PLAIN,
        },
      },
      {
        text: 'Stream',
        key: WidgetTypeConstants.STREAM,
        defaultData: {
          category: WidgetTypeConstants.STREAM,
          widgetType: WidgetTypeConstants.GRID,
        },
      },

      {
        text: 'Active Alert',
        key: WidgetTypeConstants.ACTIVE_ALERT,
        defaultData: {
          category: WidgetTypeConstants.ACTIVE_ALERT,
        },
      },
    ],
  },
  {
    displayChartText: 'Map',
    key: 'Map',
    licensePermissions: [Constants.USE_PREMIUM_WIDGET_CATEGORIES],

    children: [
      {
        text: 'Tree Map',
        key: WidgetTypeConstants.TREE_VIEW,
        defaultData: {
          category: WidgetTypeConstants.MAP_VIEW,
          widgetType: WidgetTypeConstants.TREE_VIEW,
        },
      },

      {
        text: 'Map',
        key: WidgetTypeConstants.MAP_VIEW,
        defaultData: {
          category: WidgetTypeConstants.MAP_VIEW,
          widgetType: WidgetTypeConstants.MAP_VIEW,
        },
      },
    ],
  },
  {
    displayChartText: 'AI / ML',
    key: 'AI / ML',

    licensePermissions: [Constants.ANOMALY_OUTLINER_FORECAST],

    children: [
      {
        text: 'Anomaly',
        key: WidgetTypeConstants.ANOMALY,
        defaultData: {
          category: WidgetTypeConstants.ANOMALY,
        },
        licensePermissions: [Constants.ANOMALY_LICENSE_PERMISSION],
      },

      {
        text: 'Forecast',
        key: WidgetTypeConstants.FORECAST,
        defaultData: {
          category: WidgetTypeConstants.FORECAST,
        },
        licensePermissions: [Constants.FORECAST_LICENSE_PERMISSION],
      },
    ],
  },
  {
    displayChartText: 'Event History',
    key: 'Event History',
    children: [
      {
        text: 'Event History',
        key: WidgetTypeConstants.EVENT_HISTORY,
        defaultData: {
          category: WidgetTypeConstants.EVENT_HISTORY,
          widgetType: WidgetTypeConstants.GRID,
        },
      },
    ],
  },
  {
    displayChartText: 'Text and Inserts',
    key: 'Text and Inserts',
    children: [
      {
        text: 'Free Text',
        key: WidgetTypeConstants.FREE_TEXT,
        defaultData: {
          category: WidgetTypeConstants.FREE_TEXT,
        },
      },
    ],
  },
]

export function calculateLastTimeline(last, unit) {
  return {
    startDate: Moment().subtract(last, unit).startOf(unit).unix() * 1000,
    endDate: Moment().unix() * 1000,
    endTime: Moment().format(TIME_FORMAT),
    startTime: Moment().subtract(last, unit).startOf(unit).format(TIME_FORMAT),
    selectedKey: 'custom',
  }
}

export function uncompressSnappyResponse(response) {
  if (response && response !== '') {
    return new TextDecoder('utf-8').decode(
      SnappyJs.uncompress(Buffer.from(response, 'base64'))
    )
  }
  return undefined
}

export function isUnixTimeInSeconds(timestamp) {
  return timestamp < 1e12
}

export function buildWidgetForTheGaugeDrilldown(drilldownContext = {}) {
  const drilldownName = drilldownContext.drilldownSeries?.name?.toLowerCase()
  let widget = CloneDeep(drilldownContext.widget)

  let filteredCounters

  if (drilldownContext.type === 'availability') {
    filteredCounters = widget.groups[0].counters.filter((counter) =>
      counter.counter.key.toLowerCase().includes(drilldownName)
    )
    widget.category = WidgetTypeConstants.GRID
    widget.widgetType = WidgetTypeConstants.GRID
    widget.groups[0].counters = filteredCounters
  }

  if (drilldownContext.type === 'policy') {
    widget.category = WidgetTypeConstants.ACTIVE_ALERT
    widget.widgetType = WidgetTypeConstants.GRID
    widget.groups[0].severity = Uniq([drilldownName.toUpperCase()])
    // if (drilldownContext.forGroupTemplate) {
    //   widget.groups[0].target = {
    //     entityType: 'Monitor',
    //     entities: [],
    //   }

    //   widget.groups[0].excludeSeverityResultBy = true
    //   widget.groups[0] = {
    //     useExternalCounters: true,
    //     ...widget.groups[0],
    //     counters: [
    //       {
    //         counter: { key: 'object.category' },
    //         aggrigateFn: 'count',
    //       },
    //       {
    //         counter: { key: 'severity' },
    //         aggrigateFn: 'count',
    //       },
    //     ],
    //   }
    // }
  }

  widget.id = -1

  return { widget, filteredCounters }
}

export function convertGridResultToGauge(
  data,
  severityKey = 'severity',
  severityMap
) {
  const groupBySeverity = GroupBy(data || [], severityKey)

  return {
    categories: Object.keys(groupBySeverity),
    series: [
      {
        data: sortedSeriesData(
          Object.keys(groupBySeverity).map((key) => {
            return {
              name: key,
              y: (groupBySeverity[key] || []).length,

              ...(severityMap
                ? {
                    index: Object.values(severityMap).findIndex(
                      (s) => s === key
                    ),
                  }
                : {}),
            }
          }),
          severityMap ? 'index' : undefined
        ),
      },
    ],
  }
}

export const DEFAULT_ARITHMETIC_OPERATION_OPTIONS = [
  {
    key: 'smoothing',
    name: 'Smoothing',
    groupName: 'Smoothing',
    id: 'smoothing',
  },
  {
    key: 'ema3',
    name: 'Moving Average 3',
    groupName: 'Smoothing',
    parentId: 'smoothing',
    id: 'ema3',
  },
  {
    key: 'ema5',
    name: 'Moving Average 5',
    groupName: 'Smoothing',
    parentId: 'smoothing',
    id: 'ema5',
  },
  {
    key: 'ema7',
    name: 'Moving Average 7',
    groupName: 'Smoothing',
    parentId: 'smoothing',
    id: 'ema7',
  },
  {
    key: 'ema10',
    name: 'Moving Average 10',
    groupName: 'Smoothing',
    parentId: 'smoothing',
    id: 'ema10',
  },
  {
    key: 'ema20',
    name: 'Moving Average 20',
    groupName: 'Smoothing',
    parentId: 'smoothing',
    id: 'ema20',
  },
  {
    key: 'median3',
    name: 'Median 3',
    groupName: 'Smoothing',
    parentId: 'smoothing',
    id: 'median3',
  },
  {
    key: 'median5',
    name: 'Median 5',
    groupName: 'Smoothing',
    parentId: 'smoothing',
    id: 'median5',
  },
  {
    key: 'median7',
    name: 'Median 7',
    groupName: 'Smoothing',
    parentId: 'smoothing',
    id: 'median7',
  },
  {
    key: 'median9',
    name: 'Median 9',
    groupName: 'Smoothing',
    parentId: 'smoothing',
    id: 'median9',
  },
  {
    key: 'Delta',
    name: 'Delta',
    groupName: 'Delta',
    id: 'Delta',
  },
  {
    key: 'delta',
    name: 'Value Difference',
    groupName: 'Delta',
    parentId: 'Delta',
    id: 'delta',
  },
  {
    key: 'monotonic',
    name: 'Monotonic Difference',
    groupName: 'Delta',
    parentId: 'Delta',
    id: 'monotonic',
  },
  {
    key: 'arithmetic',
    name: 'Arithmetic',
    groupName: 'Arithmetic',
    id: 'arithmetic',
  },
  {
    key: 'log2',
    name: 'Log 2',
    groupName: 'Arithmetic',
    parentId: 'arithmetic',
    id: 'log2',
  },
  {
    key: 'log10',
    name: 'Log 10',
    groupName: 'Arithmetic',
    parentId: 'arithmetic',
    id: 'log10',
  },
  {
    key: 'cumulative.sum',
    name: 'Cumulative Sum',
    groupName: 'Arithmetic',
    parentId: 'arithmetic',
    id: 'cumulative.sum',
  },
]

export const DEFAULT_ARITHMETIC_OPERATION_OPTIONS_NAME_MAP =
  DEFAULT_ARITHMETIC_OPERATION_OPTIONS.reduce(
    (acc, o) => ({ ...acc, [o.key]: o.name }),
    {}
  )
