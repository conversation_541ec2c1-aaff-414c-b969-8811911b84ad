<template>
  <div
    ref="scrollableExportRef"
    class="min-h-0 flex flex-col flex-1 w-full m-2"
    style="background: var(--page-background-color)"
  >
    <div class="mr-2 flex justify-between">
      <MInput v-model="searchTerm" class="search-box" placeholder="Search">
        <template v-slot:prefix>
          <MIcon name="search" />
        </template>
        <template v-if="searchTerm" v-slot:suffix>
          <MIcon
            name="times-circle"
            class="text-neutral-light cursor-pointer"
            @click="searchTerm = undefined"
          />
        </template>
      </MInput>
      <MButton
        v-if="!fullscreen"
        variant="neutral-lightest"
        class="mr-2 squared-button"
        @click="showFilter = !showFilter"
      >
        <MIcon name="filter" />
      </MButton>
    </div>
    <div v-if="showFilter && !fullscreen" class="px-4 my-2 pr-6">
      <Filters
        v-model="filters"
        @change="applyFilter"
        @hide="showFilter = !showFilter"
      />
    </div>
    <MGrid
      :key="fullscreen"
      class="min-w-0"
      :search-term="searchTerm"
      :columns="columns"
      :data="rows"
      :filters="appliedFilters"
      :paging="!fullscreen"
    >
      <template v-slot:policy_name="{ item }">
        <div class="flex items-center">
          <Severity
            disable-tooltip
            :severity="item.severity"
            :center="false"
            class="mr-1"
          />
          <AlertDrilldown :alert="item || {}" :field="item.policy_name" />
        </div>
      </template>
      <template v-slot:value="{ item }">
        <MStatusTag
          v-if="(item.metric || '').includes('status')"
          :status="item.value"
        />
        <MTag
          v-else
          :closable="false"
          rounded
          class="tag-primary text-ellipsis cursor-auto"
          style="max-width: 200px"
          :title="item.value"
          >{{ item.value }}</MTag
        >
      </template>
      <template v-slot:metric="{ item }">
        {{ (item.metric || '').replace(/[~^]/g, '.') }}
      </template>

      <template v-slot:lastSeen="{ item }">
        {{ item.policy_last_trigger_tick | datetime }}
      </template>
      <template v-slot:duration="{ item }">
        {{ item.duration | duration }}
      </template>
      <template v-slot:incidentDetails="{ item }">
        <a class="text-primary" @click="onClickIncidentDetails(item)">
          {{ ((item.ack_id || '').split('#') || [])[0] || '' }}
          <MIcon v-if="item.ack_id" name="external-link"></MIcon>
        </a>
      </template>
    </MGrid>
    <IncidentDetailsDrawer
      v-if="showIncidentDetailsItem !== null"
      :incident-item="showIncidentDetailsItem"
      @hide="showIncidentDetailsItem = null"
    />
  </div>
</template>

<script>
import Filters from '../components/filters.vue'
import Severity from '@components/severity.vue'
import AlertDrilldown from '@components/widgets/views/grid/view-more/alert-drilldown.vue'
import IncidentDetailsDrawer from '../../alert/components/stream/incident-details-drawer.vue'

export default {
  name: 'PolicyTemplate',
  components: {
    Filters,
    Severity,
    AlertDrilldown,
    IncidentDetailsDrawer,
  },
  inject: { policyGridContext: { default: { data: {} } } },
  props: {
    fullscreen: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      gridKey: 1,
      showFilter: false,
      filters: {},
      searchTerm: undefined,
      showIncidentDetailsItem: null,
    }
  },

  computed: {
    gridData() {
      return this.policyGridContext.data
    },
    policyWidget() {
      return this.policyGridContext.policyWidget.getContext()
    },
    rows() {
      return ((this.policyGridContext || {}).data || {}).rows || []
    },
    columns() {
      return [
        {
          key: 'policy_name',
          name: 'Policy Name',
          searchable: true,
          sortable: true,
        },
        {
          key: 'policy_type',
          name: 'Type',
          searchable: true,
          sortable: true,
        },
        {
          key: 'metric',
          name: 'Metric',
          searchable: true,
          sortable: true,
        },
        {
          name: 'Instance',
          key: 'instance',
          searchable: true,
          sortable: true,
        },
        {
          key: 'value',
          name: 'Value',
          searchable: true,
          sortable: true,
        },
        {
          key: 'ack_id',
          name: 'Incident Details',
          searchable: true,
          sortable: true,
          cellRender: 'incidentDetails',
        },
        {
          key: 'policy_last_trigger_tick',
          name: 'Last Seen',
          searchable: true,
          sortable: true,
          cellRender: 'lastSeen',
        },
        {
          key: 'duration',
          name: 'Duration',
          searchable: true,
          sortable: true,
        },
        {
          key: 'policy_note',
          name: 'Note',
          searchable: true,
          sortable: true,
        },
      ]
    },
    appliedFilters() {
      let filters = []
      const value = this.filters

      if (value.policies && value.policies.length) {
        filters = [
          ...(filters || []),
          {
            field: 'policy_id',
            operator: 'array_contains',
            value: value.policies,
          },
        ]
      }
      if (value.metrics && value.metrics.length) {
        filters = [
          ...(filters || []),
          {
            field: 'metric',
            operator: 'array_contains',
            value: value.metrics,
          },
        ]
      }

      if (value.severities && value.severities.length) {
        filters = [
          ...(filters || []),
          {
            field: 'severity',
            operator: 'array_contains',
            value: value.severities,
          },
        ]
      }
      return filters
    },
  },
  watch: {
    'policyGridContext.filteredSevertiy': {
      immediate: true,
      handler(newValue) {
        this.filters = {
          ...this.filters,
          severities: newValue,
        }
      },
    },
  },
  methods: {
    applyFilter(filter) {
      const applyFilterSeverities = filter.severities || []
      this.showFilter = false
      if (applyFilterSeverities.length) {
        applyFilterSeverities.forEach((severity) => {
          this.policyGridContext.updateFilteredSevertiy(severity, true)
        })
      } else {
        this.policyGridContext.resetFilter()
      }
    },
    getScrollContainer() {
      if (this.$refs.scrollableExportRef) {
        return this.$refs.scrollableExportRef
      }
    },
    onClickIncidentDetails(item) {
      this.showIncidentDetailsItem = { ...item, incidentDetails: item.ack_id }
    },
  },
}
</script>
