<template>
  <GroupProvider>
    <div class="flex flex-1 min-h-0 flex-col min-w-0">
      <div class="flex flex-1 min-h-0 flex-col widget-view __panel">
        <div class="flex justify-between items-center">
          <div
            class="pl-2 min-w-0 text-ellipsis flex-1 flex font-500 items-center my-2 text-xs"
          >
            {{ title }}
          </div>
        </div>

        <div class="flex flex-1 min-h-0 flex-col overflow-auto w-full">
          <FlotoContentLoader :loading="loading">
            <div
              v-if="gridData.length"
              class="flex flex-1 h-full w-full min-h-0 min-w-0 relative flex-col"
            >
              <div class="my-2 mx-2 flex justify-between">
                <MInput
                  v-if="isSearchable"
                  v-model="searchTerm"
                  class="search-box"
                  placeholder="Search"
                  name="search"
                >
                  <template v-slot:prefix>
                    <MIcon name="search" />
                  </template>
                  <template v-if="searchTerm" v-slot:suffix>
                    <MIcon
                      name="times-circle"
                      class="text-neutral-light cursor-pointer"
                      @click="searchTerm = undefined"
                    />
                  </template>
                </MInput>
              </div>

              <div class="flex flex-col flex-1 min-h-0 w-full relative min-w-0">
                <div
                  class="flex flex-1 min-h-0 dashboard-widget-grid w-full min-w-0"
                >
                  <VirtualTable
                    :columns="columns"
                    :data="gridData"
                    :paging="false"
                    style="height: 100%; margin-top: 0"
                    :search-term="searchTerm"
                  >
                    <template v-slot:type="{ props }">
                      <div class="flex items-center">
                        <MonitorType
                          :type="props.dataItem[props.field]"
                          class="mx-1 inline-flex"
                        />
                        {{ props.dataItem[props.field] }}
                      </div>
                    </template>

                    <template v-slot:ip="{ props }">
                      <MonitorName
                        :value="props.dataItem[props.field]"
                        :row="props.dataItem"
                      />
                    </template>
                    <template v-slot:duration="{ props }">
                      {{ props.dataItem[props.field] | duration }}
                    </template>
                    <template v-slot:datetime="{ props }">
                      {{ props.dataItem[props.field] | datetime }}
                    </template>
                    <template v-slot:groups="{ props }">
                      <GroupPicker
                        :value="props.dataItem[props.field]"
                        multiple
                        disabled
                      />
                    </template>
                    <template v-slot:tags="{ props }">
                      <LooseTags
                        :value="props.dataItem[props.field]"
                        disabled
                      />
                    </template>

                    <template v-slot:message="{ item }">
                      <span :title="item.message"> {{ item.message }}</span>
                    </template>

                    <template v-slot:connection="{ item }">
                      <MTag
                        :closable="false"
                        rounded
                        :class="{
                          'tag-green': item.status === $constants.STATE_RUNNING,
                          'tag-red': item.status !== $constants.STATE_RUNNING,
                        }"
                      >
                        {{
                          item.status === $constants.STATE_RUNNING
                            ? 'Connected'
                            : 'Disconnected'
                        }}
                      </MTag>
                    </template>

                    <template v-slot:primary_tag="{ props }">
                      <MTag :closable="false" rounded class="used-count-pill">
                        {{ props.dataItem[props.field] }}
                      </MTag>
                    </template>

                    <template v-slot:exactly="{ props }">
                      {{ props.dataItem[props.field] }}
                    </template>

                    <template v-slot:server_host="{ item }">
                      <MonitorName
                        :value="item.remote_event_processor_host"
                        :row="item"
                      />
                    </template>
                  </VirtualTable>
                </div>
              </div>
            </div>

            <FlotoNoData
              v-else
              hide-svg
              header-tag="h5"
              icon="exclamation-triangle"
              variant="neutral"
            />
          </FlotoContentLoader>
        </div>
      </div>
    </div>
  </GroupProvider>
</template>

<script>
// import Config from '../config'
import { generateId } from '@utils/id'

import Bus from '@utils/emitter'
import MonitorType from '@components/monitor-type.vue'
import GroupProvider from '@components/data-provider/group-provider.vue'
import LooseTags from '@components/loose-tags.vue'
import MonitorName from '@components/widgets/views/grid/view-more/monitor-name.vue'
import buildWidgetResult from '@components/widgets/result-builder'
import { WidgetTypeConstants } from '@components/widgets/constants'
import VirtualTable from '@components/crud/virtual-table.vue'

export default {
  name: 'EventGrid',
  components: {
    MonitorType,
    GroupProvider,
    LooseTags,
    MonitorName,
    VirtualTable,
  },
  props: {
    event: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    columns: {
      type: Array,
      required: true,
    },
    transformFn: { type: Function, default: undefined },
    useWorkerTranslation: { type: Boolean, default: false },
    resultEvent: {
      type: String,
      default: undefined,
    },
    eventContext: {
      type: Object,
      default: undefined,
    },
    useUuid: {
      type: Boolean,
      default: false,
    },
    extraColumns: {
      type: Array,
      default: undefined,
    },
  },
  data() {
    return {
      gridData: [],
      loading: true,
      searchTerm: undefined,
      guid: generateId(),
    }
  },
  computed: {
    isSearchable() {
      return this.gridData && this.gridData.length > 10
    },
  },
  created() {
    Bus.$on(this.resultEvent || this.event, this.handler)
    this.requestData()
  },
  beforeDestroy() {
    Bus.$off(this.resultEvent || this.event, this.handler)
  },

  methods: {
    requestData() {
      this.loading = true
      Bus.$emit('server:event', {
        'event.type': this.event,

        ...(this.eventContext
          ? {
              'event.context': {
                ...this.eventContext,
                ...(this.useUuid
                  ? { [this.$constants.UI_EVENT_UUID]: this.guid }
                  : {}),
              },
            }
          : {
              ...(this.useUuid
                ? {
                    'event.context': {
                      ...(this.useUuid
                        ? { [this.$constants.UI_EVENT_UUID]: this.guid }
                        : {}),
                    },
                  }
                : {}),
            }),
      })
    },
    async handler(data) {
      if (this.useUuid) {
        if (data[this.$constants.UI_EVENT_UUID] !== this.guid) {
          return
        }
      }
      if (this.useWorkerTranslation) {
        const columnSettings = Object.keys(data?.result?.[0] || {})
          .filter((c) => c !== 'id')
          .map((r) => ({
            rawName: r,
            name: r,
            displayName: r,
          }))
          .concat(
            this.extraColumns && Array.isArray(this.extraColumns)
              ? this.extraColumns.map((r) => ({
                  rawName: r,
                  name: r,
                  displayName: r,
                }))
              : []
          )
        let builedResult = await buildWidgetResult(
          {
            category: WidgetTypeConstants.GRID,
            widgetProperties: {
              columnSettings,
            },
          },
          {
            result: {
              [WidgetTypeConstants.GRID]: {
                data: data.result,
              },
            },
          },

          {
            useWidgetColumnsOnly: true,
          }
        )

        this.gridData = Object.freeze(builedResult?.rows || [])
        builedResult = null
      } else {
        this.gridData = data.result.map((data) => {
          return this.transformFn ? this.transformFn(data) : data
        })
      }

      this.loading = false
    },
  },
}
</script>
