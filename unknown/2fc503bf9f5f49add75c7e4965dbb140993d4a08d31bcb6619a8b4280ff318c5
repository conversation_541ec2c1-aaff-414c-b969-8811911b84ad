<template>
  <div
    class="flex flex-col min-h-0 overflow-y-auto overflow-x-hidden"
    :class="{
      'overflow-x-hidden': !allowXOverFlow,
      'overflow-x-auto': allowXOverFlow,
    }"
  >
    <MPersistedColumns
      v-model="columns"
      :default-value="columns"
      module-key="restore-settings"
      :available-columns="availableColumns"
    >
      <template
        v-slot="{
          columns: persistedColumns,
          setColumns: updatePersistedColumns,
        }"
      >
        <FlotoContentLoader :loading="loading">
          <FlotoPaginatedCrud
            ref="paginatedCrudRef"
            class="h-100"
            as-table
            resource-name="Restore"
            default-sort="-name"
            :columns="persistedColumns"
            :fetch-fn="fetchBackupProfile"
            @column-change="updatePersistedColumns"
          >
            <template v-slot:add-controls="{ filter, resetFilter, searchTerm }">
              <MRow class="flex justify-between">
                <MCol>
                  <MInput
                    name="search-rpe"
                    :value="searchTerm"
                    class="search-box"
                    placeholder="Search"
                    @update="filter"
                  >
                    <template v-slot:prefix>
                      <MIcon name="search" />
                    </template>
                    <template v-if="searchTerm" v-slot:suffix>
                      <MIcon
                        name="times-circle"
                        class="text-neutral-light cursor-pointer"
                        @click="resetFilter"
                      />
                    </template>
                  </MInput>
                </MCol>
                <MCol class="flex-1 text-right">
                  <MButton
                    variant="neutral-lightest"
                    class="mr-2 squared-button"
                    shape="circle"
                    :shadow="false"
                    :disabled="!hasHealthMonitoringPermission"
                    @click="isRestoreUploadModalVisible = true"
                  >
                    <MIcon name="monitor-export" size="lg"></MIcon>
                  </MButton>

                  <ColumnSelector
                    v-model="columns"
                    :columns="availableColumns"
                    @change="updatePersistedColumns"
                  />
                </MCol>
              </MRow>
            </template>

            <template v-slot:size="{ item }"> {{ item.size }} MB </template>

            <template v-slot:destination="{ item }">
              <span class="flex items-center">
                <MonitorType
                  :type="
                    item.destination === 'SCP/SFTP' ? 'SFTP' : item.destination
                  "
                  class="mr-2 mt-1"
                />
                {{ item.destination }}
              </span>
            </template>
          </FlotoPaginatedCrud>
        </FlotoContentLoader>
      </template>
    </MPersistedColumns>
    <!-- <MCol :size="12" class="ml-2 mt-2">
      <span class="text-neutral"> For more information: </span>
      <a
        href="https://docs.motadata.com/motadata-aiops-docs/backup-restore-management/overview"
        target="_blank"
        >Backup and Restore Management</a
      >
      <MIcon name="external-link" class="ml-1 text-primary" />
    </MCol> -->
    <MModal
      :width="500"
      centered
      :open="isRestoreUploadModalVisible"
      @cancel="handleCancel"
    >
      <template v-slot:title>
        <div class="flex justify-between items-center">
          <p class="text-primary mt-2 ml-2">Import Backup File</p>

          <span class="mr-2 cursor-pointer" @click="handleCancel">
            <MIcon name="times" />
          </span>
        </div>
      </template>

      <FileDropper
        v-model="importFile"
        :max-files="1"
        class="bg"
        :allowed-extensions="['zip']"
        @remove="fileRemove"
        @change="handleChangeUploadFile"
      />
      <template v-slot:footer>
        <MRow class="flex-row-reverse">
          <!-- <FileDropper
            v-if="!importFile.length"
            v-model="importFile"
            mode="file"
            :max-files="1"
            :allowed-extensions="['zip']"
            @change="handleChangeUploadFile"
          >
            <MButton class="mr-2">Import</MButton>
          </FileDropper> -->

          <div v-if="!importFile.length"> </div>

          <MButton v-else class="mr-2" @click="restoreFile">Restore</MButton>

          <!-- <MButton variant="default" class="mr-2" @click="handleCancel"
            >Cancel</MButton
          > -->
        </MRow>
      </template>
    </MModal>

    <FlotoConfirmModal
      v-if="showRestoreConfirmBox !== null"
      :open="showRestoreConfirmBox !== null"
      variant="primary-alt"
      :width="500"
      :mask-closable="false"
      overlay-class-name="no-padding-confrim-modal"
      hide-icon
      @hide="handleCancelConfirmation"
    >
      <template v-slot:message>
        <h5 class="my-2 text-primary">Restore - {{ fileName }} </h5>

        <MDivider class="my-2 mx-1" />

        <FlotoForm class="" @submit="handleConfirmRestore">
          <span
            v-if="confirmStep === 1"
            v-html="$message('motadata_restore_confirmation')"
          />

          <PasswordInput
            v-else
            v-model="password"
            auto-focus
            label="Password"
            rules="required"
            info-tooltip="Enter admin user password"
          />
          <template v-slot:submit="{ submit }">
            <MRow class="flex justify-end w-full mt-4 -mb-2" :gutter="0">
              <MCol class="text-right">
                <MButton
                  class="mr-2"
                  variant="default"
                  @click="handleCancelConfirmation"
                  >Cancel</MButton
                >

                <MButton class="" @click="submit">Confirm</MButton>
              </MCol>
            </MRow>
          </template>
        </FlotoForm>
      </template>

      <template v-slot:action-container>
        <span></span>
      </template>
    </FlotoConfirmModal>
  </div>
</template>

<script>
import { authComputed } from '@state/modules/auth'
import ColumnSelector from '@components/column-selector.vue'
import CloneDeep from 'lodash/cloneDeep'
import MonitorType from '@components/monitor-type.vue'
import { fetchRestoreApi, updateRestorePassword } from '../restore-api'
import FileDropper from '@components/file-dropper.vue'
import PasswordInput from '@components/password-input.vue'

const AVAILABLE_COLUMNS = [
  {
    key: 'name',
    name: 'Backup File Name',
    searchable: true,
    sortable: true,
    disable: true,
  },
  {
    key: 'size',
    name: 'Backup Size',
    searchable: true,
    sortable: true,
    disable: true,
  },
  {
    key: 'profile',
    name: 'Backup Profile Name',
    searchable: true,
    sortable: true,
    disable: true,
  },
  {
    key: 'destination',
    name: 'Backup Location',
    searchable: true,
    sortable: true,
  },
  {
    key: 'startTime',
    name: 'Timestamp',
    searchable: true,
    sortable: true,
  },
]

export default {
  name: 'BackupProfile',
  components: {
    ColumnSelector,
    MonitorType,
    FileDropper,
    PasswordInput,
  },
  props: {
    allowXOverFlow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.allActions = [
      { key: 'edit', name: 'Edit Backup Profile', icon: 'pencil' },
    ]
    return {
      columns: CloneDeep(AVAILABLE_COLUMNS),
      showResultForm: null,
      isRestoreUploadModalVisible: false,
      importFile: [],
      showRestoreConfirmBox: null,
      fileName: undefined,
      confirmStep: 1,
      password: undefined,
    }
  },
  computed: {
    ...authComputed,
    hasHealthMonitoringPermission() {
      return this.hasPermission(
        this.$constants.HEALTH_MONITORING_CREATE_PERMISSION
      )
    },
    availableColumns() {
      return AVAILABLE_COLUMNS
    },
  },
  methods: {
    fetchBackupProfile() {
      return fetchRestoreApi()
    },

    handleChangeUploadFile(file) {
      this.fileName = file[0].name
      // if (file && file.length > 0) {
      //   const result = file[0].result
      //   const id = this.uploadFileFor.id
      //   this.uploadFileFor = undefined
      //   this.$router.push(
      //     this.$currentModule.getRoute('view-log-parser', {
      //       params: {
      //         id,
      //       },
      //       query: {
      //         file: result,
      //       },
      //     })
      //   )
      // }
    },

    restoreFile() {
      this.showRestoreConfirmBox = true
    },

    handleCancel() {
      this.isRestoreUploadModalVisible = false
      this.importFile = []
      this.fileName = undefined
    },

    handleConfirmRestore() {
      if (this.confirmStep === 2) {
        return updateRestorePassword({
          fileName: this.importFile[0].result,
          password: this.password,
        }).then(() => {
          this.showRestoreConfirmBox = null
          this.handleCancel()
          this.confirmStep = 1
        })
      } else {
        this.confirmStep = 2
      }
    },

    handleCancelConfirmation() {
      this.confirmStep = 1
      this.showRestoreConfirmBox = null
    },
    fileRemove(file) {
      this.importFile = this.importFile.filter((f) => f.result !== file.result)
    },
    // fileListUpdated(fileList) {
    //   this.importFile = fileList
    // },
  },
}
</script>
