<template>
  <!-- <FlotoContentLoader :loading="loading"> -->
  <div
    class="flex flex-col min-h-0 flex-1 bg-neutral-lightest bg-neutral-lightest mb-8"
  >
    <div class="w-full flex-1 min-h-full flex flex-col">
      <div class="flex flex-col min-h-0 flex-1" :gutter="0">
        <Widgets
          disabled
          :allow-create="false"
          :dashboard-id="dashboard.id"
          :widgets="dashboard.widgets"
          :time-range="timeRange"
          :params="widgetParams"
          :max-widgets="25"
          :dashboard-style="dashboard.style"
        >
          <template v-slot:widget="{ item, queue }">
            <ApiGrid
              v-if="
                item.options && item.options.widgetType === 'DeploymentStatus'
              "
            />

            <EventGrid
              v-else-if="
                item.options &&
                (item.options.widgetType === 'MonitorsPollingIssue' ||
                  item.options.widgetType === 'DefaultEngineStatistics' ||
                  item.options.widgetType === 'CoreEngineStatistics')
              "
              :event="getEventName(item.options.widgetType)"
              :title="title(item.options.widgetType)"
              :columns="columns(item.options.widgetType)"
              :event-context="eventContext(item.options.widgetType)"
              :use-uuid="useUuid(item.options.widgetType)"
              :use-worker-translation="useUuid(item.options.widgetType)"
              :extra-columns="extraColumns(item.options.widgetType)"
            />

            <WidgetContainer
              v-else
              :widget="widgets[item.id]"
              :queue="queue"
              :server-params="widgetParams"
              disable-refresh-interval
              disabled
              :time-range="timeRange"
              is-preview
              font-size="medium"
            />
          </template>
        </Widgets>
      </div>
    </div>
  </div>
  <!-- </FlotoContentLoader> -->
</template>

<script>
import Widgets from '@components/widgets/widgets.vue'
import WidgetContainer from '@components/widgets/views/container.vue'
import {
  healthOverviewWidgets,
  DASHBOARD,
} from '../helpers/health-overview-dashboard'
import ApiGrid from './api-grid.vue'
import EventGrid from './event-grid.vue'
import Config from '../config'
import FindIndex from 'lodash/findIndex'

export default {
  name: 'HealthOverview',
  components: {
    Widgets,
    WidgetContainer,
    ApiGrid,
    EventGrid,
  },
  props: {
    collactors: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      loading: true,
      monitor: {},
    }
  },

  computed: {
    dashboard() {
      // const collectorDashboard = DASHBOARD
      // if (!this.collector.isMasterRpe) {
      //   return {
      //     ...collectorDashboard,
      //     widgets: collectorDashboard.widgets.filter(
      //       (widget) => widget.options.widgetType !== 'LiveSessionsGrid'
      //     ),
      //   }
      // }
      return DASHBOARD
    },
    widgets() {
      return healthOverviewWidgets(this.monitor)
    },
    timeRange() {
      return {
        selectedKey: 'today',
      }
    },
    widgetParams() {
      return {
        // 'entity.type': 'monitor',
        // entities: [this.monitorId],
      }
    },
  },
  async created() {
    await this.calculateMonitor()
  },
  methods: {
    extraColumns(widget) {
      if (widget === 'CoreEngineStatistics') {
        return ['dropped.events']
      }

      return undefined
    },
    eventContext(widget) {
      if (widget === 'DefaultEngineStatistics') {
        return { 'engine.category': 'default', 'engine.type': undefined }
      } else if (widget === 'CoreEngineStatistics') {
        return { 'engine.category': 'core ', 'engine.type': undefined }
      }

      return undefined
    },
    useUuid(widget) {
      return [
        'DefaultEngineStatistics',
        'CoreEngineStatistics',
        'MonitorsPollingIssue',
      ].includes(widget)
    },
    getEventName(widget) {
      if (widget === 'MonitorsPollingIssue') {
        return Config.UI_ACTION_MONITORING_POLLING_ISSUE
      } else if (widget === 'DefaultEngineStatistics') {
        return Config.EVENT_HEALTH_STATISTICS
      } else if (widget === 'CoreEngineStatistics') {
        return Config.EVENT_HEALTH_STATISTICS
      }

      return this.$constants.UI_WIDGET_RESULT_EVENT
    },
    title(widget) {
      if (widget === 'MonitorsPollingIssue') {
        return 'Monitors (Polling Issue)'
      } else if (widget === 'DefaultEngineStatistics') {
        return 'Default Engine Statistics'
      } else if (widget === 'CoreEngineStatistics') {
        return 'Core Engine Statistics'
      }

      return ''
    },

    columns(widget) {
      if (widget === 'MonitorsPollingIssue') {
        return [
          {
            key: 'object_ip',
            name: 'Monitor',
            searchable: true,
            sortable: true,
            cellRender: 'ip',
          },
          {
            key: 'object_host',
            name: 'IP/Host Address',
            searchable: true,
            sortable: true,
          },
          {
            key: 'object_type',
            name: 'Type',
            searchable: true,
            sortable: true,
            cellRender: 'type',
          },
          {
            key: 'object_groups',
            name: 'Group',
            searchable: true,
            sortable: true,
            cellRender: 'groups',
            contextKey: 'groupContext',
            searchKey: 'groupDisplay',
            sortKey: 'groupDisplay',
          },
          {
            key: 'object_tags',
            name: 'Tags',
            searchable: true,
            sortable: true,
            cellRender: 'tags',
          },
          {
            key: 'event_timestamp',
            name: 'Last Polling Failed At',
            searchable: true,
            sortable: true,
            cellRender: 'datetime',
          },
          {
            key: 'duration',
            name: 'Duration',
            searchable: true,
            sortable: true,
            cellRender: 'duration',
          },
          {
            key: 'message',
            name: 'Reason',
            searchable: true,
            sortable: true,
          },
        ]
      } else if (widget === 'DefaultEngineStatistics') {
        return [
          {
            name: 'ip',
            key: 'remote_event_processor_ip',
            searchable: true,
            sortable: true,
          },
          {
            name: 'type',
            key: 'remote_event_processor_type',
            searchable: true,
            sortable: true,
            cellRender: 'primary_tag',
          },

          {
            name: 'name',
            key: 'engine_type',
            searchable: true,
            sortable: true,
            minWidth: '200px',
          },

          {
            name: 'pending events',
            key: 'pending_events',
            searchable: true,
            sortable: true,
          },
          {
            name: 'queued events',
            key: 'queued_events',
            searchable: true,
            sortable: true,
          },
          {
            name: 'finished events',
            key: 'finished_events',
            searchable: true,
            sortable: true,
          },
        ]
      } else if (widget === 'CoreEngineStatistics') {
        return [
          {
            name: 'ip',
            key: 'remote_event_processor_ip',
            searchable: true,
            sortable: true,
          },
          {
            name: 'type',
            key: 'remote_event_processor_type',
            searchable: true,
            sortable: true,
            cellRender: 'primary_tag',
          },

          {
            name: 'name',
            key: 'engine_type',
            searchable: true,
            sortable: true,
            minWidth: '200px',
          },

          {
            name: 'pending events',
            key: 'pending_events',
            searchable: true,
            sortable: true,
          },

          {
            name: 'dropped events',
            key: 'dropped_events',
            searchable: true,
            sortable: true,
          },
          {
            name: 'idle workers',
            key: 'idle_workers',
            searchable: true,
            sortable: true,
          },
        ]
      }

      return []
    },
    calculateMonitor() {
      let index

      let failoverIndex = FindIndex(
        this.collactors,
        (c) =>
          (c.rpeType || '').toLowerCase() === 'app' &&
          ['failover'].includes((c.mode || '').toLowerCase()) &&
          (c.heartBeatState || '') === this.$constants.STATE_RUNNING
      )

      if (failoverIndex >= 0) {
        index = failoverIndex
      }

      let secondryIndex = FindIndex(
        this.collactors,
        (c) =>
          (c.rpeType || '').toLowerCase() === 'app' &&
          ['secondary'].includes((c.mode || '').toLowerCase()) &&
          (c.heartBeatState || '') === this.$constants.STATE_RUNNING
      )

      if (secondryIndex >= 0) {
        index = secondryIndex
      }

      let primaryIndex = FindIndex(
        this.collactors,
        (c) =>
          (c.rpeType || '').toLowerCase() === 'app' &&
          ['primary'].includes((c.mode || '').toLowerCase()) &&
          (c.heartBeatState || '') === this.$constants.STATE_RUNNING
      )

      if (primaryIndex >= 0) {
        index = primaryIndex
      }

      let standaloneIndex = FindIndex(
        this.collactors,
        (c) =>
          (c.rpeType || '').toLowerCase() === 'app' &&
          ['standalone'].includes((c.mode || '').toLowerCase()) &&
          (c.heartBeatState || '') === this.$constants.STATE_RUNNING
      )

      if (standaloneIndex >= 0) {
        index = standaloneIndex
      }

      if (index >= 0) {
        this.monitor = this.collactors[index]
      }

      this.loading = false
    },
  },
}
</script>
