<template>
  <MModal
    :open="open"
    :width="520"
    :mask-closable="false"
    centered
    overlay-class-name="confirm-modal"
    @hide="hide"
  >
    <template v-slot:title>
      <h4 class="text-primary my-2">Forgot your password?</h4>
    </template>
    <MRow :gutter="0">
      <MCol :size="12" class="flex flex-col">
        <h5 v-if="showForm" class="font-500 text-neutral mb-6">
          Enter your username for password reset.
        </h5>
        <h5
          v-else
          class="font-500 text-neutral mb-6"
          :class="{ 'text-secondary-red': serverMessage }"
        >
          {{ errorMessage }}
        </h5>
        <FlotoForm v-if="showForm" @submit="validateUsername">
          <FlotoFormItem
            v-model="username"
            label="Username"
            placeholder="Username"
            rules="required"
          />
          <div v-if="serverMessage" class="mb-2 text-secondary-red">
            {{ serverMessage }}
          </div>
          <div class="my-4 text-xs text-neutral">
            Note: A temporary password will be sent to the registered Email
            address associated with your username.
            <br />If you don't receive the email, please contact your
            administrator.
          </div>
          <template v-slot:submit="{ submit }">
            <MRow class="mt-4 items-center justify-center" :gutter="20">
              <MCol :size="5">
                <MButton class="mr-2" variant="default" block @click="hide">
                  Cancel
                </MButton>
              </MCol>
              <MCol :size="5">
                <MButton :loading="processing" block @click="submit">
                  Submit
                </MButton>
              </MCol>
            </MRow>
          </template>
        </FlotoForm>
        <template v-else>
          <div v-if="errorMessage" class="my-4 text-xs text-neutral">
            Please contact your admin.
          </div>
          <MRow class="mt-4 items-center justify-center" :gutter="20">
            <MCol :size="7">
              <MButton class="ml-2" variant="default" block @click="hide">
                Close
              </MButton>
            </MCol>
          </MRow>
        </template>
      </MCol>
    </MRow>

    <template v-slot:footer>
      <slot name="footer">
        <span />
      </slot>
    </template>
  </MModal>
</template>

<script>
import { forgotPasswordApi } from '../auth-api'

export default {
  name: 'ForgotPasswordModal',
  data() {
    return {
      errorCode: undefined,
      username: undefined,
      open: true,
      processing: false,
      serverMessage: undefined,
    }
  },
  computed: {
    showForm() {
      if (['MD045', 'MD044', 'MD118'].includes(this.errorCode)) {
        return false
      }
      return true
    },
    errorMessage() {
      if (this.errorCode === 'MD045') {
        return 'Kindly contact Active Directory admin for password change. This method is not supported for Active Directory User'
      } else if (this.errorCode === 'MD044') {
        return 'Kindly use admin password change utility. This method is not supported for admin password change'
      } else if (this.errorCode === 'MD118') {
        return 'Kindly contact admin for password change. This method is not supported for Disabled User'
      }
      return undefined
    },
  },
  methods: {
    hide() {
      this.open = false
      this.$emit('hide')
    },
    validateUsername() {
      this.processing = true
      this.serverMessage = undefined
      forgotPasswordApi(this.username)
        .then(() => {
          this.$successNotification({
            title: 'Success',
            message:
              'If a user exists, you will receive a temporary password on your configured Email ID.',
          })
          this.hide()
        })
        .catch((e) => {
          const errorCode = e.response.data['error.code']
          if (errorCode === 'MD119') {
            this.$errorNotification({
              title: 'Success',
              message: `We couldn't find user with provided Username`,
            })
          } else {
            if (e.response.data.message) {
              this.serverMessage = e.response.data.message
            }
            this.errorCode = e.response.data['error.code']
          }
        })
        .finally(() => {
          this.processing = false
        })
    },
  },
}
</script>
