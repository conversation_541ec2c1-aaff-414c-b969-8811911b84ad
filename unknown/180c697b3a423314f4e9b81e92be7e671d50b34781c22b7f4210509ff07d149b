import { SEVERITY_MAP } from '@data/monitor'
import { generateId } from '@utils/id'

export const FontSizeOptions = [
  { value: 'small', label: 'Small' },
  { value: 'medium', label: 'Medium' },
  { value: 'large', label: 'Large' },
]
const sizes = [16, 24, 36, 56, 72, 88]

export const FontSizeOptionsForFreeText = [
  { key: 'auto', text: 'Auto' },
  ...sizes.map((size) => ({ key: `${size}px`, text: `${size}px` })),
]

export const ColorOptionsForFreeText = [
  { key: 'default', text: 'Default', color: 'var(--page-text-color)' },
  { key: 'gray', text: 'Gray', color: 'var(--neutral-regular)' },
  { key: 'green', text: 'Green', color: 'var(--secondary-green)' },
  { key: 'blue', text: 'Blue', color: 'var(--primary)' },
  { key: 'yellow', text: 'Yellow', color: 'var(--secondary-yellow)' },
  { key: 'orange', text: 'Orange', color: 'var(--secondary-orange)' },
  { key: 'red', text: 'Red', color: 'var(--secondary-red)' },
]

export const TextAlignOptions = [
  { value: 'left', label: 'Left' },
  { value: 'center', label: 'Center' },
  { value: 'right', label: 'Right' },
]

export const LineWidthLimits = {
  min: 1,
  max: 5,
}

export const PointSizeLimits = {
  min: 1,
  max: 10,
}

export const AVAILABILITY_INCLUDED_KEYS = Object.keys(SEVERITY_MAP)
  .map((i) => i.toLowerCase())
  .concat(['up', 'suspend'])

export const IconPositionOptions = [
  { value: 'prefix', label: 'Prefix' },
  { value: 'suffix', label: 'Suffix' },
]

export const FontSizeMap = {
  small: '10px',
  medium: '12px',
  large: '14px',
}

export const OPERATOR_MAP = {
  and: 'all',
  or: 'any',
  '=': 'Equals',
  '!=': 'Not Equals',
  '<': 'Less Than',
  '>': 'Greater Than',
  '<=': 'Less than or Equal',
  '>=': 'Greater than or Equal',
  contain: 'Contains',
  in: 'In',
  'not in': 'Not In',
  'start with': 'Start With',
  'end with': 'End With',
  'not contain': 'Not Contain',
  between: 'Between',
  range: 'Between',
}

export const AGGRIGATION_OPTIONS = {
  all: [
    { key: 'last', text: 'Last' },
    { key: 'avg', name: 'Avg' },
    { key: 'min', name: 'Min' },
    { key: 'max', name: 'Max' },
    { key: 'sum', name: 'Sum' },
    { key: 'count', text: 'Count' },
  ],
  numeric: [
    { key: 'avg', name: 'Avg' },
    { key: 'min', name: 'Min' },
    { key: 'max', name: 'Max' },
    { key: 'sum', name: 'Sum' },
    { key: 'last', text: 'Last' },
  ],
  string: [
    { key: 'count', text: 'Count' },
    { key: 'last', text: 'Last' },
  ],
  ip: [
    { key: 'count', text: 'Count' },
    { key: 'last', text: 'Last' },
  ],
}

function buildOperatorOptions(allowedOptions = []) {
  if (allowedOptions.length) {
    return Object.keys(OPERATOR_MAP)
      .filter((key) => allowedOptions.indexOf(key) >= 0)
      .map((key) => ({ key, name: OPERATOR_MAP[key] }))
  }
  return Object.keys(OPERATOR_MAP).map((key) => ({
    key,
    name: OPERATOR_MAP[key],
  }))
}

export const DATA_TYPE_OPERATORS = {
  numeric: buildOperatorOptions([
    '=',
    '>',
    '<',
    '>=',
    '<=',
    // 'between'
  ]),
  string: buildOperatorOptions([
    '=',
    'start with',
    'end with',
    'in',
    'contain',
  ]),
  all: buildOperatorOptions([
    '=',
    '>',
    '<',
    '>=',
    '<=',
    // 'between',
    'in',
    'start with',
    'end with',
    'contain',
  ]),
  ip: buildOperatorOptions([
    '=',
    '>',
    '<',
    '>=',
    '<=',
    // 'between'
  ]),
  'source.host': buildOperatorOptions(['in']),
  'source.plugin': buildOperatorOptions(['in']),
  Group: buildOperatorOptions(['in']),
  'source.type': buildOperatorOptions(['in']),
}

export const WidgetTypeConstants = {
  CHART: 'Chart',
  GRID: 'Grid',
  METRIC: 'Metric',
  PARTITIONS: 'Partitions',
  FORECAST: 'Forecast',
  ANOMALY: 'Anomaly',
  TOPN: 'TopN',
  GAUGE: 'Gauge',
  AREA: 'Area',
  LINE: 'Line',
  STACKED_AREA: 'StackedArea',
  STACKED_LINE: 'StackedLine',
  VERTICAL_BAR: 'VerticalBar',
  HORIZONTAL_BAR: 'HorizontalBar',
  STACKED_VERTICAL_BAR: 'StackedVerticalBar',
  STACKED_HORIZONTAL_BAR: 'StackedHorizontalBar',
  PIE: 'Pie',
  SOLID_GAUGE: 'SolidGauge',
  METRO_TILE: 'MetroTile',
  HEATMAP: 'HeatMap',
  SANKEY: 'Sankey',
  CUSTOM: 'Custom',
  KPI_GAUGE: 'KPI Gauge',
  APPLICATION_TODAY_AVAILABILITY: 'Application Availability',
  AVAILABILITY_TIME_SERIES: 'Availability Time Series',
  APPLICATION_AVAILABILITY_TIME_SERIES: 'Application Availability Time Series',
  MONITOR_HEALTH: 'Monitor Health',
  HARDWARE_SENSOR_GRID: 'Hardware Sensor',
  VLAN_GRID: 'VLAN',
  PORT_VIEW: 'Port View',
  INTERFACE_GRID: 'Interface',
  SWTICH_PORT_MAPPER_GRID: 'Switch Port Mapper',
  STATUS_FLAP: 'Status Flap',
  APPLICATION_STATUS: 'Application Status',
  WIRELESS_SIGNAL_STRENGTH: 'Wireless Signal Strength',
  ACCESS_POINT: 'Access Point',
  CONFIGURED_ALERT: 'Configured Alerts',
  ACTIVE_ALERT: 'Active Alerts',
  HORIZONTAL_TOPN: 'Horizontal TopN',
  RADIAL_VIEW: 'Radial View',
  PROGRESS_WITH_COUNT_VIEW: 'Progress With Count View',
  HORIZONTAL_BAR_WITH_COUNT_VIEW: 'Horizontal Bar With Count View',
  TOPN_SOLID_GAUGE_VIEW: 'TopN Solid Gauge View',
  STACKED_SWITCH_VIEW: 'Stacked Switch View',
  STREAM: 'Stream',
  HEATMAP_PLAIN: 'PlainHeatMap',
  HEATMAP_WITH_HOST: 'WithHostHeatMap',
  MAP_VIEW: 'Map',
  TREE_VIEW: 'Tree View',
  ONLINE_MAP: 'Online Map',
  ALERT_HISTORY_LIST: 'Alert History List',
  KEY_VALUE_LAYOUT: 'key-value',
  METRO_TILE_COUNT_VIEW: 'MetroTileCount',
  EVENT_HISTORY: 'event.history',
  POLLING_GRID: 'Polling Grid',
  FREE_TEXT: 'Free Text',
  ALERT_STACKED_VERTICAL_BAR: 'AlertStackedVerticalBar',
  TWO_COLUMNS_LAYOUT: '2-columns',
  OVERVIEW_LAYOUT: 'overview',
  COLUMN_LAYOUT: 'column',
  INTERFACE_TRAFFIC_VIEW: 'Interface Traffic',
  SITE_VPN_VIEW: 'Site VPN',
  STORAGE_DETAILS_VIEW: 'Storage Details',
  AWS_BILLING_VIEW: 'AWS Billing',
  PACKED_BUBBLE_CHART: 'Packed Bubble Chart',
  X_RANGE: 'X Range',
}

export const AvailableWidgetCategories = {
  [WidgetTypeConstants.CHART]: 'Chart',
  [WidgetTypeConstants.GRID]: 'Grid',
  [WidgetTypeConstants.TOPN]: 'Top N',
  [WidgetTypeConstants.GAUGE]: 'Gauge',
  [WidgetTypeConstants.HEATMAP]: 'Heat Map',
  [WidgetTypeConstants.SANKEY]: 'Sankey',
  [WidgetTypeConstants.MAP_VIEW]: 'Map',
  [WidgetTypeConstants.STREAM]: 'Stream',
  [WidgetTypeConstants.ANOMALY]: 'Anomaly',
  [WidgetTypeConstants.FORECAST]: 'Forecast',
  [WidgetTypeConstants.ACTIVE_ALERT]: 'Active Alerts',
  [WidgetTypeConstants.EVENT_HISTORY]: 'Event History',
}
export const AVAILABLE_GROUP_TYPES = {
  METRIC: 'metric',
  AVAILABILITY: 'availability',
  STATUS_FLAP: 'status.flap',
  HOURLY_STATUS_FLAP: 'hourly.status.flap',
  NETROUTE_METRIC: 'netroute.metric',
  NETROUTE_EVENT: 'netroute.event',
  SLO: 'slo',
  SLO_INSTANCE_FLAP: 'slo.instance.flap',
  SLO_FLAP: 'slo.flap',
  SLO_INSTANCE: 'slo.instance',
}

export const AVAILABLE_GROUPS = [
  { name: 'Metric', key: 'metric', color: '#099dd9' },
  { name: 'Availability', key: 'availability', color: '#099dd9' },
  { name: 'Log', key: 'log', color: '#89c540' },
  { name: 'Flow', key: 'flow', color: '#f58518' },
  { name: 'Alert', key: 'policy', color: '#f5bc18' },
  {
    name: 'NetRoute',
    key: AVAILABLE_GROUP_TYPES.NETROUTE_METRIC,
    color: '#8d3abc',
  },
  // { name: 'Custom Query', key: 'query', color: '#7b8fa5' },
]

export const PROGRESS_COLOR_MAP = [
  '#099dd9',
  // '#89c540',
  // '#f5bc18',
  // '#f58518',
  // '#f45b5b',
  // '#8d3abc',
  // '#8085E9',
  // '#3279be',
  // '#90ef7f',
]

export const DATE_FORMAT = 'YYYY/MM/DD'
export const TIME_FORMAT = 'HH:mm:ss'

export const DATE_FORMAT_REJAX = /^\d{4}\/\d{2}\/\d{2}$/
export const TIME_FORMATE_REJAX = /^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/

export const AVAILABLE_RANGE_OPTIONS = [
  { key: '-5m', text: 'Last 5 Mins', shortcut: '5m' },
  { key: '-15m', text: 'Last 15 Mins', shortcut: '15m' },
  { key: '-30m', text: 'Last 30 Mins', shortcut: '30m' },
  { key: '-1h', text: 'Last 1 Hour', shortcut: '1h' },
  { key: '-6h', text: 'Last 6 Hours', shortcut: '6h' },
  { key: '-12h', text: 'Last 12 Hours', shortcut: '12h' },
  { key: '-24h', text: 'Last 24 Hours', shortcut: '24h' },
  { key: '-48h', text: 'Last 48 Hours', shortcut: '48h' },
  {
    key: 'today',
    text: 'Today',
    shortcut: 'today',
  },
  // { key: 'yesterday', text: 'Yesterday', shortcut: 'yesterday' },
  { key: 'yesterday', text: 'Last Day', shortcut: '1d' },
  // { key: '-7d', text: 'Last Week', shortcut: '7d' },
  { key: 'last.week', text: 'Last Week', shortcut: '1w' },
  { key: 'last.month', text: 'Last Month', shortcut: '1mo' },
  { key: 'this.week', text: 'This Week', shortcut: 'week' },
  { key: 'this.month', text: 'This Month', shortcut: 'month' },
  // { key: 'quarter', text: 'This Quarter', shortcut: 'quarter' },
  // { key: 'last.quarter', text: 'Last Quarter', shortcut: '1q' },
  // { key: '-90d', text: 'Last Quarter', shortcut: '1q' },
  // { key: 'year', text: 'This Year', shortcut: 'year' },
  // { key: 'last.year', text: 'Last Year', shortcut: '1y' },
  { key: 'custom', text: 'Custom', shortcut: '' },
]

export const FILTER_CONDITION_DEFAULT_DATA = {
  condition: 'and',
  inclusion: 'include',
  groups: [
    {
      key: generateId(),
      condition: 'and',
      inclusion: 'include',
      conditions: [{ key: generateId() }],
    },
  ],
}

const DEFAULT_WIDGET_SIZE = {
  [WidgetTypeConstants.CHART]: {
    h: 3,
    w: 6,
  },
  [WidgetTypeConstants.GRID]: {
    h: 4,
    w: 6,
  },
  [WidgetTypeConstants.TOPN]: {
    h: 4,
    w: 6,
  },
  [WidgetTypeConstants.GAUGE]: {
    h: 2,
    w: 2,
  },
  [WidgetTypeConstants.HEATMAP]: {
    h: 3,
    w: 6,
  },
  [WidgetTypeConstants.SANKEY]: {
    h: 3,
    w: 6,
  },
}

export function getWidgetDefaultSize(widget) {
  if (DEFAULT_WIDGET_SIZE[widget.category]) {
    let widgetSize = DEFAULT_WIDGET_SIZE[widget.category]
    if (
      widget.category === WidgetTypeConstants.GAUGE &&
      widget.groups.find(
        (g) =>
          g.type === 'availability' || g.type === 'alert' || g.type === 'policy'
      )
    ) {
      widgetSize = { h: 4, w: 4 }
    }
    if (
      widget.category === WidgetTypeConstants.TOPN &&
      (widget.widgetType || widget.widgettype) === WidgetTypeConstants.PIE
    ) {
      widgetSize = { h: 4, w: 4 }
    }

    return widgetSize
  }
  return {
    h: 3,
    w: 6,
  }
}

export const MAP_SERIES_TYPE = {
  MAP_LINE: 'mapline',
  MAP_BUBBLE: 'mapbubble',
  MAP_POINT: 'mappoint',
}

export const MIN_COL_WIDTH = 100

export const ACTION_ORDER_INDEX = 999

export const HEALTH_GROUP_TYPE = 'health.metric'

export const MIN_EVENT_COUNT = 500

export const COLOR_PALETTES = {
  'sky.blue.slate': {
    name: 'Sky Blue Slate',
    colors: ['#335A6D', '#4F7E98', '#6FA1BB', '#A7C9DC'],
    ranges: ['0-25%', '26-50%', '51-75%', '76-100%'],
  },
  'soft.lavender': {
    name: 'Soft Lavender',
    colors: ['#5E4A8F', '#836DBE', '#AA95EA', '#C8B8F2'],
    ranges: ['0-25%', '26-50%', '51-75%', '76-100%'],
  },
  'coral.sunset': {
    name: 'Coral Sunset',
    colors: ['#8A4839', '#B8644F', '#E3856B', '#F0A892'],
    ranges: ['0-25%', '26-50%', '51-75%', '76-100%'],
  },
  'erica.pink': {
    name: 'Erica Pink',
    colors: ['#702946', '#9A3E64', '#C55A83', '#E08AA8'],
    ranges: ['0-25%', '26-50%', '51-75%', '76-100%'],
  },
  'warm.caramel': {
    name: 'Warm Caramel',
    colors: ['#4E3A24', '#755738 ', '#9B7653', '#C7A98A'],
    ranges: ['0-25%', '26-50%', '51-75%', '76-100%'],
  },
}

export const QUERY_TYPE_OPTIONS = {
  AGGREGATION: 'aggregation',
  RAW: 'raw',
}

export const lineStyleOptions = [
  { value: 'solid', text: 'Solid' },
  { value: 'dash', text: 'Dash' },
]

export const dashPatternOptions = [
  { key: '3, 3', text: '3, 3' },
  { key: '3, 6', text: '3, 6' },
  { key: '6, 3', text: '6, 3' },
  { key: '2, 4', text: '2, 4' },
  { key: '2, 2', text: '2, 2' },
  { key: '4, 2', text: '4, 2' },
  { key: '4, 6', text: '4, 6' },
  { key: '4, 4', text: '4, 4' },
]
