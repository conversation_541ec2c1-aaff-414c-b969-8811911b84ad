<template>
  <MPersistedColumns
    v-model="columns"
    :default-value="allNumericCountersForIncrementalResult"
    :module-key="`flow-explorer-test-2`"
    :available-columns="allNumericCountersForIncrementalResult"
    notify-initial-load
    @loaded="updateSelectedCounters"
  >
    <template
      v-slot="{ columns: persistedColumns, setColumns: updatePersistedColumns }"
    >
      <div class="flex flex-col flex-1">
        <div class="flex min-h-0 explorer-container" style="flex-shrink: 0">
          <div class="chart-container mt-2 mr-2 flex flex-col w-full pl-2">
            <MRow :gutter="8">
              <MCol :size="3">
                <FlotoFormItem label="Counter" rules="required">
                  <FlotoDropdownPicker
                    v-model="counterValue"
                    :options="allNumericCounters"
                  />
                </FlotoFormItem>
              </MCol>
              <MCol :size="3">
                <FlotoFormItem label="Aggregation" rules="required">
                  <FlotoDropdownPicker
                    v-model="aggregator"
                    :options="aggregatorOptions"
                  />
                </FlotoFormItem>
              </MCol>
              <MCol :size="3" class="mt-1">
                <FlotoFormItem label="Flow Source">
                  <FlotoDropdownPicker
                    v-model="source"
                    multiple
                    allow-clear
                    :options="sourceOptions"
                  />
                </FlotoFormItem>
              </MCol>
              <MCol :size="3" class="mt-1">
                <FlotoFormItem label="Result by">
                  <FlotoDropdownPicker
                    v-model="resultby"
                    multiple
                    allow-clear
                    :max-allowed-selection="4"
                    :options="resultByOptions"
                  />
                </FlotoFormItem>
              </MCol>
            </MRow>
            <div class="mb-2">
              <FiltersContainer
                v-model="filters"
                :all-counters="resultByOptions"
                use-undefined-on-clear
                group-type="flow"
                :timeline="FlowRouteContext.timeline"
                :selected-counters="availableCountersForConditions"
              />
            </div>
            <MDivider />
            <!-- <div class="flex flex-col bordered rounded-lg pt-2"> -->
            <Transition name="placeholder" mode="out-in">
              <div class="flex" style="height: 400px">
                <Widget
                  v-if="chartWidgetDefinition && shouldRenderChart"
                  :key="widgetKey"
                  :widget="chartWidgetDefinition"
                  is-preview
                  disable-server-zoom
                  disabled
                  hide-actions
                  :time-range="FlowRouteContext.timeline"
                />
                <FlotoNoData
                  v-else
                  :message="noDaataMessage"
                  hide-svg
                  header-tag="h5"
                  variant="neutral"
                />
              </div>
            </Transition>

            <div
              v-if="
                context.chartType === 'Sankey' &&
                (context.selectedKeys || []).length
              "
              class="mt-0"
            >
              <SwapController />
            </div>
          </div>
        </div>
        <!-- </div> -->

        <div class="flex-1 my-4 pl-2">
          <div class="flex w-full">
            <div class="flex-1 flex">
              <MInput
                v-model="searchTerm"
                class="search-box"
                placeholder="Search"
                name="search"
              >
                <template v-slot:prefix>
                  <MIcon name="search" />
                </template>
                <template v-if="searchTerm" v-slot:suffix>
                  <MIcon
                    name="times-circle"
                    class="text-neutral-light cursor-pointer"
                    @click="searchTerm = undefined"
                  />
                </template>
              </MInput>
              <div class="flex-1 text-right">
                <!-- <FlotoDropdownPicker
              class="inline-flex"
              use-popover
              multiple
              :as-input="false"
              placeemnt="bottomRight"
              :value="selectedCounterValue"
              :options="allNumericCountersForIncrementalResult"
              @change="handleCounterSelectionChange"
            >
              <template v-slot:trigger="{ toggle }">
                <MButton
                  id="btn-tag-inventory"
                  title="Tags"
                  :shadow="false"
                  class="squared-button mr-2"
                  :rounded="false"
                  variant="neutral-lightest"
                  @click="toggle"
                >
                  <MIcon name="eye" class="mr-2" />
                </MButton>
              </template>
            </FlotoDropdownPicker> -->

                <ColumnSelector
                  v-model="columns"
                  :columns="allNumericCountersForIncrementalResult"
                  :max-allowed-selection="MAX_COUNTER_SELECTION_LIMIT"
                  @change="
                    updatePersistedColumnsAndSelectedCounters(
                      $event,
                      updatePersistedColumns
                    )
                  "
                />
              </div>
            </div>
          </div>

          <Transition name="placeholder" mode="out-in">
            <FlotoContentLoader v-if="loading" :loading="loading" />
            <MGrid
              v-else
              :data="flowGrid.rows"
              :columns="persistedColumns"
              :search-term="searchTerm"
              @column-change="updatePersistedColumns"
            >
              <template v-slot:timestamp="{ item }">
                {{ (item.timestamp / 1000) | datetime }}
              </template>
              <!-- <template v-slot:volume_bytes_value="{ item }">
                {{ item.volume_bytes_value | bytes }}
              </template> -->
              <template v-slot:defaultCell="{ props }">
                <div class="flex items-center single-cell">
                  <span class="text-ellipsis">
                    {{ props.dataItem[props.field] }}
                  </span>
                  <span v-if="shouldShowGridFilter" class="action">
                    <FlotoDropdownPicker
                      :options="dropdownOptions"
                      :searchable="false"
                      :as-input="false"
                      :overlay-style="{ minWidth: '150px' }"
                      @change="
                        updateFilter(props.dataItem, props.field, $event)
                      "
                    >
                      <template v-slot:trigger="{ toggle }">
                        <MButton variant="transparent" @click="toggle">
                          <MIcon name="filter" />
                        </MButton>
                      </template>

                      <template v-slot:before-menu-text="{ item }">
                        <MIcon :name="`filter-${item.key}`" class="mr-1" />
                      </template>
                    </FlotoDropdownPicker>
                  </span>
                </div>
                <!-- <RelativePercentage
              v-else
              :data="flowGrid.rows"
              :field="props.field"
              :row="props.dataItem"
            /> -->
              </template>
            </MGrid>
            <!-- <div v-else style="min-height: 200px" class="flex">
          <FlotoNoData
            hide-svg
            header-tag="h5"
            variant="neutral"
            icon="exclamation-triangle"
          />
        </div> -->
          </Transition>
        </div>
      </div>
    </template>
  </MPersistedColumns>
</template>

<script>
import Bus from '@utils/emitter'
import Constants from '@constants'
import CloneDeep from 'lodash/cloneDeep'
import IsEqual from 'lodash/isEqual'
import Capitalize from 'lodash/capitalize'
import UniqBy from 'lodash/uniqBy'
import Uniq from 'lodash/uniq'

import Debounce from 'lodash/debounce'

import { generateId } from '@utils/id'
import {
  FILTER_CONDITION_DEFAULT_DATA,
  WidgetTypeConstants,
} from '@components/widgets/constants'
import Widget from '@components/widgets/views/container.vue'
import FiltersContainer from '@components/filters/filters-container.vue'
import buildWidgetResult from '@components/widgets/result-builder'
import SwapController from './explorer/swap-controller.vue'
import {
  buildFlowWidgetContext,
  OTHER_OPTIONS,
  DEFAULT_GRID_COUNTER,
  AVAILABLE_COLUMNS,
  METRIC_OPTIONS,
  AGGRIGATION_OPTIONS,
  DEFAULT_SELECTED_COUNTERS,
  // SOURCE_OPTIONS,
  // DESTINATION_OPTIONS,
} from '../helper/flow'

import { convertTimeLineForServer } from '@/src/components/widgets/helper'
import { getResultByOptionsApi } from '@components/widgets/widgets-api'
import ColumnSelector from '@components/column-selector.vue'

export default {
  name: 'Explorer',
  components: {
    Widget,
    FiltersContainer,
    SwapController,
    ColumnSelector,
  },
  inject: {
    FlowRouteContext: { default: {} },
    counterContext: { default: { options: new Map() } },
  },
  props: {
    context: {
      type: Object,
      required: true,
    },
    flowSources: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    this.MAX_COUNTER_SELECTION_LIMIT = 8

    this.disabledCounters = ['event.source', 'source.ip']

    // this.filterExcludedTabs = ['post']
    this.dropdownOptions = [
      { key: 'include', text: 'Include as filter' },
      { key: 'exclude', text: 'Exclude as filter' },
    ]
    this.counterOptions = CloneDeep(METRIC_OPTIONS)
    this.aggregatorOptions = CloneDeep(AGGRIGATION_OPTIONS)
    // this.resultbyOptions = [...SOURCE_OPTIONS, ...DESTINATION_OPTIONS]
    return {
      guid: generateId(),
      loading: true,
      flowGrid: {},
      searchTerm: undefined,
      widgetKey: 1,
      fragmentedData: [],
      queryProgress: null,
      currentBatch: 1,
      error: null,
      isPaused: false,
      resultByOptions: [],
      selectedCounterValue: [],
      columns: [],
    }
  },
  computed: {
    filters: {
      get() {
        return (
          this.context.filters || {
            pre: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
            post: CloneDeep(FILTER_CONDITION_DEFAULT_DATA),
          }
        )
      },
      set(filters) {
        this.FlowRouteContext.setContext({
          ...this.context,
          filters,
        })
      },
    },
    chartWidgetDefinition() {
      if (this.context) {
        return this.FlowRouteContext.chartWidgetDefinition
      }
      return undefined
    },
    metricColumnGridKey() {
      if (this.FlowRouteContext.context) {
        const key = (this.FlowRouteContext.context.metric || [])[0]
        if (key) {
          return key.replace(/\./g, '_')
        }
      }
      return undefined
    },
    metricKeys() {
      const otherOptions = OTHER_OPTIONS.map(({ key }) => key)
      const options = Array.from(this.counterContext.options.values()) || []
      return options
        .filter((o) => {
          return (
            o.key.indexOf('source') === -1 &&
            o.key.indexOf('destination') === -1 &&
            otherOptions.includes(o.key) === false
          )
        })
        .map(({ key }) => key)
    },
    allCounters() {
      const metric = this.metricKeys
      return Array.from(this.counterContext.options.values())
        .map((o) => ({
          key: o.key,
          text: o.name,
        }))
        .filter(({ key }) => metric.includes(key) === false)
    },

    resultByOptionsWithPlugins() {
      return Array.from(this.counterContext.options.values())
    },

    allNumericCounters() {
      return Array.from(this.counterContext.options.values())
        .filter(({ key, dataType }) => {
          return dataType.includes('numeric')
        })
        .map((o) => ({
          key: o.key,
          text: o.name,
        }))
    },
    availableColumns() {
      return this.selectedCounterValue.map((key) => {
        const modifiedKey = `${(key || '').replaceAll('.', '_')}_value`
        const column = AVAILABLE_COLUMNS.find((c) => c.key === modifiedKey)
        return (
          column || {
            key: modifiedKey,
            name: Capitalize((key || '').replaceAll('.', ' ')),
            searchable: true,
            sortable: true,
            className: 'text-ellipsis ',
          }
        )
      })
    },
    counterValue: {
      get() {
        return this.context.metric?.[0]
      },
      set(metric) {
        this.FlowRouteContext.setContext({
          ...this.context,
          metric: [metric],
        })
      },
    },
    aggregator: {
      get() {
        return this.context.aggregator?.[0]
      },
      set(aggregator) {
        this.FlowRouteContext.setContext({
          ...this.context,
          aggregator: [aggregator],
        })
      },
    },
    source: {
      get() {
        return this.context.monitor
      },
      set(monitor) {
        this.FlowRouteContext.setContext({
          ...this.context,
          monitor,
        })
      },
    },
    resultby: {
      get() {
        return this.context.selectedKeys
      },
      set(selectedKeys) {
        if (selectedKeys.length > this.$constants.FLOW_MAX_RESULT_BY) {
          selectedKeys = selectedKeys.slice(
            selectedKeys.length - this.$constants.FLOW_MAX_RESULT_BY
          )
        }
        this.FlowRouteContext.setContext({
          ...this.context,
          selectedKeys,
        })
      },
    },
    sourceOptions() {
      return this.flowSources
    },
    availableCountersForConditions() {
      const explorerContext = this.context || {}
      return [
        {
          aggrigateFn: explorerContext.aggregator?.[0],
          counterName: explorerContext.metric?.[0],
          key: explorerContext.metric?.[0],
          name: explorerContext.metric?.[0],
        },
      ]
    },
    shouldRenderChart() {
      if (
        this.context.chartType === WidgetTypeConstants.SANKEY &&
        (this.context.selectedKeys || []).length < 2
      ) {
        return false
      }
      if (
        [
          WidgetTypeConstants.VERTICAL_BAR,
          WidgetTypeConstants.HORIZONTAL_BAR,
          WidgetTypeConstants.PIE,
          WidgetTypeConstants.GRID,
        ].includes(this.context.chartType) &&
        (this.context.selectedKeys || []).length === 0
      ) {
        return false
      }
      return true
    },

    noDaataMessage() {
      if (
        this.context.chartType === WidgetTypeConstants.SANKEY &&
        (this.context.selectedKeys || []).length < 2
      ) {
        return this.$message('choose_minimum_entity', {
          count: 'two',
          name: 'Result by',
        })
      } else if ((this.context.selectedKeys || []).length === 0) {
        return this.$message('choose_minimum_entity', {
          count: 'one',
          name: 'Result by',
        })
      }
      return ''
    },

    selectedPluginIds() {
      const selectedCounterNames = this.context.metric || []
      if (selectedCounterNames.length === 0) {
        return []
      }
      return this.resultByOptionsWithPlugins
        .filter((c) => selectedCounterNames.includes(c.key))
        .reduce((prev, i) => [...prev, ...(i.metricPlugins || [])], [])
    },

    shouldShowGridFilter() {
      return (
        this.context?.filters?.pre?.groups?.length < 3 || !this.context?.filters
      )
    },

    allNumericCountersForIncrementalResult() {
      return UniqBy(
        DEFAULT_SELECTED_COUNTERS.map((c) => {
          const modifiedKey = `${(c || '').replaceAll('.', '_')}_value`
          const column = AVAILABLE_COLUMNS.find((c) => c.key === modifiedKey)

          const name = Capitalize((c || '').replaceAll('.', ' '))

          return {
            ...(column || {}),

            key: modifiedKey,
            text: name,
            name,
            ...(this.disabledCounters.includes(c)
              ? {
                  disable: true,
                }
              : {}),

            rawCounter: c,
            searchable: true,
            sortable: true,
          }
        })

          .concat(
            DEFAULT_GRID_COUNTER.map((c) => {
              const modifiedKey = `${(c || '').replaceAll('.', '_')}_value`

              const column = AVAILABLE_COLUMNS.find(
                (c) => c.key === modifiedKey
              )

              const name = Capitalize((c || '').replaceAll('.', ' '))
              return {
                ...(column || {}),
                key: modifiedKey,
                text: name,
                ...(this.disabledCounters.includes(c)
                  ? {
                      disable: true,
                    }
                  : {}),

                hidden: true,
                name,
                rawCounter: c,
                searchable: true,
                sortable: true,
              }
            })
          )
          .concat(
            this.allNumericCounters.map((c) => {
              const modifiedKey = `${(c.key || '').replaceAll('.', '_')}_value`
              const column = AVAILABLE_COLUMNS.find(
                (c) => c.key === modifiedKey
              )

              const name = Capitalize((c.key || '').replaceAll('.', ' '))
              return (
                column || {
                  ...c,
                  hidden: true,
                  key: modifiedKey,
                  name,
                  text: name,
                  rawCounter: c.key,
                  searchable: true,
                  sortable: true,
                }
              )
            })
          )
          .concat(
            this.resultByOptions.map((c) => {
              const modifiedKey = `${(c.key || '').replaceAll('.', '_')}_value`

              const column = AVAILABLE_COLUMNS.find(
                (c) => c.key === modifiedKey
              )

              const name = Capitalize((c.key || '').replaceAll('.', ' '))
              return (
                column || {
                  ...c,
                  hidden: true,
                  key: modifiedKey,
                  name,
                  text: name,
                  rawCounter: c.key,
                  searchable: true,
                  sortable: true,
                }
              )
            })
          ),
        'key'
      )
    },
  },
  watch: {
    'FlowRouteContext.timeline': function (newValue) {
      this.requestDataRaw()
      this.widgetKey++
    },
    context(newValue, oldValue) {
      if (newValue.chartType !== oldValue.chartType) {
        this.widgetKey++

        return
      }

      this.widgetKey++

      this.requestDataRaw()
    },

    selectedPluginIds(newValue, oldValue) {
      if (!IsEqual(newValue, oldValue)) {
        this.getResultByOptions()
      }
    },
    'context.chartType'(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.getResultByOptions()
      }
    },
  },
  created() {
    this.requestDataRaw = Debounce(this.startRequestData, 1000, {
      traling: true,
    })
    Bus.$on('socket:connected', this.requestDataRaw)
    Bus.$on(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleReceiveData)
    this.$once('hook:beforeDestroy', () => {
      this.abortQuery()
      this.stopHeartbeat()
      Bus.$off(this.$constants.UI_WIDGET_RESULT_EVENT, this.handleReceiveData)
      Bus.$off('socket:connected', this.requestDataRaw)
    })
    // this.requestDataRaw()
  },
  methods: {
    updateFilter(dataItem, field, action) {
      const mapObj = {
        _: '.',
        _value: '',
      }
      const filters = {
        condition: 'and',
        inclusion: action,
        key: generateId(),
        conditions: [
          {
            key: generateId(),
            operand: field.replace(/_value|_/g, (matched) => {
              return mapObj[matched]
            }),
            operator: '=',
            value:
              field.indexOf('port') > 0
                ? dataItem[field].replace(/^(\d+)\s.*$/, '$1')
                : dataItem[field],
          },
        ],
      }
      const finalData = (this.context.filters || {}).pre || {
        ...FILTER_CONDITION_DEFAULT_DATA,
        groups: [],
      }
      setTimeout(() => {
        this.FlowRouteContext.setContext({
          ...this.context,
          filters: {
            pre: {
              ...finalData,
              groups: [...finalData.groups, filters],
            },
          },
        })
      })
    },
    requestData(queryId) {
      const context = {
        ...this.context,
        metric: this.selectedCounterValue,
        selectedKeys: [],
      }
      const gridWidget = {
        ...buildFlowWidgetContext(
          context,
          this.FlowRouteContext.timeline,
          WidgetTypeConstants.CHART,
          WidgetTypeConstants.GRID,
          true
        ).generateWidgetDefinition(),
        ...(queryId ? { 'query.id': queryId } : {}),
      }
      gridWidget[Constants.UI_EVENT_UUID] = this.guid
      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_WIDGET_RESULT_EVENT,
        'event.context': gridWidget,
      })
    },

    startRequestData() {
      this.abortQuery()
      this.loading = true
      this.error = null
      this.queryProgress = null
      this.auditGridData = []
      this.isPaused = false
      this.currentBatch = 1
      this.flowGrid = {}
      this.fragmentedData = []
      this.startExplorerGridIncremental()
    },

    async startExplorerGridIncremental(queryId = null) {
      this.requestData(queryId)
    },
    async handleReceiveData(data) {
      if (data[this.$constants.UI_EVENT_UUID] !== this.guid) {
        return
      }
      this.__parentQueryId = data.result.queryMeta.parentQueryId
      this.queryProgress = data.result.queryMeta.progress
      this.fragmentedData = [
        ...(this.fragmentedData || []),
        ...(((data.result || {})[WidgetTypeConstants.GRID] || {}).data || []),
      ]
      this.loading = false
      if (data.result.error) {
        this.error = data.result.error
        if (data.result.queryMeta.progress >= 100) {
          this.stopHeartbeat()
          return
        }
      }

      const context = {
        ...this.context,
        metric: DEFAULT_GRID_COUNTER,
        selectedKeys: [],
      }
      const gridWidget = buildFlowWidgetContext(
        context,
        this.FlowRouteContext.timeline,
        WidgetTypeConstants.CHART,
        WidgetTypeConstants.GRID,
        true
      ).getContext()
      const grid = await buildWidgetResult(
        { ...gridWidget, category: 'Grid' },
        {
          ...data,
          result: {
            ...data?.result,
            [WidgetTypeConstants.GRID]: {
              ...((data.result || {})[WidgetTypeConstants.GRID] || {}),
              data: this.fragmentedData || [],
            },
          },
        },
        {
          useWidgetColumnsOnly: false,
        }
      )
      this.flowGrid = Object.freeze(grid)
      if (this.currentBatch === 1) {
        this.sendActiveSessionEvent()
        this.scheduleUpdate()
      }

      if (data.result.queryMeta.progress < 100) {
        if (this.isPaused) {
          return
        }
        setTimeout(() => {
          this.$nextTick(() => {
            this.requestNextBatch()
          })
        }, 100)
      }
    },

    async requestNextBatch() {
      this.currentBatch++
      this.startExplorerGridIncremental(this.__parentQueryId)
    },
    abortQuery() {
      if (this.__parentQueryId) {
        this.queryProgress = null
        this.isPaused = true
        Bus.$emit('server:event', {
          'event.type': this.$constants.UI_WIDGET_ABORT_EVENT,
          'event.context': {
            'query.id': this.__parentQueryId,
          },
        })
      }
    },
    sendActiveSessionEvent() {
      if (!this.__parentQueryId) {
        return
      }
      Bus.$emit('server:event', {
        'event.type': this.$constants.UI_WIDGET_ACTIVE_SESSION,
        'event.context': {
          'query.id': this.__parentQueryId,
          [this.$constants.UI_EVENT_UUID]: this.guid,
          'event.context': {
            ...(this.serverParams || {}),
            ...(this.FlowRouteContext.timeline
              ? {
                  'visualization.timeline': convertTimeLineForServer(
                    this.FlowRouteContext.timeline
                  ),
                }
              : {}),
          },
        },
      })
    },
    scheduleUpdate() {
      this.stopHeartbeat()
      this.__streamingTimer = setInterval(this.sendActiveSessionEvent, 10000)
    },
    stopHeartbeat() {
      if (this.__streamingTimer) {
        clearInterval(this.__streamingTimer)
        this.__streamingTimer = null
      }
    },
    selectIcon(key) {
      if (key.indexOf('source') >= 0) {
        return 'flow-source'
      } else if (key.indexOf('destination') >= 0) {
        return 'flow-destination'
      }
      return 'flow-other'
    },
    selectType(key) {
      if (key.indexOf('source') >= 0) {
        return 'source'
      } else if (key.indexOf('destination') >= 0) {
        return 'destination'
      }
      return 'other'
    },
    handleOrderChange(items) {
      this.FlowRouteContext.setContext({
        ...this.context,
        selectedKeys: items,
      })
    },
    removeItem(key) {
      this.FlowRouteContext.setContext({
        ...this.context,
        selectedKeys: this.context.selectedKeys.filter((i) => i !== key),
      })
    },

    getResultByOptions() {
      const pluginIds = this.selectedPluginIds
      if (pluginIds.length) {
        getResultByOptionsApi(pluginIds).then((data) => {
          this.resultByOptions = Object.freeze(
            data.map((i) => ({ key: i, text: i }))
          )
          this.resultBy = this.resultBy?.filter((i) => data.includes(i))
        })
      } else {
        this.resultByOptions = []
      }
    },
    handleCounterSelectionChange(counters) {
      if (
        counters?.length >
        this.MAX_COUNTER_SELECTION_LIMIT - this.disabledCounters.length
      ) {
        counters = counters.slice(
          counters?.length -
            (this.MAX_COUNTER_SELECTION_LIMIT - this.disabledCounters.length)
        )
      }
      counters = Uniq(this.disabledCounters.concat(counters))
      this.loading = true
      this.selectedCounterValue = counters
      this.requestDataRaw()
    },
    updateSelectedCounters(columns) {
      const selectedColumns = columns
        .filter((v) => v.hidden !== true)
        .map((v) => v.rawCounter)

      if (selectedColumns.every((v) => this.selectedCounterValue.includes(v))) {
        return
      }
      this.selectedCounterValue = selectedColumns
      this.requestDataRaw()
    },

    updatePersistedColumnsAndSelectedCounters(column, updateFn) {
      if (updateFn) {
        updateFn(column)
      }
      const selectedColumns = (
        column ||
        this.allNumericCountersForIncrementalResult ||
        []
      )
        ?.filter((v) => v.hidden !== true)
        ?.map((v) => v.rawCounter)

      if (selectedColumns.every((v) => this.selectedCounterValue.includes(v))) {
        return
      }
      this.loading = true
      this.selectedCounterValue = selectedColumns
      this.requestDataRaw()
    },
  },
}
</script>
<style lang="less" scoped>
.single-cell {
  .action {
    visibility: hidden;
  }

  &:hover {
    .action {
      visibility: visible;
    }
  }
}
</style>
