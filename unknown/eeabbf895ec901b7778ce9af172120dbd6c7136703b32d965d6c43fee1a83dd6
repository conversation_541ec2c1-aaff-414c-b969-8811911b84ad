export default {
  USER_SETTINGS_READ_PERMISSION: 'user-settings:read',
  USER_SETTINGS_CREATE_PERMISSION: 'user-settings:read-write',
  USER_SETTINGS_UPDATE_PERMISSION: 'user-settings:read-write',
  USER_SETTINGS_DELETE_PERMISSION: 'user-settings:delete',
  GROUP_SETTINGS_READ_PERMISSION: 'group-settings:read',
  GROUP_SETTINGS_CREATE_PERMISSION: 'group-settings:read-write',
  GROUP_SETTINGS_UPDATE_PERMISSION: 'group-settings:read-write',
  GROUP_SETTINGS_DELETE_PERMISSION: 'group-settings:delete',
  SYSTEM_SETTINGS_READ_PERMISSION: 'system-settings:read',
  SYSTEM_SETTINGS_CREATE_PERMISSION: 'system-settings:read-write',
  SYSTEM_SETTINGS_UPDATE_PERMISSION: 'system-settings:read-write',
  SYSTEM_SETTINGS_DELETE_PERMISSION: 'system-settings:delete',
  SNMP_TRAP_READ_PERMISSION: 'snmp-trap-settings:read',
  SNMP_TRAP_CREATE_PERMISSION: 'snmp-trap-settings:read-write',
  SNMP_TRAP_UPDATE_PERMISSION: 'snmp-trap-settings:read-write',
  SNMP_TRAP_DELETE_PERMISSION: 'snmp-trap-settings:delete',

  DISCOVERY_SETTINGS_READ_PERMISSION: 'discovery-settings:read',
  DISCOVERY_SETTINGS_CREATE_PERMISSION: 'discovery-settings:read-write',
  DISCOVERY_SETTINGS_UPDATE_PERMISSION: 'discovery-settings:read-write',
  DISCOVERY_SETTINGS_DELETE_PERMISSION: 'discovery-settings:delete',

  PLUGIN_LIBRARY_SETTINGS_READ_PERMISSION: 'plugin-library-settings:read',
  PLUGIN_LIBRARY_SETTINGS_CREATE_PERMISSION:
    'plugin-library-settings:read-write',
  PLUGIN_LIBRARY_SETTINGS_UPDATE_PERMISSION:
    'plugin-library-settings:read-write',
  PLUGIN_LIBRARY_SETTINGS_DELETE_PERMISSION: 'plugin-library-settings:delete',

  MONITOR_SETTINGS_READ_PERMISSION: 'monitor-settings:read',
  MONITOR_SETTINGS_CREATE_PERMISSION: 'monitor-settings:read-write',
  MONITOR_SETTINGS_UPDATE_PERMISSION: 'monitor-settings:read-write',
  MONITOR_SETTINGS_DELETE_PERMISSION: 'monitor-settings:delete',

  AGENT_SETTINGS_READ_PERMISSION: 'agent-settings:read',
  AGENT_SETTINGS_CREATE_PERMISSION: 'agent-settings:read-write',
  AGENT_SETTINGS_UPDATE_PERMISSION: 'agent-settings:read-write',
  AGENT_SETTINGS_DELETE_PERMISSION: 'agent-settings:delete',

  AUDIT_SETTINGS_READ_PERMISSION: 'audit-settings:read',

  MY_ACCOUNT_READ_PERMISSION: 'my-account-settings:read',
  MY_ACCOUNT_UPDATE_PERMISSION: 'my-account-settings:read-write',
  FLOW_SETTINGS_READ_PERMISSION: 'flow-settings:read',
  FLOW_SETTINGS_CREATE_PERMISSION: 'flow-settings:read-write',
  FLOW_SETTINGS_UPDATE_PERMISSION: 'flow-settings:read-write',
  FLOW_SETTINGS_DELETE_PERMISSION: 'flow-settings:delete',

  LOG_SETTINGS_READ_PERMISSION: 'log-settings:read',
  LOG_SETTINGS_CREATE_PERMISSION: 'log-settings:read-write',
  LOG_SETTINGS_UPDATE_PERMISSION: 'log-settings:read-write',
  LOG_SETTINGS_DELETE_PERMISSION: 'log-settings:delete',
  NOTIFICATION_READ_PERMISSION: 'notification-settings:read',
  POLICY_SETTINGS_READ_PERMISSION: 'policy-settings:read',
  POLICY_SETTINGS_CREATE_PERMISSION: 'policy-settings:read-write',
  POLICY_SETTINGS_UPDATE_PERMISSION: 'policy-settings:read-write',
  POLICY_SETTINGS_DELETE_PERMISSION: 'policy-settings:delete',

  DASHBOARD_SETTINGS_READ_PERMISSION: 'dashboards:read',
  DASHBOARD_SETTINGS_CREATE_PERMISSION: 'dashboards:read-write',
  DASHBOARD_SETTINGS_UPDATE_PERMISSION: 'dashboards:read-write',
  DASHBOARD_SETTINGS_DELETE_PERMISSION: 'dashboards:delete',

  WIDGET_SETTINGS_READ_PERMISSION: 'widgets:read',
  WIDGET_SETTINGS_CREATE_PERMISSION: 'widgets:read-write',
  WIDGET_SETTINGS_UPDATE_PERMISSION: 'widgets:read-write',
  WIDGET_SETTINGS_DELETE_PERMISSION: 'widgets:delete',

  TEMPLATE_SETTINGS_READ_PERMISSION: 'templates:read',
  TEMPLATE_SETTINGS_CREATE_PERMISSION: 'templates:read-write',
  TEMPLATE_SETTINGS_UPDATE_PERMISSION: 'templates:read-write',
  TEMPLATE_SETTINGS_DELETE_PERMISSION: 'templates:delete',

  INVENTORY_READ_PERMISSION: 'inventory:read',

  METRIC_EXPLORER_READ_PERMISSION: 'metric-explorers:read',
  METRIC_EXPLORER_CREATE_PERMISSION: 'metric-explorers:read-write',
  METRIC_EXPLORER_UPDATE_PERMISSION: 'metric-explorers:read-write',
  METRIC_EXPLORER_DELETE_PERMISSION: 'metric-explorers:delete',

  LOG_EXPLORER_READ_PERMISSION: 'log-explorer:read',

  FLOW_EXPLORER_READ_PERMISSION: 'flow-explorer:read',

  TRAP_EXPLORER_READ_PERMISSION: 'trap-explorer:read',

  ALERT_READ_PERMISSION: 'alert-explorer:read',
  ALERT_CREATE_PERMISSION: 'alert-explorer:read-write',
  ALERT_UPDATE_PERMISSION: 'alert-explorer:read-write',

  TOPOLOGY_READ_PERMISSION: 'topology:read',
  TOPOLOGY_CREATE_PERMISSION: 'topology:read-write',
  TOPOLOGY_UPDATE_PERMISSION: 'topology:read-write',
  TOPOLOGY_DELETE_PERMISSION: 'topology:delete',

  AIOPS_SETTINGS_READ_PERMISSION: 'aiops-settings:read',
  AIOPS_SETTINGS_CREATE_PERMISSION: 'aiops-settings:read-write',
  AIOPS_SETTINGS_UPDATE_PERMISSION: 'aiops-settings:read-write',
  AIOPS_SETTINGS_DELETE_PERMISSION: 'aiops-settings:delete',

  INTEGRATION_READ_PERMISSION: 'integrations:read',
  INTEGRATION_CREATE_PERMISSION: 'integrations:read-write',
  INTEGRATION_UPDATE_PERMISSION: 'integrations:read-write',
  INTEGRATION_DELETE_PERMISSION: 'integrations:delete',

  REPORT_READ_PERMISSION: 'reports:read',
  REPORT_CREATE_PERMISSION: 'reports:read-write',
  REPORT_UPDATE_PERMISSION: 'reports:read-write',
  REPORT_DELETE_PERMISSION: 'reports:delete',

  NCM_READ_PERMISSION: 'config:read',
  NCM_CREATE_PERMISSION: 'config:read-write',
  NCM_UPDATE_PERMISSION: 'config:read-write',
  NCM_DELETE_PERMISSION: 'config:delete',

  COMPLIANCE_READ_PERMISSION: 'compliance-settings:read',
  COMPLIANCE_CREATE_PERMISSION: 'compliance-settings:read-write',
  COMPLIANCE_UPDATE_PERMISSION: 'compliance-settings:read-write',
  COMPLIANCE_DELETE_PERMISSION: 'compliance-settings:delete',

  HEALTH_MONITORING_READ_PERMISSION: 'health-monitoring:read',
  HEALTH_MONITORING_CREATE_PERMISSION: 'health-monitoring:read-write',
  HEALTH_MONITORING_UPDATE_PERMISSION: 'health-monitoring:read-write',

  // License permissions

  USE_PREMIUM_WIDGET_CATEGORIES: 'use-premium-widget-categories',
  ADD_NEW_CUSTOM_TAB: 'add-new-custom-tab',
  ANOMALY_OUTLINER_FORECAST: 'anomaly-outliner-forecast',
  LOG_PATTEREN_MATCH: 'log-pattern-match',
  CUSTOM_GROUP: 'custom-group',
  SNMP_TRAP_LISTNER_AND_FORWARDER: 'snmp-trap-listner-and-forwarder',
  APPLICATION_DATABASE_CLOUD_LICENSE_PERMISSION:
    'application-database-cloud-license-permission',
  DEPENDENCY_MAPPING_LICENSE_PERMISSION:
    'dependency-mapping-license-permission',
  ROLE_BASE_ACCESS_LICENSE_PERMISSION: 'role-base-access-license-permission',
  PLUGIN_LIBRARY_RUNBOOK_LICENSE_PERMISSION:
    'plugin-library-runbook-license-permission',
  PLUGIN_LIBRARY_METRIC_LICENSE_PERMISSION:
    'plugin-library-metric-license-permission',
  PLUGIN_LIBRARY_TOPOLOGY_LICENSE_PERMISSION:
    'plugin-library-topology-license-permission',
  LDAP_SERVER_SETTINGS_LICENSE_PERMISSION:
    'ldap-server-settings-license-permission',
  LOG_FLOW_LICENSE_PERMISSION: 'log-flow-license-permission',
  LIVE_TRAIL_LICENSE_PERMISSION: 'live-trail-license-permission',
  NETROUTE_LICENSE_PERMISSION: 'netroute-license-permission',
  NCM_LICENSE_PERMISSION: 'ncm-license-permission',

  PLUGIN_LIBRARY_PERMISSION: 'plugin-library-permission',

  PLUGIN_LIBRARY_LOG_PARSER_PLUGIN_PERMISSION:
    'plugin-library-log-parser-permission',

  ANOMALY_LICENSE_PERMISSION: 'anomaly-license-permission',
  FORECAST_LICENSE_PERMISSION: 'forecast-license-permission',
  COMPLIANCE_LICENSE_PERMISSION: 'compliance-license-permission',
  LOG_FORWARDER_LICENSE_PERMISSION: 'log-forwarder-license-permission',

  // Netroute
  NETROUTE_READ_PERMISSION: 'netroute-explorer:read',
  NETROUTE_CREATE_PERMISSION: 'netroute-explorer:read-write',
  NETROUTE_UPDATE_PERMISSION: 'netroute-explorer:read-write',

  // Netroute Explorer
  NETROUTE_EXPLORER_READ_PERMISSION: 'netroute-explorer:read',
  NETROUTE_SETTINGS_READ_PERMISSION: 'netroute-settings:read',
  NETROUTE_SETTINGS_CREATE_PERMISSION: 'netroute-settings:read-write',
  NETROUTE_SETTINGS_UPDATE_PERMISSION: 'netroute-settings:read-write',
  NETROUTE_SETTINGS_DELETE_PERMISSION: 'netroute-settings:delete',

  // SLO
  SLO_READ_PERMISSION: 'slo-settings:read',
  SLO_CREATE_PERMISSION: 'slo-settings:read-write',
  SLO_UPDATE_PERMISSION: 'slo-settings:read-write',
  SLO_DELETE_PERMISSION: 'slo-settings:delete',
}
