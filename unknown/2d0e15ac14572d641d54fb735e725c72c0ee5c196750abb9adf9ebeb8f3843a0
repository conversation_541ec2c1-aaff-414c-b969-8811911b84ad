<template>
  <div class="flex">
    <div class="flex-1">
      <FlotoFormItem
        ref="quantityRef"
        v-model="quantity"
        validation-label="Granularity"
        :rules="validationRules"
        placeholder="Granularity"
        label="Granularity"
      />
    </div>
    <div class="flex-1 mx-2 flex self-end">
      <FlotoFormItem rules="required">
        <FlotoDropdownPicker
          v-model="unit"
          :options="unitOptions"
          placeholder="Unit"
        />
      </FlotoFormItem>
    </div>
  </div>
</template>

<script>
import Debounce from 'lodash/debounce'
import Bus from '@utils/emitter'

export default {
  name: 'GranularityInput',
  model: { event: 'change' },
  props: {
    value: {
      type: Object,
      default: undefined,
    },
  },
  computed: {
    validationRules() {
      const validationRules = {
        numeric: true,
        required: true,
        nonzero: true,
        min_value: 1,
      }
      if (this.unit === 's') {
        validationRules['max_value'] = 3600
      } else if (this.unit === 'm') {
        validationRules['max_value'] = 1440
      } else if (this.unit === 'h') {
        validationRules['max_value'] = 24
      } else if (this.unit === 'd') {
        validationRules['max_value'] = 30
      } else if (this.unit === 'M') {
        validationRules['max_value'] = 12
      }
      return validationRules
    },
    unitOptions() {
      return [
        { key: 's', text: 'Second' },
        { key: 'm', text: 'Minute' },
        { key: 'h', text: 'Hour' },
        { key: 'd', text: 'Day' },
        { key: 'M', text: 'Month' },
      ]
    },
    quantity: {
      get() {
        return (this.value || {}).value
      },
      set(value) {
        this.$emit('change', { ...(this.value || {}), value })
        this.$nextTick(this.generateNewPreview)
      },
    },
    unit: {
      get() {
        return (this.value || {}).unit
      },
      set(unit) {
        this.$emit('change', { ...(this.value || {}), unit })
        this.$nextTick(this.generateNewPreview)
      },
    },
  },
  created() {
    this.generateNewPreview = Debounce(this.generateNewPreviewRaw, 700, {
      trailing: true,
    })
  },
  methods: {
    generateNewPreviewRaw() {
      this.$refs?.quantityRef?.validate().then(({ valid }) => {
        if (valid) {
          Bus.$emit('widget.generate.preview')
        }
      })
    },
  },
}
</script>
