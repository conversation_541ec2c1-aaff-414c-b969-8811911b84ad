<template>
  <div class="flex flex-col min-h-0 flex-1 bg-neutral-lightest mb-8">
    <div class="w-full flex-1 min-h-full flex flex-col">
      <div class="flex flex-col min-h-0 flex-1" :gutter="0">
        <Widgets
          disabled
          :allow-create="false"
          :dashboard-id="dashboard.id"
          :widgets="dashboard.widgets"
          :time-range="timeRange"
          :params="widgetParams"
          :max-widgets="25"
          :dashboard-style="dashboard.style"
        >
          <template v-slot:widget="{ item, queue }">
            <WidgetContainer
              :widget="widgets[item.id]"
              :queue="queue"
              :server-params="widgetParams"
              disable-refresh-interval
              disabled
              :time-range="timeRange"
              is-preview
              font-size="medium"
            />
          </template>
        </Widgets>
      </div>
    </div>
  </div>
</template>

<script>
import Widgets from '@components/widgets/widgets.vue'
import WidgetContainer from '@components/widgets/views/container.vue'
import { databaseWidgets, DASHBOARD } from '../helpers/database-dashboard'
export default {
  name: 'Database',
  components: {
    Widgets,
    WidgetContainer,
  },
  props: {
    db: {
      type: [Array, String],
      required: true,
    },
  },

  computed: {
    dashboard() {
      // const collectorDashboard = DASHBOARD
      // if (!this.collector.isMasterRpe) {
      //   return {
      //     ...collectorDashboard,
      //     widgets: collectorDashboard.widgets.filter(
      //       (widget) => widget.options.widgetType !== 'LiveSessionsGrid'
      //     ),
      //   }
      // }
      return DASHBOARD
    },
    widgets() {
      return databaseWidgets(this.db)
    },
    timeRange() {
      return {
        selectedKey: 'today',
      }
    },
    widgetParams() {
      return {
        // 'entity.type': 'monitor',
        // entities: [this.monitorId],
      }
    },
  },
}
</script>
