<template>
  <MPermissionChecker
    :permission="[$constants.INVENTORY_READ_PERMISSION]"
    handle-redirection
  >
    <FlotoModule :use-error-handler="false">
      <FlotoFixedView>
        <GroupProvider @data-fetched="isGroupLoaded = true">
          <ObjectTagProvider
            use-key-value-pair-only
            :counter="isInstanceCategory ? { key: '~' } : {}"
          >
            <MonitoringFieldsProvider>
              <MonitorAlertCountProvider :selected-target="selectedTarget">
                <FlotoContentLoader :loading="!isGroupLoaded">
                  <div class="flex flex-col h-full flex-1 content-inner-panel">
                    <div id="monitors-tabs" class="flex items-center flex-col">
                      <div
                        class="w-full flex justify-between items-center border-bot"
                      >
                        <div class="flex-1 flex items-center pl-2">
                          <template v-if="hasLeftMenu">
                            <MenuToggleButton
                              :visible="menuVisible"
                              @click="toggleSidebar"
                            />
                            <div class="mx-2 menu-divider" />
                          </template>
                          <MTab
                            :value="category"
                            class="no-border"
                            @change="handleCategoryChange"
                          >
                            <MTabPane
                              v-for="tab in tabs"
                              :key="tab.key"
                              :tab="tab.text"
                            />
                          </MTab>
                        </div>
                        <div
                          v-if="
                            !$route.params.monitorId && shouldAllowGroupTemplate
                          "
                        >
                          <MButton
                            shape="circle"
                            :title="
                              currentView === 'widget' ? 'Grid' : 'Dashboard'
                            "
                            class="squared-button mr-2"
                            variant="neutral-lightest"
                            @click="
                              currentView =
                                currentView === 'widget' ? 'grid' : 'widget'
                            "
                          >
                            <MIcon
                              :name="
                                currentView === 'widget'
                                  ? 'grid'
                                  : 'widget-view'
                              "
                              size="lg"
                            />
                          </MButton>
                        </div>
                      </div>
                    </div>
                    <MonitorHierarchyLayout
                      ref="monitorHierarchyLayoutRef"
                      v-model="selectedTarget"
                      :category="category"
                      :has-left-menu="hasLeftMenu"
                      :affixed-sidebar="true"
                      :default-menu-visible="false"
                      :auto-expand-search-results="true"
                      should-remove-element-from-dom
                      @visible-change="menuVisible = $event"
                      @change="handleTargetChanged"
                      @hierarchy-received="handleHierarchyReceived"
                    >
                      <div v-if="$route.params.monitorId" class="w-full">
                        <Transition name="slideTop" appear>
                          <MonitorDetails
                            v-if="selectedTarget && selectedTarget.id"
                            :monitor-id="selectedTarget.id"
                            :application="
                              selectedTarget.resourceType === 'application'
                                ? selectedTarget.name
                                : undefined
                            "
                            :metric-plugin-protocol="metricPluginProtocol"
                            :vm="
                              selectedTarget.resourceType === 'vm'
                                ? selectedTarget.name
                                : undefined
                            "
                            :ap="
                              selectedTarget.resourceType === 'access.point'
                                ? selectedTarget.name
                                : undefined
                            "
                          />
                        </Transition>
                      </div>
                      <div
                        v-if="
                          $route.params.groupId && category === $constants.CLOUD
                        "
                        class="w-full"
                      >
                        <Transition name="slideTop" appear>
                          <CloudServiceTypeDetails
                            v-if="selectedTarget && selectedTarget.id"
                            v-model="cloudFilters"
                            :service-type="selectedTarget.id"
                          />
                        </Transition>
                      </div>
                      <RouterView :key="$route.fullPath" />
                    </MonitorHierarchyLayout>
                  </div>
                </FlotoContentLoader>
              </MonitorAlertCountProvider>
            </MonitoringFieldsProvider>
          </ObjectTagProvider>
        </GroupProvider>
      </FlotoFixedView>
    </FlotoModule>
  </MPermissionChecker>
</template>

<script>
import MonitorHierarchyLayout from '@views/layouts/monitor-hierarchy-layout.vue'
import GroupProvider from '@components/data-provider/group-provider.vue'
import MonitorAlertCountProvider from '@components/data-provider/monitor-alert-count-provider.vue'

import { objectDBWorker } from '@/src/workers'
import MenuToggleButton from '@components/menu-toggle-button.vue'
import {
  UserPreferenceComputed,
  UserPreferenceMethods,
} from '@state/modules/user-preference'
import MonitorDetails from '../components/monitor-details.vue'
import CloudServiceTypeDetails from '../components/cloud-service-type-details.vue'

import { authComputed } from '@/src/state/modules/auth'
import MonitoringFieldsProvider from '@components/data-provider/monitoring-fields-provider.vue'
import ObjectTagProvider from '@components/data-provider/object-tag-provider.vue'

export default {
  name: 'InventoryModule',
  components: {
    GroupProvider,
    MonitorHierarchyLayout,
    MonitorDetails,
    CloudServiceTypeDetails,
    MenuToggleButton,
    MonitorAlertCountProvider,
    MonitoringFieldsProvider,
    ObjectTagProvider,
  },
  provide() {
    const groupHierarchyContext = {}
    Object.defineProperty(groupHierarchyContext, 'hierarchy', {
      enumerable: true,
      get: () => {
        return this.monitorHierarchy
      },
    })
    const InventoryViewContext = {
      setCurrentView: this.setCurrentView,
      setMetricPluginProtocol: this.setMetricPluginProtocol,
    }
    Object.defineProperty(InventoryViewContext, 'view', {
      enumerable: true,
      get: () => {
        return this.currentView
      },
    })
    Object.defineProperty(InventoryViewContext, 'target', {
      enumerable: true,
      get: () => {
        return this.selectedTarget
      },
    })
    const CloudServiceTypeFilterContext = {}
    Object.defineProperty(CloudServiceTypeFilterContext, 'filters', {
      enumerable: true,
      get: () => {
        return this.cloudFilters
      },
    })
    return {
      groupHierarchyContext,
      InventoryViewContext,
      CloudServiceTypeFilterContext,
    }
  },
  page() {
    return {
      title: 'Monitor',
    }
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      const allowedCategories = vm.tabs.map(({ key }) => key.toLowerCase())
      if (!allowedCategories.includes(to.params.category.toLowerCase())) {
        next({ name: '404' })
      }
    })
  },
  data() {
    this.defaultTabs = [
      { key: this.$constants.SERVER, text: 'Server & Apps' },
      { key: this.$constants.NETWORK, text: 'Network' },
      { key: this.$constants.SDN, text: 'SDN' },
      { key: this.$constants.CLOUD, text: 'Cloud' },
      { key: this.$constants.SERVICE_CHECK, text: 'Service Check' },
      { key: this.$constants.VIRTUALIZATION, text: 'Virtualization' },
      { key: this.$constants.HYPERCONVERGED_INFRASTRUCTURE, text: 'HCI' },
      { key: this.$constants.STORAGE, text: 'Storage' },
      {
        key: this.$constants.CONTAINER_ORCHESTRATION,
        text: 'Container Orchestration',
      },
      { key: this.$constants.SERVICE, text: 'Service' },
      { key: this.$constants.PROCESS, text: 'Process' },
      { key: this.$constants.CONTAINER, text: 'Container' },
      { key: this.$constants.INTERFACE, text: 'Interface' },
      { key: this.$constants.WAN_LINK, text: 'WAN Link' },
      { key: this.$constants.OTHER, text: 'Other' },
    ]
    return {
      menuVisible: false,
      category: undefined,
      selectedTarget: undefined,
      monitorHierarchy: [],
      currentView: 'widget',
      cloudFilters: {},
      isGroupLoaded: false,
      metricPluginProtocol: null,
      noData: false,
    }
  },
  computed: {
    ...UserPreferenceComputed,
    ...authComputed,

    tabs() {
      if (
        !this.hasLicensePermission(
          this.$constants.APPLICATION_DATABASE_CLOUD_LICENSE_PERMISSION
        )
      ) {
        return this.defaultTabs.filter((t) => t.key !== this.$constants.CLOUD)
      }

      return this.defaultTabs
    },
    shouldAllowGroupTemplate() {
      return (
        [
          this.$constants.SERVICE,
          this.$constants.SERVICE_CHECK,
          this.$constants.OTHER,
          this.$constants.WAN_LINK,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.SDN,
          this.$constants.STORAGE,
          this.$constants.CONTAINER,
          this.$constants.CONTAINER_ORCHESTRATION,
        ].includes(this.$route.params.category) === false && !this.noData
      )
    },
    hasLeftMenu() {
      if (
        [
          this.$constants.SERVICE,
          this.$constants.PROCESS,
          this.$constants.INTERFACE,
          this.$constants.WAN_LINK,
          this.$constants.CONTAINER,
        ].includes(this.category)
      ) {
        return false
      }
      return true
    },
    isInstanceCategory() {
      return [
        this.$constants.SERVICE,
        this.$constants.PROCESS,
        this.$constants.INTERFACE,
        this.$constants.WAN_LINK,
        this.$constants.CONTAINER,
      ].includes(this.category)
    },
  },
  watch: {
    currentView(newValue) {
      if (
        [
          this.$constants.SERVICE,
          this.$constants.SERVICE_CHECK,
          this.$constants.OTHER,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.SDN,
          this.$constants.STORAGE,
        ].includes(this.category)
      ) {
        return
      }

      if (this.preference.inventoryView !== newValue) {
        this.updatePartialPreference({
          inventoryView: newValue,
        })
      }
    },
    $route: {
      immediate: true,
      async handler(newValue, oldValue) {
        this.currentView = this.preference.inventoryView
        if (newValue !== oldValue) {
          if (
            [
              this.$constants.SERVICE,
              this.$constants.SERVICE_CHECK,
              this.$constants.OTHER,
              this.$constants.WAN_LINK,
              this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
              this.$constants.SDN,
              this.$constants.STORAGE,
              this.$constants.CONTAINER,
              this.$constants.CONTAINER_ORCHESTRATION,
            ].includes((newValue || {}).params.category)
          ) {
            this.currentView = 'grid'
          }
          this.selectedTarget = undefined
          await this.setSelectedTarget()
          this.category = newValue.params.category
          if (
            (newValue.params || {}).category !==
            ((oldValue || {}).params || {}).category
          ) {
            this.monitorHierarchy = []
          }
        }
      },
    },
  },
  created() {
    // Add root level event listener
    this.$root.$on('no-data', this.handleNoData)
  },
  beforeDestroy() {
    // Clean up event listener
    this.$root.$off('no-data', this.handleNoData)
  },
  methods: {
    ...UserPreferenceMethods,
    handleNoData(value) {
      this.noData = value
    },
    setMetricPluginProtocol(protocol) {
      this.metricPluginProtocol = protocol
    },
    toggleSidebar() {
      this.$refs.monitorHierarchyLayoutRef.toggleMenu()
    },
    async setSelectedTarget() {
      // this.selectedTarget = undefined
      if (this.$route.params.monitorId && this.$route.params.application) {
        const monitor = await objectDBWorker.getObjectById(
          +this.$route.params.monitorId
        )
        if (
          monitor &&
          (monitor.apps || []).includes(this.$route.params.application)
        ) {
          this.selectedTarget = {
            id: +this.$route.params.monitorId,
            resourceType: 'application',
            name: this.$route.params.application,
          }
        } else {
          this.$router.replace({ name: '404' })
        }
        return
      }
      if (this.$route.params.monitorId && this.$route.params.vm) {
        const monitor = await objectDBWorker.getObjectById(
          +this.$route.params.monitorId
        )
        if (monitor && (monitor.vms || []).includes(this.$route.params.vm)) {
          this.selectedTarget = {
            id: +this.$route.params.monitorId,
            resourceType: 'vm',
            name: this.$route.params.vm,
          }
        } else {
          this.$router.replace({ name: '404' })
        }
        return
      }
      if (this.$route.params.monitorId && this.$route.params.ap) {
        const monitor = await objectDBWorker.getObjectById(
          +this.$route.params.monitorId
        )
        if (
          monitor &&
          (monitor.accessPoints || []).includes(this.$route.params.ap)
        ) {
          this.selectedTarget = {
            id: +this.$route.params.monitorId,
            resourceType: 'access.point',
            name: this.$route.params.ap,
          }
        } else {
          this.$router.replace({ name: '404' })
        }
        return
      }
      if (this.$route.params.monitorId || this.$route.params.groupId) {
        this.selectedTarget = {
          id: this.$route.params.monitorId
            ? +this.$route.params.monitorId
            : isNaN(this.$route.params.groupId)
            ? this.$route.params.groupId
            : +this.$route.params.groupId,
          resourceType: this.$route.params.monitorId
            ? 'monitor'
            : this.$route.params.category === this.$constants.CLOUD
            ? 'CLOUD_TYPE'
            : 'group',
          ...(this.$route.params.application
            ? { name: this.$route.params.application }
            : {}),
        }
      }
    },
    setCurrentView(view) {
      this.currentView = view
    },
    async handleHierarchyReceived($event) {
      this.monitorHierarchy = Object.freeze($event)
      await this.setSelectedTarget()
    },
    handleCategoryChange(value) {
      this.$router.push(
        this.$currentModule.getRoute('group-template', {
          params: { category: value },
        })
      )
    },
    async handleTargetChanged(value) {
      if (value.disabled) {
        return
      }
      this.selectedTarget = undefined
      await this.setSelectedTarget()
      let route
      if (value && value.resourceType === 'monitor') {
        if (
          +this.$route.params.monitorId !== value.id ||
          this.$route.name.indexOf('monitor-template') === -1
        ) {
          route = this.$currentModule.getRoute('monitor-template', {
            params: {
              monitorId: value.id,
            },
          })
        }
      } else if (value && value.resourceType === 'application') {
        route = this.$currentModule.getRoute('application-tab', {
          params: {
            monitorId: value.id,
            applicationName: value.name,
          },
        })
      } else if (value && value.resourceType === 'vm') {
        route = this.$currentModule.getRoute('vm-template', {
          params: {
            monitorId: value.resourceId,
            vm: value.name,
            category: value.category,
          },
        })
      } else if (value && value.resourceType === 'access.point') {
        route = this.$currentModule.getRoute('ap-template', {
          params: {
            monitorId: value.resourceId,
            ap: value.name,
            category: value.category,
          },
        })
      } else {
        if (value.name === this.category) {
          route = this.$currentModule.getRoute('group-template')
        } else if (+this.$route.params.groupId !== (value.id || value.key)) {
          route = this.$currentModule.getRoute('group-template', {
            params: {
              groupId: value.id || value.key,
            },
          })
        }
      }
      if (route) {
        const { href } = this.$router.resolve(route)
        if (href !== this.$route.path) {
          this.$router.push(route)
        }
      }
    },
  },
}
</script>
