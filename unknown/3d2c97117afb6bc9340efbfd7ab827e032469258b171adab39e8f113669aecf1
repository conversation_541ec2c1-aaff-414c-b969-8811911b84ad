<template>
  <MCol class="slide-filters relative">
    <MRow>
      <MCol v-if="shouldShowMonitor" class="custom-col-width">
        <FlotoFormItem label="Monitors">
          <FlotoDropdownPicker
            v-model="currentValue.monitors"
            :options="monitorOptions"
            class="w-full mt-1"
            allow-clear
            multiple
            searchable
          />
        </FlotoFormItem>
      </MCol>
      <MCol v-if="shouldShowGroup" class="custom-col-width">
        <FlotoFormItem label="Groups">
          <div class="mt-1">
            <GroupPicker
              id="filter-group-picker"
              v-model="currentValue.groups"
              class="w-full"
              multiple
              allow-clear
            />
          </div>
        </FlotoFormItem>
      </MCol>
      <MCol
        v-if="category !== 'Service' && !isInstanceGrid"
        class="custom-col-width"
      >
        <FlotoFormItem label="Types">
          <MonitorTypePicker
            id="filter-monitor-type-picker"
            v-model="currentValue.types"
            class="w-full mt-1"
            multiple
            allow-clear
            as-input
          />
        </FlotoFormItem>
      </MCol>
      <MCol v-if="category === $constants.SERVER" class="custom-col-width">
        <ApplicationTypeProvider>
          <FlotoFormItem label="Apps">
            <ApplicationTypePicker
              v-model="currentValue.apps"
              class="mt-1"
              multiple
              allow-clear
              searchable
              as-input
            />
          </FlotoFormItem>
        </ApplicationTypeProvider>
      </MCol>
      <MCol v-if="shouldShowSeverityFilter" class="custom-col-width">
        <FlotoFormItem label="Severity">
          <SeverityPicker
            v-model="currentValue.severity"
            class="mt-1"
            multiple
            allow-clear
            :disabled-options="defaultDisabledSeverities"
          />
        </FlotoFormItem>
      </MCol>
      <MCol v-if="shouldShowStatus" class="custom-col-width">
        <FlotoFormItem label="Status">
          <FlotoDropdownPicker
            v-model="currentValue.status"
            :options="statusSelection"
            class="w-full mt-1"
            multiple
            allow-clear
          />
        </FlotoFormItem>
      </MCol>
      <MCol v-if="shouldShowTags" class="custom-col-width">
        <FlotoFormItem label="Tags">
          <LooseTags
            v-model="currentValue.tags"
            class="w-full mt-1"
            as-dropdown
            placeholder="Select"
            :counter="isInstanceGrid ? { key: '~' } : {}"
          />
        </FlotoFormItem>
      </MCol>
      <MCol v-if="category === $constants.WAN_LINK" class="custom-col-width">
        <FlotoFormItem label="WAN probe type">
          <FlotoDropdownPicker
            v-model="currentValue.wanProbe"
            placeholder="Select"
            allow-clear
            :options="wanProbeOptions"
          />
        </FlotoFormItem>
      </MCol>
      <MCol class="flex justiy-between items-start custom-col-width">
        <div class="flex items-center flex-1 mt-6">
          <MButton
            id="reset-btn"
            variant="default"
            @click="
              $emit('change', {
                status: undefined,
                groups: [],
                types: [],
                severity: [],
                apps: [],
                monitors: [],
                tags: [],
                wanProbe: undefined,
              })
            "
          >
            Reset
          </MButton>
          <MButton id="apply-btn" class="ml-2" @click="apply">Apply</MButton>
        </div>
      </MCol>
    </MRow>
    <MButton
      id="close-filter"
      variant="transparent"
      :shadow="false"
      shape="circle"
      style="position: absolute; top: 5px; right: 10px; z-index: 2"
      class="monitor-agent-filter-close"
      @click="$emit('hide')"
    >
      <MIcon name="times" class="text-neutral-light" />
    </MButton>
  </MCol>
</template>

<script>
import Constants from '@constants'

import SeverityPicker from '@components/severity-picker.vue'
import MonitorTypePicker from '@components/data-picker/monitor-type-picker.vue'
import ApplicationTypePicker from '@components/data-picker/application-type-picker.vue'
import ApplicationTypeProvider from '@components/data-provider/application-type-provider.vue'
import LooseTags from '@components/loose-tags.vue'
import CloneDeep from 'lodash/cloneDeep'
import {
  wanProbeOptions,
  wanProbeMap,
} from '@/src/components/rediscover-results/rediscover-api'

const statusSelection = [
  {
    key: 'Up',
    name: 'Up',
  },
  {
    key: 'Down',
    name: 'Down',
  },
  {
    key: 'Unreachable',
    name: 'Unreachable',
  },
  {
    key: 'MAINTENANCE',
    name: 'Maintenance',
  },
  {
    key: 'SUSPEND',
    name: 'Suspend',
  },
  {
    key: 'DISABLE',
    name: 'Disable',
  },
  {
    key: 'Unknown',
    name: 'Unknown',
  },
]
export default {
  name: 'InventoryFilters',
  components: {
    MonitorTypePicker,
    ApplicationTypePicker,
    ApplicationTypeProvider,
    SeverityPicker,
    LooseTags,
  },
  model: {
    event: 'change',
  },
  props: {
    category: { type: String, required: true },
    value: { type: Object, required: true },
    monitorOptions: { type: Array, default: () => [] },
    isInstanceGrid: { type: Boolean, default: false },
  },
  data() {
    this.defaultDisabledSeverities = [
      Constants.UP,
      // Constants.UNREACHABLE,
      Constants.MAINTENANCE,
      Constants.DISABLE,
      Constants.SUSPENDED,
      Constants.UNKNOWN,
    ]
    this.statusSelection = CloneDeep(statusSelection)
    this.severityOptions = [
      'Clear',
      'Warning',
      'Major',
      'Critical',
      'Down',
      'Maintenance',
      'None',
    ].map((s) => ({ key: s, text: s }))
    this.wanProbeOptions = wanProbeOptions.map((o) => ({
      key: wanProbeMap[o.key],
      text: o.text,
    }))
    return {
      currentValue: { ...this.value },
    }
  },
  computed: {
    shouldShowGroup() {
      if (
        this.isInstanceGrid &&
        (this.category === this.$constants.NETWORK ||
          this.category === this.$constants.VIRTUALIZATION ||
          this.category === this.$constants.HYPERCONVERGED_INFRASTRUCTURE)
      ) {
        return false
      }
      return (
        [
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.CLOUD,
          this.$constants.SERVICE_CHECK,
          this.$constants.SERVICE,
          this.$constants.VIRTUALIZATION,
          this.$constants.OTHER,
          this.$constants.SDN,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.STORAGE,
        ].indexOf(this.category) >= 0
      )
    },
    shouldShowStatus() {
      if (this.isInstanceGrid && this.category === this.$constants.NETWORK) {
        return true
      }
      return (
        [
          this.$constants.SERVICE,
          this.$constants.INTERFACE,
          this.$constants.PROCESS,
          this.$constants.SERVER,
          this.$constants.NETWORK,
          this.$constants.CLOUD,
          this.$constants.SERVICE_CHECK,
          this.$constants.VIRTUALIZATION,
          this.$constants.OTHER,
          this.$constants.SDN,
          this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
          this.$constants.STORAGE,
          this.$constants.WAN_LINK,
        ].indexOf(this.category) >= 0
      )
    },
    shouldShowSeverityFilter() {
      if (
        this.isInstanceGrid &&
        (this.category === this.$constants.VIRTUALIZATION ||
          this.category === this.$constants.HYPERCONVERGED_INFRASTRUCTURE)
      ) {
        return false
      }
      return true
    },
    shouldShowMonitor() {
      if (
        this.isInstanceGrid &&
        (this.category === this.$constants.NETWORK ||
          this.category === this.$constants.VIRTUALIZATION ||
          this.category === this.$constants.HYPERCONVERGED_INFRASTRUCTURE)
      ) {
        return false
      }
      return (
        [
          this.$constants.SERVICE,
          this.$constants.INTERFACE,
          this.$constants.PROCESS,
          this.$constants.WAN_LINK,
        ].indexOf(this.category) >= 0
      )
    },
    shouldShowTags() {
      if (
        !this.isInstanceGrid ||
        this.category === this.$constants.PROCESS ||
        this.category === this.$constants.INTERFACE ||
        this.category === this.$constants.WAN_LINK
      ) {
        return true
      }
      return false
    },
  },
  watch: {
    value(newValue) {
      this.currentValue = { ...newValue }
    },
  },
  methods: {
    apply() {
      this.$emit('change', this.currentValue)
    },
  },
}
</script>

<style lang="less" scoped>
.custom-col-width {
  width: 13.5%;
  max-width: 300px;
}
</style>
