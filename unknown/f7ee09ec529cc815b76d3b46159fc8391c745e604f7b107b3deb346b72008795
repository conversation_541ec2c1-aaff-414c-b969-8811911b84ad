<template>
  <div
    class="vue-grid-item h-full flex flex-col rounded-md"
    :class="{
      'overflow-x-hidden overflow-y-scroll': !loading,
    }"
  >
    <small class="font-500 mx-2 my-2 m-0 inline-block text-xs">
      Overall Failure by Severity
    </small>

    <div v-if="loading" class="flex flex-1 w-full">
      <FlotoContentLoader loading :row-gutter="0" />
    </div>

    <FlotoNoData
      v-else-if="isNodata"
      hide-svg
      header-tag="h5"
      variant="neutral"
      icon="exclamation-triangle"
    />

    <div
      v-else
      class="flex flex-wrap justify-evenly items-center gap-4 h-full flex-1"
    >
      <div
        v-for="severity in responseData"
        :key="severity.key"
        class="severity-card flex flex-col items-center justify-center rounded border shadow-sm mx-2 mb-2 cursor-pointer"
        :class="`${severity.key.toLowerCase()}-border`"
        @click="handelDrillDown(severity.key)"
      >
        <span
          class="severity-text font-medium text-sm"
          :class="`${severity.key.toLowerCase()}-color`"
        >
          {{ severity.text }}
        </span>
        <h1
          class="severity-count text-3xl font-bold"
          :class="`${severity.key.toLowerCase()}-color`"
        >
          {{ severity.count || 0 }}
        </h1>
      </div>
    </div>
  </div>
</template>

<script>
import Capitalize from 'lodash/capitalize'

import { getFailureSeverityApi } from '../../compliance-api'

export default {
  name: 'FailureBySeverity',

  data() {
    this.severities = [
      this.$constants.CRITICAL,
      this.$constants.HIGH,
      this.$constants.MEDIUM,
      this.$constants.LOW,
      this.$constants.INFO,
    ]
    return {
      loading: true,
      responseData: [],
      isNodata: false,
    }
  },
  created() {
    this.getOverallFailedWithSeverity()
  },
  methods: {
    getOverallFailedWithSeverity() {
      return getFailureSeverityApi().then((data) => {
        this.isNodata = !Object.keys(data || {}).length
        this.responseData = this.severities.map((s) => ({
          ...(data[s] || {}),
          key: s,
          text: Capitalize(s),
        }))
        this.loading = false
      })
    },
    handelDrillDown(severity) {
      this.$emit('drill-down', {
        title: `Overall Failure by Severity - ${Capitalize(severity || '')}`,
        severity,
      })
    },
  },
}
</script>

<style lang="less" scoped>
.severity-card {
  flex: 1;
  min-width: 150px;
  max-width: 200px;
  padding: 16px;
  text-align: center;
  border: 1px solid var(--border-color);
  border-top: 6.136px solid;
  transition: transform 0.2s ease;
}

.critical-border {
  border-top-color: var(--secondary-red);
}

.high-border {
  border-top-color: var(--secondary-orange);
}

.medium-border {
  border-top-color: var(--secondary-yellow);
}

.low-border {
  border-top-color: var(--secondary-green);
}

.info-border {
  border-top-color: var(--primary);
}

.severity-text {
  margin-bottom: 8px;
}

.critical-color {
  color: var(--secondary-red);
}

.high-color {
  color: var(--secondary-orange);
}

.medium-color {
  color: var(--secondary-yellow);
}

.low-color {
  color: var(--secondary-green);
}

.info-color {
  color: var(--primary);
}

.severity-count {
  margin: 0;
}
</style>
