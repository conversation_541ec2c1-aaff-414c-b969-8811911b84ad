<template>
  <RpeGrid for-health :health-columns="columns" class="flex-1">
    <template v-slot:title>
      <div
        class="pl-2 min-w-0 text-ellipsis flex font-500 items-center mt-2 text-xs"
      >
        Deployment Status
      </div>
    </template>
  </RpeGrid>
</template>

<script>
import RpeGrid from '@modules/settings/system-settings/views/remote-poller.vue'
export default {
  name: 'APiGrid',
  components: {
    RpeGrid,
  },
  data() {
    this.columns = [
      {
        key: 'server',
        name: 'Server',
        searchable: true,
        sortable: true,
        minWidth: '200px',
        disable: true,
        sortKey: 'rpeName',
        searchKey: 'rpeName',
      },
      {
        key: 'ip',
        name: 'IP',
        searchable: true,
        sortable: true,
        minWidth: '200px',
        disable: true,
      },
      {
        key: 'rpeType',
        name: 'Type',
        searchable: true,
        sortable: true,
        minWidth: '250px',
        disable: true,
      },
      {
        key: 'mode',
        name: 'Deployment',
        searchable: true,
        sortable: true,
        minWidth: '250px',
        disable: true,
      },
      // {
      //   key: 'duration',
      //   name: 'Duration',
      //   searchable: true,
      //   sortable: true,
      //   width: '200px',
      // },
      {
        key: 'lastUpdatedTime',
        name: 'Last Contact Time',
        searchable: true,
        sortable: true,
        width: '200px',
        hidden: true,
      },
      {
        key: 'version',
        name: 'Version',
        searchable: true,
        sortable: true,
        width: '130px',
        align: 'center',
        hidden: true,
      },
      {
        key: 'duration',
        name: 'Duration',
        searchable: true,
        sortable: true,
        width: '200px',
      },
      {
        key: 'state',
        name: 'State',
        searchable: true,
        sortable: true,
        align: 'center',
        sortKey: 'heartBeatState',
        searchKey: 'heartBeatState',
      },
    ]
    return {}
  },
}
</script>
