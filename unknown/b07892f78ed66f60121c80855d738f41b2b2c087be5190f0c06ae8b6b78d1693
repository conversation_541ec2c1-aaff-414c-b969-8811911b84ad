<template>
  <div class="flex flex-col h-full">
    <div
      class="flex items-center justify-center mt-2 pb-2 border-right border-bot"
    >
      <MInput
        v-model="searchTerm"
        class="search-box"
        placeholder="Search"
        name="search"
      >
        <template v-slot:prefix>
          <MIcon name="search" />
        </template>
        <template v-if="searchTerm" v-slot:suffix>
          <MIcon
            name="times-circle"
            class="text-neutral-light cursor-pointer"
            @click="searchTerm = undefined"
          />
        </template>
      </MInput>
    </div>

    <div class="flex flex-1 min-h-0 flex-col">
      <MMenu
        class="selection-menu pt-0 pr-0 h-100 overflow-scroll"
        :selected-keys="[value]"
        @select="selectionChange"
      >
        <MMenuItem v-for="type in currentCategory" :key="type.key">
          <MIcon
            v-if="type.key === 'favorites'"
            name="star"
            class="relative cursor-pointer text-secondary-yellow"
          />

          <template v-if="type.key === 'new_category'">
            <div
              v-if="!showNewCegoryForm"
              class="text-primary w-full h-full"
              @click="showForm"
            >
              {{ type.text }}</div
            >

            <FlotoForm
              v-else
              ref="form"
              class="flex-grow flex"
              @submit="handleAddNewCategory"
            >
              <FlotoFormItem
                v-model="newCategory"
                :rules="rules"
                placeholder="Enter Category Name"
                validation-label="Category Must be Unique"
                @click.stop.prevent
              >
              </FlotoFormItem>

              <template v-slot:submit="{ submit }">
                <div class="flex items-center ml-3">
                  <a class="" @click.stop.prevent="submit">
                    <MIcon
                      name="check"
                      class="text-secondary-green"
                      size="lg"
                    /> </a
                  ><a @click.stop.prevent="cancle">
                    <MIcon name="times" class="text-secondary-red" size="lg" />
                  </a>
                </div>
              </template>
            </FlotoForm>
          </template>

          <span v-if="type.key !== 'new_category'"> {{ type.text }} </span>
        </MMenuItem>
      </MMenu>
    </div>
  </div>
</template>

<script>
import { REPORT_TYPE_FILTER_MAP } from '../helpers/report'
export default {
  name: 'ReportSideBar',
  model: { event: 'change' },
  props: {
    value: {
      type: String,
      default: undefined,
    },
    isSidebarForForm: {
      type: Boolean,
      default: false,
    },
    sidebarItems: {
      type: Array,
      required: true,
    },
    tab: {
      type: String,
      required: true,
    },
  },
  data() {
    this.defaultCategory = ['favorites', 'all', 'new_category']
    this.formTypes = [
      { text: 'Alert Report', key: 'alert' },
      { text: 'Availability Report', key: 'availability' },
      { text: 'Performance Report', key: 'metric' },
    ]
    return {
      showNewCegoryForm: false,
      newCategory: undefined,
      isNewCategorySelected: false,
      searchTerm: '',
    }
  },
  computed: {
    categories() {
      if (this.isSidebarForForm) {
        return this.formTypes
      }
      return this.sidebarItems
    },
    rules() {
      return {
        required: true,
        // not_in: this.categories
        //   .filter((c) => c.key !== 'new_category')
        //   .map((category) => category.key),
      }
    },

    currentCategory() {
      const currentCategory = this.sidebarItems.filter((category) => {
        if (this.defaultCategory.includes(category.key) || category.isNew) {
          return true
        }
        return REPORT_TYPE_FILTER_MAP[this.tab].find((type) =>
          category?.reportTypes?.includes(type)
        )
      })

      if (this.searchTerm) {
        return currentCategory.filter((category) => {
          return category?.text
            ?.toLowerCase()
            ?.includes(this.searchTerm?.toLowerCase())
          // || this.defaultCategory.includes(category.key)
        })
      }

      return currentCategory
    },
  },

  methods: {
    cancle() {
      this.showNewCegoryForm = false
      this.newCategory = undefined
    },
    showForm(forceRender) {
      if (this.isNewCategorySelected || forceRender) {
        this.showNewCegoryForm = true
      } else {
        this.cancle()
      }
    },
    handleAddNewCategory() {
      if (
        this.currentCategory
          .filter((c) => c.key !== 'new_category')
          ?.map((category) => category.key)
          ?.includes(this.newCategory)
      ) {
        this.$errorNotification({
          message: 'Category name must be unique',
        })

        return
      }
      this.$emit('add-new-category', this.newCategory)
      this.cancle()
    },
    selectionChange(event) {
      if (event.key === 'new_category') {
        if (!this.isNewCategorySelected) {
          this.showForm(true)
        }
        this.isNewCategorySelected = true
      } else {
        this.$emit('change', event.key)
        this.cancle()
        this.isNewCategorySelected = false
      }
    },
  },
}
</script>
