<template>
  <FlotoFixedView>
    <div class="flex flex-col h-full flex-1 content-inner-panel">
      <FlotoPageHeader title="Motadata Health" use-divider>
        <template v-slot:before-title>
          <MIcon name="health-monitoring" class="text-primary-alt pl-2 mr-3" />
        </template>
        <MCol
          v-if="versionInfo"
          :size="6"
          class="text-right user-box justify-end"
        >
          <MRow
            :gutter="0"
            class="justify-end rightContainer items-center h-100"
          >
            <MTag
              :closable="false"
              class="cursor-auto mx-2 tag-primary"
              variant="primary"
            >
              Motadata AIOps Version: {{ versionInfo }}
            </MTag>
          </MRow>
        </MCol>
      </FlotoPageHeader>
      <FlotoContentLoader :loading="loading">
        <MRow
          class="flex min-w-0 items-center justify-between border-bot page-background-color relative"
          style="flex-shrink: 0"
          :gutter="0"
        >
          <MTab
            v-model="tab"
            class="topology-hierarchy-tab mx-2 no-border min-w-0"
          >
            <MTabPane
              key="heath-overview"
              tab="
              Health Overview
            "
            />
            <MTabPane
              key="application"
              tab="
              Application
            "
            />

            <MTabPane
              key="database"
              tab="
              Database
            "
            />
            <MTabPane
              v-if="isPrimaryAppExist"
              key="high-availability"
              tab="
              High Availability
            "
            />

            <MTabPane
              key="live-session"
              tab="
              Live Session
            "
            />
            <!-- <MTabPane
              v-for="collector in collectors"
              :key="`${collector.monitorId}-${collector.mode}-${collector.rpeType}`"
              :tab="
                collector.isMasterRpe
                  ? `Master (${collector.ip})`
                  : `${collector.rpeName} (${collector.ip})`
              "
            /> -->
            <MTabPane
              key="alert"
              tab="
              Alert
            "
            />
            <!-- <MTabPane
              key="troubleshoot"
              tab="
              Troubleshoot
            "
            /> -->
            <MTabPane key="upgrade" tab="Upgrade" />

            <MTabPane key="restore" tab="Restore" />
          </MTab>

          <div
            v-if="tab === 'application'"
            class="inline-block py-1 px-2 flex"
            style="flex-shrink: 0; width: 30%"
          >
            <div
              class="ant-form-item-label flex items-center"
              style="line-height: normal !important"
            >
              <label title="Motadata Application" class=""
                >Motadata Application</label
              >
            </div>
            <FlotoDropdownPicker
              v-model="application"
              :options="applicationOptions"
            ></FlotoDropdownPicker>

            <MButton
              id="btn-show-hide-columns"
              :shadow="false"
              :rounded="false"
              variant="neutral-lightest"
              class="squared-button mx-2"
              :disabled="!hasHealthMonitoringPermission"
              @click="handleDiagnosticsDataCollection"
            >
              <MIcon name="health-diagnostics" />
            </MButton>
          </div>
          <div
            v-if="tab === 'database'"
            class="inline-block py-1 px-2 flex"
            style="flex-shrink: 0"
          >
            <div
              class="ant-form-item-label flex items-center"
              style="line-height: normal !important"
            >
              <label title="Motadata Store" class="">Motadata Store</label>
            </div>
            <FlotoDropdownPicker
              v-model="db"
              :options="dbOptions"
            ></FlotoDropdownPicker>

            <MButton
              id="btn-show-hide-columns"
              :shadow="false"
              :rounded="false"
              variant="neutral-lightest"
              class="squared-button mx-2"
              :disabled="!hasHealthMonitoringPermission"
              @click="handleDiagnosticsDataCollection"
            >
              <MIcon name="health-diagnostics" />
            </MButton>
          </div>
        </MRow>
        <MonitorAlertCountProvider
          v-if="tab === 'alert'"
          :selected-target="widgetParams"
        >
          <PolicyTemplate />
        </MonitorAlertCountProvider>
        <Troubleshoot v-else-if="tab === 'troubleshoot'" />
        <Upgrade v-else-if="tab === 'upgrade'" />
        <Restore v-else-if="tab === 'restore'" />
        <HealthOverview
          v-else-if="tab === 'heath-overview'"
          :collactors="collectors"
        />
        <Database v-else-if="tab === 'database'" :key="db" :db="db" />
        <Application
          v-else-if="tab === 'application'"
          :key="application"
          :application="application"
        />
        <LiveSessionsGrid
          v-else-if="tab === 'live-session'"
          :key="db"
          :db="db"
        />

        <HighAvailability v-else-if="tab === 'high-availability'" />

        <CollectorDashboard
          v-else
          :key="tab"
          :monitor-id="extractMonitorIdFormActiveTab(tab)"
          :collector="selectedCollector"
        />
      </FlotoContentLoader>
    </div>
  </FlotoFixedView>
</template>

<script>
import FindIndex from 'lodash/findIndex'
import UniqBy from 'lodash/uniqBy'
import Capitalize from 'lodash/capitalize'
import { authComputed } from '@state/modules/auth'

import Constants from '@constants'
import { fetchRPEManagmentApi } from '@modules/settings/system-settings/remote-poller-api'
import { objectDBWorker } from '@/src/workers'
import CollectorDashboard from '../components/collector-dashboard.vue'
import Troubleshoot from '../components/troubleshoot.vue'
import Upgrade from '../components/upgrade.vue'

import Restore from '../components/restore.vue'
import PolicyTemplate from '@modules/inventory/views/policy-template.vue'
import MonitorAlertCountProvider from '@components/data-provider/monitor-alert-count-provider.vue'
import HealthOverview from '../components/health-overview.vue'
import Database from '../components/database.vue'
import Application from '../components/application.vue'
import HighAvailability from '../components/high-availability.vue'
import LiveSessionsGrid from '../components/live-sessions-grid.vue'
import Api from '@api'
import Bus from '@utils/emitter'
import { generateId } from '@utils/id'

export default {
  name: 'Health',
  components: {
    Upgrade,
    Restore,
    CollectorDashboard,
    // LicenseDetails,
    PolicyTemplate,
    MonitorAlertCountProvider,
    Troubleshoot,
    HealthOverview,
    Database,
    LiveSessionsGrid,
    Application,
    HighAvailability,
  },
  inject: { SocketContext: { default: {} } },

  data() {
    this.dbOptions = [
      { key: 'primary', text: 'Primary DB' },
      { key: 'secondary', text: 'Secondry DB' },
      { key: 'replica', text: 'Replica DB' },
    ]
    this.applicationOptions = [
      { key: 'master', text: 'Motadata Master' },
      { key: 'slave', text: 'Motadata Slave' },
      { key: 'observer', text: 'Motadata Observer' },
      { key: 'secondary', text: 'Motadata Secondary Application' },
    ]
    return {
      collectors: [],
      loading: true,
      tab: null,
      versionInfo: undefined,
      db: undefined,
      dbOptions: [],
      applicationOptions: [],
      application: undefined,
    }
  },
  computed: {
    ...authComputed,
    widgetParams() {
      return {
        resourceType: 'Monitor',
        id: this.collectors
          .map((c) => c.monitorId)
          .filter((id) => !id.toString().includes('null')),
      }
    },
    selectedCollector() {
      return (
        this.collectors.filter(
          (collector) =>
            `${collector.monitorId}-${collector.mode}-${collector.rpeType}` ===
            this.tab
        )[0] || {}
      )
    },

    isPrimaryAppExist() {
      return this.collectors.find(
        (collector) =>
          (collector.rpeType || '').toLowerCase() === 'app' &&
          ['primary'].includes((collector.mode || '').toLowerCase())
      )
    },
    hasHealthMonitoringPermission() {
      return this.hasPermission(
        this.$constants.HEALTH_MONITORING_CREATE_PERMISSION
      )
    },
    // healthOverviewRpe() {
    //   const standaloneRpe = this.collectors.find(
    //     (collector) =>
    //       (collector.rpeType || '').toLowerCase() === 'app' &&
    //       ['standalone'].includes((collector.mode || '').toLowerCase())
    //   )

    //   const primaryRpe = this.collectors.find(
    //     (collector) =>
    //       (collector.rpeType || '').toLowerCase() === 'app' &&
    //       ['primary'].includes((collector.mode || '').toLowerCase())
    //   )

    //   const secondryRpe = this.collectors.find(
    //     (collector) =>
    //       (collector.rpeType || '').toLowerCase() === 'app' &&
    //       ['secondry'].includes((collector.mode || '').toLowerCase())
    //   )

    //   if (standaloneRpe || primaryRpe) {
    //     return standaloneRpe || primaryRpe || secondryRpe
    //   }

    //   return this.collectors.find((collector) => collector) || {}
    // },
  },
  created() {
    this.getAllCollectors()
    this.getMotadataAiopsVersion()
  },

  methods: {
    handleDiagnosticsDataCollection() {
      const guid = generateId()
      this.SocketContext.addGuidForEvent(
        Constants.UI_NOTIFICATION_DIAGNOSTICS_EXPORT_READY,
        guid
      )
      const selectedOption = (
        this.tab === 'application' ? this.applicationOptions : this.dbOptions
      ).find(
        (a) =>
          a.key === (this.tab === 'application' ? this.application : this.db)
      )
      Bus.$emit('server:event', {
        'event.type':
          this.$currentModule.getConfig().HEALTH_DIAGNOSTICS_DATA_COLLECTION,
        'event.context': {
          'remote.event.processor.uuid': selectedOption?.uuid,
          'remote.event.processor.type': selectedOption?.rpeType,
          [this.$constants.UI_EVENT_UUID]: guid,
        },
      })
      this.$successNotification({
        message: this.$message('health_diagnostics'),
      })
    },
    //   const selectedDatabase = this.dbOptions.find((a) => a.key === this.db)
    //   Bus.$emit('server:event', {
    //     'event.type':
    //       this.$currentModule.getConfig().HEALTH_DIAGNOSTICS_DATA_COLLECTION,
    //     'event.context': {
    //       'remote.event.processor.uuid': selectedDatabase?.uuid,
    //       'remote.event.processor.type': selectedDatabase?.rpeType,
    //     },
    //   })
    //   this.$successNotification({
    //     message: this.$message('health_diagnostics'),
    //   })
    // },
    getAllCollectors() {
      fetchRPEManagmentApi().then(async (data) => {
        let fetchedCollectors = await Promise.all(
          data.map(async (eachCollector) => {
            const monitor = await objectDBWorker.getObjectByIP(eachCollector.ip)
            return {
              ...eachCollector,
              monitorId: (monitor || {}).id || `null-${eachCollector.id}`,
              isMasterRpe:
                (eachCollector.rpeType || '').toLowerCase() === 'app' &&
                ['primary', 'standalone'].includes(
                  (eachCollector.mode || '').toLowerCase()
                ),
            }
          })
        )

        const defaultIndex = FindIndex(
          fetchedCollectors,
          (item) =>
            (item.rpeType || '').toLowerCase() === 'app' &&
            ['primary', 'standalone'].includes((item.mode || '').toLowerCase())
        )

        if (defaultIndex >= 0) {
          this.collectors = Object.freeze([
            fetchedCollectors[defaultIndex],
            ...fetchedCollectors.slice(0, defaultIndex),
            ...fetchedCollectors.slice(defaultIndex + 1),
          ])
        } else {
          this.collectors = Object.freeze(fetchedCollectors)
        }

        this.dbOptions = this.collectors
          .filter((item) => (item.rpeType || '').toLowerCase() === 'datastore')
          .map((c) => ({
            ...c,
            key: c.ip,
            text: `${Capitalize(c.mode.toLowerCase())} DB`,
          }))

        this.db = this.dbOptions?.[0]?.key

        this.applicationOptions = this.collectors
          .filter((item) =>
            [
              'app',
              'collector',
              'event_collector',
              'event_processor',
              'flow_collector',
            ].includes((item.rpeType || '').toLowerCase())
          )
          .map((c) => ({
            ...c,
            key: c.ip,
            text:
              (c.rpeType || '').toLowerCase() === 'app' &&
              c.mode.toLowerCase() !== 'observer'
                ? `${c.ip} - ${c.mode} ${c.rpeType}`
                : `${c.ip} -  ${c.rpeType}`,
          }))
        this.application = this.applicationOptions?.[0]?.key

        this.collectors = UniqBy(this.collectors, 'ip')

        // if (defaultIndex >= 0) {
        //   this.tab = `${fetchedCollectors?.[defaultIndex]?.monitorId}-${fetchedCollectors?.[defaultIndex]?.mode}-${fetchedCollectors?.[defaultIndex]?.rpeType}`
        // } else {
        //   this.tab = `${fetchedCollectors?.[0]?.monitorId}-${fetchedCollectors?.[0]?.mode}-${fetchedCollectors?.[0]?.rpeType}`
        // }

        this.tab = 'heath-overview'
        this.loading = false
      })
    },

    getMotadataAiopsVersion() {
      return Api.get('/system/motadata-app/version').then((result) => {
        this.versionInfo = result.result || {}
      })
    },
    extractMonitorIdFormActiveTab(tab) {
      return (tab || '').split('-')?.[0] || ''
    },
  },
}
</script>
